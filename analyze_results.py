#!/usr/bin/env python3
"""
Load Test Results Analyzer

This script analyzes and compares load test results from multiple test runs.
It can generate performance reports, identify trends, and highlight issues.
"""

import json
import argparse
import statistics
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import sys


class ResultsAnalyzer:
    """Analyzer for load test results"""
    
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
    
    def load_results(self, file_paths: List[str]) -> bool:
        """Load results from multiple JSON files"""
        loaded_count = 0
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r') as f:
                    result = json.load(f)
                    result['source_file'] = file_path
                    self.results.append(result)
                    loaded_count += 1
                    print(f"✓ Loaded results from {file_path}")
            except FileNotFoundError:
                print(f"✗ File not found: {file_path}")
            except json.JSONDecodeError as e:
                print(f"✗ Invalid JSON in {file_path}: {e}")
            except Exception as e:
                print(f"✗ Error loading {file_path}: {e}")
        
        print(f"\nLoaded {loaded_count} result files")
        return loaded_count > 0
    
    def generate_comparison_report(self) -> str:
        """Generate a comparison report of all loaded results"""
        if not self.results:
            return "No results loaded for comparison"
        
        report = []
        report.append("LOAD TEST COMPARISON REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Summary table
        report.append("SUMMARY TABLE")
        report.append("-" * 80)
        report.append(f"{'Test':<20} {'Requests':<10} {'Success%':<10} {'Avg RT(ms)':<12} {'95p RT(ms)':<12} {'RPS':<8}")
        report.append("-" * 80)
        
        for i, result in enumerate(self.results):
            test_name = Path(result['source_file']).stem
            overall = result.get('overall_metrics', {})
            
            total_req = result.get('total_requests', 0)
            success_rate = overall.get('success_rate', 0)
            avg_rt = overall.get('avg_response_time', 0) * 1000
            p95_rt = overall.get('p95_response_time', 0) * 1000
            rps = overall.get('throughput_rps', 0)
            
            report.append(f"{test_name:<20} {total_req:<10} {success_rate:<10.2f} {avg_rt:<12.2f} {p95_rt:<12.2f} {rps:<8.2f}")
        
        report.append("")
        
        # Detailed analysis
        report.append("DETAILED ANALYSIS")
        report.append("-" * 80)
        
        for i, result in enumerate(self.results):
            test_name = Path(result['source_file']).stem
            report.append(f"\nTest: {test_name}")
            report.append(f"Duration: {result.get('total_duration', 0):.2f} seconds")
            report.append(f"Start Time: {result.get('start_time', 'Unknown')}")
            
            overall = result.get('overall_metrics', {})
            if overall:
                report.append(f"  Total Requests: {result.get('total_requests', 0)}")
                report.append(f"  Successful: {result.get('successful_requests', 0)}")
                report.append(f"  Failed: {result.get('failed_requests', 0)}")
                report.append(f"  Success Rate: {overall.get('success_rate', 0):.2f}%")
                report.append(f"  Avg Response Time: {overall.get('avg_response_time', 0)*1000:.2f} ms")
                report.append(f"  Median Response Time: {overall.get('median_response_time', 0)*1000:.2f} ms")
                report.append(f"  95th Percentile: {overall.get('p95_response_time', 0)*1000:.2f} ms")
                report.append(f"  99th Percentile: {overall.get('p99_response_time', 0)*1000:.2f} ms")
                report.append(f"  Throughput: {overall.get('throughput_rps', 0):.2f} RPS")
            
            # User type breakdown
            auth_metrics = result.get('authenticated_metrics', {})
            anon_metrics = result.get('anonymous_metrics', {})
            
            if auth_metrics:
                report.append(f"  Authenticated Users:")
                report.append(f"    Requests: {auth_metrics.get('total_requests', 0)}")
                report.append(f"    Success Rate: {auth_metrics.get('success_rate', 0):.2f}%")
                report.append(f"    Avg Response Time: {auth_metrics.get('avg_response_time', 0)*1000:.2f} ms")
            
            if anon_metrics:
                report.append(f"  Anonymous Users:")
                report.append(f"    Requests: {anon_metrics.get('total_requests', 0)}")
                report.append(f"    Success Rate: {anon_metrics.get('success_rate', 0):.2f}%")
                report.append(f"    Avg Response Time: {anon_metrics.get('avg_response_time', 0)*1000:.2f} ms")
            
            # Errors
            errors = result.get('errors', [])
            if errors:
                report.append(f"  Errors: {len(errors)} total")
                error_summary = {}
                for error in errors:
                    key = f"{error.get('status_code', 'Unknown')}: {error.get('error_message', 'Unknown')}"
                    error_summary[key] = error_summary.get(key, 0) + 1
                
                for error_type, count in error_summary.items():
                    report.append(f"    {error_type}: {count}")
        
        # Performance trends
        if len(self.results) > 1:
            report.append("\nPERFORMANCE TRENDS")
            report.append("-" * 80)
            
            response_times = []
            success_rates = []
            throughputs = []
            
            for result in self.results:
                overall = result.get('overall_metrics', {})
                response_times.append(overall.get('avg_response_time', 0) * 1000)
                success_rates.append(overall.get('success_rate', 0))
                throughputs.append(overall.get('throughput_rps', 0))
            
            if response_times:
                rt_trend = self._calculate_trend(response_times)
                report.append(f"Response Time Trend: {rt_trend}")
            
            if success_rates:
                sr_trend = self._calculate_trend(success_rates)
                report.append(f"Success Rate Trend: {sr_trend}")
            
            if throughputs:
                tp_trend = self._calculate_trend(throughputs)
                report.append(f"Throughput Trend: {tp_trend}")
        
        # Recommendations
        report.append("\nRECOMMENDations")
        report.append("-" * 80)
        recommendations = self._generate_recommendations()
        for rec in recommendations:
            report.append(f"• {rec}")
        
        return "\n".join(report)
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for a series of values"""
        if len(values) < 2:
            return "Insufficient data"
        
        # Simple linear trend calculation
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        
        if abs(slope) < 0.01:
            return "Stable"
        elif slope > 0:
            return f"Increasing (+{slope:.2f})"
        else:
            return f"Decreasing ({slope:.2f})"
    
    def _generate_recommendations(self) -> List[str]:
        """Generate performance recommendations based on results"""
        recommendations = []
        
        if not self.results:
            return ["No results available for analysis"]
        
        # Analyze latest result for recommendations
        latest_result = self.results[-1]
        overall = latest_result.get('overall_metrics', {})
        
        success_rate = overall.get('success_rate', 0)
        avg_rt = overall.get('avg_response_time', 0) * 1000
        p95_rt = overall.get('p95_response_time', 0) * 1000
        errors = latest_result.get('errors', [])
        
        # Success rate recommendations
        if success_rate < 95:
            recommendations.append("Low success rate detected. Check server capacity and error logs.")
        elif success_rate < 99:
            recommendations.append("Success rate could be improved. Monitor for intermittent issues.")
        
        # Response time recommendations
        if avg_rt > 500:
            recommendations.append("High average response time. Consider optimizing database queries or caching.")
        elif avg_rt > 200:
            recommendations.append("Response time is acceptable but could be optimized.")
        
        if p95_rt > 1000:
            recommendations.append("High 95th percentile response time indicates performance outliers.")
        
        # Error analysis
        if errors:
            error_types = set(error.get('status_code', 0) for error in errors)
            if 500 in error_types:
                recommendations.append("Server errors (5xx) detected. Check application logs and server health.")
            if 429 in error_types:
                recommendations.append("Rate limiting detected. Consider reducing request rate or increasing limits.")
            if 401 in error_types or 403 in error_types:
                recommendations.append("Authentication errors detected. Verify token validity.")
        
        # Performance comparison
        if len(self.results) > 1:
            prev_result = self.results[-2]
            prev_overall = prev_result.get('overall_metrics', {})
            
            prev_rt = prev_overall.get('avg_response_time', 0) * 1000
            if avg_rt > prev_rt * 1.2:
                recommendations.append("Response time degradation detected compared to previous test.")
            
            prev_success = prev_overall.get('success_rate', 0)
            if success_rate < prev_success * 0.95:
                recommendations.append("Success rate degradation detected compared to previous test.")
        
        if not recommendations:
            recommendations.append("Performance looks good! Consider testing with higher load.")
        
        return recommendations
    
    def export_csv(self, output_file: str) -> bool:
        """Export results summary to CSV format"""
        try:
            import csv
            
            with open(output_file, 'w', newline='') as csvfile:
                fieldnames = [
                    'test_name', 'total_requests', 'successful_requests', 'failed_requests',
                    'success_rate', 'avg_response_time_ms', 'median_response_time_ms',
                    'p95_response_time_ms', 'p99_response_time_ms', 'throughput_rps',
                    'total_duration', 'start_time'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for result in self.results:
                    test_name = Path(result['source_file']).stem
                    overall = result.get('overall_metrics', {})
                    
                    row = {
                        'test_name': test_name,
                        'total_requests': result.get('total_requests', 0),
                        'successful_requests': result.get('successful_requests', 0),
                        'failed_requests': result.get('failed_requests', 0),
                        'success_rate': overall.get('success_rate', 0),
                        'avg_response_time_ms': overall.get('avg_response_time', 0) * 1000,
                        'median_response_time_ms': overall.get('median_response_time', 0) * 1000,
                        'p95_response_time_ms': overall.get('p95_response_time', 0) * 1000,
                        'p99_response_time_ms': overall.get('p99_response_time', 0) * 1000,
                        'throughput_rps': overall.get('throughput_rps', 0),
                        'total_duration': result.get('total_duration', 0),
                        'start_time': result.get('start_time', '')
                    }
                    writer.writerow(row)
            
            print(f"✓ CSV export saved to {output_file}")
            return True
            
        except Exception as e:
            print(f"✗ CSV export failed: {e}")
            return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Analyze load test results")
    
    parser.add_argument("files", nargs="+", help="JSON result files to analyze")
    parser.add_argument("--output", help="Output file for the report")
    parser.add_argument("--csv", help="Export summary to CSV file")
    parser.add_argument("--format", choices=["text", "json"], default="text",
                       help="Output format for the report")
    
    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = ResultsAnalyzer()
    
    # Load results
    if not analyzer.load_results(args.files):
        print("No valid result files loaded")
        sys.exit(1)
    
    # Generate report
    if args.format == "text":
        report = analyzer.generate_comparison_report()
    else:
        # JSON format (simplified)
        report = json.dumps({
            "summary": "Load test analysis",
            "results_count": len(analyzer.results),
            "files_analyzed": [result['source_file'] for result in analyzer.results]
        }, indent=2)
    
    # Output report
    if args.output:
        try:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"✓ Report saved to {args.output}")
        except Exception as e:
            print(f"✗ Failed to save report: {e}")
    else:
        print(report)
    
    # Export CSV if requested
    if args.csv:
        analyzer.export_csv(args.csv)


if __name__ == "__main__":
    main()
