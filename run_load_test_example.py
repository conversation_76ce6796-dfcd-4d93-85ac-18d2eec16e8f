#!/usr/bin/env python3
"""
Example script demonstrating how to run comprehensive load tests
for the recommendation API endpoint.

This script shows different testing scenarios and how to interpret results.
"""

import asyncio
import subprocess
import json
import sys
from pathlib import Path


def run_command(command: list, description: str) -> bool:
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print('='*60)
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"Command not found: {command[0]}")
        return False


def load_and_display_results(results_file: str):
    """Load and display key metrics from results file"""
    try:
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        print(f"\n{'='*60}")
        print("QUICK RESULTS SUMMARY")
        print('='*60)
        
        overall = results.get('overall_metrics', {})
        print(f"Total Requests: {results.get('total_requests', 0)}")
        print(f"Success Rate: {overall.get('success_rate', 0):.2f}%")
        print(f"Average Response Time: {overall.get('avg_response_time', 0)*1000:.2f} ms")
        print(f"95th Percentile: {overall.get('p95_response_time', 0)*1000:.2f} ms")
        print(f"Throughput: {overall.get('throughput_rps', 0):.2f} RPS")
        
        if results.get('errors'):
            print(f"Errors: {len(results['errors'])} occurred")
        
    except FileNotFoundError:
        print(f"Results file {results_file} not found")
    except json.JSONDecodeError:
        print(f"Invalid JSON in results file {results_file}")


def main():
    """Run example load testing scenarios"""
    
    print("Recommendation API Load Testing Examples")
    print("="*60)
    
    # Check if Python is available
    try:
        subprocess.run([sys.executable, "--version"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("Python not available")
        return
    
    # Scenario 1: Basic smoke test
    print("\n🔥 SCENARIO 1: Smoke Test (Basic functionality)")
    smoke_test_cmd = [
        sys.executable, "load_test_recommendations.py",
        "--concurrent-users", "5",
        "--total-requests", "25",
        "--requests-per-second", "5",
        "--output", "smoke_test_results.json",
        "--verbose"
    ]
    
    if run_command(smoke_test_cmd, "Smoke Test"):
        load_and_display_results("smoke_test_results.json")
    
    # Scenario 2: Load test with anonymous users only
    print("\n📊 SCENARIO 2: Anonymous Users Load Test")
    anon_test_cmd = [
        sys.executable, "load_test_recommendations.py",
        "--concurrent-users", "20",
        "--total-requests", "100",
        "--requests-per-second", "15",
        "--no-authenticated",
        "--output", "anonymous_test_results.json"
    ]
    
    if run_command(anon_test_cmd, "Anonymous Users Load Test"):
        load_and_display_results("anonymous_test_results.json")
    
    # Scenario 3: Configuration file test
    print("\n⚙️  SCENARIO 3: Configuration File Test")
    
    # Check if config file exists
    if Path("load_test_config.json").exists():
        config_test_cmd = [
            sys.executable, "load_test_recommendations.py",
            "--config", "load_test_config.json"
        ]
        
        if run_command(config_test_cmd, "Configuration File Test"):
            load_and_display_results("load_test_results.json")
    else:
        print("⚠️  load_test_config.json not found, skipping configuration file test")
    
    # Scenario 4: Stress test (if user confirms)
    print("\n🚀 SCENARIO 4: Stress Test (Optional)")
    response = input("Run stress test with 50 concurrent users? (y/N): ").lower().strip()
    
    if response == 'y':
        stress_test_cmd = [
            sys.executable, "load_test_recommendations.py",
            "--concurrent-users", "50",
            "--total-requests", "200",
            "--requests-per-second", "30",
            "--output", "stress_test_results.json"
        ]
        
        if run_command(stress_test_cmd, "Stress Test"):
            load_and_display_results("stress_test_results.json")
    else:
        print("Skipping stress test")
    
    # Authentication helper example
    print("\n🔐 AUTHENTICATION HELPER EXAMPLE")
    print("To test with authenticated users:")
    print("1. Update sample_credentials.json with real user credentials")
    print("2. Run: python auth_helper.py --credentials-file sample_credentials.json")
    print("3. Use generated tokens in load test:")
    print("   python load_test_recommendations.py --auth-tokens token1 token2 token3")
    
    # Performance analysis tips
    print("\n📈 PERFORMANCE ANALYSIS TIPS")
    print("-" * 40)
    print("Good Performance Indicators:")
    print("  • Success rate > 99%")
    print("  • Average response time < 200ms")
    print("  • 95th percentile < 500ms")
    print("  • Consistent throughput")
    print()
    print("Warning Signs:")
    print("  • Success rate < 95%")
    print("  • Response times increasing with load")
    print("  • High error rates")
    print("  • Timeouts or connection errors")
    
    print("\n✅ Load testing examples completed!")
    print("Check the generated *_results.json files for detailed metrics.")


if __name__ == "__main__":
    main()
