
services:
  memetracker:
    container_name: memetracker
    image: gitlab.aurora:5050/toci/api/memetracker:${VERSION:-latest}
    build: 
      context: .
      dockerfile: src/memetracker/Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
        BASE_IMAGE: gitlab.aurora:5050/toci/api/base:v1.1
      target: prod
    env_file:
      - /etc/api.env
    command: ["start.sh"]
    networks:
      - toci-net

  memeprice:
    container_name: memeprice
    image: gitlab.aurora:5050/toci/api/memeprice:${VERSION:-latest}
    build: 
      context: .
      dockerfile: src/memeprice/Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
        BASE_IMAGE: gitlab.aurora:5050/toci/api/base:v1.1
      target: prod
    env_file:
      - /etc/api.env
    command: ["start.sh"]
    networks:
      - toci-net

  memekline:
    container_name: memekline
    image: gitlab.aurora:5050/toci/api/memekline:${VERSION:-latest}
    build: 
      context: .
      dockerfile: src/memekline/Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
        BASE_IMAGE: gitlab.aurora:5050/toci/api/base:v1.1
      target: prod
    env_file:
      - /etc/api.env
    command: ["start.sh"]
    networks:
      - toci-net

#  memekline_tick:
#    container_name: memekline_tick
#    image: gitlab.aurora:5050/toci/api/memekline_tick:${VERSION:-latest}
#    build:
#      context: .
#      dockerfile: src/memekline_tick/Dockerfile
#      args:
#        BUILDKIT_INLINE_CACHE: 1
#        BASE_IMAGE: gitlab.aurora:5050/toci/api/base:v1.1
#      target: prod
#    env_file:
#      - /etc/api.env
#    volumes:
#      - ./src/memecoin:/src/memecoin
#      - ./src/memekline:/src/memekline
#    command: ["start.sh"]
#    networks:
#      - toci-net

  memekafka:
    container_name: memekafka
    image: gitlab.aurora:5050/toci/api/memekafka:${VERSION:-latest}
    build: 
      context: .
      dockerfile: src/memekafka/Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
        BASE_IMAGE: gitlab.aurora:5050/toci/api/base:v1.1
      target: prod
    env_file:
      - /etc/api.env
    command: ["start.sh"]
    networks:
      - toci-net

networks:
  toci-net:
    external: true
