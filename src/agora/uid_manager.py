import random
import hashlib
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from redis.asyncio import Redis

from .models import LiveChannel, ChannelAudience
from .settings import settings
from src.common.redis_cli import RedisCli


class UIDManager:
    """UID 管理器，负责统一分配和管理用户 UID"""
    
    def __init__(self):
        self.redis_client = None
        self.uid_range_start = settings.UID_RANGE_START
        self.uid_range_end = settings.UID_RANGE_END
    
    async def init_redis(self):
        """初始化 Redis 连接"""
        if not self.redis_client:
            self.redis_client = RedisCli.async_()
    
    async def allocate_uid(
        self, 
        db: Session, 
        user_id: str, 
        channel_name: str,
        preferred_uid: Optional[int] = None
    ) -> int:
        """
        为用户分配 UID
        
        Args:
            db: 数据库会话
            user_id: 业务用户ID
            channel_name: 频道名称
            preferred_uid: 客户端指定的 UID（可选）
            
        Returns:
            int: 分配的 UID
        """
        await self.init_redis()
        
        # 检查是否已经为该用户在该频道分配过 UID
        existing_uid = await self._get_cached_uid(user_id, channel_name)
        if existing_uid:
            return existing_uid
        
        # 根据配置决定 UID 分配方式
        if settings.UID_ALLOCATION_MODE == "client" and preferred_uid:
            # 客户端指定 UID 模式
            if await self._is_uid_available(db, channel_name, preferred_uid):
                allocated_uid = preferred_uid
            else:
                # 如果客户端指定的 UID 不可用，后端重新分配
                allocated_uid = await self._generate_backend_uid(db, channel_name)
        else:
            # 后端统一分配模式
            allocated_uid = await self._generate_backend_uid(db, channel_name)
        
        # 缓存 UID 映射关系
        await self._cache_uid_mapping(user_id, channel_name, allocated_uid)
        
        return allocated_uid
    
    async def release_uid(self, user_id: str, channel_name: str) -> bool:
        """
        释放用户 UID
        
        Args:
            user_id: 业务用户ID
            channel_name: 频道名称
            
        Returns:
            bool: 释放结果
        """
        await self.init_redis()
        
        # 清除缓存的 UID 映射
        cache_key = f"{settings.REDIS_UID_PREFIX}{user_id}:{channel_name}"
        await self.redis_client.delete(cache_key)
        
        # 清除频道内 UID 占用记录
        uid = await self._get_cached_uid(user_id, channel_name)
        if uid:
            channel_uid_key = f"{settings.REDIS_CHANNEL_PREFIX}{channel_name}:uids"
            await self.redis_client.srem(channel_uid_key, str(uid))
        
        return True
    
    async def get_user_uid(self, user_id: str, channel_name: str) -> Optional[int]:
        """
        获取用户在指定频道的 UID
        
        Args:
            user_id: 业务用户ID
            channel_name: 频道名称
            
        Returns:
            Optional[int]: 用户 UID
        """
        return await self._get_cached_uid(user_id, channel_name)
    
    async def get_channel_uids(self, channel_name: str) -> list:
        """
        获取频道内所有活跃的 UID
        
        Args:
            channel_name: 频道名称
            
        Returns:
            list: UID 列表
        """
        await self.init_redis()
        
        channel_uid_key = f"{settings.REDIS_CHANNEL_PREFIX}{channel_name}:uids"
        uids = await self.redis_client.smembers(channel_uid_key)
        return [int(uid) for uid in uids]
    
    async def validate_uid_uniqueness(self, db: Session, channel_name: str, uid: int) -> bool:
        """
        验证 UID 在频道内的唯一性
        
        Args:
            db: 数据库会话
            channel_name: 频道名称
            uid: 要验证的 UID
            
        Returns:
            bool: 是否唯一
        """
        return await self._is_uid_available(db, channel_name, uid)
    
    def generate_uid_from_user_id(self, user_id: str, channel_name: str) -> int:
        """
        基于用户ID和频道名生成确定性的 UID
        
        Args:
            user_id: 业务用户ID
            channel_name: 频道名称
            
        Returns:
            int: 生成的 UID
        """
        # 使用哈希算法生成确定性的 UID
        content = f"{user_id}:{channel_name}"
        hash_value = hashlib.md5(content.encode()).hexdigest()
        
        # 将哈希值转换为指定范围内的整数
        uid = int(hash_value[:8], 16) % (self.uid_range_end - self.uid_range_start) + self.uid_range_start
        return uid
    
    async def _get_cached_uid(self, user_id: str, channel_name: str) -> Optional[int]:
        """从缓存获取用户 UID"""
        await self.init_redis()
        
        cache_key = f"{settings.REDIS_UID_PREFIX}{user_id}:{channel_name}"
        cached_uid = await self.redis_client.get(cache_key)
        
        if cached_uid:
            return int(cached_uid)
        return None
    
    async def _cache_uid_mapping(self, user_id: str, channel_name: str, uid: int):
        """缓存 UID 映射关系"""
        await self.init_redis()
        
        # 缓存用户 -> UID 映射
        cache_key = f"{settings.REDIS_UID_PREFIX}{user_id}:{channel_name}"
        await self.redis_client.setex(cache_key, 3600, str(uid))  # 1小时过期
        
        # 记录频道内活跃 UID
        channel_uid_key = f"{settings.REDIS_CHANNEL_PREFIX}{channel_name}:uids"
        await self.redis_client.sadd(channel_uid_key, str(uid))
        await self.redis_client.expire(channel_uid_key, 3600)  # 1小时过期
    
    async def _is_uid_available(self, db: Session, channel_name: str, uid: int) -> bool:
        """检查 UID 在频道内是否可用"""
        await self.init_redis()
        
        # 检查缓存中是否已被占用
        channel_uid_key = f"{settings.REDIS_CHANNEL_PREFIX}{channel_name}:uids"
        is_cached = await self.redis_client.sismember(channel_uid_key, str(uid))
        
        if is_cached:
            return False
        
        # 检查数据库中是否有活跃用户使用该 UID
        channel = db.query(LiveChannel).filter(
            LiveChannel.channel_name == channel_name
        ).first()
        
        if not channel:
            return True
        
        # 注意：现在 user_id 是字符串类型，这里的逻辑需要重新考虑
        # 因为 uid 是整数而 user_id 是字符串，不能直接比较
        # 这个检查可能需要重新设计或移除
        active_audience = None  # 暂时禁用这个检查
        
        return active_audience is None
    
    async def _generate_backend_uid(self, db: Session, channel_name: str) -> int:
        """后端生成可用的 UID"""
        max_attempts = 100
        
        for _ in range(max_attempts):
            # 随机生成 UID
            uid = random.randint(self.uid_range_start, self.uid_range_end)
            
            if await self._is_uid_available(db, channel_name, uid):
                return uid
        
        # 如果随机生成失败，使用顺序查找
        for uid in range(self.uid_range_start, self.uid_range_end + 1):
            if await self._is_uid_available(db, channel_name, uid):
                return uid
        
        raise ValueError("No available UID in the specified range")

# 全局 UID 管理器实例
uid_manager = UIDManager() 