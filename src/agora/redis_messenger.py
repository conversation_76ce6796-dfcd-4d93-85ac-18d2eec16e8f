"""
Redis消息管理器 - 用于红包实时通知的发布订阅机制
"""
import asyncio
import json
import logging
from typing import Optional, Dict, Any, Callable, Awaitable, List
import redis.asyncio as redis
import redis as sync_redis

from src.common.redis_cli import RedisCli
from src.agora.settings import settings

logger = logging.getLogger(__name__)


class RedisMessenger:
    """Redis消息管理器，处理红包相关的发布订阅"""
    
    # Redis频道名称
    RED_ENVELOPE_CHANNEL = "red_envelope_updates"
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.sync_redis_client: Optional[sync_redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None
        self.is_listening = False
    
    async def init_redis(self):
        """初始化异步Redis连接"""
        if not self.redis_client:
            self.redis_client = RedisCli.async_()
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis消息管理器连接成功")
    
    def init_sync_redis(self):
        """初始化同步Redis连接"""
        if not self.sync_redis_client:
            self.sync_redis_client = RedisCli.sync()
            
            # 测试连接
            self.sync_redis_client.ping()
            logger.info("同步Redis消息管理器连接成功")

    async def publish_audience_count_update(
        self,
        channel_name: str,
        current_audience: int,
    ) -> None:
        """发布观众人数更新事件（异步版本）

        消息会发布到与红包相同的频道，便于现有 WS 统一订阅。
        """
        try:
            await self.init_redis()

            message = {
                "type": "audience_count",
                "channel_name": channel_name,
                "data": {
                    "current_audience": int(current_audience),
                    "timestamp": "now",  # 接收方用当前时间替换
                },
            }

            await self.redis_client.publish(
                self.RED_ENVELOPE_CHANNEL,
                json.dumps(message),
            )

            logger.info(
                f"发布观众人数更新事件: channel={channel_name}, current={current_audience}"
            )
        except Exception as e:
            logger.error(f"发布观众人数更新事件失败: {str(e)}")
            raise
    
    async def publish_red_envelope_created(
        self, 
        envelope_id: str,
        channel_name: str,
        creator_user_id: str,
        creator_name: str,
        creator_avatar: str,
        token_address: str,
        total_amount: str,
        usd_amount: float,
        operation_id: str,
        gift_name: Optional[str] = None,
        gift_image: Optional[str] = None,
        token_name: Optional[str] = None,
        token_symbol: Optional[str] = None
    ):
        """发布红包创建事件（异步版本）
        
        Args:
            envelope_id: 红包ID
            channel_name: 频道名称
            creator_user_id: 创建者用户ID
            creator_name: 创建者用户名 (即hostname)
            creator_avatar: 创建者头像URL (即avatar)
            token_address: 代币地址
            total_amount: 代币数量
            usd_amount: USD金额
            operation_id: 操作ID
            gift_name: 礼物名称（可选）
            gift_image: 礼物图标URL（可选）
            token_name: 代币名称（可选）
            token_symbol: 代币符号（可选）
        """
        try:
            await self.init_redis()
            
            message = {
                "type": "red_envelope_created",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "creator_user_id": creator_user_id,
                    "creator_name": creator_name,
                    "creator_avatar": creator_avatar,
                    "token_address": token_address,
                    "total_amount": total_amount,
                    "usd_amount": usd_amount,
                    "operation_id": operation_id,
                    "gift_name": gift_name,
                    "gift_image": gift_image,
                    "token_name": token_name,
                    "token_symbol": token_symbol,
                    "total_parts": 88,
                    "claimed_parts": 0,
                    "status": "active",
                    "created_at": "now",  # 接收方会用当前时间
                    "message": f"A red envelope worth ${usd_amount:.2f} has been created in {channel_name}!"
                }
            }
            
            await self.redis_client.publish(
                self.RED_ENVELOPE_CHANNEL, 
                json.dumps(message)
            )
            
            logger.info(f"发布红包创建事件: envelope_id={envelope_id}, channel={channel_name}")
            
        except Exception as e:
            logger.error(f"发布红包创建事件失败: {str(e)}")
            raise
    
    def publish_red_envelope_created_sync(
        self, 
        envelope_id: str,
        channel_name: str,
        creator_user_id: str,
        creator_name: str,
        creator_avatar: str,
        token_address: str,
        total_amount: str,
        usd_amount: float,
        operation_id: str,
        gift_name: Optional[str] = None,
        gift_image: Optional[str] = None,
        token_name: Optional[str] = None,
        token_symbol: Optional[str] = None
    ):
        """发布红包创建事件（同步版本）
        
        Args:
            envelope_id: 红包ID
            channel_name: 频道名称
            creator_user_id: 创建者用户ID
            creator_name: 创建者用户名 (即hostname)
            creator_avatar: 创建者头像URL (即avatar)
            token_address: 代币地址
            total_amount: 代币数量
            usd_amount: USD金额
            operation_id: 操作ID
            gift_name: 礼物名称（可选）
            gift_image: 礼物图标URL（可选）
            token_name: 代币名称（可选）
            token_symbol: 代币符号（可选）
        """
        try:
            self.init_sync_redis()
            
            message = {
                "type": "red_envelope_created",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "creator_user_id": creator_user_id,
                    "creator_name": creator_name,
                    "creator_avatar": creator_avatar,
                    "token_address": token_address,
                    "total_amount": total_amount,
                    "usd_amount": usd_amount,
                    "operation_id": operation_id,
                    "gift_name": gift_name,
                    "gift_image": gift_image,
                    "token_name": token_name,
                    "token_symbol": token_symbol,
                    "total_parts": 88,
                    "claimed_parts": 0,
                    "status": "active",
                    "created_at": "now",  # 接收方会用当前时间
                    "message": f"A red envelope worth ${usd_amount:.2f} has been created in {channel_name}!"
                }
            }
            
            self.sync_redis_client.publish(
                self.RED_ENVELOPE_CHANNEL, 
                json.dumps(message)
            )
            
            logger.info(f"发布红包创建事件（同步）: envelope_id={envelope_id}, channel={channel_name}")
            
        except Exception as e:
            logger.error(f"发布红包创建事件失败（同步）: {str(e)}")
            raise
    
    async def publish_red_envelope_expired(
        self,
        envelope_id: str,
        channel_name: str,
        remaining_amount: float,
        claimed_count: int,
        claim_details: List[Dict[str, Any]] = None
    ):
        """发布红包过期事件（异步版本）
        
        Args:
            envelope_id: 红包ID
            channel_name: 频道名称
            remaining_amount: 剩余金额
            claimed_count: 已领取人数
            claim_details: 领取详情列表，每个元素包含user_id, username, avatar, amount
        """
        try:
            await self.init_redis()
            
            # 使用传入的详细领取信息，如果没有则使用空列表
            if claim_details is None:
                claim_details = []
            else:
                # 按金额降序排序
                claim_details.sort(key=lambda x: float(x["amount"]), reverse=True)
            
            message = {
                "type": "red_envelope_expired",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "remaining_amount": str(remaining_amount),
                    "claimed_count": claimed_count,
                    "total_parts": 88,
                    "claim_details": claim_details,
                    "expired_at": "now",
                    "message": f"Red envelope {envelope_id} has expired. {claimed_count}/88 parts were claimed."
                }
            }
            
            await self.redis_client.publish(
                self.RED_ENVELOPE_CHANNEL,
                json.dumps(message)
            )
            
            logger.info(f"发布红包过期事件: envelope_id={envelope_id}")
            
        except Exception as e:
            logger.error(f"发布红包过期事件失败: {str(e)}")
            raise
    
    def publish_red_envelope_expired_sync(
        self,
        envelope_id: str,
        channel_name: str,
        remaining_amount: float,
        claimed_count: int,
        claim_details: List[Dict[str, Any]] = None
    ):
        """发布红包过期事件（同步版本）
        
        Args:
            envelope_id: 红包ID
            channel_name: 频道名称
            remaining_amount: 剩余金额
            claimed_count: 已领取人数
            claim_details: 领取详情列表，每个元素包含user_id, username, avatar, amount
        """
        try:
            self.init_sync_redis()
            
            # 使用传入的详细领取信息，如果没有则使用空列表
            if claim_details is None:
                claim_details = []
            else:
                # 按金额降序排序
                claim_details.sort(key=lambda x: float(x["amount"]), reverse=True)
            
            message = {
                "type": "red_envelope_expired",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "remaining_amount": str(remaining_amount),
                    "claimed_count": claimed_count,
                    "total_parts": 88,
                    "claim_details": claim_details,
                    "expired_at": "now",
                    "message": f"Red envelope {envelope_id} has expired. {claimed_count}/88 parts were claimed."
                }
            }
            
            self.sync_redis_client.publish(
                self.RED_ENVELOPE_CHANNEL,
                json.dumps(message)
            )
            
            logger.info(f"发布红包过期事件（同步）: envelope_id={envelope_id}")
            
        except Exception as e:
            logger.error(f"发布红包过期事件失败（同步）: {str(e)}")
            raise
    
    async def publish_red_envelope_completed(
        self,
        envelope_id: str,
        channel_name: str,
        total_amount: str,
        claimed_by: Dict[str, str]
    ):
        """发布红包领完事件"""
        try:
            await self.init_redis()
            
            # 构建领取详情
            claim_details = []
            for user_id, amount in claimed_by.items():
                claim_details.append({
                    "user_id": user_id,
                    "amount": amount
                })
            # 按金额降序排序
            claim_details.sort(key=lambda x: float(x["amount"]), reverse=True)
            
            message = {
                "type": "red_envelope_completed",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "total_amount": total_amount,
                    "total_claimed": len(claimed_by),
                    "claim_details": claim_details,
                    "completed_at": "now",
                    "message": f"Red envelope {envelope_id} has been fully claimed!"
                }
            }
            
            await self.redis_client.publish(
                self.RED_ENVELOPE_CHANNEL,
                json.dumps(message)
            )
            
            logger.info(f"发布红包领完事件: envelope_id={envelope_id}")
            
        except Exception as e:
            logger.error(f"发布红包领完事件失败: {str(e)}")
            raise

    async def subscribe_channel_updates(
        self,
        message_handler: Callable[[Dict[str, Any]], Awaitable[None]],
    ) -> tuple[redis.client.PubSub, asyncio.Task]:
        """为调用者创建独立的 PubSub 监听任务并返回 (pubsub, task)。

        调用方需在结束时：
        1) 取消 task
        2) unsubscribe 并 close pubsub
        """
        await self.init_redis()

        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(self.RED_ENVELOPE_CHANNEL)
        logger.info(f"开始订阅Redis频道(独立): {self.RED_ENVELOPE_CHANNEL}")

        async def _listen():
            try:
                async for message in pubsub.listen():
                    if message["type"] == "message":
                        try:
                            data = json.loads(message["data"])
                            await message_handler(data)
                        except json.JSONDecodeError as e:
                            logger.error(f"解析Redis消息失败: {type(e).__name__}: {str(e)}")
                        except Exception as e:
                            error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                            logger.error(f"处理Redis消息失败: {error_msg}")
            except asyncio.CancelledError:
                logger.info("独立Redis订阅任务已取消")
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"独立订阅监听异常: {error_msg}")

        task = asyncio.create_task(_listen())
        return pubsub, task
    
    async def stop_listening(self):
        """停止监听"""
        self.is_listening = False
        
        if self.pubsub:
            try:
                await self.pubsub.unsubscribe(self.RED_ENVELOPE_CHANNEL)
                await self.pubsub.close()
                logger.info("Redis发布订阅已关闭")
            except AttributeError as e:
                # pubsub对象可能已经被释放
                logger.debug(f"Redis pubsub对象已释放: {type(e).__name__}: {str(e)}")
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"关闭Redis发布订阅时出错: {error_msg}")
            finally:
                self.pubsub = None  # 确保设置为None，即使关闭失败
    
    async def close(self):
        """关闭连接"""
        await self.stop_listening()
        
        if self.redis_client:
            try:
                await self.redis_client.close()
                logger.info("Redis消息管理器连接已关闭")
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"关闭Redis连接时出错: {error_msg}")
            finally:
                self.redis_client = None  # 确保设置为None，即使关闭失败
        
        if self.sync_redis_client:
            try:
                self.sync_redis_client.close()
                logger.info("同步Redis消息管理器连接已关闭")
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"关闭同步Redis连接时出错: {error_msg}")
            finally:
                self.sync_redis_client = None  # 确保设置为None，即使关闭失败


# 全局实例
redis_messenger = RedisMessenger() 