import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict

class AgoraSettings(BaseSettings):
    """Agora service configuration settings"""
    
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")
    
    # Agora App Configuration
    AGORA_CONSUMER_ENABLED: bool = True
    APP_ID: str = os.getenv("AGORA_APP_ID", "********************************")
    APP_CERTIFICATE: str = os.getenv("AGORA_APP_CERTIFICATE", "********************************")
    
    # Agora Customer Authentication (for RESTful API)
    CUSTOMER_ID: str = os.getenv("AGORA_CUSTOMER_ID", "")
    CUSTOMER_SECRET: str = os.getenv("AGORA_CUSTOMER_SECRET", "")
    
    # Authentication mode: 'app_cert' (APP_ID:APP_CERTIFICATE) or 'customer' (CUSTOMER_ID:CUSTOMER_SECRET)
    AUTH_MODE: str = os.getenv("AGORA_AUTH_MODE", "app_cert")
    
    # Token Configuration
    TOKEN_EXPIRATION_TIME: int = int(os.getenv("AGORA_TOKEN_EXPIRATION", "7200"))  # 2 hour
    RTM_TOKEN_EXPIRATION_TIME: int = int(os.getenv("AGORA_RTM_TOKEN_EXPIRATION", "86400"))  # 24 hours
    
    # UID Management
    UID_ALLOCATION_MODE: str = os.getenv("AGORA_UID_MODE", "backend")  # backend, client
    UID_RANGE_START: int = int(os.getenv("AGORA_UID_START", "100000"))
    UID_RANGE_END: int = int(os.getenv("AGORA_UID_END", "999999"))
    UID_SALT: str = os.getenv("AGORA_UID_SALT", "JyzuC2!EPq8@EvF-zdqjdsh6NTpkr_nz")
    
    # Recording Configuration
    RECORDING_ENABLED: bool = os.getenv("AGORA_RECORDING_ENABLED", "false").lower() == "true"
    RECORDING_BUCKET: Optional[str] = os.getenv("AGORA_RECORDING_BUCKET")
    RECORDING_REGION: Optional[str] = os.getenv("AGORA_RECORDING_REGION")
    RECORDING_VENDOR: str = os.getenv("AGORA_RECORDING_VENDOR", "cloudflare")
    
    # Cloud Recording API
    RECORDING_CUSTOMER_ID: str = os.getenv("AGORA_RECORDING_CUSTOMER_ID", "")
    RECORDING_CUSTOMER_SECRET: str = os.getenv("AGORA_RECORDING_CUSTOMER_SECRET", "")
    RECORDING_API_BASE_URL: str = os.getenv("AGORA_RECORDING_API_URL", "https://api.agora.io")
    
    # Redis Configuration
    REDIS_URL: str
    REDIS_UID_PREFIX: str = "agora:uid:"
    REDIS_CHANNEL_PREFIX: str = "agora:channel:"

    # Cloudflare Queue Configuration
    CF_ACCOUNT_ID: str
    CF_QUEUES_API_TOKEN: str
    CF_QUEUE_ID: str

    # Memecoin Service Configuration
    MEMECOIN_SERVICE_URL: str = "http://memecoin:8000"

    # RTM API Configuration
    RTM_API_BASE_URL: str = os.getenv("AGORA_RTM_API_URL", "https://api.agora.io")
    RTM_API_TIMEOUT: int = int(os.getenv("AGORA_RTM_API_TIMEOUT", "30"))  # 30 seconds
    RTM_API_RETRY_COUNT: int = int(os.getenv("AGORA_RTM_API_RETRY_COUNT", "3"))  # 重试次数
    RTM_API_RETRY_DELAY: int = int(os.getenv("AGORA_RTM_API_RETRY_DELAY", "1"))  # 重试延迟(秒)
    
    # Webhook Configuration
    WEBHOOK_SECRET: Optional[str] = os.getenv("AGORA_WEBHOOK_SECRET", None)

settings = AgoraSettings() 