import asyncio
import uuid

import httpx
from typing import Optional, Dict, Any

import requests
from fastapi import HTTPException
from src.agora.logger import logger
from src.agora.settings import settings
from src.agora.schemas import BuyGiftRequest, BuyGiftResponse


class MemecoinClient:
    """Memecoin 服务客户端"""
    
    def __init__(self):
        self.base_url = settings.MEMECOIN_SERVICE_URL
        self.timeout = 30.0
    
    def buy_token(self, user_token: str, token_address: str, usd_amount_to_buy: str) -> str:
        """
        调用 memecoin 服务的 buy_token 接口
        
        Args:
            user_token: 用户认证token
            request: 购买请求
            
        Returns:
            BuyGiftResponse: 购买响应
        """
        url = f"{self.base_url}/buy_token"
        headers = {
            "Authorization": f"Bearer {user_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "token_address": token_address,
            "amount": usd_amount_to_buy,
            "order_id": str(uuid.uuid4())
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers)

            if response.status_code == 200 or response.status_code == 201:
                data = response.json()
                txid=data.get("txid", "")
                return txid
            else:
                # 处理错误响应
                try:
                    error_data = response.json()
                    error_message = error_data.get("detail", f"HTTP {response.status_code} error")
                except:
                    error_message = f"HTTP {response.status_code} error"

                logger.error(f"Memecoin service error: {error_message}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=error_message
                )
                    
        except httpx.TimeoutException:
            logger.error("Memecoin service timeout")
            raise HTTPException(
                status_code=504,
                detail="Memecoin service timeout"
            )
        except httpx.ConnectError:
            logger.error("Failed to connect to memecoin service")
            raise HTTPException(
                status_code=503,
                detail="Memecoin service unavailable"
            )
        except Exception as e:
            logger.error(f"Unexpected error calling memecoin service: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error: {str(e)}"
            )

    async def get_user_balance(self, user_token: str) -> Dict[str, Any]:
        """
        调用 memecoin 服务的 bnb_balance 接口获取用户BNB余额
        
        Args:
            user_token: 用户认证token
            
        Returns:
            Dict: 余额响应，包含 amount, ui_amount 等字段
        """
        url = f"{self.base_url}/cash_amount"
        headers = {
            "Authorization": f"Bearer {user_token}",
            "Content-Type": "application/json"
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    return data
                else:
                    # 处理错误响应
                    try:
                        error_data = response.json()
                        error_message = error_data.get("detail", f"HTTP {response.status_code} error")
                    except:
                        error_message = f"HTTP {response.status_code} error"
                    
                    logger.error(f"Memecoin service bnb_balance error: {error_message}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=error_message
                    )
                    
        except httpx.TimeoutException:
            logger.error("Memecoin service bnb_balance timeout")
            raise HTTPException(
                status_code=504,
                detail="Memecoin service timeout"
            )
        except httpx.ConnectError:
            logger.error("Failed to connect to memecoin service for bnb_balance")
            raise HTTPException(
                status_code=503,
                detail="Memecoin service unavailable"
            )
        except Exception as e:
            logger.error(f"Unexpected error calling memecoin bnb_balance: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error: {str(e)}"
            )
    
    async def get_eth_price(self) -> float:
        """
        获取当前ETH价格（USD）
        
        Returns:
            float: ETH价格（USD）
        """
        url = f"{self.base_url}/eth_price"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    data = response.json()
                    price = float(data.get("price", 2700.0))
                    logger.debug(f"Retrieved ETH price from memecoin service: ${price}")
                    return price
                else:
                    # 如果memecoin服务失败，回退到Binance API
                    logger.warning(f"Memecoin service eth_price failed: HTTP {response.status_code}, falling back to Binance")
                    return await self._get_eth_price_fallback()
                    
        except httpx.TimeoutException:
            logger.warning("Memecoin service eth_price timeout, falling back to Binance")
            return await self._get_eth_price_fallback()
        except httpx.ConnectError:
            logger.warning("Failed to connect to memecoin service for eth_price, falling back to Binance")
            return await self._get_eth_price_fallback()
        except Exception as e:
            logger.error(f"Unexpected error calling memecoin eth_price: {str(e)}, falling back to Binance")
            return await self._get_eth_price_fallback()

    async def _get_eth_price_fallback(self) -> float:
        """
        回退方法：直接从Binance API获取ETH价格
        
        Returns:
            float: ETH价格（USD）
        """
        try:
            # 直接调用Binance API获取ETH价格
            url = "https://api.binance.com/api/v3/ticker/price?symbol=ETHUSDT"
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    data = response.json()
                    price = float(data.get("price", "2700.0"))
                    logger.debug(f"Retrieved ETH price from Binance fallback: ${price}")
                    return price
                else:
                    logger.warning(f"Failed to get ETH price from Binance fallback: HTTP {response.status_code}")
                    return 2700.0  # 默认价格
                    
        except Exception as e:
            logger.error(f"Error getting ETH price from Binance fallback: {str(e)}")
            return 2700.0  # 默认价格

    async def get_token_info(self, token_address: str) -> Dict[str, Any]:
        """
        调用 memecoin 服务的 /token 接口获取代币信息
        
        Args:
            token_address: 代币地址
            
        Returns:
            Dict[str, Any]: 代币信息
        """
        url = f"{self.base_url}/token"
        params = {
            "token_address": token_address
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.debug(f"Retrieved token info from memecoin service: {token_address}")
                    return data
                else:
                    # 处理错误响应
                    try:
                        error_data = response.json()
                        error_message = error_data.get("detail", f"HTTP {response.status_code} error")
                    except:
                        error_message = f"HTTP {response.status_code} error"
                    
                    logger.error(f"Memecoin service get_token_info error: {error_message}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=error_message
                    )
                    
        except httpx.TimeoutException:
            logger.error("Memecoin service get_token_info timeout")
            raise HTTPException(
                status_code=504,
                detail="Memecoin service timeout"
            )
        except httpx.ConnectError:
            logger.error("Failed to connect to memecoin service for get_token_info")
            raise HTTPException(
                status_code=503,
                detail="Memecoin service unavailable"
            )
        except Exception as e:
            logger.error(f"Unexpected error calling memecoin get_token_info: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error: {str(e)}"
            )
    
    async def convert_usd_to_token(self, token_address: str, usd_amount: str) -> Dict[str, Any]:
        """
        调用 memecoin 服务的 /convert 接口将USD转换为代币数量
        
        Args:
            token_address: 代币地址
            usd_amount: USD金额（字符串格式）
            
        Returns:
            Dict[str, Any]: 转换结果
        """
        url = f"{self.base_url}/convert"
        params = {
            "token_address": token_address,
            "usd_amount": usd_amount
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.debug(f"USD to token conversion successful: {usd_amount} USD -> {data.get('token_amount')} tokens")
                    return data
                else:
                    # 处理错误响应
                    try:
                        error_data = response.json()
                        error_message = error_data.get("detail", f"HTTP {response.status_code} error")
                    except:
                        error_message = f"HTTP {response.status_code} error"
                    
                    logger.error(f"Memecoin service convert error: {error_message}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=error_message
                    )
                    
        except httpx.TimeoutException:
            logger.error("Memecoin service convert timeout")
            raise HTTPException(
                status_code=504,
                detail="Memecoin service timeout"
            )
        except httpx.ConnectError:
            logger.error("Failed to connect to memecoin service for convert")
            raise HTTPException(
                status_code=503,
                detail="Memecoin service unavailable"
            )
        except Exception as e:
            logger.error(f"Unexpected error calling memecoin convert: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error: {str(e)}"
            )


# 全局客户端实例
memecoin_client = MemecoinClient() 