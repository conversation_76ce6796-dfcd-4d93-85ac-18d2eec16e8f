from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone


def utc_now_for_response():
    """为响应模型生成UTC时间（保持带时区信息用于API响应）"""
    return datetime.now(timezone.utc)

class TokenRequest(BaseModel):
    """Token 请求模型"""
    channel_id: str = Field(..., description="直播间ID")
    role: str = Field(default="audience", description="用户角色 (audience/host)")
    expiration_time: Optional[int] = Field(default=7200, description="过期时间（秒），默认7200秒（2小时）")

class TokenResponse(BaseModel):
    """Token 响应模型"""
    token: str = Field(..., description="生成的Token")
    app_id: str = Field(..., description="Agora App ID")
    channel_id: str = Field(..., description="直播间ID")
    uid: int = Field(..., description="用户ID")
    role: str = Field(..., description="用户角色")
    expires_at: datetime = Field(..., description="过期时间")

class RTMTokenRequest(BaseModel):
    """RTM Token 请求模型"""
    expiration_time: Optional[int] = Field(default=7200, description="过期时间（秒），默认7200秒（2小时）")

class RTMTokenResponse(BaseModel):
    """RTM Token 响应模型"""
    token: str = Field(..., description="生成的RTM Token")
    user_id: str = Field(..., description="用户ID")
    expires_at: datetime = Field(..., description="过期时间")

class RTCAndRTMTokenRequest(BaseModel):
    """RTC和RTM合并Token请求模型"""
    channel_id: str = Field(..., description="频道名称，唯一标识AgoraRTC会话")
    role: str = Field(default="audience", description="用户角色: 'host'为主播，'audience'为观众（对应UserRole枚举值）")
    expiration_time: Optional[int] = Field(default=7200, description="Token有效期（从现在开始的秒数），默认7200秒（2小时）")

class RTCAndRTMTokenResponse(BaseModel):
    """RTC和RTM合并Token响应模型"""
    token: str = Field(..., description="生成的RTC+RTM合并Token")
    app_id: str = Field(..., description="Agora App ID")
    channel_id: str = Field(..., description="直播间ID")
    uid: int = Field(..., description="用户ID")
    role: str = Field(..., description="用户角色")
    expires_at: datetime = Field(..., description="过期时间")

class ChannelCreateRequest(BaseModel):
    """创建直播间请求模型"""
    channel_id: str = Field(..., min_length=1, max_length=64, description="直播间ID")
    token_address: Optional[str] = Field(None, description="代币地址")

class ChannelInfo(BaseModel):
    """直播间信息模型"""
    channel_id: str = Field(..., description="直播间ID，即直播间的名字")
    broadcaster_id: str = Field(..., description="主播平台用户ID")
    broadcaster_uid: int = Field(..., description="主播UID")
    broadcaster_name: Optional[str] = Field(None, description="主播姓名")
    broadcaster_username: Optional[str] = Field(None, description="主播用户名")
    broadcaster_avatar: Optional[str] = Field(None, description="主播头像")
    status: str = Field(..., description="直播间状态 (created/live/ended)")
    current_audience: int = Field(default=0, description="当前观众数")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开播时间")
    ended_at: Optional[datetime] = Field(None, description="结束时间")
    cover: Optional[str] = Field(None, description="频道绑定的代币封面图片URL")
    token_name: Optional[str] = Field(None, description="关联的代币名称")
    token_address: Optional[str] = Field(None, description="关联的代币地址")

class ChannelJoinRequest(BaseModel):
    """加入直播间请求模型"""
    role: str = Field(default="audience", description="用户角色 (audience/host)")

class ChannelJoinResponse(BaseModel):
    """加入直播间响应模型"""
    rtc_token: str = Field(..., description="RTC Token")
    rtm_token: str = Field(..., description="RTM Token")
    channel_info: ChannelInfo = Field(..., description="直播间信息")

class ChannelJoinSimpleResponse(BaseModel):
    """加入直播间简单响应模型（不包含token）"""
    channel_info: ChannelInfo = Field(..., description="直播间信息")
    joined: bool = Field(..., description="是否成功加入")

class ChannelLeaveRequest(BaseModel):
    """离开直播间请求模型"""
    pass

class AudienceInfo(BaseModel):
    """观众信息模型"""
    user_id: str = Field(..., description="用户ID")
    joined_at: datetime = Field(..., description="加入时间")
    role: str = Field(..., description="用户角色 (audience/host)")

class ChannelStatsResponse(BaseModel):
    """直播间统计响应模型"""
    channel_id: str = Field(..., description="直播间ID/频道名称")
    current_audience: int = Field(..., description="当前观众数")
    duration: int = Field(..., description="直播时长（秒）")
    live_trading_volume: float = Field(..., description="直播期间的交易额（USD）")
    gift_count: int = Field(..., description="频道礼物总数")

class WebhookEvent(BaseModel):
    """Webhook 事件模型 - 支持不同类型的Agora Webhook事件"""
    # 必须字段 - 所有事件都有
    noticeId: str = Field(..., description="通知ID")
    
    # 可选字段 - 不同事件类型可能有不同字段
    appId: Optional[str] = Field(None, description="Agora应用ID（可选）")
    productId: Optional[int] = Field(None, description="产品ID（可选）") 
    eventType: Optional[int] = Field(None, description="事件类型（可选）")
    notifyMs: Optional[int] = Field(None, description="通知时间戳(毫秒)")
    ts: Optional[int] = Field(None, description="时间戳(秒)")
    sid: Optional[str] = Field(None, description="Session ID")
    payload: Optional[Dict[str, Any]] = Field(None, description="事件载荷数据")
    
    class Config:
        # 允许额外字段，这样可以接收未知的字段
        extra = "allow"
        # 允许通过字段名访问
        populate_by_name = True
    
    @property
    def timestamp(self) -> Optional[int]:
        """获取时间戳，优先使用notifyMs，其次使用ts"""
        return self.notifyMs or self.ts

class RecordingRequest(BaseModel):
    """录制请求模型"""
    channel_id: str = Field(..., description="直播间ID")
    uid: int = Field(..., description="用户ID")

class RecordingResponse(BaseModel):
    """录制响应模型"""
    resource_id: str = Field(..., description="资源ID")
    sid: str = Field(..., description="录制ID")
    channel_id: str = Field(..., description="直播间ID")
    status: str = Field(..., description="录制状态 (started/stopped/failed)")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

class RecordingStartRequest(BaseModel):
    """开始录制请求"""
    channel_id: str = Field(..., description="直播间ID")

class APIResponse(BaseModel):
    """API 统一响应格式"""
    success: bool = Field(True, description="是否成功")
    message: str = Field("success", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=utc_now_for_response, description="响应时间")

class PaginatedResponse(BaseModel):
    """分页响应"""
    items: List[Any] = Field(..., description="数据列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class ChannelResponse(BaseModel):
    """直播间响应模型"""
    channel_id: str = Field(..., description="直播间ID")
    broadcaster_uid: int = Field(..., description="主播ID")
    status: str = Field(..., description="直播间状态 (created/live/ended)")
    current_audience: int = Field(..., description="当前观众数")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    ended_at: Optional[datetime] = Field(None, description="结束时间")

class AudienceResponse(BaseModel):
    """观众响应"""
    id: int = Field(..., description="记录ID")
    channel_id: int = Field(..., description="直播间ID")
    user_id: str = Field(..., description="用户ID")
    role: str = Field(..., description="用户角色 (audience/host)")
    joined_at: datetime = Field(..., description="加入时间")
    left_at: Optional[datetime] = Field(None, description="离开时间")

class RecordingSessionResponse(BaseModel):
    """录制会话响应"""
    id: int = Field(..., description="录制ID")
    channel_id: int = Field(..., description="直播间ID")
    resource_id: str = Field(..., description="资源ID")
    sid: str = Field(..., description="会话ID")
    status: str = Field(..., description="录制状态 (started/stopped/failed)")
    started_at: datetime = Field(..., description="开始时间")
    stopped_at: Optional[datetime] = Field(None, description="停止时间")

class UIDQueryRequest(BaseModel):
    """UID 查询请求模型"""
    user_id: Optional[str] = Field(None, description="用户ID（通过user_id获取UID时使用）")
    uid: Optional[int] = Field(None, description="UID（通过UID获取user_id时使用）")
    user_id_hint: Optional[str] = Field(None, description="用户ID提示（验证UID时使用）")
    
    @model_validator(mode='after')
    def validate_query_params(self):
        """验证查询参数，确保至少提供一个"""
        if self.user_id is None and self.uid is None:
            raise ValueError('必须提供 user_id 或 uid 中的至少一个参数')
        return self

class UIDQueryResponse(BaseModel):
    """UID 查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    user_id: Optional[str] = Field(None, description="用户ID")
    uid: Optional[int] = Field(None, description="UID")
    is_valid: Optional[bool] = Field(None, description="UID和user_id匹配验证结果")
    salt_hash: str = Field(..., description="盐值哈希（用于验证客户端配置）")
    algorithm: str = Field(default="CRC32_SALTED", description="UID生成算法")
    message: Optional[str] = Field(None, description="响应消息或错误信息")

class LiveChannelsResponse(BaseModel):
    """直播频道列表响应模型"""
    items: List[ChannelInfo] = Field(..., description="直播频道列表")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总直播频道数")

class ChannelAudienceInfo(BaseModel):
    """频道观众信息模型"""
    user_id: str = Field(..., description="用户ID")
    uid: int = Field(..., description="Agora UID")
    username: str = Field(..., description="用户名")
    avatar: Optional[str] = Field(None, description="用户头像")
    role: str = Field(..., description="用户角色 (audience/host)")
    platform: int = Field(..., description="平台类型 (1: Android, 2: iOS, 5: Windows, 6: Linux, 7: Web, 8: macOS, 0: Other)")
    joined_at: datetime = Field(..., description="加入时间")

class ChannelAudienceResponse(BaseModel):
    """频道观众响应模型"""
    channel_name: str = Field(..., description="频道名称")
    channel_status: str = Field(..., description="频道状态")
    total_audience: int = Field(..., description="总观众数")
    active_audience: int = Field(..., description="当前活跃观众数")
    audiences: List[ChannelAudienceInfo] = Field(..., description="观众列表")

class BuyGiftRequest(BaseModel):
    """购买礼物请求模型"""
    operation_id: str = Field(..., description="客户端生成的唯一操作ID，用于去重和追踪")
    channel_name: str = Field(..., description="直播间名称")
    gift_id: int = Field(..., description="礼物ID")
    quantity: int = Field(default=1, ge=1, le=999, description="购买数量，默认为1，最小1，最大999")
    message: Optional[str] = Field(None, description="可选的打赏留言（JSON字符串格式）")
    
    @field_validator('operation_id')
    def validate_operation_id(cls, v):
        if not v or v.strip() == "":
            raise ValueError("operation_id不能为空")
        # 长度限制，防止过长的ID
        if len(v) > 100:
            raise ValueError("operation_id长度不能超过100个字符")
        return v.strip()

class BuyGiftResponse(BaseModel):
    """购买礼物响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="交易详情")

class ChannelGiftRecord(BaseModel):
    """直播间礼物记录模型"""
    channel_name: str = Field(..., description="直播间名称")
    tipper: str = Field(..., description="发送者用户ID")
    gift_id: int = Field(..., description="礼物ID")
    gift_name: Optional[str] = Field(None, description="礼物名称")
    gift_count: int = Field(..., description="礼物数量")
    gift_price: int = Field(..., description="单个礼物价格USD")
    gift_currency: str = Field(..., description="礼物货币")
    message: Optional[str] = Field(None, description="打赏留言")
    operation_id: Optional[str] = Field(None, description="客户端生成的唯一操作ID")
    txid: Optional[str] = Field(None, description="交易哈希")
    created_at: datetime = Field(..., description="创建时间")

class CreateGiftRecordRequest(BaseModel):
    """创建礼物记录请求模型"""
    channel_name: str = Field(..., description="直播间名称")
    token_address: str = Field(..., description="代币地址")
    amount_eth: str = Field(..., description="购买金额(ETH)")
    txid: str = Field(..., description="交易哈希")
    gift_name: Optional[str] = Field(None, description="礼物名称")
    message: Optional[str] = Field(None, description="打赏留言")

class ChannelGiftsRequest(BaseModel):
    """频道礼物历史请求模型"""
    page: int = Field(default=1, ge=1, description="页码，从1开始，默认1")
    page_size: int = Field(default=10, ge=1, le=100, description="每页大小，默认10，最小1，最大100")

class ChannelGiftsResponse(BaseModel):
    """频道礼物列表响应模型"""
    items: List[ChannelGiftRecord] = Field(..., description="礼物记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")

# RTM 消息发送相关Schema
class RTMChannelMessageRequest(BaseModel):
    """RTM频道消息发送请求模型"""
    channel_names: Optional[List[str]] = Field(None, description="频道名称列表")
    message: str = Field(..., max_length=32768, description="消息内容，不能超过32KB")
    broadcast: Optional[bool] = Field(False, description="是否广播到所有正在直播的频道，只有为true且channel_names为空时才生效")
    
    @model_validator(mode='after')
    def validate_send_conditions(self):
        """验证发送条件"""
        # 处理broadcast为None的情况，默认为False
        broadcast_value = self.broadcast if self.broadcast is not None else False
        
        if broadcast_value:
            # 如果要广播到所有频道，channel_names必须为空
            if self.channel_names:
                raise ValueError("当broadcast为true时，channel_names必须为空")
        else:
            # 如果不广播到所有频道，必须指定channel_names
            if not self.channel_names:
                raise ValueError("当broadcast为false时，必须指定channel_names")
        return self
    
    @field_validator('channel_names')
    def validate_channel_names(cls, v):
        if v is not None:
            if len(v) == 0:
                return None  # 空列表转换为None
            # 去重并验证每个频道名称
            unique_channels = []
            for channel in v:
                if not channel or channel.strip() == "":
                    raise ValueError("频道名称不能为空")
                channel = channel.strip()
                if channel not in unique_channels:
                    unique_channels.append(channel)
            if len(unique_channels) > 100:
                raise ValueError("最多支持100个频道")
            return unique_channels
        return v
    
    @field_validator('message')
    def validate_message(cls, v):
        if not v or v.strip() == "":
            raise ValueError("消息内容不能为空")
        if len(v.encode('utf-8')) > 32768:
            raise ValueError("消息内容不能超过32KB")
        return v

class RTMChannelMessageResponse(BaseModel):
    """RTM频道消息发送响应模型"""
    total_channels: int = Field(..., description="总频道数")
    successful_channels: int = Field(..., description="发送成功的频道数")
    failed_channels: int = Field(..., description="发送失败的频道数")
    results: List[Dict[str, Any]] = Field(..., description="每个频道的发送结果详情")
    overall_success: bool = Field(..., description="整体是否成功")
    message: str = Field(..., description="操作结果描述")


# 礼物列表相关Schema
class GiftListItem(BaseModel):
    """礼物列表项模型"""
    id: int = Field(..., description="礼物唯一标识")
    name: str = Field(..., description="礼物名称")
    description: Optional[str] = Field(None, description="礼物描述")
    price: int = Field(..., description="价格（以gwei为单位）")
    currency: str = Field(..., description="货币类型")
    icon_url: Optional[str] = Field(None, description="礼物图标URL")
    animation_url: Optional[str] = Field(None, description="动画效果URL")
    animation_md5: Optional[str] = Field(None, description="动画文件MD5哈希值")
    animation_hd_url: Optional[str] = Field(None, description="高清动画效果URL")
    animation_hd_md5: Optional[str] = Field(None, description="高清动画文件MD5哈希值")
    animation_mp4_url: Optional[str] = Field(None, description="MP4 动画效果URL")
    animation_mp4_md5: Optional[str] = Field(None, description="MP4 动画文件MD5哈希值")
    category: str = Field(..., description="礼物分类")

class GiftListResponse(BaseModel):
    """礼物列表响应模型"""
    items: List[GiftListItem] = Field(..., description="礼物列表，按sort_order从大到小排序")
    total: int = Field(..., description="总记录数")

class ChannelTokenResponse(BaseModel):
    """频道绑定的代币信息响应模型"""
    channel_name: str = Field(..., description="频道名称")
    has_token: bool = Field(..., description="是否绑定了代币")
    token_address: Optional[str] = Field(None, description="代币地址")
    token_info: Optional[Dict[str, Any]] = Field(None, description="代币详细信息")

class TokenChannelsResponse(BaseModel):
    """代币相关的直播频道响应模型"""
    token_address: str = Field(..., description="代币地址")
    total_channels: int = Field(..., description="总频道数")
    live_channels: List[ChannelInfo] = Field(..., description="正在直播的频道列表")

class ChannelGiftRankingRequest(BaseModel):
    """频道打赏排行榜请求模型"""
    page: int = Field(default=1, ge=1, description="页码，从1开始，默认1")
    page_size: int = Field(default=10, ge=1, le=100, description="每页大小，默认10，最小1，最大100")

class GiftRankingItem(BaseModel):
    """打赏排行榜项模型"""
    rank: int = Field(..., description="排名（从1开始）")
    user_id: str = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    avatar: Optional[str] = Field(None, description="用户头像")
    total_gift_value: int = Field(..., description="总打赏价值（USD分）")
    total_gift_count: int = Field(..., description="总打赏次数")
    gift_types: int = Field(..., description="打赏的不同礼物种类数")
    latest_gift_time: datetime = Field(..., description="最后一次打赏时间")

class ChannelGiftRankingResponse(BaseModel):
    """频道打赏排行榜响应模型"""
    channel_name: str = Field(..., description="频道名称")
    total_tippers: int = Field(..., description="总打赏人数")
    total_gift_value: int = Field(..., description="频道总打赏价值（USD分）")
    rankings: List[GiftRankingItem] = Field(..., description="打赏排行榜列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")


class AnonymousTokenBatchRequest(BaseModel):
    """匿名批量Token请求模型（用于压力测试）"""
    channel_id: str = Field(..., description="频道名称，唯一标识AgoraRTC会话")
    role: str = Field(default="audience", description="用户角色: 'host'为主播，'audience'为观众")
    expiration_time: Optional[int] = Field(default=7200, description="Token有效期（从现在开始的秒数），默认7200秒（2小时）")
    count: int = Field(default=1, ge=1, le=1000, description="生成Token数量，默认1个，最大1000个")

    @field_validator('count')
    def validate_count(cls, v):
        if v > 1000:
            raise ValueError('批量生成Token数量不能超过1000个')
        return v

    @field_validator('role')
    def validate_role(cls, v):
        if v not in ['audience', 'host', 'broadcaster']:
            raise ValueError('角色必须是 audience、host 或 broadcaster')
        return v


class AnonymousTokenItem(BaseModel):
    """匿名Token项模型"""
    token: str = Field(..., description="生成的RTC Token")
    app_id: str = Field(..., description="Agora App ID")
    channel_id: str = Field(..., description="直播间ID")
    uid: int = Field(..., description="用户ID")
    anonymous_user_id: str = Field(..., description="匿名用户ID")
    role: str = Field(..., description="用户角色")
    expires_at: datetime = Field(..., description="过期时间")


class AnonymousTokenBatchResponse(BaseModel):
    """匿名批量Token响应模型"""
    total_tokens: int = Field(..., description="总生成Token数量")
    channel_id: str = Field(..., description="频道名称")
    role: str = Field(..., description="用户角色")
    expires_at: datetime = Field(..., description="过期时间")
    tokens: List[AnonymousTokenItem] = Field(..., description="Token列表")
    generated_at: datetime = Field(default_factory=utc_now_for_response, description="生成时间")


# 礼物转换相关Schema
class GiftConvertRequest(BaseModel):
    """礼物转换请求模型"""
    gift_id: int = Field(..., description="礼物ID")
    quantity: int = Field(default=1, ge=1, le=999, description="礼物数量，默认为1，最小1，最大999")
    token_address: str = Field(..., description="目标代币地址")
    
    @field_validator('token_address')
    def validate_token_address(cls, v):
        if not v or v.strip() == "":
            raise ValueError("token_address不能为空")
        return v.strip()


class GiftConvertResponse(BaseModel):
    """礼物转换响应模型（简化版）"""
    token_amount: str = Field(..., description="转换后的代币数量")
    conversion_rate: str = Field(..., description="转换汇率（1 USD = X tokens）")


# 红包相关Schema
class ClaimDetail(BaseModel):
    """领取详情"""
    user_id: str = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    avatar: Optional[str] = Field(None, description="头像")
    amount: str = Field(..., description="领取金额")


class RedEnvelopeInfo(BaseModel):
    """红包信息模型"""
    envelope_id: str = Field(..., description="红包ID")
    creator_user_id: str = Field(..., description="创建者用户ID")
    creator_name: Optional[str] = Field(None, description="创建者用户名")
    creator_avatar: Optional[str] = Field(None, description="创建者头像URL")
    channel_name: str = Field(..., description="频道名称")
    token_address: str = Field(..., description="代币地址")
    total_amount: str = Field(..., description="总金额")
    usd_amount: float = Field(..., description="USD金额")
    total_parts: int = Field(default=88, description="总份数")
    claimed_parts: int = Field(default=0, description="已领取份数")
    status: str = Field(..., description="状态: active, expired, completed")
    created_at: datetime = Field(..., description="创建时间")
    first_claim_at: Optional[datetime] = Field(None, description="首次领取时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    claim_details: Optional[List[ClaimDetail]] = Field(None, description="领取详情（仅在completed或expired状态时返回）")


class ChannelRedEnvelopesResponse(BaseModel):
    """频道红包列表响应"""
    channel_name: str = Field(..., description="频道名称")
    envelopes: List[RedEnvelopeInfo] = Field(default_factory=list, description="红包列表")
    total: int = Field(..., description="红包总数")


class ClaimRedEnvelopeRequest(BaseModel):
    """领取红包请求"""
    envelope_id: str = Field(..., description="红包ID")
    channel_name: str = Field(..., description="频道名称")


class ClaimRedEnvelopeResponse(BaseModel):
    """领取红包响应"""
    success: bool = Field(..., description="是否成功")
    envelope_id: str = Field(..., description="红包ID")
    claimed_amount: Optional[str] = Field(None, description="领取金额")
    claimed_part_index: Optional[int] = Field(None, description="领取的份数索引")
    message: str = Field(..., description="消息")
    txid: Optional[str] = Field(None, description="交易ID")
    remaining_parts: Optional[int] = Field(None, description="剩余份数")
    total_claimed_amount: Optional[str] = Field(None, description="该用户总共领取的金额")


# Kick User and Mute User related schemas
class KickUserRequest(BaseModel):
    """踢出用户请求模型"""
    channel_name: str = Field(..., description="频道名称")
    user_id: str = Field(..., description="要踢出的用户ID")
    reason: Optional[str] = Field(None, description="踢出原因")
    duration: int = Field(300, ge=1, le=86400, description="踢出时长（秒），默认300秒，最大24小时")
    
    @field_validator('channel_name')
    def validate_channel_name(cls, v):
        if not v or v.strip() == "":
            raise ValueError("channel_name不能为空")
        return v.strip()
    
    @field_validator('user_id')
    def validate_user_id(cls, v):
        if not v or v.strip() == "":
            raise ValueError("user_id不能为空")
        return v.strip()
    
    @field_validator('duration')
    def validate_duration(cls, v):
        if v <= 0:
            raise ValueError("踢出时长必须大于0秒")
        if v > 86400:  # 24小时
            raise ValueError("踢出时长不能超过24小时")
        return v


class KickUserResponse(BaseModel):
    """踢出用户响应模型"""
    success: bool = Field(..., description="是否成功")
    channel_name: str = Field(..., description="频道名称")
    kicked_user_id: str = Field(..., description="被踢出的用户ID")
    message: str = Field(..., description="操作结果消息")


 