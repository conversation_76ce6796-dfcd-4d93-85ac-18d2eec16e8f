"""
Agora服务的任务客户端
基于common模块的通用Celery客户端，封装agora相关的任务调用
"""
from typing import Any
from src.common.celery_client import create_celery_client
from src.agora.logger import logger


class AgoraTaskClient:
    """Agora服务的任务客户端"""
    
    def __init__(self):
        # 使用common模块的工厂函数创建Celery客户端
        self.celery_client = create_celery_client("agora")
    
    def submit_gift_purchase_task(
        self,
        user_id: str,
        channel_name: str,
        channel_id: int,
        gift_id: int,
        quantity: int,
        user_token: str,
        token_address: str,
        message: str = None,
        operation_id: str = None
    ) -> Any:
        """
        提交礼物购买任务到worker队列
        
        Args:
            user_id: 用户ID
            channel_name: 频道名称
            channel_id: 频道ID（数据库主键）
            gift_id: 礼物ID
            quantity: 购买数量
            user_token: 用户认证token
            message: 可选的消息内容
            operation_id: 客户端生成的唯一操作ID
            
        Returns:
            AsyncResult: Celery任务结果对象
        """
        task_name = "src.worker.tasks.agora.process_gift_purchase_task"
        
        return self.celery_client.send_task(
            task_name=task_name,
            args=[user_id, channel_name, channel_id, gift_id, quantity, user_token, token_address, message, operation_id]
        )
    
    def submit_gift_notification_task(
        self,
        channel_name: str,
        sender_user_id: str,
        gift_id: int,
        quantity: int,
        txid: str
    ) -> Any:
        """
        提交礼物通知任务到worker队列
        
        Args:
            channel_name: 频道名称
            sender_user_id: 发送者用户ID
            gift_id: 礼物ID
            quantity: 数量
            txid: 交易ID
            
        Returns:
            AsyncResult: Celery任务结果对象
        """
        task_name = "src.worker.tasks.agora.send_gift_notification_task"
        
        return self.celery_client.send_task(
            task_name=task_name,
            args=[channel_name, sender_user_id, gift_id, quantity, txid]
        )
    
    def get_task_status(self, task_id: str) -> dict:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 任务状态信息
        """
        return self.celery_client.get_task_status(task_id)
    
    def revoke_task(self, task_id: str, terminate: bool = False) -> bool:
        """
        撤销任务
        
        Args:
            task_id: 任务ID
            terminate: 是否强制终止正在执行的任务
            
        Returns:
            bool: 撤销是否成功
        """
        return self.celery_client.revoke_task(task_id, terminate)


# 创建全局实例
agora_task_client = AgoraTaskClient() 