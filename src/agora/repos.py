from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, desc, select, func, text

from src.database.models.Agora import LiveChannel, ChannelAudience, RecordingSession, ChannelGiftHistory, GiftList, ChannelStats, utc_now_naive
from src.agora.constants import ChannelStatus, RecordingStatus
from src.database.session import get_read_session


class ChannelRepository:
    """直播间相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_channel(self, channel_name: str, broadcaster_uid: int, user_id: str, token_address: Optional[str] = None) -> LiveChannel:
        """创建直播间"""
        channel = LiveChannel(
            channel_name=channel_name,
            broadcaster_uid=broadcaster_uid,
            user_id=user_id,
            token_address=token_address,
            status=ChannelStatus.CREATED
        )
        self.db.add(channel)
        await self.db.flush()
        await self.db.refresh(channel)
        return channel
    
    async def get_channel_by_name(self, channel_name: str, status: Optional[str] = ChannelStatus.LIVE) -> Optional[LiveChannel]:
        """根据名称获取频道，如果有多个匹配则返回最新的"""
        async with get_read_session() as session:
            stmt = select(LiveChannel).where(LiveChannel.channel_name == channel_name)
            if status is not None:
                stmt = stmt.where(LiveChannel.status == status)
            # 按创建时间倒序排序，取最新的一个
            stmt = stmt.order_by(desc(LiveChannel.created_at)).limit(1)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()
    
    async def get_channel_by_id(self, channel_id: int) -> Optional[LiveChannel]:
        """根据ID获取频道"""
        return await self.db.get(LiveChannel, channel_id)
    
    async def update_channel_status(self, channel_id: int, status: str, sid: Optional[str] = None, started_at: Optional[datetime] = None, ended_at: Optional[datetime] = None) -> bool:
        """更新频道状态"""
        channel = await self.get_channel_by_id(channel_id)
        if not channel:
            return False
        
        channel.status = status
        
        # 如果提供了sid，更新sid字段
        if sid is not None:
            channel.sid = sid
            
        if status == ChannelStatus.LIVE:
            # 如果提供了started_at，使用提供的时间，否则使用当前时间
            channel.started_at = started_at if started_at is not None else utc_now_naive()
        elif status == ChannelStatus.ENDED:
            # 如果提供了ended_at，使用提供的时间，否则使用当前时间
            channel.ended_at = ended_at if ended_at is not None else utc_now_naive()
            
            # 计算duration（秒）
            if channel.started_at and channel.ended_at:
                duration_seconds = int((channel.ended_at - channel.started_at).total_seconds())
                channel.duration = max(0, duration_seconds)  # 确保duration不为负数
            
            # 频道结束时，观众数直接清零
            channel.current_audience = 0
            
        await self.db.flush()
        return True
    
    async def update_channel_audience_count(self, channel_id: int, count_change: int) -> bool:
        """更新频道观众数"""
        channel = await self.get_channel_by_id(channel_id)
        if not channel:
            return False
            
        channel.current_audience = max(0, channel.current_audience + count_change)
        await self.db.flush()
        return True
    
    async def get_live_channels(self, limit: int = 50, offset: int = 0) -> List[LiveChannel]:
        """获取所有正在直播的频道"""
        async with get_read_session() as session:
            stmt = select(LiveChannel).where(
                LiveChannel.status == ChannelStatus.LIVE
            ).order_by(desc(LiveChannel.started_at)).limit(limit).offset(offset)
            
            result = await session.execute(stmt)
            return result.scalars().all()

    async def get_live_channels_count(self) -> int:
        """获取正在直播的频道总数"""
        async with get_read_session() as session:
            stmt = select(func.count(LiveChannel.id)).where(
                LiveChannel.status == ChannelStatus.LIVE
            )
            result = await session.execute(stmt)
            return result.scalar() or 0

    async def get_live_channels_by_token(self, token_address: str) -> List[LiveChannel]:
        """获取绑定了指定代币且正在直播的频道"""
        stmt = select(LiveChannel).where(
            and_(
                LiveChannel.token_address == token_address,
                LiveChannel.status == ChannelStatus.LIVE
            )
        ).order_by(desc(LiveChannel.started_at))
        
        result = await self.db.execute(stmt)
        return result.scalars().all()


class AudienceRepository:
    """观众相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def add_audience(self, sid: str, channel_name: str, user_id: str, platform: int, role: str, client_seq: int = 0, joined_at: Optional[datetime] = None, channel_id: Optional[int] = None) -> ChannelAudience:
        """添加观众"""
        # 先检查是否已存在活跃的记录
        existing = await self.get_active_audience_by_user(channel_name, user_id)
        
        joined_time = joined_at if joined_at is not None else utc_now_naive()
        
        if existing:
            # 更新现有记录
            existing.sid = sid
            existing.role = role
            existing.platform = platform
            existing.client_seq = client_seq
            existing.joined_at = joined_time
            existing.left_at = None
            existing.leave_reason = 0
            # 如果提供了channel_id，更新该字段
            if channel_id is not None:
                existing.channel_id = channel_id
            await self.db.flush()
            return existing
        else:
            # 创建新记录
            audience = ChannelAudience(
                sid=sid,
                channel_id=channel_id,
                channel_name=channel_name,
                user_id=user_id,
                platform=platform,
                role=role,
                client_seq=client_seq,
                joined_at=joined_time
            )
            self.db.add(audience)
            await self.db.flush()
            await self.db.refresh(audience)
            return audience
    
    async def get_audience(self, channel_id: int, user_id: str) -> Optional[ChannelAudience]:
        """获取观众记录 (已废弃，保持向后兼容)"""
        stmt = select(ChannelAudience).where(
            and_(
                ChannelAudience.channel_id == channel_id,
                ChannelAudience.user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_active_audience_by_user(self, channel_name: str, user_id: str) -> Optional[ChannelAudience]:
        """获取用户在频道中的活跃观众记录，如果有多个则返回最新的"""
        async with get_read_session() as session:
            stmt = select(ChannelAudience).where(
                and_(
                    ChannelAudience.channel_name == channel_name,
                    ChannelAudience.user_id == user_id,
                    ChannelAudience.left_at.is_(None)
                )
            )
            # 按加入时间倒序排序，取最新的一个
            stmt = stmt.order_by(desc(ChannelAudience.joined_at)).limit(1)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()
    
    async def remove_audience(self, channel_id: int, user_id: str) -> bool:
        """移除观众 (已废弃，保持向后兼容)"""
        audience = await self.get_audience(channel_id, user_id)
        if not audience:
            return False
            
        audience.left_at = utc_now_naive()
        await self.db.flush()
        return True
    
    async def remove_audience_by_channel_name(self, channel_name: str, user_id: str, leave_reason: int = 1, left_at: Optional[datetime] = None) -> bool:
        """移除观众（通过频道名称）"""
        audience = await self.get_active_audience_by_user(channel_name, user_id)
        if not audience:
            return False
            
        audience.left_at = left_at if left_at is not None else utc_now_naive()
        audience.leave_reason = leave_reason
        await self.db.flush()
        return True
    
    async def remove_audience_by_sid(self, sid: str, user_id: str, leave_reason: int = 1, left_at: Optional[datetime] = None, duration: Optional[int] = None) -> bool:
        """移除观众（通过 SID）"""
        stmt = select(ChannelAudience).where(
            and_(
                ChannelAudience.sid == sid,
                ChannelAudience.user_id == user_id,
                ChannelAudience.left_at.is_(None)
            )
        )
        # 按加入时间倒序排序，取最新的一个
        stmt = stmt.order_by(desc(ChannelAudience.joined_at)).limit(1)
        result = await self.db.execute(stmt)
        audience = result.scalar_one_or_none()
        
        if not audience:
            return False
            
        audience.left_at = left_at if left_at is not None else utc_now_naive()
        audience.leave_reason = leave_reason
        if duration is not None:
            audience.duration = duration
        await self.db.flush()
        return True
    
    async def get_channel_audiences(self, channel_id: int, active_only: bool = True) -> List[ChannelAudience]:
        """获取频道所有观众"""
        stmt = select(ChannelAudience).where(ChannelAudience.channel_id == channel_id)
        if active_only:
            stmt = stmt.where(ChannelAudience.left_at.is_(None))
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_channel_audiences_by_name(self, channel_name: str, active_only: bool = True) -> List[ChannelAudience]:
        """通过频道名称获取频道所有观众"""
        stmt = select(ChannelAudience).where(ChannelAudience.channel_name == channel_name)
        if active_only:
            stmt = stmt.where(ChannelAudience.left_at.is_(None))
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def clear_all_active_audiences(self, channel_id: int) -> int:
        """清除频道所有活跃观众"""
        stmt = select(ChannelAudience).where(
            and_(
                ChannelAudience.channel_id == channel_id,
                ChannelAudience.left_at.is_(None)
            )
        )
        result = await self.db.execute(stmt)
        active_audiences = result.scalars().all()
        
        count = 0
        for audience in active_audiences:
            audience.left_at = utc_now_naive()
            count += 1
        
        await self.db.flush()
        return count
    
    async def get_total_unique_audience_count(self, channel_id: int) -> int:
        """获取频道的累计观众数（去重）"""
        stmt = select(func.count(func.distinct(ChannelAudience.user_id))).where(
            ChannelAudience.channel_id == channel_id
        )
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    
    async def update_audience_role(self, channel_id: int, user_id: str, new_role: str) -> bool:
        """更新观众角色"""
        audience = await self.get_audience(channel_id, user_id)
        if not audience:
            return False
            
        audience.role = new_role
        await self.db.flush()
        return True


class RecordingRepository:
    """录制相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_recording_session(self, channel_id: int, resource_id: str, sid: str) -> RecordingSession:
        """创建录制会话"""
        recording_session = RecordingSession(
            channel_id=channel_id,
            resource_id=resource_id,
            sid=sid,
            status=RecordingStatus.STARTED
        )
        self.db.add(recording_session)
        await self.db.flush()
        await self.db.refresh(recording_session)
        return recording_session
    
    async def get_recording_session(self, resource_id: str, sid: str) -> Optional[RecordingSession]:
        """获取录制会话"""
        stmt = select(RecordingSession).where(
            and_(
                RecordingSession.resource_id == resource_id,
                RecordingSession.sid == sid
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_recording_status(self, resource_id: str, sid: str, status: str) -> bool:
        """更新录制状态"""
        recording_session = await self.get_recording_session(resource_id, sid)
        if not recording_session:
            return False
        
        recording_session.status = status
        if status == RecordingStatus.STOPPED:
            recording_session.stopped_at = utc_now_naive()
        
        await self.db.flush()
        return True
    
    async def get_channel_recordings(self, channel_id: int) -> List[RecordingSession]:
        """获取频道所有录制会话"""
        stmt = select(RecordingSession).where(RecordingSession.channel_id == channel_id)
        result = await self.db.execute(stmt)
        return result.scalars().all()


class ChannelGiftRepository:
    """直播间礼物相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_gift_record(
        self,
        channel_id,
        channel_name: str,
        tipper: str,
        gift_id: int,
        txid: str,
        quantity: int = 1,
        gift_value: int = 0,
        message: Optional[str] = None,
        operation_id: Optional[str] = None
    ) -> ChannelGiftHistory:
        """
        创建礼物购买记录
        
        Args:
            channel_id: 直播间ID
            channel_name: 直播间名称
            tipper: 发送者用户ID
            gift_id: 礼物ID
            txid: 交易哈希
            quantity: 购买数量，默认为1
            gift_value: 总打赏价值（USD分），应该等于单价 * 数量
            message: 打赏留言（可选）
            operation_id: 客户端生成的唯一操作ID（可选）
        """
        
        gift_record = ChannelGiftHistory(
            channel_id=channel_id,
            channel_name=channel_name,
            tipper=tipper,
            gift_id=gift_id,
            gift_count=quantity,
            gift_value=gift_value,
            message=message,
            operation_id=operation_id,
            txid=txid
        )
        
        self.db.add(gift_record)
        await self.db.flush()
        await self.db.refresh(gift_record)
        return gift_record
    
    async def get_channel_gifts(
        self,
        channel_name: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[ChannelGiftHistory]:
        """获取频道的礼物记录"""
        stmt = select(ChannelGiftHistory).where(
            ChannelGiftHistory.channel_name == channel_name
        ).order_by(desc(ChannelGiftHistory.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_channel_gifts_by_id(
        self,
        channel_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> List[ChannelGiftHistory]:
        """根据频道ID获取频道的礼物记录"""
        stmt = select(ChannelGiftHistory).where(
            ChannelGiftHistory.channel_id == channel_id
        ).order_by(desc(ChannelGiftHistory.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_channel_gifts_count_by_id(
        self,
        channel_id: int
    ) -> int:
        """根据频道ID获取频道礼物记录总数"""
        stmt = select(func.count(ChannelGiftHistory.id)).where(
            ChannelGiftHistory.channel_id == channel_id
        )
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    
    async def get_user_gift_history(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[ChannelGiftHistory]:
        """获取用户的打赏历史"""
        stmt = select(ChannelGiftHistory).where(
            ChannelGiftHistory.tipper == user_id
        ).order_by(desc(ChannelGiftHistory.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def check_operation_id_exists(self, operation_id: str) -> bool:
        """
        检查操作ID是否已存在
        
        Args:
            operation_id: 客户端生成的唯一操作ID
            
        Returns:
            bool: True if operation exists, False otherwise
        """
        from sqlalchemy import select, func
        
        stmt = select(func.count(ChannelGiftHistory.id)).where(
            ChannelGiftHistory.operation_id == operation_id
        )
        result = await self.db.execute(stmt)
        count = result.scalar() or 0
        return count > 0
    
    async def get_gift_by_operation_id(self, operation_id: str) -> Optional[ChannelGiftHistory]:
        """
        根据操作ID获取礼物记录
        
        Args:
            operation_id: 客户端生成的唯一操作ID
            
        Returns:
            Optional[ChannelGiftHistory]: 礼物记录，如果不存在返回None
        """
        stmt = select(ChannelGiftHistory).where(
            ChannelGiftHistory.operation_id == operation_id
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_channel_gift_ranking(
        self,
        channel_name: str,
        limit: int = 50,
        offset: int = 0
    ) -> tuple[List[Dict[str, Any]], int]:
        """
        获取频道打赏排行榜
        
        Args:
            channel_name: 频道名称
            limit: 返回条数限制
            offset: 偏移量
            
        Returns:
            tuple: (排行榜数据列表, 总打赏人数)
        """
        # 使用新的gift_value字段，避免JOIN操作，提高查询性能
        query = text("""
            SELECT 
                tipper as user_id,
                COUNT(*) as total_gift_count,
                COUNT(DISTINCT gift_id) as gift_types,
                SUM(gift_value) as total_gift_value,
                MAX(created_at) as latest_gift_time
            FROM channel_gift_history
            WHERE channel_name = :channel_name
            GROUP BY tipper
            ORDER BY total_gift_value DESC, latest_gift_time DESC
            LIMIT :limit OFFSET :offset
        """)
        
        # 获取总打赏人数
        count_query = text("""
            SELECT COUNT(DISTINCT tipper) as total_tippers
            FROM channel_gift_history
            WHERE channel_name = :channel_name
        """)
        
        # 执行查询
        result = await self.db.execute(query, {
            "channel_name": channel_name,
            "limit": limit,
            "offset": offset
        })
        
        count_result = await self.db.execute(count_query, {
            "channel_name": channel_name
        })
        
        rankings = []
        for idx, row in enumerate(result.mappings().all()):
            rankings.append({
                "rank": offset + idx + 1,
                "user_id": row["user_id"],
                "total_gift_value": int(row["total_gift_value"] or 0),
                "total_gift_count": int(row["total_gift_count"] or 0),
                "gift_types": int(row["gift_types"] or 0),
                "latest_gift_time": row["latest_gift_time"]
            })
        
        total_tippers = count_result.scalar() or 0
        
        return rankings, total_tippers

    async def get_channel_total_gift_value(self, channel_name: str) -> int:
        """
        获取频道总打赏价值
        
        Args:
            channel_name: 频道名称
            
        Returns:
            int: 总打赏价值（USD分）
        """
        query = text("""
            SELECT SUM(gift_value) as total_value
            FROM channel_gift_history
            WHERE channel_name = :channel_name
        """)
        
        result = await self.db.execute(query, {
            "channel_name": channel_name
        })
        row = result.first()
        if row:
            return int(row[0] or 0)  # Access by index for raw SQL query results
        return 0
    
    async def get_unique_gifters_count(self, channel_name: str) -> int:
        """
        获取频道的唯一打赏者数量
        
        Args:
            channel_name: 频道名称
            
        Returns:
            int: 唯一打赏者数量
        """
        query = text("""
            SELECT COUNT(DISTINCT tipper) as unique_count
            FROM channel_gift_history
            WHERE channel_name = :channel_name
        """)
        
        result = await self.db.execute(query, {
            "channel_name": channel_name
        })
        row = result.first()
        if row:
            return int(row[0] or 0)  # Access by index for raw SQL query results
        return 0
    
    async def get_channel_total_gift_count(self, channel_name: str) -> int:
        """
        获取频道的礼物总数（所有gift_count的总和）
        
        Args:
            channel_name: 频道名称
            
        Returns:
            int: 礼物总数
        """
        query = text("""
            SELECT SUM(gift_count) as total_count
            FROM channel_gift_history
            WHERE channel_name = :channel_name
        """)
        
        result = await self.db.execute(query, {
            "channel_name": channel_name
        })
        row = result.first()
        if row:
            return int(row[0] or 0)  # Access by index for raw SQL query results
        return 0


class GiftListRepository:
    """礼物列表相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_gift_list(
        self, 
        min_price: Optional[int] = None, 
        max_price: Optional[int] = None
    ) -> List[GiftList]:
        """获取礼物列表，按sort_order从小到大排序，支持价格过滤"""
        stmt = select(GiftList).where(GiftList.is_active == 1)
        
        # 应用价格过滤
        if min_price is not None:
            stmt = stmt.where(GiftList.price >= min_price)
        
        if max_price is not None:
            stmt = stmt.where(GiftList.price <= max_price)
        
        # 固定按sort_order从小到大排序
        stmt = stmt.order_by(GiftList.sort_order.asc())
        
        # 二级排序（确保结果稳定）
        stmt = stmt.order_by(GiftList.id.asc())
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_gift_list_count(
        self, 
        min_price: Optional[int] = None, 
        max_price: Optional[int] = None
    ) -> int:
        """获取礼物列表总数，支持价格过滤"""
        stmt = select(func.count(GiftList.id)).where(GiftList.is_active == 1)
        
        # 应用价格过滤
        if min_price is not None:
            stmt = stmt.where(GiftList.price >= min_price)
        
        if max_price is not None:
            stmt = stmt.where(GiftList.price <= max_price)
        
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    
    async def get_categories(self) -> List[str]:
        """获取所有可用的礼物分类"""
        stmt = select(GiftList.category).where(GiftList.is_active == 1).distinct()
        result = await self.db.execute(stmt)
        return [category for category in result.scalars().all() if category]
    
    async def get_gift_by_id(self, gift_id: int) -> Optional[GiftList]:
        """根据gift_id获取礼物信息"""
        # 如果gift_id是数字字符串，转换为整数查询
        try:
            stmt = select(GiftList).where(
                and_(
                    GiftList.id == gift_id,
                    GiftList.is_active == 1
                )
            )
        except (ValueError, TypeError):
            # 如果无法转换为整数，则作为字符串字段查询
            stmt = select(GiftList).where(
                and_(
                    GiftList.gift_id == gift_id,
                    GiftList.is_active == 1
                )
            )
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()


class ChannelStatsRepository:
    """直播间统计相关的数据库操作"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_or_update_stats(self, channel: LiveChannel) -> ChannelStats:
        """创建或更新频道统计数据"""
        # 查找现有的统计记录
        stmt = select(ChannelStats).where(
            and_(
                ChannelStats.channel_id == channel.id,
                ChannelStats.channel_name == channel.channel_name
            )
        )
        result = await self.db.execute(stmt)
        stats = result.scalar_one_or_none()
        
        if not stats:
            # 创建新的统计记录
            stats = ChannelStats(
                channel_id=channel.id,
                channel_name=channel.channel_name,
                user_id=channel.user_id,
                started_at=channel.started_at,
                ended_at=channel.ended_at,
                live_duration=channel.duration or 0
            )
            self.db.add(stats)
        else:
            # 更新现有记录
            stats.ended_at = channel.ended_at
            stats.live_duration = channel.duration or 0
            stats.updated_at = utc_now_naive()
        
        await self.db.flush()
        return stats
    
    async def update_gift_stats(self, channel_name: str, gift_value: int, gift_count: int = 1) -> bool:
        """更新礼物统计数据"""
        stmt = select(ChannelStats).where(
            ChannelStats.channel_name == channel_name
        ).order_by(desc(ChannelStats.created_at)).limit(1)
        
        result = await self.db.execute(stmt)
        stats = result.scalar_one_or_none()
        
        if stats:
            stats.total_gift_value += gift_value
            stats.total_gift_count += gift_count
            stats.updated_at = utc_now_naive()
            await self.db.flush()
            return True
        return False
    
    async def update_audience_stats(self, channel_name: str, current_audience: int) -> bool:
        """更新观众统计数据"""
        stmt = select(ChannelStats).where(
            ChannelStats.channel_name == channel_name
        ).order_by(desc(ChannelStats.created_at)).limit(1)
        
        result = await self.db.execute(stmt)
        stats = result.scalar_one_or_none()
        
        if stats:
            # 更新峰值观众数
            if current_audience > stats.peak_audience:
                stats.peak_audience = current_audience
            stats.updated_at = utc_now_naive()
            await self.db.flush()
            return True
        return False
    
    async def get_channel_stats(self, channel_name: str) -> Optional[ChannelStats]:
        """获取频道最新的统计数据"""
        stmt = select(ChannelStats).where(
            ChannelStats.channel_name == channel_name
        ).order_by(desc(ChannelStats.created_at)).limit(1)
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()


class AgoraRepository:
    """Agora服务的统一Repository入口"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.channels = ChannelRepository(db)
        self.audiences = AudienceRepository(db)
        self.recordings = RecordingRepository(db)
        self.gifts = ChannelGiftRepository(db)
        self.gift_list = GiftListRepository(db)
        self.stats = ChannelStatsRepository(db)
    
    async def close(self):
        """关闭数据库会话"""
        await self.db.close() 