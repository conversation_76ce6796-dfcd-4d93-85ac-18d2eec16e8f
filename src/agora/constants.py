from strenum import StrEnum
from enum import IntEnum


class ChannelStatus(StrEnum):
    """频道状态枚举"""
    CREATED = "created"
    LIVE = "live"
    ENDED = "ended"


class UserRole(StrEnum):
    """用户角色枚举"""
    AUDIENCE = "audience"
    HOST = "host"


class AgoraRole(IntEnum):
    """Agora 角色常量 - 根据 Agora Token Builder 文档定义"""
    PUBLISHER = 1    # 主播（广播者）
    SUBSCRIBER = 2   # 观众（订阅者，默认）


class RecordingStatus(StrEnum):
    """录制状态枚举"""
    STARTED = "started"
    STOPPED = "stopped"
    FAILED = "failed"


class EventType(IntEnum):
    """事件类型枚举 - 匹配Agora webhook实际发送的整数类型"""
    CHANNEL_CREATE = 101
    CHANNEL_DESTROY = 102
    BROADCASTER_JOIN = 103
    BROADCASTER_LEAVE = 104
    AUDIENCE_JOIN = 105
    AUDIENCE_LEAVE = 106
    ROLE_CHANGE_TO_BROADCASTER = 111
    ROLE_CHANGE_TO_AUDIENCE = 112


class TokenType(StrEnum):
    """Token 类型枚举"""
    pass  # 如需要可以在这里添加具体的 token 类型


class RecordingVendor(StrEnum):
    """录制服务提供商枚举"""
    AGORA_CLOUD = "agora_cloud"


class GiftCategory(StrEnum):
    """礼物类别枚举"""
    NORMAL = "normal"      # 普通礼物
    SPECIAL = "special"    # 特殊礼物
    PREMIUM = "premium"    # 高级礼物
    SUPREME = "supreme"    # 至尊礼物 
    