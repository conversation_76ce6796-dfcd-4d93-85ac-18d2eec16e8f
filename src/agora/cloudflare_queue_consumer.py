"""
Cloudflare Queue消费者 - Python实现

从Cloudflare Queue读取Agora webhook事件并处理
"""

import asyncio
import json
import logging
import os
import time
from typing import Optional, Dict, Any

import httpx
from httpx import AsyncClient
import redis.asyncio as redis

from src.agora.schemas import WebhookEvent
from src.agora.repos import AgoraRepository
from src.agora.service import AgoraService
from src.agora.logger import logger
from src.agora.settings import settings
from src.database.session import get_transactional_session
from src.common.redis_cli import RedisCli


class CloudflareQueueConsumer:
    """Cloudflare Queue消费者"""
    
    def __init__(self):
        # Cloudflare API配置
        self.account_id = os.getenv("CF_ACCOUNT_ID")
        self.api_token = os.getenv("CF_QUEUES_API_TOKEN")
        self.queue_id = os.getenv("CF_QUEUE_ID")
        
        # API客户端
        self.client = AsyncClient(
            base_url="https://api.cloudflare.com/client/v4",
            headers={
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )
        
        # 消费者配置
        self.batch_size = 5  # 减少批量大小，加快处理速度
        self.visibility_timeout = 120  # 增加到2分钟，给足够时间处理和确认
        self.poll_interval = 2.0
        
        # Redis配置
        self.redis_client = None
        self.redis_key_prefix = "agora:event:processed:"
        self.dedup_ttl = 600  # 10分钟TTL
        
        logger.info(f"初始化Cloudflare Queue消费者: queue={self.queue_id}, batch_size={self.batch_size}, visibility_timeout={self.visibility_timeout}s")
    
    async def init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = RedisCli.async_()
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis连接成功")
            
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            raise
    
    async def start_consuming(self):
        """开始消费队列消息"""
        logger.info("开始消费Cloudflare Queue中的Agora webhook事件...")
        
        while True:
            try:
                # 从队列拉取消息
                raw_messages = await self.pull_messages()
                
                if raw_messages:
                    logger.debug(f"拉取到 {len(raw_messages)} 条原始消息")
                    
                    # 将原始消息转换为WebhookEvent对象
                    webhook_events = self._parse_messages_to_webhook_events(raw_messages)
                    logger.debug(f"成功解析 {len(webhook_events)} 条webhook事件")
                    
                    # 过滤重复消息
                    unique_events = await self._filter_duplicate_webhook_events(webhook_events)
                    if len(unique_events) < len(webhook_events):
                        logger.debug(f"过滤掉 {len(webhook_events) - len(unique_events)} 条重复事件")
                    
                    if unique_events:
                        # 逐个处理事件并立即确认，避免批量延迟
                        success_count = 0
                        error_count = 0
                        
                        for webhook_event, lease_id in unique_events:
                            try:
                                # 处理事件
                                success = await self.process_webhook_event(webhook_event)
                                
                                if success:
                                    # 立即确认事件，避免超时
                                    ack_success = await self.ack_message_by_lease_id(lease_id)
                                    if ack_success:
                                        success_count += 1
                                        # 添加到已处理缓存
                                        await self._add_to_processed_cache(webhook_event.noticeId)
                                    else:
                                        error_count += 1
                                        logger.warning(f"事件处理成功但确认失败: {lease_id}")
                                else:
                                    error_count += 1
                                    
                            except Exception as e:
                                logger.error(f"处理单个事件时出错: {str(e)}", exc_info=True)
                                error_count += 1
                        
                        logger.debug(f"事件处理完成: 成功 {success_count}, 失败 {error_count}")
                    else:
                        logger.debug("所有事件都已被过滤（重复事件）")
                else:
                    # 没有事件，等待后再次轮询
                    await asyncio.sleep(self.poll_interval)
                    
            except Exception as e:
                logger.error(f"消费队列时出错: {str(e)}", exc_info=True)
                await asyncio.sleep(self.poll_interval)
    
    async def pull_messages(self) -> list:
        """从Cloudflare Queue拉取消息"""
        try:
            # Cloudflare Queue HTTP Pull API endpoint
            url = f"/accounts/{self.account_id}/queues/{self.queue_id}/messages/pull"
            
            # 构建请求体 - 按照官方API文档格式
            payload = {
                "batch_size": self.batch_size,
                "visibility_timeout_ms": self.visibility_timeout * 1000
            }
            
            logger.debug(f"发送pull请求: {url} with payload: {payload}")
            
            response = await self.client.post(url, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查API响应的success字段
                if not data.get("success", False):
                    errors = data.get("errors", [])
                    logger.error(f"API响应显示失败: {errors}")
                    return []
                
                # 根据API文档正确解析响应结构
                result = data.get("result", {})
                messages = result.get("messages", [])
                message_backlog_count = result.get("message_backlog_count", 0)
                
                if messages:
                    logger.debug(f"成功拉取到 {len(messages)} 条消息，积压消息数: {message_backlog_count}")
                    return messages
                else:
                    logger.debug(f"队列中暂无消息，积压消息数: {message_backlog_count}")
                    return []
            elif response.status_code == 204:
                # 204 No Content - 队列为空
                logger.debug("队列为空，无消息可拉取")
                return []
            else:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                logger.error(f"拉取消息失败: {response.status_code} {response.text}")
                logger.error(f"错误详情: {error_data}")
                return []
                
        except Exception as e:
            logger.error(f"拉取消息时出错: {str(e)}", exc_info=True)
            return []
    
    async def ack_message_by_lease_id(self, lease_id: str) -> bool:
        """根据lease_id确认事件处理完成"""
        try:
            if not lease_id:
                logger.error("lease_id为空，无法确认事件")
                return False
                
            url = f"/accounts/{self.account_id}/queues/{self.queue_id}/messages/ack"
            
            payload = {
                "acks": [{"lease_id": lease_id}]
            }
            
            logger.debug(f"确认事件: lease_id={lease_id}")
            
            response = await self.client.post(url, json=payload)
            
            if response.status_code == 200:
                # 检查API响应的success字段
                data = response.json()
                if data.get("success", False):
                    logger.debug(f"事件确认成功: lease_id={lease_id}")
                    return True
                else:
                    errors = data.get("errors", [])
                    logger.error(f"事件确认API响应失败: {errors}")
                    return False
            else:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                logger.error(f"确认事件失败: {response.status_code} {response.text}")
                logger.error(f"错误详情: {error_data}")
                return False
                
        except Exception as e:
            logger.error(f"确认事件时出错: {str(e)}", exc_info=True)
            return False

    async def ack_message(self, message: Dict[str, Any]) -> bool:
        """确认事件处理完成"""
        try:
            url = f"/accounts/{self.account_id}/queues/{self.queue_id}/messages/ack"
            
            # 获取lease_id
            lease_id = message.get("lease_id")
            if not lease_id:
                logger.error(f"事件缺少lease_id: {message}")
                return False
            
            payload = {
                "acks": [{"lease_id": lease_id}]
            }
            
            logger.debug(f"确认事件: lease_id={lease_id}")
            
            response = await self.client.post(url, json=payload)
            
            if response.status_code == 200:
                # 检查API响应的success字段
                data = response.json()
                if data.get("success", False):
                    logger.debug(f"事件确认成功: lease_id={lease_id}")
                    return True
                else:
                    errors = data.get("errors", [])
                    logger.error(f"事件确认API响应失败: {errors}")
                    return False
            else:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                logger.error(f"确认事件失败: {response.status_code} {response.text}")
                logger.error(f"错误详情: {error_data}")
                return False
                
        except Exception as e:
            logger.error(f"确认事件时出错: {str(e)}", exc_info=True)
            return False
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
        
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis连接已关闭")
            
        logger.info("Cloudflare Queue客户端已关闭")

    async def _add_to_processed_cache(self, notice_id: str):
        """将noticeId添加到Redis缓存，设置10分钟TTL"""
        if not self.redis_client:
            logger.warning("Redis未连接，无法缓存已处理事件")
            return
            
        try:
            redis_key = f"{self.redis_key_prefix}{notice_id}"
            # 设置键值和TTL（10分钟）
            await self.redis_client.setex(redis_key, self.dedup_ttl, "1")
            logger.debug(f"添加到Redis缓存: noticeId={notice_id}, TTL={self.dedup_ttl}s")
            
        except Exception as e:
            logger.error(f"Redis缓存失败: {str(e)}, noticeId={notice_id}")
            # 缓存失败不影响处理流程

    def _parse_messages_to_webhook_events(self, messages: list) -> list:
        """将原始消息转换为WebhookEvent对象"""
        webhook_events = []
        
        for message in messages:
            # 解析消息体（处理双重编码的JSON）
            body = message.get("body", {})
            json_body = body  # 初始化
            
            try:
                # 处理双重编码的JSON字符串
                if isinstance(body, str):
                    # 第一次解析：移除外层引号
                    json_body = json.loads(body)
                
                # 第二次解析：如果还是字符串，继续解析
                if isinstance(json_body, str):
                    json_body = json.loads(json_body)
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析消息体JSON失败: {str(e)}, body: {body}")
                continue
            
            # 检查body是否为字典
            if not isinstance(json_body, dict):
                logger.error(f"消息体格式错误，期望字典，得到: {type(json_body)}, body: {json_body}")
                continue
            
            # 创建WebhookEvent对象
            try:
                webhook_event = WebhookEvent(**json_body)
                logger.debug(f"解析成功，获得WebhookEvent对象: noticeId={webhook_event.noticeId}")
                
                # 返回简单的元组：(webhook_event, lease_id)
                webhook_events.append((webhook_event, message.get('lease_id')))
            except Exception as e:
                logger.error(f"创建WebhookEvent对象失败: {str(e)}, json_body: {json_body}")
                continue
        
        return webhook_events

    async def _filter_duplicate_webhook_events(self, events: list) -> list:
        """根据noticeId过滤重复事件，使用Redis进行去重"""
        if not self.redis_client:
            logger.warning("Redis未连接，跳过去重检查")
            return events
            
        unique_events = []
        seen_in_batch = set()  # 跟踪当前批次中已见过的noticeId
        
        for event, lease_id in events:
            notice_id = event.noticeId
            
            if notice_id:
                # 检查Redis和当前批次
                redis_key = f"{self.redis_key_prefix}{notice_id}"
                
                try:
                    # 检查Redis中是否已处理
                    is_processed = await self.redis_client.exists(redis_key)
                    
                    if not is_processed and notice_id not in seen_in_batch:
                        unique_events.append((event, lease_id))
                        seen_in_batch.add(notice_id)  # 添加到当前批次跟踪
                    else:
                        if is_processed:
                            logger.debug(f"跳过Redis重复事件: noticeId={notice_id}")
                        else:
                            logger.debug(f"跳过批次内重复事件: noticeId={notice_id}")
                            
                except Exception as e:
                    logger.error(f"Redis检查失败: {str(e)}, 允许处理事件: {notice_id}")
                    # Redis失败时允许处理，避免丢失消息
                    if notice_id not in seen_in_batch:
                        unique_events.append((event, lease_id))
                        seen_in_batch.add(notice_id)
            else:
                logger.warning(f"事件缺少noticeId，使用lease_id: {lease_id}")
                # 如果没有noticeId，直接处理（风险较低）
                unique_events.append((event, lease_id))
        
        return unique_events

    async def process_webhook_event(self, webhook_event: WebhookEvent) -> bool:
        """处理Webhook事件"""
        try:
            # 创建新的数据库会话和service
            async with get_transactional_session() as session:
                try:
                    repo = AgoraRepository(session)
                    service = AgoraService(repo)
                    await service.init_redis()
                    
                    # 处理webhook事件
                    success = await service.handle_webhook_event(webhook_event)
                    
                    if success:
                        logger.debug(f"Webhook事件处理成功: noticeId={webhook_event.noticeId}")
                        return True
                    else:
                        logger.info(f"Webhook事件跳过处理: noticeId={webhook_event.noticeId}")
                        # 返回True以确保消息被确认，避免重复处理
                        return True
                        
                except Exception as e:
                    logger.error(f"处理Webhook事件时出错: {str(e)}", exc_info=True)
                    return False
                
        except Exception as e:
            logger.error(f"处理Webhook事件时出错: {str(e)}", exc_info=True)
            return False
