import json
import base64
import hashlib
import hmac
import time
from typing import Optional, Dict, Any, List
import httpx

from .settings import settings
from .schemas import RecordingRequest, RecordingResponse
from .constants import RecordingStatus

class RecordingManager:
    """录制管理器，集成 Agora Cloud Recording API"""
    
    def __init__(self):
        self.customer_id = settings.RECORDING_CUSTOMER_ID
        self.customer_secret = settings.RECORDING_CUSTOMER_SECRET
        self.api_base_url = settings.RECORDING_API_BASE_URL
        self.app_id = settings.APP_ID
        
        if not all([self.customer_id, self.customer_secret, self.app_id]):
            raise ValueError("Recording credentials must be configured")
    
    def _generate_authorization(self, method: str, uri: str, body: str = "") -> str:
        """
        生成 Agora Cloud Recording API 授权头
        
        Args:
            method: HTTP 方法
            uri: 请求 URI
            body: 请求体
            
        Returns:
            str: Authorization 头值
        """
        timestamp = str(int(time.time()))
        nonce = str(int(time.time() * 1000))
        
        # 构建签名字符串
        string_to_sign = f"{method}\n{uri}\n{body}\n{self.customer_id}\n{timestamp}\n{nonce}"
        
        # 生成签名
        signature = base64.b64encode(
            hmac.new(
                self.customer_secret.encode(),
                string_to_sign.encode(),
                hashlib.sha256
            ).digest()
        ).decode()
        
        # 构建授权头
        auth_header = f"agora token={self.customer_id}:{signature}, timestamp={timestamp}, nonce={nonce}"
        return auth_header
    
    async def acquire_resource(
        self, 
        channel_name: str, 
        uid: int,
        client_request: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取录制资源
        
        Args:
            channel_name: 频道名称
            uid: 录制用户 UID
            client_request: 客户端请求参数
            
        Returns:
            Dict: 资源信息
        """
        uri = f"/v1/apps/{self.app_id}/cloud_recording/acquire"
        url = f"{self.api_base_url}{uri}"
        
        # 默认客户端请求参数
        if client_request is None:
            client_request = {
                "scene": 0,  # 实时音视频录制
                "resourceExpiredHour": 24  # 资源过期时间（小时）
            }
        
        body = {
            "cname": channel_name,
            "uid": str(uid),
            "clientRequest": client_request
        }
        
        body_json = json.dumps(body)
        authorization = self._generate_authorization("POST", uri, body_json)
        
        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body_json)
            response.raise_for_status()
            return response.json()
    
    async def start_recording(
        self,
        channel_name: str,
        uid: int,
        resource_id: str,
        recording_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        开始录制
        
        Args:
            channel_name: 频道名称
            uid: 录制用户 UID
            resource_id: 资源 ID
            recording_config: 录制配置
            
        Returns:
            Dict: 录制会话信息
        """
        uri = f"/v1/apps/{self.app_id}/cloud_recording/resourceid/{resource_id}/mode/mix/start"
        url = f"{self.api_base_url}{uri}"
        
        # 默认录制配置
        if recording_config is None:
            recording_config = self._get_default_recording_config()
        
        body = {
            "cname": channel_name,
            "uid": str(uid),
            "clientRequest": recording_config
        }
        
        body_json = json.dumps(body)
        authorization = self._generate_authorization("POST", uri, body_json)
        
        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body_json)
            response.raise_for_status()
            return response.json()
    
    async def stop_recording(
        self,
        channel_name: str,
        uid: int,
        resource_id: str,
        sid: str,
        async_stop: bool = False
    ) -> Dict[str, Any]:
        """
        停止录制
        
        Args:
            channel_name: 频道名称
            uid: 录制用户 UID
            resource_id: 资源 ID
            sid: 录制会话 ID
            async_stop: 是否异步停止
            
        Returns:
            Dict: 停止录制结果
        """
        uri = f"/v1/apps/{self.app_id}/cloud_recording/resourceid/{resource_id}/sid/{sid}/mode/mix/stop"
        url = f"{self.api_base_url}{uri}"
        
        body = {
            "cname": channel_name,
            "uid": str(uid),
            "clientRequest": {
                "async_stop": async_stop
            }
        }
        
        body_json = json.dumps(body)
        authorization = self._generate_authorization("POST", uri, body_json)
        
        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body_json)
            response.raise_for_status()
            return response.json()
    
    async def query_recording(
        self,
        resource_id: str,
        sid: str
    ) -> Dict[str, Any]:
        """
        查询录制状态
        
        Args:
            resource_id: 资源 ID
            sid: 录制会话 ID
            
        Returns:
            Dict: 录制状态信息
        """
        uri = f"/v1/apps/{self.app_id}/cloud_recording/resourceid/{resource_id}/sid/{sid}/mode/mix/query"
        url = f"{self.api_base_url}{uri}"
        
        authorization = self._generate_authorization("GET", uri)
        
        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
    
    async def update_recording_layout(
        self,
        channel_name: str,
        uid: int,
        resource_id: str,
        sid: str,
        layout_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新录制布局
        
        Args:
            channel_name: 频道名称
            uid: 录制用户 UID
            resource_id: 资源 ID
            sid: 录制会话 ID
            layout_config: 布局配置
            
        Returns:
            Dict: 更新结果
        """
        uri = f"/v1/apps/{self.app_id}/cloud_recording/resourceid/{resource_id}/sid/{sid}/mode/mix/updateLayout"
        url = f"{self.api_base_url}{uri}"
        
        body = {
            "cname": channel_name,
            "uid": str(uid),
            "clientRequest": layout_config
        }
        
        body_json = json.dumps(body)
        authorization = self._generate_authorization("POST", uri, body_json)
        
        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body_json)
            response.raise_for_status()
            return response.json()
    
    def _get_default_recording_config(self) -> Dict[str, Any]:
        """获取默认录制配置"""
        storage_config = self._get_storage_config()
        
        return {
            "recordingConfig": {
                "maxIdleTime": 30,  # 最大空闲时间（秒）
                "streamTypes": 2,   # 录制音视频
                "audioProfile": 1,  # 音频质量
                "channelType": 1,   # 频道类型：直播
                "videoStreamType": 1,  # 视频流类型：高清
                "transcodingConfig": {
                    "height": 720,
                    "width": 1280,
                    "bitrate": 2260,
                    "fps": 30,
                    "mixedVideoLayout": 1,  # 悬浮布局
                    "backgroundColor": "#000000"
                },
                "subscribeVideoUids": ["#allstream#"],  # 订阅所有视频流
                "subscribeAudioUids": ["#allstream#"]   # 订阅所有音频流
            },
            "recordingFileConfig": {
                "avFileType": ["hls", "mp4"]  # 录制文件格式
            },
            "storageConfig": storage_config
        }
    
    def _get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        vendor = settings.RECORDING_VENDOR.lower()
        
        if vendor == "aws":
            return {
                "vendor": 1,  # AWS S3
                "region": settings.AWS_REGION,
                "bucket": settings.RECORDING_BUCKET,
                "accessKey": settings.AWS_ACCESS_KEY_ID,
                "secretKey": settings.AWS_SECRET_ACCESS_KEY,
                "fileNamePrefix": ["recordings", "agora"]
            }
        elif vendor == "aliyun":
            return {
                "vendor": 2,  # 阿里云 OSS
                "region": settings.RECORDING_REGION,
                "bucket": settings.RECORDING_BUCKET,
                "accessKey": settings.ALIYUN_ACCESS_KEY_ID,
                "secretKey": settings.ALIYUN_ACCESS_KEY_SECRET,
                "endpoint": settings.ALIYUN_OSS_ENDPOINT,
                "fileNamePrefix": ["recordings", "agora"]
            }
        elif vendor == "tencent":
            return {
                "vendor": 3,  # 腾讯云 COS
                "region": settings.TENCENT_COS_REGION,
                "bucket": settings.RECORDING_BUCKET,
                "accessKey": settings.TENCENT_SECRET_ID,
                "secretKey": settings.TENCENT_SECRET_KEY,
                "fileNamePrefix": ["recordings", "agora"]
            }
        else:
            raise ValueError(f"Unsupported storage vendor: {vendor}")
    
    async def create_recording_session(
        self,
        channel_name: str,
        uid: int,
        recording_config: Optional[Dict[str, Any]] = None
    ) -> RecordingResponse:
        """
        创建完整的录制会话
        
        Args:
            channel_name: 频道名称
            uid: 录制用户 UID
            recording_config: 录制配置
            
        Returns:
            RecordingResponse: 录制会话信息
        """
        try:
            # 1. 获取资源
            acquire_result = await self.acquire_resource(channel_name, uid)
            resource_id = acquire_result["resourceId"]
            
            # 2. 开始录制
            start_result = await self.start_recording(
                channel_name, uid, resource_id, recording_config
            )
            sid = start_result["sid"]
            
            return RecordingResponse(
                resource_id=resource_id,
                sid=sid,
                channel_id=channel_name,
                status=RecordingStatus.STARTED
            )
            
        except Exception as e:
            raise ValueError(f"Failed to create recording session: {str(e)}")
    
    async def get_recording_files(
        self,
        resource_id: str,
        sid: str
    ) -> List[Dict[str, Any]]:
        """
        获取录制文件列表
        
        Args:
            resource_id: 资源 ID
            sid: 录制会话 ID
            
        Returns:
            List: 录制文件列表
        """
        try:
            query_result = await self.query_recording(resource_id, sid)
            server_response = query_result.get("serverResponse", {})
            file_list = server_response.get("fileList", [])
            
            return file_list
            
        except Exception as e:
            raise ValueError(f"Failed to get recording files: {str(e)}")


recording_manager = RecordingManager() if settings.RECORDING_ENABLED else None
