"""
礼物缓存管理器

提供高性能的内存礼物数据缓存，支持：
- 启动时预加载
- 快速查询
- 热更新
- 线程安全
- 优雅降级
"""

import asyncio
import time
from typing import Optional, Dict, List, Any
from datetime import datetime, timezone
import logging
from dataclasses import dataclass

from src.database.session import get_session, get_read_session
from src.agora.repos import AgoraRepository
from src.database.models.Agora import GiftList

# 配置日志
logger = logging.getLogger(__name__)

# 缓存配置常量
CACHE_MAX_RETRIES = 3  # 最大重试次数
CACHE_LOAD_TIMEOUT = 30  # 加载超时时间（秒）
CACHE_WARMUP_DELAY = 5  # 缓存预热延迟（秒）


@dataclass
class GiftInfo:
    """礼物信息数据类"""
    id: int
    name: str
    description: Optional[str]
    price: int  # USD 分为单位（避免浮点数精度问题）
    currency: str
    icon_url: Optional[str]
    animation_url: Optional[str]
    animation_md5: Optional[str]
    animation_hd_url: Optional[str]
    animation_hd_md5: Optional[str]
    animation_mp4_url: Optional[str]
    animation_mp4_md5: Optional[str]
    category: str
    rarity: int
    is_active: bool
    sort_order: int
    created_at: datetime
    updated_at: datetime
    meta: Optional[Dict[str, Any]] = None  # 添加meta字段以支持翻译


class GiftCacheManager:
    """
    礼物缓存管理器
    
    特性：
    - 内存缓存礼物数据
    - 支持ID和名称查询
    - 线程安全
    - 自动重试机制
    - 性能监控
    """
    
    def __init__(self):
        self._gifts_by_id: Dict[int, GiftInfo] = {}
        self._gifts_by_name: Dict[str, GiftInfo] = {}
        self._all_gifts: List[GiftInfo] = []
        self._last_updated: Optional[datetime] = None
        self._is_loaded: bool = False
        self._lock = asyncio.Lock()
        self._load_attempts = 0
        self._max_retries = CACHE_MAX_RETRIES
        
    @property
    def is_loaded(self) -> bool:
        """检查缓存是否已加载"""
        return self._is_loaded
    
    @property
    def last_updated(self) -> Optional[datetime]:
        """获取上次更新时间"""
        return self._last_updated
    
    @property
    def gift_count(self) -> int:
        """获取缓存中的礼物数量"""
        return len(self._gifts_by_id)
    
    async def initialize(self) -> bool:
        """
        初始化缓存（启动时调用）
        
        Returns:
            bool: 是否成功初始化
        """
        logger.info("开始初始化礼物缓存...")
        
        success = await self._load_gifts_with_retry()
        if success:
            logger.info(f"礼物缓存初始化成功，加载了 {self.gift_count} 个礼物")
        else:
            logger.error("礼物缓存初始化失败")
            
        return success
    
    async def _load_gifts_with_retry(self) -> bool:
        """带重试机制的礼物加载"""
        for attempt in range(1, self._max_retries + 1):
            try:
                self._load_attempts = attempt
                await self._load_gifts_from_database()
                return True
            except Exception as e:
                logger.warning(f"礼物缓存加载第 {attempt} 次尝试失败: {str(e)}")
                if attempt < self._max_retries:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    logger.error(f"礼物缓存加载失败，已重试 {self._max_retries} 次")
        return False
    
    async def _load_gifts_from_database(self) -> None:
        """从数据库加载礼物数据"""
        start_time = time.time()
        
        async with self._lock:
            async with get_read_session() as session:
                try:
                    repo = AgoraRepository(session)
                    gift_models = await repo.gift_list.get_gift_list()
                    
                    # 清空现有缓存
                    self._gifts_by_id.clear()
                    self._gifts_by_name.clear()
                    self._all_gifts.clear()
                    
                    # 转换数据并构建索引
                    for gift_model in gift_models:
                        gift_info = self._convert_model_to_info(gift_model)
                        self._gifts_by_id[gift_info.id] = gift_info
                        self._gifts_by_name[gift_info.name.lower()] = gift_info
                        self._all_gifts.append(gift_info)
                    
                    # 按sort_order从小到大排序
                    self._all_gifts.sort(key=lambda x: x.sort_order, reverse=False)
                    
                    self._last_updated = datetime.now(timezone.utc)
                    self._is_loaded = True
                    
                    load_time = time.time() - start_time
                    logger.info(f"成功加载 {len(self._gifts_by_id)} 个礼物到缓存，耗时 {load_time:.3f}s")
                    
                except Exception as e:
                    logger.error(f"从数据库加载礼物数据失败: {str(e)}")
                    raise
    
    def _convert_model_to_info(self, gift_model: GiftList) -> GiftInfo:
        """将数据库模型转换为缓存对象"""
        meta = gift_model.meta or {}
        name = meta.get('name', f'Gift {gift_model.id}')
        description = meta.get('description')
        animation_md5 = meta.get('animation_md5')
        animation_hd_md5 = meta.get('animation_hd_md5')
        animation_mp4_md5 = meta.get('animation_mp4_md5')
        # 预计算高清动画URL
        if gift_model.animation:
            if gift_model.animation.endswith('.svga'):
                animation_hd_url = gift_model.animation.replace('.svga', '-HD.svga')
            else:
                animation_hd_url = gift_model.animation
        else:
            animation_hd_url = None

        # 预计算 mp4 动画 URL（将扩展名统一替换为 .mp4）
        if gift_model.animation:
            if '.' in gift_model.animation:
                base, _ext = gift_model.animation.rsplit('.', 1)
                animation_mp4_url = f"{base}.mp4"
            else:
                animation_mp4_url = f"{gift_model.animation}.mp4"
        else:
            animation_mp4_url = None
        
        return GiftInfo(
            id=gift_model.id,
            name=name,
            description=description,
            price=gift_model.price,  # 保持原始整数价格
            currency=gift_model.currency,
            icon_url=gift_model.cover,
            animation_url=gift_model.animation,
            animation_md5=animation_md5,
            animation_hd_url=animation_hd_url,
            animation_hd_md5=animation_hd_md5,
            animation_mp4_url=animation_mp4_url,
            animation_mp4_md5=animation_mp4_md5,
            category=gift_model.category,
            rarity=gift_model.rarity,
            is_active=bool(gift_model.is_active),
            sort_order=gift_model.sort_order,
            created_at=gift_model.created_at,
            updated_at=gift_model.updated_at,
            meta=meta  # 保存完整的meta数据以支持翻译
        )
    
    async def get_gift_by_id(self, gift_id: int) -> Optional[GiftInfo]:
        """
        根据ID获取礼物信息
        
        Args:
            gift_id: 礼物ID
            
        Returns:
            Optional[GiftInfo]: 礼物信息，如果不存在则返回None
        """
        if not self._is_loaded:
            logger.warning("缓存未加载，尝试重新加载...")
            await self._load_gifts_with_retry()
        
        return self._gifts_by_id.get(gift_id)
    
    async def get_gift_by_name(self, name: str) -> Optional[GiftInfo]:
        """
        根据名称获取礼物信息（不区分大小写）
        
        Args:
            name: 礼物名称
            
        Returns:
            Optional[GiftInfo]: 礼物信息，如果不存在则返回None
        """
        if not self._is_loaded:
            logger.warning("缓存未加载，尝试重新加载...")
            await self._load_gifts_with_retry()
        
        return self._gifts_by_name.get(name.lower())
    
    async def get_all_gifts(self) -> List[GiftInfo]:
        """
        获取所有礼物列表（按sort_order排序）
        
        Returns:
            List[GiftInfo]: 礼物列表
        """
        if not self._is_loaded:
            logger.warning("缓存未加载，尝试重新加载...")
            await self._load_gifts_with_retry()
        
        return self._all_gifts.copy()  # 返回副本防止外部修改
    
    async def get_gifts_by_category(self, category: str) -> List[GiftInfo]:
        """
        根据分类获取礼物列表
        
        Args:
            category: 礼物分类
            
        Returns:
            List[GiftInfo]: 该分类的礼物列表
        """
        all_gifts = await self.get_all_gifts()
        return [gift for gift in all_gifts if gift.category == category and gift.is_active]
    
    async def get_gifts_by_price_range(
        self, 
        min_price: Optional[int] = None, 
        max_price: Optional[int] = None
    ) -> List[GiftInfo]:
        """
        根据价格范围获取礼物列表
        
        Args:
            min_price: 最小价格（USD分），大于等于此价格
            max_price: 最大价格（USD分），小于等于此价格
            
        Returns:
            List[GiftInfo]: 符合价格范围的礼物列表，按sort_order排序
        """
        all_gifts = await self.get_all_gifts()
        
        # 过滤条件：只返回激活的礼物
        filtered_gifts = [gift for gift in all_gifts if gift.is_active]
        
        # 应用价格过滤
        if min_price is not None:
            filtered_gifts = [gift for gift in filtered_gifts if gift.price >= min_price]
        
        if max_price is not None:
            filtered_gifts = [gift for gift in filtered_gifts if gift.price <= max_price]
        
        logger.debug(f"价格过滤结果: min_price={min_price}, max_price={max_price}, 筛选出 {len(filtered_gifts)} 个礼物")
        
        return filtered_gifts
    
    async def refresh_cache(self) -> bool:
        """
        刷新缓存（从数据库重新加载）
        
        Returns:
            bool: 是否刷新成功
        """
        logger.info("开始刷新礼物缓存...")
        success = await self._load_gifts_with_retry()
        
        if success:
            logger.info("礼物缓存刷新成功")
        else:
            logger.error("礼物缓存刷新失败")
            
        return success
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            "is_loaded": self._is_loaded,
            "gift_count": self.gift_count,
            "last_updated": self._last_updated.isoformat() if self._last_updated else None,
            "load_attempts": self._load_attempts,
            "categories": list(set(gift.category for gift in self._all_gifts)),
            "active_gifts": len([g for g in self._all_gifts if g.is_active])
        }


# 全局缓存实例
gift_cache = GiftCacheManager()


async def get_gift_cache() -> GiftCacheManager:
    """获取礼物缓存管理器实例"""
    return gift_cache 