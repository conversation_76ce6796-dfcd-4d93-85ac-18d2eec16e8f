"""
Agora服务依赖注入模块

提供FastAPI依赖注入系统中所需的依赖项
"""

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from .repos import AgoraRepository
from .service import AgoraService


async def get_agora_repository(
    session: AsyncSession = Depends(get_session)
) -> AgoraRepository:
    """
    获取 AgoraRepository 实例
    
    Args:
        session: 数据库会话
        
    Returns:
        AgoraRepository: Repository实例
    """
    return AgoraRepository(session)


async def get_agora_service(
    repo: AgoraRepository = Depends(get_agora_repository)
) -> AgoraService:
    """
    获取 AgoraService 实例
    
    Args:
        repo: AgoraRepository实例
        
    Returns:
        AgoraService: Service实例
    """
    service = AgoraService(repo)
    await service.init_redis()
    return service 