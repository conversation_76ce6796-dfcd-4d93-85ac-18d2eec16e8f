from typing import Dict, Any, Optional
import asyncio
from datetime import datetime, timezone
from redis.asyncio import Redis
from sqlalchemy.exc import IntegrityError

from .schemas import WebhookEvent
from .repos import AgoraRepository
from .logger import logger
from .constants import Channel<PERSON>tatus, UserRole, EventType
from .token_manager import token_manager
from src.database.models.Agora import utc_now_naive
from .redis_messenger import redis_messenger


class AgoraWebhookHandler:
    """Agora Webhook 事件处理器"""
    
    def __init__(self, repo: AgoraRepository, redis_client: Optional[Redis] = None):
        self._repo = repo
        self.redis_client = redis_client
        # 去抖窗口毫秒
        self._audience_publish_window_ms = 300

    def _debounce_key(self, channel_name: str) -> str:
        return f"audience_count:debounce:{channel_name}"

    async def _schedule_audience_count_publish(self, channel_name: str) -> None:
        """在去抖窗口内聚合多次变更，仅发布一次 audience_count。"""
        if not self.redis_client:
            return
        try:
            # 使用 SET NX PX 实现去抖锁
            ok = await self.redis_client.set(
                self._debounce_key(channel_name),
                "1",
                nx=True,
                px=self._audience_publish_window_ms,
            )
            if not ok:
                return

            async def _publish_later():
                try:
                    await asyncio.sleep(self._audience_publish_window_ms / 1000.0)
                    channel_key = f"channel:{channel_name}"
                    cur_raw = await self.redis_client.hget(channel_key, "current_audience")
                    cur = int(cur_raw) if cur_raw is not None else 0
                    await redis_messenger.publish_audience_count_update(channel_name, cur)
                except Exception as e:
                    logger.error(f"延迟发布观众人数失败: {str(e)}")

            asyncio.create_task(_publish_later())
        except Exception as e:
            logger.error(f"安排去抖发布失败: {str(e)}")
    
    async def _invalidate_channel_cache(self, channel_name: str) -> None:
        """
        使频道的Redis缓存失效
        
        Args:
            channel_name: 频道名称
        """
        if not self.redis_client:
            return
            
        try:
            channel_key = f"channel:{channel_name}"
            await self.redis_client.delete(channel_key)
            logger.info(f"Invalidated Redis cache for channel {channel_name}")
        except Exception as e:
            logger.error(f"Failed to invalidate channel cache: {str(e)}")
    
    async def _update_channel_cache(self, channel_name: str, channel_data: Dict[str, Any], expire_seconds: int = 86400) -> None:
        """
        更新频道的Redis缓存信息
        
        Args:
            channel_name: 频道名称
            channel_data: 频道数据字典
            expire_seconds: 过期时间（秒），默认24小时
        """
        if not self.redis_client:
            return
            
        try:
            channel_key = f"channel:{channel_name}"
            await self.redis_client.hset(channel_key, mapping=channel_data)
            await self.redis_client.expire(channel_key, expire_seconds)
            logger.info(f"Updated Redis cache for channel {channel_name}")
        except Exception as e:
            logger.error(f"Failed to update channel cache: {str(e)}")
    
    def _build_channel_cache_data(self, channel, status: str = None, sid: str = None, 
                                 started_at: datetime = None, ended_at: datetime = None) -> Dict[str, Any]:
        """
        构建频道缓存数据字典
        
        Args:
            channel: LiveChannel对象
            status: 频道状态（可选，如果不提供则使用channel.status）
            sid: 会话ID（可选）
            started_at: 开始时间（可选）
            ended_at: 结束时间（可选）
            
        Returns:
            Dict[str, Any]: 频道缓存数据字典
        """
        return {
            "id": str(channel.id),
            "channel_name": channel.channel_name,
            "status": status or channel.status,
            "user_id": channel.user_id,
            "broadcaster_uid": str(channel.broadcaster_uid),
            "token_address": channel.token_address or "",
            "created_at": channel.created_at.isoformat() if channel.created_at else "",
            "sid": sid or channel.sid or "",
            "started_at": started_at.isoformat() if started_at else (channel.started_at.isoformat() if channel.started_at else ""),
            "ended_at": ended_at.isoformat() if ended_at else (channel.ended_at.isoformat() if channel.ended_at else "")
        }
    
    async def handle_event(self, event: WebhookEvent) -> bool:
        """
        处理 Webhook 事件的主入口
        
        Args:
            event: Webhook 事件
            
        Returns:
            bool: 处理结果
        """
        try:
            logger.debug(f"处理Webhook事件: noticeId={event.noticeId}, sid={event.sid}, , eventType={event.eventType}, payload={event.payload}")
            
            if event.eventType is None:
                return await self._handle_generic_event(event)
            
            # 根据事件类型分发处理
            handler_map = {
                EventType.CHANNEL_CREATE: self._handle_channel_create,
                EventType.CHANNEL_DESTROY: self._handle_channel_destroy,
                EventType.BROADCASTER_JOIN: self._handle_broadcaster_join,
                EventType.BROADCASTER_LEAVE: self._handle_broadcaster_leave,
                EventType.AUDIENCE_JOIN: self._handle_audience_join,
                EventType.AUDIENCE_LEAVE: self._handle_audience_leave
            }
            
            handler = handler_map.get(event.eventType)
            if handler:
                return await handler(event)
            else:
                logger.warning(f"未知的事件类型: {event.eventType}")
                return await self._handle_unknown_event(event)
                
        except Exception as e:
            logger.error(f"处理Webhook事件时出错: {str(e)}", exc_info=True)
            return False
    
    async def _handle_channel_create(self, event: WebhookEvent) -> bool:
        """
        处理频道创建事件 (eventType: 101)
        
        Payload字段:
        - channelName: String - 频道名称
        - ts: Number - Unix时间戳(秒)
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        ts = payload.get("ts")
        
        if not channel_name:
            logger.error("频道创建事件缺少channelName字段")
            return False
        
        logger.info(f"频道已创建: channel={channel_name}, ts={ts}")
        
        try:
            # 查找对应的频道并更新状态
            channel = await self._repo.channels.get_channel_by_name(channel_name, ChannelStatus.CREATED)
            if channel:
                # 将ts转换为datetime
                started_at = None
                if ts:
                    started_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
                
                # 更新为直播状态，同时更新sid和started_at（处理sid唯一冲突）
                try:
                    await self._repo.channels.update_channel_status(
                        channel.id, ChannelStatus.LIVE, event.sid, started_at
                    )
                    logger.info(
                        f"频道 {channel_name} 状态已更新为 LIVE，sid={event.sid}，started_at={started_at}"
                    )
                except IntegrityError as e:
                    # sid 唯一约束冲突，可能重复事件或该 sid 已绑定其它记录
                    logger.warning(
                        f"设置频道 sid 冲突，改为仅更新状态与时间: channel={channel_name}, sid={event.sid}, error={str(e)}"
                    )
                    await self._repo.channels.update_channel_status(
                        channel.id, ChannelStatus.LIVE, None, started_at
                    )
                
                # 更新Redis缓存中的频道信息
                channel_data = self._build_channel_cache_data(
                    channel, 
                    status=ChannelStatus.LIVE, 
                    sid=event.sid, 
                    started_at=started_at
                )
                await self._update_channel_cache(channel_name, channel_data)
                # 回写Redis中的峰值观众数到统计，并删除Redis人数字段
                try:
                    if self.redis_client:
                        channel_key = f"channel:{channel_name}"
                        peak_val_raw = await self.redis_client.hget(channel_key, "peak_audience")
                        peak_val = int(peak_val_raw) if peak_val_raw is not None else 0

                        try:
                            updated_channel = await self._repo.channels.get_channel_by_id(channel.id)
                            if updated_channel and hasattr(self._repo, "stats"):
                                stats = await self._repo.stats.create_or_update_stats(updated_channel)
                                if peak_val > stats.peak_audience:
                                    stats.peak_audience = peak_val
                                await self._repo.db.flush()
                        except Exception as se:
                            logger.error(f"更新统计峰值观众失败: {str(se)}")

                        # 删除Redis中的人数字段
                        await self.redis_client.hdel(channel_key, "current_audience", "peak_audience")
                except Exception as re:
                    logger.error(f"回写峰值观众或清理Redis失败: {str(re)}")
                # 初始化Redis中的观众计数（当前/峰值）
                try:
                    if self.redis_client:
                        channel_key = f"channel:{channel_name}"
                        await self.redis_client.hset(channel_key, mapping={
                            "current_audience": 0,
                            "peak_audience": 0,
                        })
                except Exception as e:
                    logger.error(f"初始化频道观众计数失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"处理频道创建事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_channel_destroy(self, event: WebhookEvent) -> bool:
        """
        处理频道销毁事件 (eventType: 102)
        
        Payload字段:
        - channelName: String - 频道名称
        - ts: Number - Unix时间戳(秒)
        - lastUid: Number - 最后离开的用户ID
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        ts = payload.get("ts")
        last_uid = payload.get("lastUid")
        
        if not channel_name:
            logger.error("频道销毁事件缺少channelName字段")
            return False
        
        logger.info(f"频道已销毁: channel={channel_name}, lastUid={last_uid}, ts={ts}")
        
        try:
            # 查找对应的频道并更新状态
            channel = await self._repo.channels.get_channel_by_name(channel_name)
            if channel:
                # 将ts转换为datetime
                ended_at = None
                if ts:
                    ended_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
                
                # 更新为结束状态，同时更新ended_at、duration和观众数
                await self._repo.channels.update_channel_status(channel.id, ChannelStatus.ENDED, ended_at=ended_at)
                
                logger.info(f"频道 {channel_name} 已销毁，状态更新为 ENDED，ended_at={ended_at}")
                
                # 创建或更新频道统计数据
                try:
                    # 获取最新的频道信息（包含duration等更新后的数据）
                    updated_channel = await self._repo.channels.get_channel_by_id(channel.id)
                    if updated_channel:
                        # 创建或更新统计记录
                        stats = await self._repo.stats.create_or_update_stats(updated_channel)
                        
                        # 获取并更新礼物统计
                        total_gift_value = await self._repo.gifts.get_channel_total_gift_value(channel_name)
                        if total_gift_value > 0:
                            stats.total_gift_value = total_gift_value
                        
                        # 获取唯一打赏者数量
                        unique_gifters = await self._repo.gifts.get_unique_gifters_count(channel_name)
                        stats.unique_gifters = unique_gifters
                        
                        # 获取累计观众数
                        total_audience = await self._repo.audiences.get_total_unique_audience_count(channel.id)
                        stats.total_audience = total_audience
                        
                        # 更新峰值观众数（如果当前观众数更高）
                        if channel.current_audience > stats.peak_audience:
                            stats.peak_audience = channel.current_audience
                        
                        await self._repo.db.flush()
                        logger.info(f"频道 {channel_name} 统计数据已保存: 时长={stats.live_duration}秒, 礼物总值=${stats.total_gift_value/100:.2f}, 峰值观众={stats.peak_audience}")
                except Exception as e:
                    logger.error(f"保存频道统计数据失败: {str(e)}")
                
                # 更新Redis缓存中的频道信息，保持缓存数据的最新状态
                channel_data = self._build_channel_cache_data(
                    channel, 
                    status=ChannelStatus.ENDED, 
                    ended_at=ended_at
                )
                await self._update_channel_cache(channel_name, channel_data)
            else:
                logger.warning(f"Can't find channel={channel_name} to destroy")
            
        except Exception as e:
            logger.error(f"处理频道销毁事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_broadcaster_join(self, event: WebhookEvent) -> bool:
        """
        处理主播加入频道事件 (eventType: 103)
        
        Payload字段:
        - channelName: String - 频道名称
        - uid: Number - 主播的用户ID
        - platform: Number - 平台类型
        - clientType: Number - 客户端类型(可选)
        - clientSeq: Number - 客户端序列号
        - ts: Number - Unix时间戳(秒)
        - account: String - 用户账号
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        uid = payload.get("uid")
        account = payload.get("account")
        platform = payload.get("platform")
        ts = payload.get("ts")
        
        if not channel_name or uid is None:
            logger.error("主播加入事件缺少必要字段")
            return False
        
        logger.info(f"主播加入频道: channel={channel_name}, uid={uid}, account={account}, platform={platform}")
        
        try:
            # 通过 UID 获取真实的用户ID
            user_id = await token_manager.get_user_id_by_uid(uid, account)
            if not user_id:
                logger.warning(f"无法通过 UID {uid} 获取用户ID (account={account})")
                # 对于无法识别的用户，暂时跳过处理而不是返回失败
                return True
            
            # 通过频道名称获取频道信息以获取channel_id
            channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
            channel_id = channel.id if channel else None
            
            # 将ts转换为datetime
            joined_at = None
            if ts:
                joined_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
            
            # 记录主播加入
            await self._repo.audiences.add_audience(
                sid=event.sid,
                channel_name=channel_name,
                user_id=user_id,
                platform=platform,
                role=UserRole.HOST,
                client_seq=payload.get("clientSeq", 0),
                joined_at=joined_at,
                channel_id=channel_id
            )
            logger.debug(f"主播已添加到观众记录: channel={channel_name}, user_id={user_id}, uid={uid}, platform={platform}, channel_id={channel_id}")
            
        except Exception as e:
            logger.error(f"处理主播加入事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_broadcaster_leave(self, event: WebhookEvent) -> bool:
        """
        处理主播离开频道事件 (eventType: 104)
        
        Payload字段:
        - channelName: String - 频道名称  
        - uid: Number - 主播的用户ID
        - platform: Number - 平台类型
        - clientType: Number - 客户端类型(可选)
        - clientSeq: Number - 客户端序列号
        - reason: Number - 离开原因
        - ts: Number - Unix时间戳(秒)
        - duration: Number - 在频道中的时长
        - account: String - 用户账号
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        uid = payload.get("uid")
        account = payload.get("account")
        reason = payload.get("reason", 0)
        duration = payload.get("duration")
        ts = payload.get("ts")
        
        if not channel_name or uid is None:
            logger.error("主播离开事件缺少必要字段")
            return False
        
        logger.info(f"主播离开频道: channel={channel_name}, uid={uid}, account={account}, reason={reason}, duration={duration}")
        
        try:
            # 通过 UID 获取真实的用户ID
            user_id = await token_manager.get_user_id_by_uid(uid, account)
            if not user_id:
                logger.warning(f"无法通过 UID {uid} 获取用户ID (account={account})")
                # 对于无法识别的用户，暂时跳过处理而不是返回失败
                return True
            
            # 将ts转换为datetime
            left_at = None
            if ts:
                left_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
            
            # 更新观众记录：设置离开时间、离开原因和持续时间
            success = await self._repo.audiences.remove_audience_by_sid(event.sid, user_id, reason, left_at, duration)
            if success:
                logger.info(f"主播离开记录已更新: sid={event.sid}, channel={channel_name}, user_id={user_id}, uid={uid}, reason={reason}, duration={duration}, left_at={left_at}")
            else:
                logger.warning(f"未找到主播的观众记录: sid={event.sid}, user_id={user_id}")
            
        except Exception as e:
            logger.error(f"处理主播离开事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_audience_join(self, event: WebhookEvent) -> bool:
        """
        处理观众加入频道事件 (eventType: 105)
        
        Payload字段:
        - channelName: String - 频道名称
        - uid: Number - 观众的用户ID
        - platform: Number - 平台类型
        - clientType: Number - 客户端类型(可选)
        - clientSeq: Number - 客户端序列号
        - ts: Number - Unix时间戳(秒)
        - account: String - 用户账号
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        uid = payload.get("uid")
        account = payload.get("account")
        platform = payload.get("platform")
        ts = payload.get("ts")
        
        if not channel_name or uid is None:
            logger.error("观众加入事件缺少必要字段")
            return False
        
        logger.info(f"观众加入频道: channel={channel_name}, uid={uid}, account={account}, platform={platform}")
        
        try:
            # 通过 UID 获取真实的用户ID
            user_id = await token_manager.get_user_id_by_uid(uid, account)
            if not user_id:
                logger.warning(f"无法通过 UID {uid} 获取用户ID (account={account})")
                # 对于无法识别的用户，暂时跳过处理而不是返回失败
                return True
            
            # 通过频道名称获取频道信息以获取channel_id
            channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
            channel_id = channel.id if channel else None
            
            # 将ts转换为datetime
            joined_at = None
            if ts:
                joined_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
            
            # 记录观众加入
            await self._repo.audiences.add_audience(
                sid=event.sid,
                channel_name=channel_name,
                user_id=user_id,
                platform=platform,
                role=UserRole.AUDIENCE,
                client_seq=payload.get("clientSeq", 0),
                joined_at=joined_at,
                channel_id=channel_id
            )
            logger.info(f"观众已添加到观众记录: channel={channel_name}, user_id={user_id}, uid={uid}, platform={platform}, channel_id={channel_id}")
            # 更新Redis中的当前/峰值观众并发布WS消息（去抖）
            try:
                if self.redis_client:
                    channel_key = f"channel:{channel_name}"
                    new_current = await self.redis_client.hincrby(channel_key, "current_audience", 1)
                    # 峰值更新
                    try:
                        peak_raw = await self.redis_client.hget(channel_key, "peak_audience")
                        peak_val = int(peak_raw) if peak_raw is not None else 0
                    except Exception:
                        peak_val = 0
                    if new_current > peak_val:
                        await self.redis_client.hset(channel_key, "peak_audience", new_current)

                    # 去抖发布 audience_count
                    await self._schedule_audience_count_publish(channel_name)
            except Exception as e:
                logger.error(f"更新Redis观众数或发布消息失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"处理观众加入事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_audience_leave(self, event: WebhookEvent) -> bool:
        """
        处理观众离开频道事件 (eventType: 106)
        
        Payload字段:
        - channelName: String - 频道名称
        - uid: Number - 观众的用户ID
        - platform: Number - 平台类型
        - clientType: Number - 客户端类型(可选)
        - clientSeq: Number - 客户端序列号
        - reason: Number - 离开原因
        - ts: Number - Unix时间戳(秒)
        - duration: Number - 在频道中的时长
        - account: String - 用户账号
        """
        payload = event.payload or {}
        channel_name = payload.get("channelName")
        uid = payload.get("uid")
        account = payload.get("account")
        reason = payload.get("reason", 0)
        duration = payload.get("duration")
        ts = payload.get("ts")
        
        if not channel_name or uid is None:
            logger.error("观众离开事件缺少必要字段")
            return False
        
        logger.info(f"观众离开频道: channel={channel_name}, uid={uid}, account={account}, reason={reason}, duration={duration}")
        
        try:
            # 通过 UID 获取真实的用户ID
            user_id = await token_manager.get_user_id_by_uid(uid, account)
            if not user_id:
                logger.warning(f"无法通过 UID {uid} 获取用户ID (account={account})")
                # 对于无法识别的用户，暂时跳过处理而不是返回失败
                return True
            
            # 将ts转换为datetime
            left_at = None
            if ts:
                left_at = datetime.fromtimestamp(ts, tz=timezone.utc).replace(tzinfo=None)
            
            # 更新观众记录：设置离开时间、离开原因和持续时间
            success = await self._repo.audiences.remove_audience_by_sid(event.sid, user_id, reason, left_at, duration)
            if success:
                logger.info(f"观众离开记录已更新: sid={event.sid}, channel={channel_name}, user_id={user_id}, uid={uid}, reason={reason}, duration={duration}, left_at={left_at}")
            else:
                logger.warning(f"未找到观众记录: sid={event.sid}, user_id={user_id}")
            # 更新Redis中的当前观众并发布WS消息（非负保护 + 去抖）
            try:
                if self.redis_client:
                    channel_key = f"channel:{channel_name}"
                    new_current = await self.redis_client.hincrby(channel_key, "current_audience", -1)
                    if new_current < 0:
                        await self.redis_client.hset(channel_key, "current_audience", 0)
                        new_current = 0
                    await self._schedule_audience_count_publish(channel_name)
            except Exception as e:
                logger.error(f"减少Redis观众数或发布消息失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"处理观众离开事件时出错: {str(e)}")
            return False
            
        return True
    
    async def _handle_unknown_event(self, event: WebhookEvent) -> bool:
        """处理未知事件类型"""
        logger.warning(f"收到未知事件类型: eventType={event.eventType}, payload={event.payload}")
        return True
    
    async def _handle_generic_event(self, event: WebhookEvent) -> bool:
        """处理没有eventType的通用事件"""
        logger.info(f"处理通用事件: noticeId={event.noticeId}, payload={event.payload}")
        return True 