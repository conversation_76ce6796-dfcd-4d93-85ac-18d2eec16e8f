from fastapi import FastAPI, HTTPException, Request, Depends, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import json
import logging
import os
import asyncio
from websockets.exceptions import ConnectionClosedOK, ConnectionClosedError

from src.infra.logger import get_logger
from src.infra.app import create_app
from src.agora.settings import settings
from src.agora.token_manager import token_manager
from src.agora.task_client import agora_task_client
from src.agora.schemas import (
    TokenRequest,
    TokenResponse,
    RTMTokenRequest,
    RTMTokenResponse,
    ChannelCreateRequest,
    ChannelInfo,
    ChannelJoinRequest,
    ChannelJoinResponse,
    ChannelLeaveRequest,
    ChannelStatsResponse,
    WebhookEvent,
    ErrorResponse,
    LiveChannelsResponse,
    ChannelJoinSimpleResponse,
    ChannelAudienceResponse,
    ChannelAudienceInfo,
    BuyGiftRequest,
    BuyGiftResponse,
    ChannelGiftsResponse,
    ChannelGiftsRequest,
    RTMChannelMessageRequest,
    RTMChannelMessageResponse,
    GiftListResponse,
    GiftListItem,
    RTCAndRTMTokenRequest,
    RTCAndRTMTokenResponse,
    ChannelTokenResponse,
    TokenChannelsResponse,
    ChannelGiftRankingResponse,
    ChannelGiftRankingRequest,
    AnonymousTokenBatchRequest,
    AnonymousTokenBatchResponse,
    AnonymousTokenItem,
    GiftConvertRequest,
    GiftConvertResponse,
    RedEnvelopeInfo,
    ChannelRedEnvelopesResponse,
    ClaimRedEnvelopeRequest,
    ClaimRedEnvelopeResponse,
    KickUserRequest,
    KickUserResponse,
)
from src.agora.dependencies import get_agora_service
from src.agora.service import AgoraService
from src.agora.repos import AgoraRepository
from src.auth.dependecies import current_user
from src.database.models.User import User
from src.database.session import AsyncSession, get_session
from src.agora.cloudflare_queue_consumer import CloudflareQueueConsumer
from src.agora.memecoin_client import memecoin_client
from src.agora.gift_cache import gift_cache

# 全局变量存储Consumer和后台任务
_cloudflare_consumer: Optional[CloudflareQueueConsumer] = None
_consumer_task: Optional[asyncio.Task] = None
_consumer_enabled: bool = settings.AGORA_CONSUMER_ENABLED

# 统一请求耗时中间件与服务日志（供 infra.create_app 使用）
logger = get_logger("agora", level="INFO", file_path="latest.log")


async def _startup_hook(app: FastAPI) -> None:
    global _cloudflare_consumer, _consumer_task
    logger.info("启动Agora服务...")
    logger.info(f"Cloudflare Consumer启用状态: {_consumer_enabled}")

    # 初始化礼物缓存（失败则降级，返回 False 仅告警，不抛出）
    cache_success = await gift_cache.initialize()
    if not cache_success:
        logger.warning("礼物缓存初始化失败，将使用数据库查询作为后备方案")

    if _consumer_enabled:
        _cloudflare_consumer = CloudflareQueueConsumer()
        # 初始化Redis连接
        await _cloudflare_consumer.init_redis()
        # 启动消费者任务
        _consumer_task = asyncio.create_task(_cloudflare_consumer.start_consuming())
        logger.info("Cloudflare Queue Consumer后台任务已启动")
    else:
        logger.info("Cloudflare Consumer已被禁用，跳过启动")


async def _shutdown_hook(app: FastAPI) -> None:
    global _cloudflare_consumer, _consumer_task
    logger.info("关闭Agora服务...")
    # 关闭Cloudflare Consumer
    if _consumer_task and not _consumer_task.done():
        logger.info("正在关闭Cloudflare Consumer...")
        _consumer_task.cancel()
        await asyncio.wait_for(_consumer_task, timeout=10.0)
    if _cloudflare_consumer:
        await _cloudflare_consumer.close()
        logger.info("Cloudflare Consumer已关闭")
        _cloudflare_consumer = None


app = create_app(
    title="Agora Live Streaming Service",
    description="Agora 直播服务 API - MVP版本",
    version="1.0.0",
    request_logger=logger,
    cors_allow_origin_regex=".*",
    startup_hook=_startup_hook,
    shutdown_hook=_shutdown_hook,
)


 


@app.get("/health")
async def health_check():
    """健康检查端点"""
    global _consumer_task, _consumer_enabled

    # 检查缓存健康状态
    cache_healthy = gift_cache.is_loaded and gift_cache.gift_count > 0

    health_status = {
        "status": "healthy",
        "service": "agora",
        "cloudflare_consumer": {
            "enabled": _consumer_enabled,
            "running": (
                _consumer_task is not None and not _consumer_task.done()
                if _consumer_task
                else False
            ),
        },
        "gift_cache": {
            "healthy": cache_healthy,
            "loaded": gift_cache.is_loaded,
            "gift_count": gift_cache.gift_count,
            "last_updated": (
                gift_cache.last_updated.isoformat() if gift_cache.last_updated else None
            ),
        },
    }

    return health_status


@app.post("/token", response_model=TokenResponse, tags=["Token Management"])
async def generate_rtc_token(
    token_request: TokenRequest,
    user: User = Depends(current_user),
):
    """
    生成 RTC Token
    """
    try:
        if not await token_manager.validate_token_rate_limit(user.id):
            raise HTTPException(status_code=429, detail="Too many token requests")

        token_response = await token_manager.generate_rtc_token_with_uid_management(
            channel_name=token_request.channel_id,
            user_id=user.id,
            role=token_request.role,
            expiration_time=token_request.expiration_time,
        )

        return token_response
    except Exception as e:
        logger.error(f"Failed to generate RTC token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/rtm_token", response_model=RTMTokenResponse, tags=["Token Management"])
async def generate_rtm_token(
    rtm_request: RTMTokenRequest, user: User = Depends(current_user)
):
    """
    生成 RTM Token
    """
    try:
        if not await token_manager.validate_token_rate_limit(user.id):
            raise HTTPException(status_code=429, detail="Too many token requests")

        token_response = await token_manager.generate_rtm_token_enhanced(
            user_id=user.id, expiration_time=rtm_request.expiration_time
        )
        return token_response
    except Exception as e:
        logger.error(f"Failed to generate RTM token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post(
    "/rtc_rtm_token", response_model=RTCAndRTMTokenResponse, tags=["Token Management"]
)
async def generate_combined_token(
    combined_request: RTCAndRTMTokenRequest, user: User = Depends(current_user)
):
    """
    生成同时具有RTC和RTM权限的合并Token

    根据Agora官方文档实现，此接口生成一个包含RTC和RTM权限的单一token，可用于：
    - 加入RTC频道进行音视频通话
    - 使用RTM功能发送消息

    参数说明：
    - channel_id: 频道名称，字符串格式，长度小于64字节
    - role: 用户角色，host/broadcaster为主播，audience为观众（默认）
    - expiration_time: Token和权限有效期（秒），从当前时间开始计算，默认使用配置值
    """
    try:
        if not await token_manager.validate_token_rate_limit(user.id):
            raise HTTPException(status_code=429, detail="Too many token requests")

        token_response = await token_manager.generate_rtc_rtm_combined_token(
            channel_name=combined_request.channel_id,
            user_id=user.id,
            role=combined_request.role,
            expiration_time=combined_request.expiration_time,
        )
        return token_response
    except Exception as e:
        logger.error(f"Failed to generate combined RTC+RTM token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post(
    "/rtc_token_batch",
    response_model=AnonymousTokenBatchResponse,
    tags=["Token Management"],
)
async def generate_anonymous_token_batch(
    batch_request: AnonymousTokenBatchRequest,
):
    """
    批量生成匿名RTC Token（用于压力测试）

    此接口无需认证，专门用于前端压力测试。
    会为每个token生成一个唯一的匿名用户ID和对应的UID。

    参数说明：
    - channel_id: 频道名称，字符串格式，长度小于64字节
    - role: 用户角色，host/broadcaster为主播，audience为观众（默认）
    - expiration_time: Token和权限有效期（秒），从当前时间开始计算，默认使用配置值
    - count: 生成Token数量，范围1-1000，默认为1

    返回说明：
    - 返回包含多个token的列表，每个token都有唯一的匿名用户ID和UID
    - 所有token具有相同的过期时间和角色
    - 适用于压力测试和负载测试场景
    - 生成的是纯RTC Token，仅用于音视频通话，不包含RTM消息功能
    """
    try:
        # 调用token manager的批量生成方法
        tokens = await token_manager.generate_anonymous_token_batch(
            channel_name=batch_request.channel_id,
            count=batch_request.count,
            role=batch_request.role,
            expiration_time=batch_request.expiration_time,
        )

        # 构造响应
        token_items = []
        for token_data in tokens:
            token_item = AnonymousTokenItem(
                token=token_data["token"],
                app_id=token_data["app_id"],
                channel_id=token_data["channel_id"],
                uid=token_data["uid"],
                anonymous_user_id=token_data["anonymous_user_id"],
                role=token_data["role"],
                expires_at=token_data["expires_at"],
            )
            token_items.append(token_item)

        response = AnonymousTokenBatchResponse(
            total_tokens=len(token_items),
            channel_id=batch_request.channel_id,
            role=batch_request.role,
            expires_at=token_items[0].expires_at if token_items else None,
            tokens=token_items,
        )

        logger.info(f"Generated {len(token_items)} anonymous RTC tokens for channel {batch_request.channel_id}")
        return response

    except ValueError as e:
        logger.error(f"Invalid parameters for batch token generation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to generate anonymous token batch: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post(
    "/rtc_rtm_token_batch",
    response_model=AnonymousTokenBatchResponse,
    tags=["Token Management"],
)
async def generate_anonymous_rtc_rtm_token_batch(
    batch_request: AnonymousTokenBatchRequest,
):
    """
    批量生成匿名RTC+RTM合并Token（用于压力测试）

    此接口无需认证，专门用于前端压力测试。
    会为每个token生成一个唯一的匿名用户ID和对应的UID。

    参数说明：
    - channel_id: 频道名称，字符串格式，长度小于64字节
    - role: 用户角色，host/broadcaster为主播，audience为观众（默认）
    - expiration_time: Token和权限有效期（秒），从当前时间开始计算，默认使用配置值
    - count: 生成Token数量，范围1-1000，默认为1

    返回说明：
    - 返回包含多个token的列表，每个token都有唯一的匿名用户ID和UID
    - 所有token具有相同的过期时间和角色
    - 适用于压力测试和负载测试场景
    - 生成的是RTC+RTM合并Token，可用于音视频通话和实时消息
    """
    try:
        # 调用token manager的批量生成RTC+RTM合并token方法
        tokens = await token_manager.generate_anonymous_rtc_rtm_token_batch(
            channel_name=batch_request.channel_id,
            count=batch_request.count,
            role=batch_request.role,
            expiration_time=batch_request.expiration_time,
        )

        # 构造响应
        token_items = []
        for token_data in tokens:
            token_item = AnonymousTokenItem(
                token=token_data["token"],
                app_id=token_data["app_id"],
                channel_id=token_data["channel_id"],
                uid=token_data["uid"],
                anonymous_user_id=token_data["anonymous_user_id"],
                role=token_data["role"],
                expires_at=token_data["expires_at"],
            )
            token_items.append(token_item)

        response = AnonymousTokenBatchResponse(
            total_tokens=len(token_items),
            channel_id=batch_request.channel_id,
            role=batch_request.role,
            expires_at=token_items[0].expires_at if token_items else None,
            tokens=token_items,
        )

        logger.info(f"Generated {len(token_items)} anonymous RTC+RTM tokens for channel {batch_request.channel_id}")
        return response

    except ValueError as e:
        logger.error(f"Invalid parameters for batch RTC+RTM token generation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to generate anonymous RTC+RTM token batch: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/token/refresh", tags=["Token Management"])
async def refresh_token(
    channel_id: str,
    token_type: str = "rtc",
    user: User = Depends(current_user),
):
    """
    刷新即将过期的 Token

    支持的token类型：
    - rtc: RTC Token
    - rtm: RTM Token
    - rtc_rtm: 合并Token (RTC+RTM)
    """
    try:
        uid = token_manager.get_uid_by_user_id(user.id)
        refreshed_token = await token_manager.refresh_token(
            channel_id, uid, token_type, user_id_hint=user.id
        )

        if not refreshed_token:
            raise HTTPException(
                status_code=404, detail="Token not found or not eligible for refresh"
            )

        return refreshed_token
    except Exception as e:
        logger.error(f"Failed to refresh token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/token/revoke", tags=["Token Management"])
async def revoke_token(
    channel_id: str,
    token_type: str = "rtc",
    user: User = Depends(current_user),
):
    """
    撤销 Token

    支持的token类型：
    - rtc: RTC Token
    - rtm: RTM Token
    - rtc_rtm: 合并Token (RTC+RTM)
    """
    try:
        uid = token_manager.get_uid_by_user_id(user.id)
        success = await token_manager.revoke_token(channel_id, uid, token_type)
        return {"message": "Token revoked successfully", "success": success}
    except Exception as e:
        logger.error(f"Failed to revoke token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# UID 相关端点
@app.get("/get_uid", tags=["UID Management"])
async def get_uid_from_user_id(user_id: str):
    """
    通过 user_id 生成 UID
    """
    try:
        uid = token_manager.get_uid_by_user_id(user_id)
        return {"success": True, "user_id": user_id, "uid": uid}
    except Exception as e:
        logger.error(f"Failed to generate UID from user_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/get_user_id", tags=["UID Management"])
async def get_user_id_from_uid(uid: int, user_id_hint: Optional[str] = None):
    """
    通过 UID 获取 user_id（从 Redis 中获取，可选择提供 user_id_hint 进行验证）
    """
    try:
        validated_user_id = await token_manager.get_user_id_by_uid(uid, user_id_hint)
        if validated_user_id:
            return {"success": True, "user_id": validated_user_id, "uid": uid}
        else:
            return {
                "success": False,
                "uid": uid,
                "message": "User ID not found for the provided UID",
            }
    except Exception as e:
        logger.error(f"Failed to get user_id from UID: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 直播间管理端点
@app.post("/channels", response_model=ChannelInfo, tags=["Channel Management"])
async def create_channel(
    request: ChannelCreateRequest,
    user: User = Depends(current_user),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    创建直播间
    """
    try:
        # 从认证用户获取 user_id
        user_id = user.id
        # 通过 token_manager 生成 UID
        uid = token_manager.get_uid_by_user_id(user_id)

        channel_info = await agora_service.create_channel(request, user_id, uid)
        return channel_info
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create channel: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/channels/{channel_id}", response_model=ChannelInfo, tags=["Channel Management"]
)
async def get_channel(
    channel_id: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取直播间信息
    """
    try:
        channel_info = await agora_service.get_channel_by_name(channel_id)
        if not channel_info:
            raise HTTPException(status_code=404, detail="Channel not found")
        return channel_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get channel: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/channels/{channel_id}/stats", response_model=ChannelStatsResponse, tags=["Channel Management"]
)
async def get_channel_stats(
    channel_id: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取直播间统计信息
    
    返回直播间的观众数、直播时长和礼物交易总额（USD）
    """
    try:
        stats = await agora_service.get_channel_stats(channel_id)
        return stats
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get channel stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/channels/user/{user_id}", response_model=ChannelInfo, tags=["Channel Management"]
)
async def get_channel_by_userid(
    user_id: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    根据用户名获取直播间信息
    """
    try:
        channel_info = await agora_service.get_channel_by_userid(user_id)
        if not channel_info:
            raise HTTPException(status_code=404, detail="Channel not found for user")
        return channel_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get channel by username: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/live_channels", response_model=LiveChannelsResponse, tags=["Channel Management"]
)
async def get_live_channels(
    size: int = 10,
    page: int = 1,
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    获取所有正在直播的频道列表，包含总数信息
    """
    try:
        live_channels_response = await agora_service.get_live_channels(size, page)
        return live_channels_response
    except Exception as e:
        logger.error(f"Failed to get live channels: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/channels/{channel_name}/audiences",
    response_model=ChannelAudienceResponse,
    tags=["Channel Management"],
)
async def get_channel_audiences(
    channel_name: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取指定频道的观众信息

    只有处于LIVE状态的频道才能查询观众信息
    """
    try:
        audience_response = await agora_service.get_channel_audiences(channel_name)
        return audience_response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get channel audiences: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get(
    "/channels/{channel_name}/user/{uid}",
    response_model=ChannelAudienceInfo,
    tags=["Channel Management"],
)
async def get_user_info_by_uid(
    channel_name: str,
    uid: int,
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    根据UID和频道名称获取用户信息（头像和用户名）

    Args:
        channel_name: 频道名称
        uid: Agora UID

    Returns:
        ChannelAudienceInfo: 包含用户信息的响应
    """
    try:
        user_info = await agora_service.get_user_info_by_uid(uid, channel_name)
        return user_info
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get user info by uid: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



@app.post("/buy_gift", response_model=BuyGiftResponse, tags=["Gift Management"])
async def buy_gift(
    request_body: BuyGiftRequest,
    request: Request,
    user: User = Depends(current_user),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    购买礼物

    根据礼物ID、直播间名称和购买数量购买礼物。
    会先检查用户BNB余额是否足够支付礼物的USD价格。
    实际的购买和数据库操作将在后台异步执行。

    参数说明：
    - operation_id: 客户端生成的唯一操作ID，用于去重和追踪
    - channel_name: 直播间名称
    - gift_id: 礼物ID
    - quantity: 购买数量，默认为1，最小1，最大999
    - message: 可选的打赏留言
    """
    return await agora_service.process_gift_purchase(
        user_id=user.id,
        request=request,
        request_body=request_body
    )


@app.get(
    "/channels/{channel_name}/gift_history",
    response_model=ChannelGiftsResponse,
    tags=["Gift Management"],
)
async def get_channel_gifts(
    channel_name: str,
    params: ChannelGiftsRequest = Depends(),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    获取频道的礼物历史记录

    Args:
        channel_name: 频道名称
        params: 分页参数（page: 页码，从1开始，默认1；page_size: 每页大小，默认10，最大100）

    Returns:
        ChannelGiftsResponse: 包含礼物历史记录列表的响应
    """
    try:
        gifts_response = await agora_service.get_channel_gifts(
            channel_name=channel_name, page=params.page, page_size=params.page_size
        )
        return gifts_response
    except Exception as e:
        logger.error(f"Failed to get channel gifts for {channel_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/gifts", response_model=GiftListResponse, tags=["Gift Management"])
async def get_gift_list(
    le: Optional[int] = None,
    ge: Optional[int] = None,
    language: Optional[str] = None,
    agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取礼物列表（优先使用缓存）

    按sort_order从小到大排序返回所有礼物，支持价格过滤和多语言

    Args:
        le: 小于等于指定价格的礼物（USD分）
        ge: 大于等于指定价格的礼物（USD分）
        language: 语言代码，支持 th, ja, ko, es, zh-tw。不指定则返回英文名称
        
    Returns:
        GiftListResponse: 包含礼物列表和元数据的响应
    """
    def get_translated_name(gift_info, language: Optional[str]) -> str:
        """根据语言参数获取翻译后的礼物名称"""
        if not language:
            return gift_info.name  # 默认返回英文名称
        
        # 语言代码到meta字段的映射
        lang_field_map = {
            'th': 'name_th',        # 泰语
            'ja': 'name_ja',        # 日语
            'ko': 'name_ko',        # 韩语
            'es': 'name_es',        # 西班牙语
            'zh-tw': 'name_zh_tw'   # 繁体中文
        }
        
        field_name = lang_field_map.get(language.lower())
        if not field_name:
            return gift_info.name  # 不支持的语言，返回英文
        
        # 尝试从gift_info的额外数据中获取翻译（如果缓存支持）
        if hasattr(gift_info, 'meta') and gift_info.meta and field_name in gift_info.meta:
            translated_name = gift_info.meta[field_name]
            if translated_name and translated_name.strip():
                return translated_name
        
        return gift_info.name  # 没有找到翻译，返回英文名称
    
    try:
        # 优先从缓存获取礼物列表
        if le is not None or ge is not None:
            # 需要价格过滤，使用缓存的价格过滤方法
            cached_gifts = await gift_cache.get_gifts_by_price_range(min_price=ge, max_price=le)
        else:
            # 不需要价格过滤，获取所有礼物
            cached_gifts = await gift_cache.get_all_gifts()

        if cached_gifts:
            # 转换为API响应格式
            gift_items = []
            for gift_info in cached_gifts:
                if gift_info.is_active:  # 只返回激活的礼物
                    gift_items.append(
                        GiftListItem(
                            id=gift_info.id,
                            name=get_translated_name(gift_info, language),
                            description=gift_info.description,
                            price=gift_info.price,
                            currency=gift_info.currency,
                            icon_url=gift_info.icon_url,
                            animation_url=gift_info.animation_url,
                            animation_md5=gift_info.animation_md5,
                            animation_hd_url=(
                                getattr(gift_info, 'animation_hd_url', None)
                                if getattr(gift_info, 'animation_hd_url', None) is not None
                                else (
                                    gift_info.animation_url.replace('.svga', '-HD.svga')
                                    if gift_info.animation_url and gift_info.animation_url.endswith('.svga')
                                    else gift_info.animation_url
                                )
                            ),
                            animation_hd_md5=(
                                getattr(gift_info, 'animation_hd_md5', None)
                                if getattr(gift_info, 'animation_hd_md5', None) is not None
                                else (gift_info.meta.get('animation_hd_md5') if getattr(gift_info, 'meta', None) else None)
                            ),
                            animation_mp4_url=(
                                getattr(gift_info, 'animation_mp4_url', None)
                                if getattr(gift_info, 'animation_mp4_url', None) is not None
                                else (
                                    (gift_info.animation_url.rsplit('.', 1)[0] + '.mp4')
                                    if gift_info.animation_url
                                    else None
                                )
                            ),
                            animation_mp4_md5=(
                                getattr(gift_info, 'animation_mp4_md5', None)
                                if getattr(gift_info, 'animation_mp4_md5', None) is not None
                                else (gift_info.meta.get('animation_mp4_md5') if getattr(gift_info, 'meta', None) else None)
                            ),
                            category=gift_info.category,
                        )
                    )

            logger.debug(f"从缓存返回 {len(gift_items)} 个活跃礼物 (价格过滤: ge={ge}, le={le})")

            return GiftListResponse(items=gift_items, total=len(gift_items))

        # 缓存不可用，使用数据库查询作为后备方案
        logger.info("缓存不可用，使用数据库查询获取礼物列表")
        gifts_response = await agora_service.get_gift_list(min_price=ge, max_price=le, language=language)
        return gifts_response

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get gift list: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/gifts/convert", response_model=GiftConvertResponse, tags=["Gift Management"])
async def convert_gift_to_token(
    request: GiftConvertRequest,
    agora_service: AgoraService = Depends(get_agora_service)
):
    """
    将礼物转换为对应的代币数量（简化版）

    根据礼物ID、数量和目标代币地址，计算礼物对应的代币数量。
    这个接口会：
    1. 从礼物缓存获取礼物价格信息
    2. 计算礼物的总USD价值
    3. 调用memecoin服务的convert接口获取对应的代币数量
    4. 返回转换结果（仅包含代币数量和转换汇率）

    Args:
        request: 礼物转换请求
        - gift_id: 礼物ID
        - quantity: 礼物数量，默认为1，最小1，最大999
        - token_address: 目标代币地址

    Returns:
        GiftConvertResponse: 简化的转换结果
        - token_amount: 转换后的代币数量
        - conversion_rate: 转换汇率（1 USD = X tokens）
    """
    try:
        result = await agora_service.convert_gift_to_token(
            gift_id=request.gift_id,
            quantity=request.quantity,
            token_address=request.token_address
        )
        
        return GiftConvertResponse(**result)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to convert gift to token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/gifts/cache/refresh", tags=["Gift Management"])
async def refresh_gift_cache():
    """
    刷新礼物缓存
    
    从数据库重新加载礼物数据到缓存中。当礼物数据在数据库中更新后，
    可以使用此接口刷新缓存以确保最新数据可用。
    
    Returns:
        Dict: 刷新结果和缓存统计信息
    """
    try:
        logger.info("开始刷新礼物缓存...")
        success = await gift_cache.refresh_cache()
        
        if success:
            cache_stats = gift_cache.get_cache_stats()
            logger.info(f"礼物缓存刷新成功，缓存统计: {cache_stats}")
            return {
                "success": True,
                "message": "礼物缓存刷新成功",
                "cache_stats": cache_stats
            }
        else:
            logger.error("礼物缓存刷新失败")
            raise HTTPException(status_code=500, detail="礼物缓存刷新失败")
            
    except Exception as e:
        logger.error(f"刷新礼物缓存时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新礼物缓存失败: {str(e)}")


# RTM 消息发送端点
@app.post(
    "/send_channel_message",
    response_model=RTMChannelMessageResponse,
    tags=["Channel Management"],
)
async def send_rtm_message(
    request: RTMChannelMessageRequest,
    user: User = Depends(current_user),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    发送RTM消息（统一接口）

    支持以下几种发送模式：
    1. 指定频道列表：设置 channel_names 数组，broadcast 必须为 false
    2. 广播到所有正在直播的频道：设置 broadcast 为 true 且 channel_names 为空

    Args:
        request: RTM消息发送请求
        - channel_names: 频道名称列表（当broadcast为false时必填）
        - message: 消息内容
        - broadcast: 是否广播到所有正在直播的频道（默认false）

    Returns:
        RTMChannelMessageResponse: 消息发送结果
    """
    try:
        result = await agora_service.send_rtm_channel_message(
            user_id=user.id, request=request
        )

        if request.broadcast:
            logger.info(f"用户 {user.id} 成功广播RTM消息到所有正在直播的频道")
        else:
            logger.info(
                f"用户 {user.id} 成功发送RTM消息到指定频道: {len(request.channel_names)}个频道"
            )

        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"RTM消息发送失败: user={user.id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"消息发送失败: {str(e)}")


@app.get(
    "/channels/{channel_name}/token",
    tags=["Channel Management"],
)
async def get_channel_token(
    channel_name: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取频道绑定的代币信息

    根据频道名称查询该频道绑定的代币地址，并调用memecoin服务获取代币的详细信息。

    Args:
        channel_name: 频道名称

    Returns:
        ChannelTokenResponse: 包含频道代币信息的响应
        - has_token: 是否绑定了代币
        - token_address: 代币地址（如果已绑定）
        - token_info: 代币详细信息（从memecoin服务获取）
    """
    try:
        result = await agora_service.get_channel_token_info(channel_name)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取频道代币信息失败: channel={channel_name}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道代币信息失败: {str(e)}")


@app.get(
    "/token/{token_address}/channels",
    response_model=TokenChannelsResponse,
    tags=["Channel Management"],
)
async def get_token_channels(
    token_address: str, agora_service: AgoraService = Depends(get_agora_service)
):
    """
    获取绑定了指定代币的正在直播的频道列表

    根据代币地址查询所有绑定了该代币且正在直播的频道。

    Args:
        token_address: 代币地址

    Returns:
        TokenChannelsResponse: 包含代币相关频道信息的响应
        - token_address: 代币地址
        - total_channels: 总频道数
        - live_channels: 正在直播的频道列表
    """
    try:
        result = await agora_service.get_token_channels(token_address)
        return TokenChannelsResponse(**result)
    except Exception as e:
        logger.error(f"获取代币相关频道失败: token={token_address}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取代币相关频道失败: {str(e)}")


@app.get(
    "/channels/{channel_name}/gift_ranking",
    response_model=ChannelGiftRankingResponse,
    tags=["Gift Management"],
)
async def get_channel_gift_ranking(
    channel_name: str,
    params: ChannelGiftRankingRequest = Depends(),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    获取频道打赏排行榜

    根据用户在指定频道的打赏总价值进行排序，返回打赏排行榜。
    统计每个用户的总打赏金额、打赏次数、礼物种类数等信息。

    Args:
        channel_name: 频道名称
        params: 分页参数（page: 页码，从1开始，默认1；page_size: 每页大小，默认10，最大100）

    Returns:
        ChannelGiftRankingResponse: 包含打赏排行榜的响应
        - total_tippers: 总打赏人数
        - total_gift_value: 频道总打赏价值（USD分）
        - rankings: 排行榜列表，按打赏价值降序排列
    """
    try:
        result = await agora_service.get_channel_gift_ranking(
            channel_name=channel_name, page=params.page, page_size=params.page_size
        )
        return ChannelGiftRankingResponse(**result)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取频道打赏排行榜失败: channel={channel_name}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道打赏排行榜失败: {str(e)}")


# 红包相关端点
@app.get(
    "/red_envelopes/all",
    tags=["Red Envelope"],
)
async def get_all_red_envelopes(
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    获取所有频道的活跃红包列表
    
    Returns:
        Dict: 包含所有活跃红包的响应
        {
            "envelopes": [...],
            "total": 10,
            "total_channels": 3,
            "channels": ["channel1", "channel2", "channel3"]
        }
    """
    try:
        result = await agora_service.get_all_active_red_envelopes()
        return result
    except Exception as e:
        logger.error(f"获取所有红包列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取所有红包列表失败: {str(e)}")


@app.get(
    "/channels/{channel_name}/red_envelopes",
    response_model=ChannelRedEnvelopesResponse,
    tags=["Red Envelope"],
)
async def get_channel_red_envelopes(
    channel_name: str,
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    获取频道中的活跃红包列表
    
    Args:
        channel_name: 频道名称
        
    Returns:
        ChannelRedEnvelopesResponse: 红包列表
    """
    try:
        result = await agora_service.get_channel_red_envelopes(channel_name)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取频道红包列表失败: channel={channel_name}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道红包列表失败: {str(e)}")


@app.post(
    "/red_envelopes/claim",
    response_model=ClaimRedEnvelopeResponse,
    tags=["Red Envelope"],
)
async def claim_red_envelope(
    request: ClaimRedEnvelopeRequest,
    user: User = Depends(current_user),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    领取红包
    
    红包会被分成88份，每个用户只能领取一次。
    从收到第一个领取请求开始，红包只持续3秒。
    3秒后未领取完的金额会返还给创建者。
    
    Args:
        request: 领取红包请求
        
    Returns:
        ClaimRedEnvelopeResponse: 领取结果
    """
    try:
        result = await agora_service.claim_red_envelope(
            user_id=user.id,
            envelope_id=request.envelope_id,
            channel_name=request.channel_name
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"领取红包失败: user={user.id}, envelope={request.envelope_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"领取红包失败: {str(e)}")


# Kick User and Mute User endpoints
@app.post(
    "/channels/{channel_name}/kick",
    response_model=KickUserResponse,
    tags=["Channel Management"],
)
async def kick_user(
    channel_name: str,
    request: KickUserRequest,
    user: User = Depends(current_user),
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    从频道中踢出用户
    
    只有频道主播才能执行此操作。被踢出的用户在指定时长内无法重新加入频道。
    
    Args:
        channel_name: 频道名称
        request: 踢出用户请求
        - user_id: 要踢出的用户ID
        - reason: 踢出原因（可选）
        - duration: 踢出时长（秒），默认300秒，最大24小时
        
    Returns:
        KickUserResponse: 踢出结果
        
    Raises:
        403: 权限不足（不是主播）
        404: 频道不存在或未在直播中
        400: 参数错误（如试图踢出自己）
        500: 系统错误
    """
    try:
        # 验证请求中的channel_name与路径参数一致
        if request.channel_name != channel_name:
            raise HTTPException(status_code=400, detail="频道名称不匹配")
        
        result = await agora_service.kick_user_from_channel(
            channel_name=channel_name,
            user_id_to_kick=request.user_id,
            kicked_by_user_id=user.id,
            reason=request.reason,
            duration=request.duration
        )
        
        return KickUserResponse(**result)
        
    except ValueError as e:
        # 权限不足或频道不存在
        if "只有主播" in str(e):
            raise HTTPException(status_code=403, detail=str(e))
        else:
            raise HTTPException(status_code=404, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"踢出用户失败: channel={channel_name}, user_to_kick={request.user_id}, kicked_by={user.id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"踢出用户失败: {str(e)}")



# WebSocket端点 - 频道更新通知
@app.websocket("/ws/channel_updates")
async def ws_channel_updates(
    websocket: WebSocket,
    agora_service: AgoraService = Depends(get_agora_service),
):
    """
    WebSocket端点：实时推送频道的所有更新
    
    连接后会推送以下类型的消息：
    1. red_envelope_* - 红包相关消息（创建、过期、完成）- 推送所有频道
    2. token_price - 频道绑定代币的价格更新 - 根据订阅频道过滤
    3. audience_count - 频道观众人数更新 - 根据订阅频道过滤
    4. active_red_envelopes - 连接时的当前活跃红包列表 - 所有频道
    5. ping - 心跳消息
    
    客户端可以发送以下类型的消息：
    
    1. 初始订阅设置（连接后10秒内发送）：
    {
        "subscribe_channels": ["channel1", "channel2"],  // token价格推送的频道列表，空数组表示所有频道
        "enable_token_price": true,  // 是否启用token价格推送，默认true
        "price_interval": 5  // token价格推送间隔（秒），默认6秒，最小1秒
    }
    
    2. 动态更新订阅设置：
    {
        "subscribe_channels": ["new_channel1", "new_channel2"],
        "enable_token_price": false,
        "price_interval": 10
    }
    
    注意：红包消息始终推送所有频道，subscribe_channels仅影响token价格与观众人数推送
    
    服务器推送的消息格式：
    {
        "type": "red_envelope_created" | "red_envelope_expired" | "red_envelope_completed" | "token_price" | "audience_count" | "active_red_envelopes" | "subscription_confirmed" | "subscription_error",
        "channel_name": "...",  // 相关频道名称（ping、subscription_confirmed、subscription_error消息无此字段）
        "data": {
            // 具体数据内容，根据消息类型不同而变化
        }
    }
    
    订阅确认消息示例：
    {
        "type": "subscription_confirmed",
        "data": {
            "subscribed_channels": ["channel1", "channel2"],
            "invalid_channels": [],  // 验证失败的频道（仅在validate_channels=true时包含）
            "timestamp": "2024-01-01T12:00:00.000Z"
        }
    }
    
    Token价格消息示例：
    {
        "type": "token_price",
        "channel_name": "channel1",
        "data": {
            "token_address": "0x...",
            "price": "0.000123",
            "24h_change_percent": "5.2", 
            "market_cap": "1234567",
            "timestamp": "2024-01-01T12:00:00.000Z"
        }
    }
    """
    from src.agora.redis_messenger import redis_messenger
    from datetime import datetime
    import json
    
    await websocket.accept()
    redis_task = None
    token_price_task = None
    websocket_connected = True
    
    # 订阅设置
    subscribed_channels = []  # 空数组表示订阅所有频道
    enable_token_price = True  # 默认启用token价格推送
    price_interval = 6  # 默认6秒间隔
    # 频道主播信息缓存：channel_name -> { user_id, name, avatar }
    broadcaster_cache: Dict[str, Dict[str, Any]] = {}

    # TODO(high-concurrency, multi-connection): 优化主播信息获取
    # - 进程级 LRU/TTL 缓存（例如 30–60 秒）在所有 WS 连接间共享，显著减少 DB 访问
    # - 将频道基础信息落到 Redis（key: channel_info:{channel_name}，短 TTL），接收侧优先读 Redis，再回退 DB
    # - 在发布侧（Redis 消息）直接附带 broadcaster 字段，接收侧零查询
    # - 埋点统计：缓存命中率、查询 QPS、P95/P99 延迟，必要时再启用上述方案
    async def get_broadcaster_info(channel_name: Optional[str]) -> Optional[Dict[str, Any]]:
        """根据频道名获取主播信息并做简单缓存。"""
        if not channel_name:
            return None
        cached = broadcaster_cache.get(channel_name)
        if cached:
            return cached
        try:
            channel_info = await agora_service.get_channel_by_name(channel_name)
            if not channel_info:
                return None
            name_value = channel_info.broadcaster_name or channel_info.broadcaster_username
            result = {
                "user_id": channel_info.broadcaster_id,
                "name": name_value,
                "avatar": channel_info.broadcaster_avatar,
            }
            broadcaster_cache[channel_name] = result
            return result
        except Exception:
            return None
    
    try:
        # 等待客户端发送订阅消息（可选，如果不发送则使用默认设置）
        try:
            subscription_msg = await asyncio.wait_for(
                websocket.receive_text(), 
                timeout=10.0
            )
            subscription_data = json.loads(subscription_msg)
            
            # 解析订阅设置
            subscribed_channels = subscription_data.get("subscribe_channels", [])
            enable_token_price = subscription_data.get("enable_token_price", True)  # 默认启用
            price_interval = subscription_data.get("price_interval", 6)
            
            # 验证参数
            if not isinstance(subscribed_channels, list):
                subscribed_channels = []
            if not isinstance(price_interval, (int, float)) or price_interval < 1:
                price_interval = 6
                
            logger.info(f"WebSocket订阅设置: token_price_channels={subscribed_channels}, token_price_enabled={enable_token_price}, price_interval={price_interval}s")
            
        except asyncio.TimeoutError:
            # 超时则使用默认设置
            logger.info("WebSocket未收到订阅消息，使用默认设置: 红包推送所有频道，token价格推送所有频道")
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"解析订阅消息失败，使用默认设置: {str(e)}")
        
        # 发送当前活跃红包列表（始终发送所有频道），并补充主播信息
        # TODO(batch-optimization): 对大批 envelope 一次性批量查询频道信息（新增 service.get_channels_by_names），
        # 以减少 N 次单条查询；或先聚合 channel_name 去重后批量获取
        all_envelopes = await agora_service.get_all_active_red_envelopes()
        try:
            for envelope in all_envelopes.get("envelopes", []):
                channel_name = envelope.get("channel_name")
                broadcaster = await get_broadcaster_info(channel_name)
                if broadcaster:
                    envelope["broadcaster"] = broadcaster
        except Exception:
            # 保守失败不影响原有发送
            pass
        await websocket.send_json({
            "type": "active_red_envelopes",
            "data": all_envelopes
        })
        
        # 定义红包消息处理器
        async def handle_redis_message(message: Dict[str, Any]):
            """处理从Redis接收到的频道消息（红包为全量，人数按订阅过滤）"""
            nonlocal websocket_connected
            
            if not websocket_connected:
                logger.debug("WebSocket已断开，跳过红包消息发送")
                return
                
            try:
                # 检查WebSocket连接状态
                if websocket.client_state.name != "CONNECTED":
                    logger.debug(f"WebSocket未连接，跳过红包消息发送: state={websocket.client_state.name}")
                    websocket_connected = False
                    return
                
                message_type = message.get("type")
                # 红包消息：始终推送所有频道
                if message_type in ("red_envelope_created", "red_envelope_expired", "red_envelope_completed"):
                    # 设置红包相关的时间戳
                    if message.get("data", {}).get("created_at") == "now":
                        message["data"]["created_at"] = datetime.utcnow().isoformat()
                    if message.get("data", {}).get("expired_at") == "now":
                        message["data"]["expired_at"] = datetime.utcnow().isoformat()
                    if message.get("data", {}).get("completed_at") == "now":
                        message["data"]["completed_at"] = datetime.utcnow().isoformat()

                    # 为红包事件补充主播信息
                    channel_name_for_env = message.get("data", {}).get("channel_name")
                    broadcaster = await get_broadcaster_info(channel_name_for_env)
                    if broadcaster is not None:
                        message.setdefault("data", {})["broadcaster"] = broadcaster

                    await websocket.send_json(message)
                    logger.info(
                        f"发送红包WebSocket消息: type={message.get('type')}, channel={channel_name_for_env}, envelope_id={message.get('data', {}).get('envelope_id')}"
                    )
                    return

                # 观众人数：按订阅频道过滤
                if message_type == "audience_count":
                    ch_name = message.get("channel_name") or message.get("data", {}).get("channel_name")
                    if subscribed_channels and ch_name not in subscribed_channels:
                        return
                    # 规范化时间戳
                    if message.get("data", {}).get("timestamp") == "now":
                        message["data"]["timestamp"] = datetime.utcnow().isoformat()
                    await websocket.send_json(message)
                    logger.debug(f"发送观众人数更新: channel={ch_name}, current={message.get('data', {}).get('current_audience')}")
                    return

                # 其它消息：直接转发
                await websocket.send_json(message)
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"发送红包WebSocket消息失败: {error_msg}")
                websocket_connected = False
        
        # 定义token价格推送任务
        async def token_price_push_task():
            """推送token价格更新"""
            nonlocal websocket_connected
            
            try:
                while websocket_connected and websocket.client_state.name == "CONNECTED":
                    try:
                        # 获取需要推送价格的频道列表
                        target_channels = subscribed_channels
                        logger.debug(f"Token价格推送任务运行: 目标频道数={len(target_channels)}, 频道={target_channels[:5]}{'...' if len(target_channels) > 5 else ''}")
                        
                        if not target_channels:
                            logger.debug("没有找到需要推送价格的频道，等待下次推送")
                            await asyncio.sleep(price_interval)
                            continue
                        
                        # 初始化Redis连接
                        await agora_service.init_redis()
                        if not agora_service.redis_client:
                            logger.warning("Redis连接不可用，跳过token价格推送")
                            await asyncio.sleep(price_interval)
                            continue
                        
                        # 为每个频道获取其绑定的token价格
                        sent_count = 0
                        for channel_name in target_channels:
                            try:
                                # 获取频道绑定的token信息
                                channel_token_info = await agora_service.get_channel_token_info(channel_name)
                                if not channel_token_info.get("has_token") or not channel_token_info.get("token_address"):
                                    logger.debug(f"频道 {channel_name} 未绑定token，跳过价格推送")
                                    continue
                                
                                token_address = channel_token_info["token_address"]
                                logger.debug(f"频道 {channel_name} 绑定token: {token_address}")
                                
                                # 从Redis获取该token的价格数据
                                redis_key = f"memecoin:{token_address}"
                                token_cache = await agora_service.redis_client.hgetall(redis_key)
                                
                                if not token_cache:
                                    logger.debug(f"Redis中未找到token {token_address} 的价格数据")
                                    continue
                                
                                # 提取价格相关信息
                                price = token_cache.get(b'price', b'0').decode() if isinstance(token_cache.get(b'price'), bytes) else token_cache.get('price', '0')
                                market_cap = token_cache.get(b'market_cap', b'0').decode() if isinstance(token_cache.get(b'market_cap'), bytes) else token_cache.get('market_cap', '0')
                                price_change_percent = token_cache.get(b'price_change_percent', b'0').decode() if isinstance(token_cache.get(b'price_change_percent'), bytes) else token_cache.get('price_change_percent', '0')
                                
                                # 发送价格更新到WebSocket
                                await websocket.send_json({
                                    "type": "token_price",
                                    "channel_name": channel_name,
                                    "data": {
                                        "token_address": token_address,
                                        "price": price,
                                        "24h_change_percent": price_change_percent,
                                        "market_cap": market_cap,
                                        "timestamp": datetime.utcnow().isoformat()
                                    }
                                })
                                sent_count += 1
                                logger.debug(f"发送token价格更新: channel={channel_name}, token={token_address}, price={price}")
                                
                            except Exception as e:
                                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                                logger.debug(f"处理频道 {channel_name} 的token价格失败: {error_msg}")
                        
                        logger.info(f"Token价格推送完成: 发送了 {sent_count} 个频道的价格更新")
                        await asyncio.sleep(price_interval)
                         
                    except Exception as e:
                        error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                        logger.error(f"Token价格推送任务异常: {error_msg}")
                        await asyncio.sleep(price_interval)
                         
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
                logger.error(f"Token价格推送任务终止: {error_msg}")
                websocket_connected = False
        
        # 启动独立的Redis订阅任务（每个WS连接一个）
        pubsub = None
        try:
            pubsub, redis_task = await redis_messenger.subscribe_channel_updates(handle_redis_message)
            logger.info("频道WebSocket 独立Redis订阅已启动")
        except Exception as e:
            logger.error(f"启动Redis订阅失败: {e}")
        
        # 启动token价格推送任务（如果启用）
        if enable_token_price:
            token_price_task = asyncio.create_task(token_price_push_task())
            logger.info(f"频道WebSocket token价格推送已启动，间隔: {price_interval}秒，价格推送频道: {subscribed_channels if subscribed_channels else '所有频道'}")
        else:
            logger.info("频道WebSocket token价格推送已禁用")
        
        # 保持WebSocket连接，等待客户端断开或任务完成
        try:
            ping_count = 0
            while websocket_connected and websocket.client_state.name == "CONNECTED":
                try:
                    # 检查客户端是否发送了新的订阅消息
                    try:
                        update_msg = await asyncio.wait_for(websocket.receive_text(), timeout=0.1)
                        update_data = json.loads(update_msg)
                        
                        # 常规的订阅设置更新
                        if "subscribe_channels" in update_data:
                            subscribed_channels = update_data.get("subscribe_channels", [])
                            logger.info(f"更新token价格推送频道订阅: {subscribed_channels}")
                            
                            # 重新发送当前活跃红包列表，并补充主播信息
                            all_envelopes = await agora_service.get_all_active_red_envelopes()
                            try:
                                for envelope in all_envelopes.get("envelopes", []):
                                    ch_name = envelope.get("channel_name")
                                    broadcaster = await get_broadcaster_info(ch_name)
                                    if broadcaster:
                                        envelope["broadcaster"] = broadcaster
                            except Exception:
                                pass
                            await websocket.send_json({
                                "type": "active_red_envelopes",
                                "data": all_envelopes
                            })
                            logger.info(f"已重新发送活跃红包列表给客户端")
                        
                        if "enable_token_price" in update_data:
                            new_enable_token_price = update_data.get("enable_token_price", False)
                            if new_enable_token_price != enable_token_price:
                                enable_token_price = new_enable_token_price
                                
                                # 启动或停止token价格推送任务
                                if enable_token_price and (not token_price_task or token_price_task.done()):
                                    token_price_task = asyncio.create_task(token_price_push_task())
                                    logger.info("启动token价格推送任务")
                                elif not enable_token_price and token_price_task and not token_price_task.done():
                                    token_price_task.cancel()
                                    logger.info("停止token价格推送任务")
                        
                        if "price_interval" in update_data:
                            new_interval = update_data.get("price_interval", 6)
                            if isinstance(new_interval, (int, float)) and new_interval >= 1:
                                price_interval = new_interval
                                logger.info(f"更新token价格推送间隔: {price_interval}秒")
                        
                    except asyncio.TimeoutError:
                        pass  # 正常超时，继续发送ping
                    except (json.JSONDecodeError, Exception) as e:
                        logger.debug(f"处理客户端消息失败: {str(e)}")
                    
                    # 发送ping消息
                    ping_count += 1
                    await websocket.send_json({
                        "type": "ping", 
                        "timestamp": datetime.utcnow().isoformat(),
                        "sequence": ping_count,
                        "subscribed_channels": subscribed_channels,
                        "token_price_enabled": enable_token_price
                    })
                    await asyncio.sleep(60)  # 每60秒发送一次ping
                    
                except ConnectionClosedOK:
                    logger.debug("WebSocket连接正常关闭")
                    websocket_connected = False
                    break
                except ConnectionClosedError as e:
                    logger.debug(f"WebSocket连接异常关闭: {str(e)}")
                    websocket_connected = False
                    break
                except Exception as ping_error:
                    error_msg = f"{type(ping_error).__name__}: {str(ping_error)}" if str(ping_error) else f"{type(ping_error).__name__}: 无具体错误信息"
                    logger.debug(f"Ping失败，WebSocket可能已断开: {error_msg}")
                    websocket_connected = False
                    break
        except Exception as loop_error:
            error_msg = f"{type(loop_error).__name__}: {str(loop_error)}" if str(loop_error) else f"{type(loop_error).__name__}: 无具体错误信息"
            logger.debug(f"WebSocket主循环异常: {error_msg}")
            websocket_connected = False
            
    except WebSocketDisconnect:
        logger.info("频道WebSocket断开连接")
        websocket_connected = False
    except Exception as e:
        error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
        logger.error(f"频道WebSocket错误: {error_msg}")
        websocket_connected = False
    finally:
        # 标记连接已断开
        websocket_connected = False
        
        # 清理Redis订阅任务
        if redis_task and not redis_task.done():
            logger.info("关闭频道WebSocket Redis订阅...")
            redis_task.cancel()
            try:
                await asyncio.wait_for(redis_task, timeout=5.0)
            except (asyncio.CancelledError, asyncio.TimeoutError):
                logger.info("Redis订阅任务已取消")
            except Exception as cleanup_error:
                error_msg = f"{type(cleanup_error).__name__}: {str(cleanup_error)}" if str(cleanup_error) else f"{type(cleanup_error).__name__}: 无具体错误信息"
                logger.error(f"清理Redis订阅任务失败: {error_msg}")
        
        # 清理token价格推送任务
        if token_price_task and not token_price_task.done():
            logger.info("关闭token价格推送任务...")
            token_price_task.cancel()
            try:
                await asyncio.wait_for(token_price_task, timeout=3.0)
            except (asyncio.CancelledError, asyncio.TimeoutError):
                logger.info("Token价格推送任务已取消")
            except Exception as cleanup_error:
                error_msg = f"{type(cleanup_error).__name__}: {str(cleanup_error)}" if str(cleanup_error) else f"{type(cleanup_error).__name__}: 无具体错误信息"
                logger.error(f"清理token价格推送任务失败: {error_msg}")
        
        # 停止Redis监听（独立 pubsub）
        try:
            if redis_task and not redis_task.done():
                redis_task.cancel()
                try:
                    await asyncio.wait_for(redis_task, timeout=3.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    pass
            if pubsub:
                try:
                    await pubsub.unsubscribe(redis_messenger.RED_ENVELOPE_CHANNEL)
                except Exception:
                    pass
                try:
                    await pubsub.close()
                except Exception:
                    pass
        except Exception as e:
            error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
            logger.error(f"清理Redis独立订阅失败: {error_msg}")
        
        # 关闭WebSocket连接（如果还未关闭）
        try:
            if websocket.client_state.name == "CONNECTED":
                await websocket.close()
        except Exception as e:
            error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}: 无具体错误信息"
            logger.debug(f"关闭WebSocket连接时出现异常: {error_msg}")


# 异常处理器
@app.exception_handler(ValueError)
async def value_error_handler(request: Request, exc: ValueError):
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(error="validation_error", message=str(exc)).dict(),
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="internal_server_error", message="An internal server error occurred"
        ).dict(),
    )
