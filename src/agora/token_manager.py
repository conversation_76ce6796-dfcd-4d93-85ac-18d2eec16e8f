import time
import hashlib
import json
import zlib
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta, timezone
from redis.asyncio import Redis

from .constants import AgoraRole
from .logger import logger
from .settings import settings
from .schemas import TokenResponse, RTMTokenResponse, RTCAndRTMTokenResponse
from .token_builder.RtcTokenBuilder2 import RtcTokenBuilder
from .token_builder.RtmTokenBuilder2 import RtmTokenBuilder
from src.common.redis_cli import RedisCli


class TokenManager:
    # 最大 UID 值（2^31-1）
    MAX_UID = 2_147_483_647

    def __init__(self):
        self.app_id = settings.APP_ID
        self.app_certificate = settings.APP_CERTIFICATE
        self.redis_client = None

        if not self.app_id or not self.app_certificate:
            raise ValueError("Agora APP_ID and APP_CERTIFICATE must be configured")

        # 验证盐值配置
        self._validate_salt_configuration()

    def _validate_salt_configuration(self):
        """验证盐值配置的安全性"""
        if not settings.UID_SALT:
            raise ValueError("UID_SALT must be configured for security")

        # 检查是否使用了默认盐值（在生产环境中应该警告）
        if settings.UID_SALT == "agora_secure_salt_2024_default_change_in_production":
            logger.warning(
                "Using default UID salt! This is insecure for production. "
                "Please set AGORA_UID_SALT environment variable to a unique value."
            )

        # 检查盐值长度
        if len(settings.UID_SALT) < 16:
            logger.warning(
                f"UID salt is too short ({len(settings.UID_SALT)} chars). "
                "Recommend at least 16 characters for better security."
            )

    def get_salt_hash(self) -> str:
        """
        获取盐值的哈希值（用于验证客户端配置）
        
        Returns:
            str: 盐值的 SHA256 哈希值（前8位）
        """
        salt_hash = hashlib.sha256(settings.UID_SALT.encode('utf-8')).hexdigest()
        return salt_hash[:8]  # 只返回前8位用于验证

    def generate_uid_from_user_id(self, user_id: str) -> int:
        """
        使用加盐的确定性算法从 user_id 生成 UID
        
        Args:
            user_id: 业务用户ID
            
        Returns:
            int: 生成的 UID (1 到 2^31-1)
        """
        # 将盐值与用户ID组合，增强安全性
        salted_input = f"{settings.UID_SALT}:{user_id}"

        # 使用 CRC32 算法生成确定性哈希
        crc32_hash = zlib.crc32(salted_input.encode('utf-8')) & 0xffffffff

        # 取模确保在有效范围内，避免0值
        uid = (crc32_hash % (self.MAX_UID - 1)) + 1

        logger.debug(f"Generated UID {uid} for user_id {user_id} (salted)")
        return uid

    async def init_redis(self):
        """初始化 Redis 连接"""
        if not self.redis_client:
            self.redis_client = RedisCli.async_()

    async def generate_rtc_token_with_uid_management(
            self,
            channel_name: str,
            user_id: str,
            role: str = "audience",
            expiration_time: Optional[int] = None,
            preferred_uid: Optional[int] = None
    ) -> TokenResponse:
        """
        生成 RTC Token 使用确定性 UID
        
        Args:
            channel_name: 频道名称
            user_id: 业务用户ID
            role: 用户角色 (audience/host)
            expiration_time: 过期时间（秒）
            preferred_uid: 客户端指定的 UID（如果提供，将验证其正确性）
            
        Returns:
            TokenResponse: Token 响应对象
            
        Raises:
            HTTPException: 如果用户被踢出或禁言
        """
        await self.init_redis()

        # 检查用户限制状态
        restrictions = await self.check_user_restrictions(user_id, channel_name)
        if restrictions["is_kicked"]:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=403,
                detail=f"您已被踢出该频道: {restrictions['kick_reason']}"
            )

        # 使用确定性算法生成UID
        uid = self.generate_uid_from_user_id(user_id)

        # 如果客户端提供了preferred_uid，验证其正确性
        if preferred_uid is not None and preferred_uid != uid:
            logger.warning(f"Client provided uid {preferred_uid} doesn't match generated uid {uid} for user {user_id}")
            # 可以选择使用客户端提供的UID或拒绝请求
            # 这里选择使用生成的UID以保证一致性

        # 检查是否有有效的缓存 Token
        cached_token = await self._get_cached_token(channel_name, uid, "rtc")
        if cached_token and not self._is_token_near_expiry(cached_token):
            # 即使有缓存token，也要确保uid到user_id的映射存在
            await self._save_uid_user_mapping(uid, user_id, expiration_time)
            return TokenResponse(**cached_token)

        # 生成新的 Token
        if expiration_time is None or expiration_time <= 0:
            expiration_time = settings.TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数
        expiration_time = max(1, int(expiration_time))

        current_timestamp = int(time.time())
        privilege_expired_ts = current_timestamp + expiration_time

        # 直接确定Agora角色
        from .constants import UserRole
        from .token_builder.RtcTokenBuilder2 import Role_Publisher, Role_Subscriber
        
        # 转换为token builder所需的角色
        builder_role = Role_Publisher if role == UserRole.HOST else Role_Subscriber

        # 生成 Token - 使用本地token builder
        token = RtcTokenBuilder.build_token_with_uid(
            self.app_id,
            self.app_certificate,
            channel_name,
            uid,
            builder_role,
            expiration_time,  # token过期时间（秒）
            expiration_time   # 权限过期时间（秒）
        )

        token_response = TokenResponse(
            token=token,
            app_id=self.app_id,
            channel_id=channel_name,
            uid=uid,
            role=role,
            expires_at=datetime.fromtimestamp(privilege_expired_ts, tz=timezone.utc)
        )

        # 缓存 Token
        await self._cache_token(channel_name, uid, "rtc", token_response.dict(), expiration_time)

        # 保存 UID 到 user_id 的映射关系到 Redis
        await self._save_uid_user_mapping(uid, user_id, expiration_time)

        # 记录 Token 生成日志
        await self._log_token_generation(channel_name, uid, user_id, "rtc", role)

        return token_response

    async def generate_rtm_token_enhanced(
            self,
            user_id: str,
            expiration_time: Optional[int] = None
    ) -> RTMTokenResponse:
        """
        生成增强的 RTM Token
        
        Args:
            user_id: 用户ID
            expiration_time: 过期时间（秒）
            
        Returns:
            RTMTokenResponse: RTM Token 响应对象
        """
        await self.init_redis()

        # 检查是否有有效的缓存 Token
        cached_token = await self._get_cached_token("", user_id, "rtm")
        if cached_token and not self._is_token_near_expiry(cached_token):
            return RTMTokenResponse(**cached_token)

        if expiration_time is None or expiration_time <= 0:
            expiration_time = settings.RTM_TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数
        expiration_time = max(1, int(expiration_time))

        current_timestamp = int(time.time())
        
        # 生成 RTM Token - 使用本地token builder
        token = RtmTokenBuilder.build_token(
            self.app_id,
            self.app_certificate,
            user_id,
            expiration_time  # 直接传递过期时间（秒）
        )

        token_response = RTMTokenResponse(
            token=token,
            user_id=user_id,
            expires_at=datetime.fromtimestamp(current_timestamp + expiration_time, tz=timezone.utc)
        )

        # 缓存 Token
        await self._cache_token("", user_id, "rtm", token_response.dict(), expiration_time)

        # 对于 RTM token，user_id 可以直接作为标识符，不需要单独的 UID 映射
        # 但为了保持一致性，我们可以为 RTM 也生成 UID 映射（如果需要的话）

        # 记录 Token 生成日志
        await self._log_token_generation("", user_id, user_id, "rtm", "user")

        return token_response

    async def generate_rtc_rtm_combined_token(
            self,
            channel_name: str,
            user_id: str,
            role: str = "audience",
            expiration_time: Optional[int] = None,
            preferred_uid: Optional[int] = None
    ) -> "RTCAndRTMTokenResponse":
        """
        生成同时具有RTC和RTM权限的组合Token
        
        Args:
            channel_name: 频道名称
            user_id: 业务用户ID
            role: 用户角色 (audience/host)
            expiration_time: Token和权限过期时间（秒）
            preferred_uid: 客户端指定的 UID（如果提供，将验证其正确性）
            
        Returns:
            RTCAndRTMTokenResponse: 组合Token响应对象
            
        Raises:
            HTTPException: 如果用户被踢出或禁言
        """
        await self.init_redis()

        # 检查用户限制状态
        restrictions = await self.check_user_restrictions(user_id, channel_name)
        if restrictions["is_kicked"]:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=403,
                detail=f"您已被踢出该频道: {restrictions['kick_reason']}"
            )

        # 使用确定性算法生成UID
        uid = self.generate_uid_from_user_id(user_id)

        # 如果客户端提供了preferred_uid，验证其正确性
        if preferred_uid is not None and preferred_uid != uid:
            logger.warning(f"Client provided uid {preferred_uid} doesn't match generated uid {uid} for user {user_id}")

        # 检查是否有有效的缓存 Token
        cached_token = await self._get_cached_token(channel_name, uid, "rtc_rtm")
        if cached_token and not self._is_token_near_expiry(cached_token):
            # 即使有缓存token，也要确保uid到user_id的映射存在
            await self._save_uid_user_mapping(uid, user_id, expiration_time)
            return RTCAndRTMTokenResponse(**cached_token)

        # 生成新的组合Token
        if expiration_time is None or expiration_time <= 0:
            expiration_time = settings.TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数
        expiration_time = max(1, int(expiration_time))

        current_timestamp = int(time.time())

        # 直接确定token builder所需的角色
        from .constants import UserRole
        from .token_builder.RtcTokenBuilder2 import Role_Publisher, Role_Subscriber
        
        # 直接转换为token builder所需的角色
        builder_role = Role_Publisher if role == UserRole.HOST else Role_Subscriber

        # 生成合并Token（包含RTC和RTM权限）
        # 根据Agora文档，account参数应该是用户账号，不是UID
        # token_expire和privilege_expire使用同一个过期时间
        token = RtcTokenBuilder.build_token_with_rtm(
            self.app_id,
            self.app_certificate,
            channel_name,
            user_id,  # account参数使用用户ID作为账号
            builder_role,
            expiration_time,      # token_expire: token的有效时间（秒）
            expiration_time       # privilege_expire: 权限的有效时间（秒），与token有效期相同
        )

        token_response = RTCAndRTMTokenResponse(
            token=token,
            app_id=self.app_id,
            channel_id=channel_name,
            uid=uid,
            role=role,
            expires_at=datetime.fromtimestamp(current_timestamp + expiration_time, tz=timezone.utc),
        )

        # 缓存合并Token
        await self._cache_token(channel_name, uid, "rtc_rtm", token_response.dict(), expiration_time)

        # 保存 UID 到 user_id 的映射关系到 Redis
        await self._save_uid_user_mapping(uid, user_id, expiration_time)

        # 记录 Token 生成日志
        await self._log_token_generation(channel_name, uid, user_id, "rtc_rtm", role)

        return token_response

    async def generate_anonymous_token_batch(
            self,
            channel_name: str,
            count: int,
            role: str = "audience",
            expiration_time: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        批量生成匿名RTC Token（用于压力测试）
        
        Args:
            channel_name: 频道名称
            count: 生成token数量
            role: 用户角色 (audience/host)
            expiration_time: Token和权限过期时间（秒）
            
        Returns:
            List[Dict[str, Any]]: 批量token响应列表
        """
        await self.init_redis()

        # 验证参数
        if count <= 0 or count > 1000:
            raise ValueError("Token生成数量必须在1-1000之间")

        # 生成新的RTC Token
        if expiration_time is None or expiration_time <= 0:
            expiration_time = settings.TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数
        expiration_time = max(1, int(expiration_time))

        current_timestamp = int(time.time())

        # 直接确定token builder所需的角色
        from .constants import UserRole
        from .token_builder.RtcTokenBuilder2 import Role_Publisher, Role_Subscriber
        
        # 直接转换为token builder所需的角色
        builder_role = Role_Publisher if role == UserRole.HOST else Role_Subscriber

        tokens = []
        
        for i in range(count):
            # 生成匿名用户ID
            import uuid
            anonymous_user_id = f"anonymous_test_{uuid.uuid4().hex[:8]}_{i}"
            
            # 生成UID
            uid = self.generate_uid_from_user_id(anonymous_user_id)
            
            # 生成RTC Token（仅包含RTC权限）
            token = RtcTokenBuilder.build_token_with_uid(
                self.app_id,
                self.app_certificate,
                channel_name,
                uid,
                builder_role,
                expiration_time,  # token过期时间（秒）
                expiration_time   # 权限过期时间（秒）
            )

            token_item = {
                "token": token,
                "app_id": self.app_id,
                "channel_id": channel_name,
                "uid": uid,
                "anonymous_user_id": anonymous_user_id,
                "role": role,
                "expires_at": datetime.fromtimestamp(current_timestamp + expiration_time, tz=timezone.utc),
            }
            
            tokens.append(token_item)

        # 记录批量生成日志
        logger.info(f"Generated {count} anonymous RTC tokens for channel {channel_name} with role {role}")

        return tokens

    async def generate_anonymous_rtc_rtm_token_batch(
            self,
            channel_name: str,
            count: int,
            role: str = "audience",
            expiration_time: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        批量生成匿名RTC+RTM合并Token（用于压力测试）
        
        Args:
            channel_name: 频道名称
            count: 生成token数量
            role: 用户角色 (audience/host)
            expiration_time: Token和权限过期时间（秒）
            
        Returns:
            List[Dict[str, Any]]: 批量token响应列表
        """
        await self.init_redis()

        # 验证参数
        if count <= 0 or count > 1000:
            raise ValueError("Token生成数量必须在1-1000之间")

        # 生成新的RTC+RTM合并Token
        if expiration_time is None or expiration_time <= 0:
            expiration_time = settings.TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数
        expiration_time = max(1, int(expiration_time))

        current_timestamp = int(time.time())

        # 直接确定token builder所需的角色
        from .constants import UserRole
        from .token_builder.RtcTokenBuilder2 import Role_Publisher, Role_Subscriber
        
        # 直接转换为token builder所需的角色
        builder_role = Role_Publisher if role == UserRole.HOST else Role_Subscriber

        tokens = []
        
        for i in range(count):
            # 生成匿名用户ID
            import uuid
            anonymous_user_id = f"anonymous_test_{uuid.uuid4().hex[:8]}_{i}"
            
            # 生成UID
            uid = self.generate_uid_from_user_id(anonymous_user_id)
            
            # 生成合并Token（包含RTC和RTM权限）
            # 根据Agora文档，account参数应该是用户账号，不是UID
            # token_expire和privilege_expire使用同一个过期时间
            token = RtcTokenBuilder.build_token_with_rtm(
                self.app_id,
                self.app_certificate,
                channel_name,
                anonymous_user_id,  # account参数使用匿名用户ID作为账号
                builder_role,
                expiration_time,      # token_expire: token的有效时间（秒）
                expiration_time       # privilege_expire: 权限的有效时间（秒），与token有效期相同
            )
            
            # 准备token响应项
            token_item = {
                "token": token,
                "app_id": self.app_id,
                "channel_id": channel_name,
                "uid": uid,
                "anonymous_user_id": anonymous_user_id,
                "role": role,
                "expires_at": datetime.fromtimestamp(current_timestamp + expiration_time, tz=timezone.utc),
            }
            
            tokens.append(token_item)

        # 记录批量生成日志
        logger.info(f"Generated {count} anonymous RTC+RTM tokens for channel {channel_name} with role {role}")

        return tokens

    async def refresh_token(
            self,
            channel_name: str,
            uid: int,
            token_type: str = "rtc",
            user_id_hint: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        刷新即将过期的 Token
        
        Args:
            channel_name: 频道名称
            uid: 用户 UID
            token_type: Token 类型 (rtc/rtm)
            user_id_hint: 用户ID提示（用于确定性UID验证）
            
        Returns:
            Optional[Dict]: 新的 Token 信息
        """
        await self.init_redis()

        # 获取当前 Token
        cached_token = await self._get_cached_token(channel_name, uid, token_type)
        if not cached_token:
            return None

        # 检查是否需要刷新
        if not self._is_token_near_expiry(cached_token):
            return cached_token

        # 生成新的 Token
        if token_type == "rtc":
            # 需要user_id来重新生成token
            if not user_id_hint:
                logger.error(f"Cannot refresh RTC token without user_id hint for uid {uid}")
                return None

            # 验证user_id_hint的正确性
            user_id = await self.get_user_id_by_uid(uid, user_id_hint)
            if not user_id:
                logger.error(f"Invalid user_id hint for uid {uid}")
                return None

            role = cached_token.get("role", "audience")
            new_token = await self.generate_rtc_token_with_uid_management(
                channel_name, user_id, role, preferred_uid=uid
            )
            return new_token.dict()
        elif token_type == "rtm":
            if not user_id_hint:
                logger.error(f"Cannot refresh RTM token without user_id hint for uid {uid}")
                return None
            new_token = await self.generate_rtm_token_enhanced(user_id_hint)
            return new_token.dict()
        elif token_type == "rtc_rtm":
            # 刷新合并token
            if not user_id_hint:
                logger.error(f"Cannot refresh RTC+RTM token without user_id hint for uid {uid}")
                return None

            # 验证user_id_hint的正确性
            user_id = await self.get_user_id_by_uid(uid, user_id_hint)
            if not user_id:
                logger.error(f"Invalid user_id hint for uid {uid}")
                return None

            role = cached_token.get("role", "audience")
            new_token = await self.generate_rtc_rtm_combined_token(
                channel_name, user_id, role, preferred_uid=uid
            )
            return new_token.dict()

        return None

    async def revoke_token(self, channel_name: str, uid: int, token_type: str = "rtc") -> bool:
        """
        撤销 Token
        
        Args:
            channel_name: 频道名称
            uid: 用户 UID
            token_type: Token 类型
            
        Returns:
            bool: 撤销结果
        """
        await self.init_redis()

        # 从缓存中删除 Token
        cache_key = self._get_token_cache_key(channel_name, uid, token_type)
        if self.redis_client:
            await self.redis_client.delete(cache_key)

        # 记录撤销日志
        await self._log_token_revocation(channel_name, uid, token_type)

        return True

    async def validate_token_rate_limit(self, user_id: str) -> bool:
        """
        验证 Token 生成频率限制
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否通过频率限制
        """
        await self.init_redis()

        if not self.redis_client:
            return True

        # 设置频率限制：每分钟最多生成 10 个 Token
        rate_limit_key = f"token_rate_limit:{user_id}"
        current_count = await self.redis_client.get(rate_limit_key)

        if current_count is None:
            # 第一次请求，设置计数器
            try:
                await self.redis_client.setex(rate_limit_key, 60, 1)
            except Exception as e:
                logger.error(f"Failed to set rate limit: key={rate_limit_key}, error={str(e)}")
                # 如果Redis失败，允许请求通过
                return True
            return True
        elif int(current_count) < 10:
            # 未超过限制，增加计数器
            await self.redis_client.incr(rate_limit_key)
            return True
        else:
            # 超过频率限制
            return False

    async def get_token_statistics(self) -> Dict[str, Any]:
        """
        获取 Token 统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        await self.init_redis()

        if not self.redis_client:
            return {"error": "Redis not available"}

        # 获取缓存中的 Token 数量
        cached_tokens = 0
        async for key in self.redis_client.scan_iter(match="token:*"):
            cached_tokens += 1

        return {
            "cached_tokens": cached_tokens,
            "salt_hash": self.get_salt_hash(),  # 用于验证盐值配置
            "uid_algorithm": "CRC32_SALTED",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    def _get_token_cache_key(self, channel_name: str, uid: int, token_type: str) -> str:
        """生成 Token 缓存键"""
        if token_type == "rtm":
            return f"token:rtm:{uid}"
        elif token_type == "rtc_rtm":
            return f"token:rtc_rtm:{channel_name}:{uid}"
        return f"token:rtc:{channel_name}:{uid}"

    async def _get_cached_token(
            self,
            channel_name: str,
            uid: int,
            token_type: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的 Token"""
        await self.init_redis()

        if not self.redis_client:
            return None

        cache_key = self._get_token_cache_key(channel_name, uid, token_type)
        cached_data = await self.redis_client.get(cache_key)

        if cached_data:
            try:
                return json.loads(cached_data)
            except json.JSONDecodeError:
                return None

        return None

    async def _cache_token(
            self,
            channel_name: str,
            uid: int,
            token_type: str,
            token_data: Dict[str, Any],
            expiration_time: int
    ):
        """缓存 Token"""
        await self.init_redis()

        if not self.redis_client:
            return

        cache_key = self._get_token_cache_key(channel_name, uid, token_type)

        # 验证过期时间
        if not isinstance(expiration_time, int) or expiration_time <= 0:
            # 使用默认过期时间
            expiration_time = settings.TOKEN_EXPIRATION_TIME if token_type == "rtc" else settings.RTM_TOKEN_EXPIRATION_TIME

        # 确保过期时间为正整数且不超过最大值 (30天)
        max_expiration = 30 * 24 * 3600  # 30天
        expiration_time = max(1, min(int(expiration_time), max_expiration))

        # 序列化 token_data，处理 datetime 对象
        serializable_data = token_data.copy()
        for key, value in serializable_data.items():
            if isinstance(value, datetime):
                serializable_data[key] = value.isoformat()

        try:
            await self.redis_client.setex(
                cache_key,
                expiration_time,
                json.dumps(serializable_data)
            )
        except Exception as e:
            # 记录详细错误信息
            logger.error(f"Failed to cache token: key={cache_key}, expiration={expiration_time}, error={str(e)}")
            raise

    def _is_token_near_expiry(self, token_data: Dict[str, Any]) -> bool:
        """
        检查 Token 是否即将过期
        
        Args:
            token_data: Token 数据
            
        Returns:
            bool: 是否即将过期
        """
        expires_at_str = token_data.get("expires_at")
        if not expires_at_str:
            return True

        try:
            # 处理标准ISO格式：2025-06-05T10:44:54.218Z
            expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            # 如果 Token 在 5 分钟内过期，认为即将过期
            diff = (expires_at - now).total_seconds()
            return diff < 300
        except (ValueError, AttributeError) as e:
            logger.warning(f"Failed to parse expires_at: {expires_at_str}, error: {e}")
            return True

    async def _log_token_generation(
            self,
            channel_name: str,
            uid: int,
            user_id: str,
            token_type: str,
            role: str
    ):
        """记录 Token 生成日志"""
        await self.init_redis()

        if not self.redis_client:
            return

        log_entry = {
            "channel_name": channel_name,
            "uid": uid,
            "user_id": user_id,
            "token_type": token_type,
            "role": role,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": "generate"
        }

        # 保存到日志队列（可选）
        await self.redis_client.lpush("token_logs", json.dumps(log_entry))
        await self.redis_client.ltrim("token_logs", 0, 999)  # 保留最近 1000 条日志

    async def _log_token_revocation(self, channel_name: str, uid: int, token_type: str):
        """记录 Token 撤销日志"""
        await self.init_redis()

        if not self.redis_client:
            return

        log_entry = {
            "channel_name": channel_name,
            "uid": uid,
            "token_type": token_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": "revoke"
        }

        await self.redis_client.lpush("token_logs", json.dumps(log_entry))
        await self.redis_client.ltrim("token_logs", 0, 999)

    async def get_user_id_by_uid(self, uid: int, user_id_hint: Optional[str] = None) -> Optional[str]:
        """
        公共接口：通过 UID 获取用户ID（从 Redis 中获取）
        
        Args:
            uid: Agora UID
            user_id_hint: 用户ID提示（已废弃，保持兼容性）
            
        Returns:
            Optional[str]: 用户ID，如果在 Redis 中找到
        """
        # 首先尝试从 Redis 中获取
        user_id = await self._get_user_id_from_redis(uid)
        if user_id:
            return user_id

        # 如果 Redis 中没有找到，并且提供了 hint，则尝试验证
        if user_id_hint:
            # 验证提供的user_id是否能生成相同的uid
            if self.generate_uid_from_user_id(user_id_hint) == uid:
                # 如果验证通过，将映射保存到 Redis 中（使用默认过期时间）
                await self._save_uid_user_mapping(uid, user_id_hint, settings.TOKEN_EXPIRATION_TIME)
                return user_id_hint

        logger.debug(f"Cannot find user_id for uid {uid} in Redis and no valid hint provided")
        return None

    def get_uid_by_user_id(self, user_id: str) -> int:
        """
        公共接口：通过用户ID获取 UID（使用确定性算法）
        
        Args:
            user_id: 业务用户ID
            
        Returns:
            int: 对应的 UID
        """
        return self.generate_uid_from_user_id(user_id)

    def validate_uid_user_id_pair(self, uid: int, user_id: str) -> bool:
        """
        公共接口：验证 UID 和 user_id 是否匹配
        
        Args:
            uid: Agora UID
            user_id: 业务用户ID
            
        Returns:
            bool: 是否匹配
        """
        expected_uid = self.generate_uid_from_user_id(user_id)
        return uid == expected_uid

    async def _save_uid_user_mapping(self, uid: int, user_id: str, expiration_time: int):
        """保存 UID 到 user_id 的映射关系到 Redis"""
        await self.init_redis()

        if not self.redis_client:
            return

        # 使用专门的key来存储uid到user_id的映射
        uid_mapping_key = f"uid_mapping:{uid}"
        await self.redis_client.setex(
            uid_mapping_key,
            expiration_time,
            user_id
        )

    async def _get_user_id_from_redis(self, uid: int) -> Optional[str]:
        """从 Redis 中获取 UID 对应的 user_id"""
        await self.init_redis()

        if not self.redis_client:
            return None

        uid_mapping_key = f"uid_mapping:{uid}"
        user_id = await self.redis_client.get(uid_mapping_key)
        return user_id

    async def check_user_restrictions(self, user_id: str, channel_name: str) -> Dict[str, Any]:
        """
        检查用户在指定频道的限制状态（踢出）
        
        Args:
            user_id: 用户ID
            channel_name: 频道名称
            
        Returns:
            Dict[str, Any]: 限制状态
            - is_kicked: 是否被踢出
            - kick_reason: 踢出原因
        """
        await self.init_redis()
        
        if not self.redis_client:
            return {
                "is_kicked": False,
                "kick_reason": None
            }
        
        # 检查踢出状态
        kick_key = f"channel_kick:{channel_name}:{user_id}"
        kick_data_str = await self.redis_client.get(kick_key)
        
        is_kicked = False
        kick_reason = None
        if kick_data_str:
            try:
                kick_data = json.loads(kick_data_str)
                is_kicked = True
                kick_reason = kick_data.get("reason", "Kicked by host")
            except json.JSONDecodeError:
                pass
        
        return {
            "is_kicked": is_kicked,
            "kick_reason": kick_reason
        }

    async def revoke_user_channel_tokens(self, user_id: str, channel_name: str):
        """
        撤销用户在指定频道的所有token
        
        Args:
            user_id: 用户ID
            channel_name: 频道名称
        """
        await self.init_redis()
        
        if not self.redis_client:
            return
        
        uid = self.get_uid_by_user_id(user_id)
        
        # 撤销RTC token
        await self.revoke_token(channel_name, uid, "rtc")
        
        # 撤销RTC+RTM合并token
        await self.revoke_token(channel_name, uid, "rtc_rtm")
        
        logger.info(f"Revoked all tokens for user {user_id} (uid: {uid}) in channel {channel_name}")


# 全局 Token 管理器实例
token_manager = TokenManager()
