from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone
import time
import base64
import asyncio
import httpx
import json
from redis.asyncio import Redis
from sqlalchemy import select
from fastapi import Request, HTTPException

from src.database.models.Agora import LiveChannel, utc_now_naive, ChannelGiftHistory
from src.database.models.Author import Author
from src.database.models.Pair import Pair
from .memecoin_client import memecoin_client
from .repos import AgoraRepository
from .logger import logger
from .schemas import (
    ChannelCreateRequest, ChannelInfo,
    ChannelJoinRequest, ChannelJoinResponse, ChannelJoinSimpleResponse, ChannelLeaveRequest,
    AudienceInfo, ChannelStatsResponse, WebhookEvent, LiveChannelsResponse,
    ChannelAudienceInfo, ChannelAudienceResponse, ChannelGiftRecord, CreateGiftRecordRequest,
    ChannelGiftsResponse, RTMChannelMessageRequest, RTMChannelMessageResponse,
    GiftListItem, GiftListResponse, BuyGiftRequest, BuyGiftResponse
)
from .settings import settings
from .token_manager import token_manager
from .constants import ChannelStatus, RecordingStatus, GiftCategory
from .webhook_handler import AgoraWebhookHandler
from src.common.utils import add_utc_timezone
from src.agora.gift_cache import gift_cache
from src.common.redis_cli import RedisCli


class AgoraService:
    """Agora 服务业务逻辑"""
    
    def __init__(self, repo: AgoraRepository):
        self._repo = repo
        self.redis_client = None
        self.webhook_handler = None
    
    async def init_redis(self):
        if not self.redis_client:
            self.redis_client = RedisCli.async_()
        # 初始化webhook handler的Redis客户端
        if not self.webhook_handler:
            self.webhook_handler = AgoraWebhookHandler(self._repo, self.redis_client)
        else:
            self.webhook_handler.redis_client = self.redis_client
    
    async def _get_channel_from_redis(self, channel_name: str) -> Optional[Dict[str, Any]]:
        """
        从Redis缓存中获取频道信息
        
        Args:
            channel_name: 频道名称
            
        Returns:
            Dict[str, Any]: 频道信息字典，如果未找到则返回None
            返回的字典包含以下字段:
            - id: 频道ID
            - channel_name: 频道名称
            - status: 频道状态
            - user_id: 用户ID
            - broadcaster_uid: 主播UID
            - token_address: 代币地址（可能为空）
            - created_at: 创建时间
        """
        if not self.redis_client:
            return None
            
        try:
            channel_key = f"channel:{channel_name}"
            channel_data = await self.redis_client.hgetall(channel_key)
            
            if not channel_data:
                return None
                
            # 处理Redis返回的数据，确保正确的类型转换
            result = {}
            for key, value in channel_data.items():
                # 处理bytes类型的key和value
                if isinstance(key, bytes):
                    key = key.decode()
                if isinstance(value, bytes):
                    value = value.decode()
                    
                # 处理空字符串
                if key == 'token_address' and value == '':
                    value = None
                    
                result[key] = value
            
            logger.info(f"Found channel {channel_name} in Redis cache")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get channel from Redis: {str(e)}")
            return None
    
    async def _cache_channel_to_redis(self, channel: LiveChannel, expire_seconds: int = 86400) -> None:
        """
        将频道信息缓存到Redis
        
        Args:
            channel: LiveChannel对象
            expire_seconds: 过期时间（秒），默认24小时
        """
        if not self.redis_client:
            return
            
        try:
            channel_key = f"channel:{channel.channel_name}"
            channel_data = {
                "id": str(channel.id),
                "channel_name": channel.channel_name,
                "status": channel.status,
                "user_id": channel.user_id,
                "broadcaster_uid": str(channel.broadcaster_uid),
                "token_address": channel.token_address or "",
                "created_at": channel.created_at.isoformat() if channel.created_at else ""
            }
            
            await self.redis_client.hset(channel_key, mapping=channel_data)
            await self.redis_client.expire(channel_key, expire_seconds)
            logger.info(f"Cached channel {channel.channel_name} in Redis")
            
        except Exception as e:
            logger.error(f"Failed to cache channel in Redis: {str(e)}")
    
    async def _invalidate_channel_cache(self, channel_name: str) -> None:
        """
        使频道的Redis缓存失效
        
        Args:
            channel_name: 频道名称
        """
        if not self.redis_client:
            return
            
        try:
            channel_key = f"channel:{channel_name}"
            await self.redis_client.delete(channel_key)
            logger.info(f"Invalidated Redis cache for channel {channel_name}")
        except Exception as e:
            logger.error(f"Failed to invalidate channel cache: {str(e)}")
    
    def _handle_rtm_task_exception(self, task):
        """
        处理RTM消息发送任务的异常
        
        Args:
            task: 完成的asyncio.Task对象
        """
        if task.exception():
            logger.error(f"RTM message task failed: {task.exception()}", exc_info=task.exception())
    
    def _broadcast_supreme_gift(self, user_id: str, message: Optional[str]) -> None:
        """当存在非空文案时广播至尊礼物消息。"""
        if not message or not message.strip():
            logger.debug("Supreme gift without message, skip broadcasting")
            return
        task = asyncio.create_task(
            self.send_rtm_channel_message(
                user_id,
                RTMChannelMessageRequest(message=message, broadcast=True),
            )
        )
        task.add_done_callback(self._handle_rtm_task_exception)
        logger.debug("Supreme gift broadcasting message to all channels")
    
    def _validate_channel_name(self, channel_name: str) -> bool:
        """
        验证频道名称格式
        
        Args:
            channel_name: 频道名称
            
        Returns:
            bool: 验证结果
        """
        return True

    def _generate_rtm_api_authorization(self) -> str:
        """生成RTM API的Basic认证头"""
        if settings.AUTH_MODE == "customer" and settings.CUSTOMER_ID and settings.CUSTOMER_SECRET:
            # 使用Customer ID和Secret认证
            credentials = f"{settings.CUSTOMER_ID}:{settings.CUSTOMER_SECRET}"
        else:
            # 使用APP_ID和APP_CERTIFICATE认证（默认）
            credentials = f"{settings.APP_ID}:{settings.APP_CERTIFICATE}"
        
        encoded_credentials = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')
        return f"Basic {encoded_credentials}"

    async def _generate_rtm_headers(self, user_id: str) -> Dict[str, str]:
        """
        生成RTM Server API请求所需的头部信息
        
        RTM Server RESTful API需要Basic认证 + RTM token
        
        Args:
            user_id: 发送者用户ID
            
        Returns:
            Dict[str, str]: 包含认证信息的头部字典
        """
        # 生成RTM Token用于服务端API认证
        rtm_token_response = await token_manager.generate_rtm_token_enhanced(
            user_id=user_id,
            expiration_time=3600  # 1小时有效期，用于API调用
        )
        
        headers = {
            "Authorization": self._generate_rtm_api_authorization(),
            "Content-Type": "application/json;charset=utf-8",
            "x-agora-token": rtm_token_response.token,
            "x-agora-uid": user_id  # 使用user_id而不是UID
        }
        
        return headers

    async def _send_channel_message(
        self, 
        user_id: str, 
        channel_name: str, 
        message: str
    ) -> Dict[str, Any]:
        """
        向指定频道发送RTM消息
        
        Args:
            user_id: 发送者用户ID
            channel_name: 频道名称
            message: 消息内容
            
        Returns:
            Dict: API响应
        """
        url = f"{settings.RTM_API_BASE_URL}/dev/v2/project/{settings.APP_ID}/rtm/users/{user_id}/channel_messages"
        
        headers = await self._generate_rtm_headers(user_id)
        
        request_body = {
            "channel_name": channel_name,
            "payload": message,
            "enable_historical_messaging": False
        }
        
        # 重试机制
        for attempt in range(settings.RTM_API_RETRY_COUNT + 1):
            try:
                async with httpx.AsyncClient(timeout=settings.RTM_API_TIMEOUT) as client:
                    response = await client.post(url, headers=headers, json=request_body)
                    
                    if response.status_code == 200:
                        return response.json()
                    elif response.status_code == 429:  # 频率限制
                        if attempt < settings.RTM_API_RETRY_COUNT:
                            await asyncio.sleep(settings.RTM_API_RETRY_DELAY * (2 ** attempt))  # 指数退避
                            continue
                        else:
                            raise Exception(f"RTM API频率限制，已重试{settings.RTM_API_RETRY_COUNT}次")
                    else:
                        error_detail = response.text
                        raise Exception(f"RTM API请求失败: HTTP {response.status_code}, {error_detail}")
                        
            except httpx.TimeoutException:
                if attempt < settings.RTM_API_RETRY_COUNT:
                    await asyncio.sleep(settings.RTM_API_RETRY_DELAY)
                    continue
                else:
                    raise Exception(f"RTM API请求超时，已重试{settings.RTM_API_RETRY_COUNT}次")
            except httpx.RequestError as e:
                if attempt < settings.RTM_API_RETRY_COUNT:
                    await asyncio.sleep(settings.RTM_API_RETRY_DELAY)
                    continue
                else:
                    raise Exception(f"RTM API网络错误: {str(e)}")
        
        raise Exception("RTM API请求失败，已达到最大重试次数")

    async def _send_batch_channel_messages(
        self,
        user_id: str,
        channel_names: List[str],
        message: str
    ) -> Dict[str, Any]:
        """
        向多个频道批量发送RTM消息
        
        Args:
            user_id: 发送者用户ID
            channel_names: 频道名称列表
            message: 消息内容
            
        Returns:
            Dict: 批量发送结果
        """
        results = []
        successful_count = 0
        failed_count = 0
        
        # 使用信号量控制并发数，避免过多并发请求
        semaphore = asyncio.Semaphore(10)  # 最多10个并发请求
        
        async def send_to_channel(channel_name: str):
            async with semaphore:
                try:
                    result = await self._send_channel_message(
                        user_id=user_id,
                        channel_name=channel_name,
                        message=message
                    )
                    return {
                        "channel_name": channel_name,
                        "success": True,
                        "result": result
                    }
                except Exception as e:
                    return {
                        "channel_name": channel_name,
                        "success": False,
                        "error": str(e)
                    }
        
        # 并发发送到所有频道
        tasks = [send_to_channel(channel) for channel in channel_names]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                processed_results.append({
                    "channel_name": "unknown",
                    "success": False,
                    "error": str(result)
                })
            else:
                if result["success"]:
                    successful_count += 1
                else:
                    failed_count += 1
                processed_results.append(result)
        
        return {
            "total_channels": len(channel_names),
            "successful_channels": successful_count,
            "failed_channels": failed_count,
            "results": processed_results,
            "overall_success": failed_count == 0
        }

    async def create_channel(self, request: ChannelCreateRequest, user_id: str, uid: int) -> ChannelInfo:
        """
        创建直播间
        
        Args:
            request: 创建直播间请求
            user_id: 用户ID
            uid: 用户UID
            
        Returns:
            ChannelInfo: 直播间信息
        """
        if not self._validate_channel_name(request.channel_id):
            raise ValueError("Invalid channel name format")

        existing_channel = await self._repo.channels.get_channel_by_name(request.channel_id)
        if existing_channel:
            raise ValueError("Channel name already exists")

        channel = await self._repo.channels.create_channel(
            request.channel_id,
            uid,  # broadcaster_uid 就是用户的 uid
            user_id,
            request.token_address
        )
        # 先提交事务，确保读库可见
        await self._repo.db.commit()

        # 将频道信息缓存到Redis中，确保立即可查询
        await self._cache_channel_to_redis(channel)
        
        return await self._get_channel_complete_info(channel)
    
    async def get_channel(self, channel_id: int) -> Optional[ChannelInfo]:
        """
        获取直播间信息
        
        Args:
            channel_id: 直播间ID
            
        Returns:
            ChannelInfo: 直播间信息
        """
        channel = await self._repo.channels.get_channel_by_id(str(channel_id))
        
        if not channel:
            return None
        
        return await self._get_channel_complete_info(channel)
    
    async def get_channel_by_name(self, channel_id: str) -> Optional[ChannelInfo]:
        """
        根据直播间ID获取直播间信息
        
        Args:
            channel_id: 直播间ID
            
        Returns:
            ChannelInfo: 直播间信息
        """
        # 先尝试从Redis缓存获取
        redis_data = await self._get_channel_from_redis(channel_id)
        
        if redis_data:
            # 需要从数据库获取完整的channel对象来调用_get_channel_complete_info
            # 但我们可以利用缓存中的状态信息来优化查询
            channel = await self._repo.channels.get_channel_by_name(channel_id, status=redis_data.get('status'))
        else:
            # Redis中没有，直接查询数据库
            channel = await self._repo.channels.get_channel_by_name(channel_id)
        
        if not channel:
            return None
            
        # 如果从数据库查询到了，缓存到Redis
        if not redis_data and channel:
            await self._cache_channel_to_redis(channel)
        
        return await self._get_channel_complete_info(channel)
    
    async def get_channel_by_userid(self, user_id: str) -> Optional[ChannelInfo]:
        """
        根据主播用户名获取直播间信息
        
        Args:
            user_id: 主播用户名
            
        Returns:
            ChannelInfo: 直播间信息，如果未找到则返回None
        """
        try:
            # 根据用户ID查找该用户创建的频道（取最新的一个）
            stmt = select(LiveChannel).where(
                LiveChannel.user_id == user_id
            ).order_by(LiveChannel.created_at.desc()).limit(1)
            result = await self._repo.db.execute(stmt)
            channel = result.scalar_one_or_none()
            
            if not channel:
                logger.debug(f"No channel found for user: {user_id}")
                return None
            
            return await self._get_channel_complete_info(channel)
            
        except Exception as e:
            logger.error(f"Failed to get channel by {user_id}: {str(e)}")
            return None
    
    async def join_channel(
        self, 
        channel_id: str, 
        request: ChannelJoinRequest,
        user_id: str
    ) -> ChannelJoinSimpleResponse:
        """
        加入直播间
        
        Args:
            channel_id: 直播间ID
            request: 加入直播间请求
            user_id: 用户ID
            
        Returns:
            ChannelJoinSimpleResponse: 加入直播间响应
        """
        # 获取频道信息 - 先尝试从Redis缓存
        redis_data = await self._get_channel_from_redis(channel_id)
        
        if redis_data:
            # 验证频道状态
            if redis_data.get('status') == ChannelStatus.ENDED:
                raise ValueError("Channel has ended")
            # 从数据库获取完整channel对象
            channel = await self._repo.channels.get_channel_by_name(channel_id, status=redis_data.get('status'))
        else:
            # Redis中没有，查询数据库
            channel = await self._repo.channels.get_channel_by_name(channel_id)
            
        if not channel:
            raise ValueError("Channel not found")
            
        # 缓存到Redis（如果之前没有缓存）
        if not redis_data and channel:
            await self._cache_channel_to_redis(channel)
        
        # 检查频道状态
        if channel.status == ChannelStatus.ENDED:
            raise ValueError("Channel has ended")

        # 记录观众信息
        existing_audience = await self._repo.audiences.get_audience(channel.id, user_id)
        if not existing_audience and request.role == "audience":
            # 新观众，增加观众数
            await self._repo.channels.update_channel_audience_count(channel.id, 1)
        
        await self._repo.audiences.add_audience(
            sid="",  # 在直接加入时，sid可能为空
            channel_name=channel.channel_name,
            user_id=user_id,
            platform=0,  # 默认平台类型为其他
            role=request.role,
            channel_id=channel.id
        )
        
        # 缓存用户状态
        await self._cache_user_channel_status(user_id, channel.id, True)
        
        return ChannelJoinSimpleResponse(
            channel_info=await self._get_channel_complete_info(channel),
            joined=True
        )
    
    async def leave_channel(
        self, 
        channel_id: str,
        user_id: str
    ) -> bool:
        """
        离开频道
        
        Args:
            channel_id: 频道ID
            user_id: 用户ID
            
        Returns:
            bool: 操作结果
        """
        # 获取频道信息
        channel = await self._repo.channels.get_channel_by_name(channel_id)
        if not channel:
            return False
        
        # 获取观众记录
        audience = await self._repo.audiences.get_audience(channel.id, user_id)
        if audience and audience.role == "audience":
            # 减少观众数
            await self._repo.channels.update_channel_audience_count(channel.id, -1)
        
        # 更新观众记录
        success = await self._repo.audiences.remove_audience(channel.id, user_id)
        
        # 更新缓存状态
        await self._cache_user_channel_status(user_id, channel.id, False)
        
        return success
    
    async def start_live(self, channel_id: str, broadcaster_uid: int) -> bool:
        """
        开始直播
        
        Args:
            channel_id: 频道ID
            broadcaster_uid: 主播UID
            
        Returns:
            bool: 操作结果
        """
        channel = await self._repo.channels.get_channel_by_name(channel_id)
        if not channel:
            return False
        
        if channel.broadcaster_uid != broadcaster_uid:
            raise ValueError("Only broadcaster can start the live")
        
        if channel.status == ChannelStatus.LIVE:
            return True  # 已经在直播中
        
        # 更新频道状态
        success = await self._repo.channels.update_channel_status(channel.id, ChannelStatus.LIVE)
        return success
    
    async def stop_live(self, channel_id: str, broadcaster_uid: int) -> bool:
        """
        结束直播
        
        Args:
            channel_id: 频道ID
            broadcaster_uid: 主播UID
            
        Returns:
            bool: 操作结果
        """
        channel = await self._repo.channels.get_channel_by_name(channel_id)
        if not channel:
            return False
        
        if channel.broadcaster_uid != broadcaster_uid:
            raise ValueError("Only broadcaster can stop the live")
        
        # 更新频道状态（会自动清零观众数）
        await self._repo.channels.update_channel_status(channel.id, ChannelStatus.ENDED)
        
        # 清除所有观众记录
        await self._repo.audiences.clear_all_active_audiences(channel.id)
        
        # 创建或更新频道统计数据
        try:
            # 获取最新的频道信息（包含duration等更新后的数据）
            updated_channel = await self._repo.channels.get_channel_by_id(channel.id)
            if updated_channel:
                # 创建或更新统计记录
                stats = await self._repo.stats.create_or_update_stats(updated_channel)
                
                # 获取并更新礼物统计
                total_gift_value = await self._repo.gifts.get_channel_total_gift_value(channel_id)
                if total_gift_value > 0:
                    await self._repo.stats.update_gift_stats(channel_id, 0, 0)
                    # 直接设置总值，避免累加
                    stats.total_gift_value = total_gift_value
                    await self._repo.db.flush()
                
                # 获取唯一打赏者数量
                unique_gifters = await self._repo.gifts.get_unique_gifters_count(channel_id)
                stats.unique_gifters = unique_gifters
                
                # 获取累计观众数
                total_audience = await self._repo.audiences.get_total_unique_audience_count(channel.id)
                stats.total_audience = total_audience
                
                await self._repo.db.flush()
                logger.info(f"频道 {channel_id} 统计数据已保存: 时长={stats.live_duration}秒, 礼物总值=${stats.total_gift_value/100:.2f}")
        except Exception as e:
            logger.error(f"保存频道统计数据失败: {str(e)}")
        
        return True
    
    async def get_channel_stats(self, channel_name: str) -> ChannelStatsResponse:
        """
        获取频道统计信息
        
        Args:
            channel_name: 频道名称
            
        Returns:
            ChannelStatsResponse: 频道统计信息
        """
        channel = await self._repo.channels.get_channel_by_name(channel_name)
        if not channel:
            raise ValueError("Channel not found")
        
        # 计算直播时长
        duration = 0
        if channel.started_at:
            end_time = channel.ended_at or utc_now_naive()
            duration = int((end_time - channel.started_at).total_seconds())
        
        # 获取礼物交易总额（USD分）
        live_trading_volume = await self._repo.gifts.get_channel_total_gift_value(channel_name)
        
        # 获取礼物总数（所有gift_count的总和）
        gift_count = await self._repo.gifts.get_channel_total_gift_count(channel_name)
        
        # 当前观众数优先从 Redis 读取
        current_audience_val = channel.current_audience
        try:
            if self.redis_client is None:
                await self.init_redis()
            if self.redis_client:
                redis_key = f"channel:{channel_name}"
                val = await self.redis_client.hget(redis_key, "current_audience")
                if val is not None:
                    current_audience_val = int(val)
        except Exception:
            # 读取失败则回退到数据库值
            pass

        return ChannelStatsResponse(
            channel_id=channel_name,
            current_audience=current_audience_val,
            duration=duration,
            live_trading_volume=live_trading_volume,
            gift_count=gift_count
        )
    
    async def get_live_channels(self, size: int = 10, page: int = 1) -> LiveChannelsResponse:
        """
        获取所有正在直播的频道列表
        
        Args:
            size: 每页数量
            page: 页码（从1开始）
            
        Returns:
            LiveChannelsResponse: 包含频道列表和总数的响应
        """
        # 计算偏移量
        offset = (page - 1) * size
        
        # 顺序获取频道列表和总数（AsyncSession 不支持并发操作）
        channels = await self._repo.channels.get_live_channels(size, offset)
        total = await self._repo.channels.get_live_channels_count()
        
        # 如果没有频道，直接返回空结果
        if not channels:
            return LiveChannelsResponse(items=[], page=page, size=size, total=total)
        
        # 一次性提取所有需要的ID列表，避免重复计算
        token_addresses = [channel.token_address for channel in channels if channel.token_address]
        user_ids = [channel.user_id for channel in channels]
        
        # 串行执行批量查询以避免AsyncSession并发问题
        # AsyncSession不支持同时执行多个查询，并行可能导致连接状态混乱
        token_info = await self._get_token_info_batch(token_addresses)
        authors_info = await self._get_authors_info_batch(user_ids)
        
        # 构建响应，避免重复的条件检查
        channel_infos = [
            self._channel_to_info(
                channel,
                cover=token_info.get(channel.token_address, {}).get("cover"),
                author_info=authors_info.get(channel.user_id, {}),
                token_name=token_info.get(channel.token_address, {}).get("name")
            )
            for channel in channels
        ]
        
        return LiveChannelsResponse(
            items=channel_infos,
            page=page,
            size=size,
            total=total
        )
    
    async def handle_webhook_event(self, event: WebhookEvent) -> bool:
        """
        处理 Webhook 事件
        
        Args:
            event: Webhook 事件
            
        Returns:
            bool: 处理结果
        """
        return await self.webhook_handler.handle_event(event)
    
    async def get_channel_audiences(self, channel_name: str) -> ChannelAudienceResponse:
        """
        获取频道观众信息
        
        Args:
            channel_name: 频道名称
            
        Returns:
            ChannelAudienceResponse: 频道观众信息
        """
        # 首先查询频道是否存在且处于LIVE状态
        channel = await self._repo.channels.get_channel_by_name(channel_name, status=ChannelStatus.LIVE)
        if not channel:
            raise ValueError("Channel not found or not live")
        
        # 获取频道观众列表
        audiences = await self._repo.audiences.get_channel_audiences_by_name(channel_name, active_only=True)
        
        # 获取所有观众的user_id列表
        user_ids = [audience.user_id for audience in audiences]
        
        # 从数据库中查询Author信息
        authors_info = {}
        if user_ids:
            # 查询Author表获取username和avatar
            stmt = select(Author).where(Author.user_id.in_(user_ids))
            result = await self._repo.db.execute(stmt)
            authors = result.scalars().all()
            
            # 构建user_id到author信息的映射
            for author in authors:
                authors_info[author.user_id] = {
                    "username": author.username,
                    "avatar": author.avatar
                }
        
        # 转换为响应模型
        audience_infos = []
        for audience in audiences:
            # 获取对应的author信息
            author_info = authors_info.get(audience.user_id, {
                "username": "Unknown",
                "avatar": None
            })
            
            # 获取用户的 UID
            uid = token_manager.get_uid_by_user_id(audience.user_id)
            
            audience_info = ChannelAudienceInfo(
                user_id=audience.user_id,
                uid=uid,
                username=author_info["username"],
                avatar=author_info["avatar"],
                role=audience.role,
                platform=audience.platform,
                joined_at=add_utc_timezone(audience.joined_at)
            )
            audience_infos.append(audience_info)
        
        return ChannelAudienceResponse(
            channel_name=channel_name,
            channel_status=channel.status,
            total_audience=len(audiences),
            active_audience=len(audiences),  # 因为我们只查询了活跃观众
            audiences=audience_infos
        )
    
    async def get_user_info_by_uid(self, uid: int, channel_name: str) -> ChannelAudienceInfo:
        """
        根据UID和频道名称获取用户信息
        
        Args:
            uid: Agora UID
            channel_name: 频道名称
            
        Returns:
            ChannelAudienceInfo: 用户信息
        """
        # 首先通过token_manager获取user_id
        user_id = await token_manager.get_user_id_by_uid(uid)
        if not user_id:
            raise ValueError(f"无法通过 UID {uid} 获取用户ID")
        
        # 查询观众记录
        audience = await self._repo.audiences.get_active_audience_by_user(channel_name, user_id)
        if not audience:
            raise ValueError(f"用户 {user_id} (UID: {uid}) 不在频道 {channel_name} 中")
        
        # 从数据库中查询Author信息
        stmt = select(Author).where(Author.user_id == user_id)
        result = await self._repo.db.execute(stmt)
        author = result.scalar_one_or_none()
        
        # 准备返回的用户信息
        username = author.username if author else "Unknown"
        avatar = author.avatar if author else None
        
        return ChannelAudienceInfo(
            user_id=user_id,
            uid=uid,
            username=username,
            avatar=avatar,
            role=audience.role,
            platform=audience.platform,
            joined_at=add_utc_timezone(audience.joined_at)
        )
    
    async def _cache_user_channel_status(self, user_id: str, channel_id: int, is_active: bool):
        """缓存用户频道状态"""
        await self.init_redis()
        if self.redis_client:
            key = f"user_channel:{user_id}:{channel_id}"
            if is_active:
                await self.redis_client.setex(key, 3600, "active")  # 1小时过期
            else:
                await self.redis_client.delete(key)
    
    async def _get_token_cover(self, token_address: str) -> Optional[str]:
        """
        获取单个代币的封面图片URL
        
        Args:
            token_address: 代币地址
            
        Returns:
            Optional[str]: 封面图片URL，如果没有则返回None
        """
        if not token_address:
            return None
            
        # 首先从Redis获取
        await self.init_redis()
        if self.redis_client:
            redis_key = f"token_cover:{token_address}"
            try:
                cover_url = await self.redis_client.get(redis_key)
                if cover_url:
                    logger.debug(f"从Redis获取到代币封面: {token_address} -> {cover_url}")
                    return cover_url
            except Exception as e:
                logger.warning(f"从Redis获取代币封面失败: {token_address}, error: {str(e)}")
        
        # Redis中没有，从Pair表获取
        try:
            stmt = select(Pair.base_image_url).where(Pair.base == token_address)
            result = await self._repo.db.execute(stmt)
            cover_url = result.scalar_one_or_none()
            
            if cover_url:
                logger.debug(f"从Pair表获取到代币封面: {token_address} -> {cover_url}")
                # 缓存到Redis
                if self.redis_client:
                    try:
                        await self.redis_client.setex(redis_key, 3600, cover_url)  # 1小时过期
                    except Exception as e:
                        logger.warning(f"缓存代币封面到Redis失败: {token_address}, error: {str(e)}")
                return cover_url
            else:
                logger.debug(f"未找到代币封面: {token_address}")
                return None
                
        except Exception as e:
            logger.error(f"从Pair表获取代币封面失败: {token_address}, error: {str(e)}")
            return None
    
    async def _get_token_info_from_redis(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        从Redis获取单个代币的完整信息
        
        Args:
            token_address: 代币地址
            
        Returns:
            Optional[Dict[str, Any]]: 代币信息，如果不存在则返回None
        """
        if not token_address:
            return None
            
        await self.init_redis()
        if not self.redis_client:
            logger.warning("Redis连接不可用，无法获取token信息")
            return None
        
        try:
            # 从Redis获取token缓存数据
            # 参考memecoin服务的实现，key格式为: memecoin:{token_address}
            redis_key = f"memecoin:{token_address}"
            token_cache = await self.redis_client.hgetall(redis_key)
            
            if not token_cache:
                logger.debug(f"Redis中未找到token {token_address} 的信息")
                return None
            
            # 处理Redis中的字节数据，转换为字符串
            def decode_value(value):
                if isinstance(value, bytes):
                    return value.decode('utf-8')
                return str(value) if value is not None else ''
            
            # 提取代币信息
            token_info = {
                "token_name": decode_value(token_cache.get(b'name') or token_cache.get('name', '')),
                "token_symbol": decode_value(token_cache.get(b'symbol') or token_cache.get('symbol', '')),
                "image_url": decode_value(token_cache.get(b'image_url') or token_cache.get('image_url', '')),
                "price": decode_value(token_cache.get(b'price') or token_cache.get('price', '0')),
                "market_cap": decode_value(token_cache.get(b'market_cap') or token_cache.get('market_cap', '0')),
                "price_change_percent": decode_value(token_cache.get(b'price_change_percent') or token_cache.get('price_change_percent', '0')),
            }
            
            # 过滤掉空值
            token_info = {k: v for k, v in token_info.items() if v}
            
            if token_info:
                logger.debug(f"从Redis获取到token {token_address} 信息: price={token_info.get('price', 'N/A')}")
                return token_info
            else:
                logger.debug(f"Redis中token {token_address} 信息为空")
                return None
                
        except Exception as e:
            logger.error(f"从Redis获取token {token_address} 信息失败: {str(e)}")
            return None

    async def _get_token_info_batch(self, token_addresses: List[str]) -> Dict[str, Dict[str, Optional[str]]]:
        """
        批量获取多个代币的完整信息（名称和封面）
        
        Args:
            token_addresses: 代币地址列表
            
        Returns:
            Dict[str, Dict[str, Optional[str]]]: 代币地址到信息的映射
            {
                "token_address": {
                    "name": "token_name",
                    "cover": "cover_url"
                }
            }
        """
        # 过滤有效的代币地址
        valid_token_addresses = [addr for addr in token_addresses if addr]
        if not valid_token_addresses:
            return {}
            
        token_info = {}
        remaining_tokens = []
        
        # 首先从Redis批量获取封面（名称不缓存，因为变化较少且查询成本低）
        await self.init_redis()
        if self.redis_client:
            try:
                redis_keys = [f"token_cover:{addr}" for addr in valid_token_addresses]
                cached_covers = await self.redis_client.mget(redis_keys)
                
                # 分离已缓存封面和需要查询的token
                for i, token_address in enumerate(valid_token_addresses):
                    if cached_covers[i]:
                        # 只有封面被缓存，仍需查询名称
                        token_info[token_address] = {"cover": cached_covers[i], "name": None}
                    else:
                        remaining_tokens.append(token_address)
                        
            except Exception as e:
                logger.warning(f"从Redis批量获取代币封面失败: {str(e)}")
                remaining_tokens = valid_token_addresses
        else:
            remaining_tokens = valid_token_addresses
        
        # 从Pair表批量获取所有token信息（无论是否有缓存的封面）
        try:
            # 查询所有token的完整信息
            stmt = select(Pair.base, Pair.base_name, Pair.base_image_url).where(Pair.base.in_(valid_token_addresses))
            result = await self._repo.db.execute(stmt)
            pairs = result.fetchall()
            
            # 准备Redis缓存数据（只缓存封面）
            redis_data = {}
            
            # 处理数据库查询结果
            for token_address, base_name, base_image_url in pairs:
                # 标准化数据
                name = base_name if base_name else None
                cover = base_image_url if base_image_url else None
                
                # 如果已有缓存的封面，使用数据库的名称更新
                if token_address in token_info:
                    token_info[token_address]["name"] = name
                else:
                    token_info[token_address] = {"name": name, "cover": cover}
                
                # 准备缓存封面到Redis
                if cover and token_address in remaining_tokens:
                    redis_data[f"token_cover:{token_address}"] = cover
            
            # 批量缓存封面到Redis
            if redis_data and self.redis_client:
                try:
                    await self.redis_client.mset(redis_data)
                except Exception as e:
                    logger.warning(f"批量缓存代币封面到Redis失败: {str(e)}")
            
            # 为缺失的代币设置默认值
            for token_address in valid_token_addresses:
                if token_address not in token_info:
                    token_info[token_address] = {"name": None, "cover": None}
                    
        except Exception as e:
            logger.error(f"从Pair表批量获取代币信息失败: {str(e)}")
            # 错误情况下，设置默认值，保留已缓存的封面
            for token_address in valid_token_addresses:
                if token_address not in token_info:
                    token_info[token_address] = {"name": None, "cover": None}
        
        return token_info
    
    async def _get_authors_info_batch(self, user_ids: List[str]) -> Dict[str, Dict[str, Optional[str]]]:
        """
        批量获取多个用户的作者信息
        
        Args:
            user_ids: 用户ID列表
            
        Returns:
            Dict[str, Dict[str, Optional[str]]]: 用户ID到作者信息的映射
        """
        # 过滤有效的用户ID
        valid_user_ids = [uid for uid in user_ids if uid]
        if not valid_user_ids:
            return {}
        
        # 默认值
        default_author_info = {"name": None, "username": "Unknown", "avatar": None}
        
        try:
            # 批量查询Author表
            stmt = select(Author.user_id, Author.name, Author.username, Author.avatar).where(Author.user_id.in_(valid_user_ids))
            result = await self._repo.db.execute(stmt)
            authors = result.fetchall()
            
            # 构建映射，使用字典推导式和默认值
            authors_info = {
                user_id: {"name": name, "username": username, "avatar": avatar}
                for user_id, name, username, avatar in authors
            }
            
            # 为缺失的用户设置默认值
            return {
                user_id: authors_info.get(user_id, default_author_info)
                for user_id in valid_user_ids
            }
                    
        except Exception as e:
            logger.error(f"批量获取用户信息失败: {str(e)}")
            # 错误情况下返回所有用户的默认值
            return {user_id: default_author_info for user_id in valid_user_ids}
    
    async def start_recording(self, channel_id: str) -> Dict[str, Any]:
        """开始录制"""
        import uuid
        
        # 查找频道
        channel = await self._repo.channels.get_channel_by_name(channel_id)
        if not channel:
            raise ValueError("Channel not found")
        
        # 生成简单的resource_id和sid
        resource_id = str(uuid.uuid4())[:8]
        sid = str(uuid.uuid4())[:8]
        
        # 创建录制会话记录
        recording_session = await self._repo.recordings.create_recording_session(
            channel.id, resource_id, sid
        )
        
        return {
            "resource_id": resource_id,
            "sid": sid,
            "channel_id": channel_id,
            "status": RecordingStatus.STARTED
        }
    
    async def stop_recording(self, resource_id: str, sid: str) -> Dict[str, Any]:
        """停止录制"""
        # 查找录制会话
        recording_session = await self._repo.recordings.get_recording_session(resource_id, sid)
        if not recording_session:
            raise ValueError("Recording session not found")
        
        # 更新状态
        success = await self._repo.recordings.update_recording_status(resource_id, sid, RecordingStatus.STOPPED)
        
        return {
            "message": "Recording stopped successfully",
            "resource_id": resource_id,
            "sid": sid,
            "success": success
        }

    async def _get_channel_complete_info(self, channel: LiveChannel) -> ChannelInfo:
        """
        获取频道的完整信息，包括作者信息和token信息
        
        Args:
            channel: 频道对象
            
        Returns:
            ChannelInfo: 包含完整信息的频道对象
        """
        # 获取作者信息
        author_info = None
        if channel.user_id:
            authors_data = await self._get_authors_info_batch([channel.user_id])
            author_info = authors_data.get(channel.user_id, {})
        
        # 获取token信息
        token_info = None
        if channel.token_address:
            token_data = await self._get_token_info_batch([channel.token_address])
            token_info = token_data.get(channel.token_address, {})
        
        return self._channel_to_info(
            channel,
            cover=token_info.get("cover") if token_info else None,
            author_info=author_info,
            token_name=token_info.get("name") if token_info else None
        )
    
    def _channel_to_info(
        self, 
        channel: LiveChannel, 
        cover: Optional[str] = None,
        author_info: Optional[Dict[str, Optional[str]]] = None,
        token_name: Optional[str] = None
    ) -> ChannelInfo:
        """将数据库模型转换为API响应模型"""
        # 提取作者信息
        if author_info:
            broadcaster_name = author_info.get("name")
            broadcaster_username = author_info.get("username")
            broadcaster_avatar = author_info.get("avatar")
        else:
            broadcaster_name = None
            broadcaster_username = "Unknown"
            broadcaster_avatar = None
            
        return ChannelInfo(
            channel_id=channel.channel_name,
            broadcaster_id=channel.user_id,
            broadcaster_uid=channel.broadcaster_uid,
            broadcaster_name=broadcaster_name,
            broadcaster_username=broadcaster_username,
            broadcaster_avatar=broadcaster_avatar,
            status=channel.status,
            current_audience=channel.current_audience,
            created_at=add_utc_timezone(channel.created_at),
            started_at=add_utc_timezone(channel.started_at) if channel.started_at else None,
            ended_at=add_utc_timezone(channel.ended_at) if channel.ended_at else None,
            cover=cover,
            token_name=token_name,
            token_address=channel.token_address
        )

    async def create_gift_record(
        self,
        channel_name: str,
        tipper: str,
        gift_id: int,
        txid: str,
        quantity: int = 1,
        message: Optional[str] = None,
        operation_id: Optional[str] = None
    ):
        """
        创建礼物购买记录
        
        Args:
            channel_name: 直播间名称
            tipper: 发送者用户ID
            gift_id: 礼物ID
            txid: 交易哈希
            quantity: 购买数量，默认为1
            message: 打赏留言（可选）
            operation_id: 客户端生成的唯一操作ID（可选）
            
        Returns:
            ChannelGiftRecord: 礼物记录
        """
        # 获取频道ID（可选）
        channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
        channel_id = channel.id if channel else None
        
        # 获取礼物价格并计算总价值
        gift_value = 0
        try:
            # 优先从缓存获取礼物价格
            gift_info = await gift_cache.get_gift_by_id(gift_id)
            if gift_info and gift_info.is_active:
                gift_price = gift_info.price
                gift_value = gift_price * quantity
                logger.debug(f"Calculated gift value from cache: {gift_value} = {gift_price} * {quantity}")
            else:
                # 缓存不可用，从数据库获取价格
                gift_price = await self.get_gift_price(gift_id)
                if gift_price is not None:
                    gift_value = gift_price * quantity
                    logger.debug(f"Calculated gift value from database: {gift_value} = {gift_price} * {quantity}")
                else:
                    logger.warning(f"Gift price not found for gift_id {gift_id}, using value 0")
        except Exception as e:
            logger.error(f"Error calculating gift value for gift_id {gift_id}: {str(e)}")
        
        # 创建礼物记录
        await self._repo.gifts.create_gift_record(
            channel_name=channel_name,
            tipper=tipper,
            gift_id=gift_id,
            txid=txid,
            channel_id=channel_id,
            quantity=quantity,
            gift_value=gift_value,
            message=message,
            operation_id=operation_id
        )

    
    async def get_channel_gifts(
        self,
        channel_name: str,
        page: int = 1,
        page_size: int = 10
    ) -> ChannelGiftsResponse:
        """
        获取频道的礼物记录
        
        Args:
            channel_name: 频道名称
            page: 页码
            page_size: 每页大小
            
        Returns:
            ChannelGiftsResponse: 礼物列表响应
        """
        # 首先查询频道是否存在
        channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
        if not channel:
            raise ValueError("Channel not found")
        
        offset = (page - 1) * page_size
        
        # 使用频道ID查询礼物记录
        gifts = await self._repo.gifts.get_channel_gifts_by_id(
            channel_id=channel.id,
            limit=page_size,
            offset=offset
        )
        
        # 获取总记录数
        total = await self._repo.gifts.get_channel_gifts_count_by_id(channel.id)
        
        # 使用缓存转换礼物记录，优先从缓存获取礼物详情
        gift_records = []
        for gift in gifts:
            gift_record = await self._gift_to_record_with_cache(gift)
            gift_records.append(gift_record)
        
        return ChannelGiftsResponse(
            items=gift_records,
            total=total,
            page=page,
            page_size=page_size
        )
    
    async def _gift_to_record_with_cache(self, gift: ChannelGiftHistory) -> ChannelGiftRecord:
        """
        将数据库模型转换为API响应模型（使用缓存优化）
        
        Args:
            gift: 礼物记录模型
            
        Returns:
            ChannelGiftRecord: 礼物记录响应模型
        """
        # 优先从缓存获取礼物详情
        gift_info = await gift_cache.get_gift_by_id(gift.gift_id)
        
        if gift_info:
            # 从缓存获取成功
            gift_name = gift_info.name
            gift_price = gift_info.price
            gift_currency = gift_info.currency
        else:
            # 缓存中没有找到，使用数据库查询作为后备方案
            try:
                gift_detail = await self._repo.gift_list.get_gift_by_id(gift.gift_id)
                if gift_detail:
                    meta = gift_detail.meta or {}
                    gift_name = meta.get('name', f'Gift {gift.gift_id}')
                    gift_price = gift_detail.price or 0
                    gift_currency = gift_detail.currency or 'USD'
                else:
                    # 礼物不存在，使用默认值
                    gift_name = f'Gift {gift.gift_id}'
                    gift_price = 0
                    gift_currency = 'USD'
            except Exception as e:
                logger.warning(f"Failed to get gift details for gift_id {gift.gift_id}: {str(e)}")
                gift_name = f'Gift {gift.gift_id}'
                gift_price = 0
                gift_currency = 'USD'
        
        return ChannelGiftRecord(
            channel_name=gift.channel_name,
            tipper=gift.tipper,
            gift_id=gift.gift_id,
            gift_name=gift_name,
            gift_count=gift.gift_count,
            gift_price=gift_price,
            gift_currency=gift_currency,
            message=gift.message,
            operation_id=gift.operation_id,
            txid=gift.txid,
            created_at=add_utc_timezone(gift.created_at)
        )
    
    def _gift_to_record(self, gift: ChannelGiftHistory, gift_detail=None) -> ChannelGiftRecord:
        """
        将数据库模型转换为API响应模型（遗留方法，保留兼容性）
        
        Args:
            gift: 礼物记录模型
            gift_detail: 礼物详细信息（来自gift_list表）
            
        Returns:
            ChannelGiftRecord: 礼物记录响应模型
        """
        # 从gift_detail中提取礼物信息
        gift_name = None
        gift_price = 0
        
        if gift_detail:
            # 从meta JSONB字段中提取礼物名称
            meta = gift_detail.meta or {}
            gift_name = meta.get('name', f'Gift {gift.gift_id}')
            gift_price = gift_detail.price or 0
            gift_currency = gift_detail.currency or 'USD'
        else:
            # 如果没有找到礼物详情，使用默认值
            gift_name = f'Gift {gift.gift_id}'
            gift_price = 0
            gift_currency = 'USD'
        
        return ChannelGiftRecord(
            channel_name=gift.channel_name,
            tipper=gift.tipper,
            gift_id=gift.gift_id,  # 修复：使用 gift.gift_id 而不是 gift.id
            gift_name=gift_name,
            gift_count=gift.gift_count,
            gift_price=gift_price,
            gift_currency=gift_currency,
            message=gift.message,
            operation_id=gift.operation_id,
            txid=gift.txid,
            created_at=add_utc_timezone(gift.created_at)
        )

    async def send_rtm_channel_message(
        self,
        user_id: str,
        request: RTMChannelMessageRequest
    ) -> RTMChannelMessageResponse:
        """
        发送RTM消息（统一接口）
        
        Args:
            user_id: 发送者用户ID
            request: 消息发送请求
            
        Returns:
            RTMChannelMessageResponse: 发送结果
        """
        try:
            channel_names = []
            
            # 确定目标频道列表
            broadcast_value = request.broadcast if request.broadcast is not None else False
            if broadcast_value and not request.channel_names:
                # 广播到所有正在直播的频道
                logger.info(f"准备广播RTM消息到所有正在直播的频道: user={user_id}")
                
                channels = await self._repo.channels.get_live_channels(limit=1000, offset=0)
                channel_names = [channel.channel_name for channel in channels]
                
                if not channel_names:
                    raise ValueError("当前没有正在直播的频道")
                    
            else:
                # 发送到指定频道
                logger.info(f"准备向指定频道发送RTM消息: user={user_id}, channels={len(request.channel_names)}")
                
                # 验证频道是否存在（可选，跳过不存在的频道）
                existing_channels = []
                for channel_name in request.channel_names:
                    channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
                    if channel:
                        existing_channels.append(channel_name)
                    else:
                        logger.warning(f"频道 {channel_name} 不存在，跳过")
                
                if not existing_channels:
                    raise ValueError("所有指定的频道都不存在")
                
                channel_names = existing_channels
            
            # 调用Agora RTM API批量发送消息
            result = await self._send_batch_channel_messages(
                user_id=user_id,
                channel_names=channel_names,
                message=request.message
            )
            
            # 构造响应消息
            if broadcast_value:
                message = f"已广播消息到所有正在直播的频道"
            else:
                message = f"已向{len(request.channel_names)}个指定频道发送消息"
            
            logger.info(f"RTM消息发送完成: user={user_id}, total={result['total_channels']}, success={result['successful_channels']}, failed={result['failed_channels']}")
            
            response = RTMChannelMessageResponse(
                total_channels=result['total_channels'],
                successful_channels=result['successful_channels'],
                failed_channels=result['failed_channels'],
                results=result['results'],
                overall_success=result['overall_success'],
                message=message
            )
            
            return response
            
        except Exception as e:
            logger.error(f"RTM消息发送失败: user={user_id}, error={str(e)}")
            raise

    async def get_gift_list(
        self, 
        min_price: Optional[int] = None, 
        max_price: Optional[int] = None,
        language: Optional[str] = None
    ) -> GiftListResponse:
        """
        获取礼物列表
        
        按sort_order从小到大排序返回所有礼物，支持价格过滤和多语言
        
        Args:
            min_price: 最小价格（USD分），大于等于此价格
            max_price: 最大价格（USD分），小于等于此价格
            language: 语言代码，支持 th, ja, ko, es, zh-tw
            
        Returns:
            GiftListResponse: 礼物列表响应
        """
        try:
            # 使用缓存获取礼物列表，支持价格过滤
            cached_gifts = await gift_cache.get_gifts_by_price_range(
                min_price=min_price, 
                max_price=max_price
            )
            
            # 转换为响应模型
            gift_items = [self._cached_gift_to_item(gift, language) for gift in cached_gifts]
            
            logger.info(f"获取礼物列表成功: total={len(gift_items)}, size={len(gift_items)}, 价格过滤: min_price={min_price}, max_price={max_price}")
            
            return GiftListResponse(
                items=gift_items,
                total=len(gift_items)
            )
            
        except Exception as e:
            logger.error(f"获取礼物列表失败: error={str(e)}")
            raise
    
    def _gift_list_to_item(self, gift) -> GiftListItem:
        """将数据库模型转换为API响应模型"""
        # 从meta JSONB字段中提取礼物信息
        meta = gift.meta or {}
        gift_name = meta.get('name', f'Gift {gift.id}')
        description = meta.get('description')
        animation_md5 = meta.get('animation_md5')
        animation_hd_md5 = meta.get('animation_hd_md5')
        # 基于普通动画URL生成高清URL（按约定将 .svga 替换为 -HD.svga）
        animation_hd_url = None
        if gift.animation:
            if gift.animation.endswith('.svga'):
                animation_hd_url = gift.animation.replace('.svga', '-HD.svga')
            else:
                animation_hd_url = gift.animation  # 回退：非svga则保持原样
        
        return GiftListItem(
            id=gift.id,
            name=gift_name,
            description=description,
            price=gift.price,
            currency=gift.currency,
            icon_url=gift.cover,  # 数据库字段名是cover
            animation_url=gift.animation,  # 数据库字段名是animation
            animation_md5=animation_md5,
            animation_hd_url=animation_hd_url,
            animation_hd_md5=animation_hd_md5,
            category=gift.category
        )
    
    def _cached_gift_to_item(self, gift_info, language: Optional[str] = None) -> GiftListItem:
        """将缓存的礼物信息转换为API响应模型"""
        def get_translated_name(gift_info, language: Optional[str]) -> str:
            """根据语言参数获取翻译后的礼物名称"""
            if not language:
                return gift_info.name  # 默认返回英文名称
            
            # 语言代码到meta字段的映射
            lang_field_map = {
                'th': 'name_th',        # 泰语
                'ja': 'name_ja',        # 日语
                'ko': 'name_ko',        # 韩语
                'es': 'name_es',        # 西班牙语
                'zh-tw': 'name_zh_tw'   # 繁体中文
            }
            
            field_name = lang_field_map.get(language.lower())
            if not field_name:
                return gift_info.name  # 不支持的语言，返回英文
            
            # 尝试从gift_info的额外数据中获取翻译（如果缓存支持）
            if hasattr(gift_info, 'meta') and gift_info.meta and field_name in gift_info.meta:
                translated_name = gift_info.meta[field_name]
                if translated_name and translated_name.strip():
                    return translated_name
            
            return gift_info.name  # 没有找到翻译，返回英文名称
        
        return GiftListItem(
            id=gift_info.id,
            name=get_translated_name(gift_info, language),
            description=gift_info.description,
            price=gift_info.price,
            currency=gift_info.currency,
            icon_url=gift_info.icon_url,
            animation_url=gift_info.animation_url,
            animation_md5=gift_info.animation_md5,
            animation_hd_url=(
                getattr(gift_info, 'animation_hd_url', None)
                if getattr(gift_info, 'animation_hd_url', None) is not None
                else (
                    gift_info.animation_url.replace('.svga', '-HD.svga')
                    if gift_info.animation_url and gift_info.animation_url.endswith('.svga')
                    else gift_info.animation_url
                )
            ),
            animation_hd_md5=(
                getattr(gift_info, 'animation_hd_md5', None)
                if getattr(gift_info, 'animation_hd_md5', None) is not None
                else (gift_info.meta.get('animation_hd_md5') if getattr(gift_info, 'meta', None) else None)
            ),
            category=gift_info.category
        )

    async def get_gift_price(self, gift_id: int) -> Optional[int]:
        """
        获取礼物价格（USD）
        
        Args:
            gift_id: 礼物ID
            
        Returns:
            Optional[int]: 礼物价格（USD），如果礼物不存在则返回None
        """
        try:
            gift = await self._repo.gift_list.get_gift_by_id(gift_id)
            if gift:
                return gift.price
            return None
        except Exception as e:
            logger.error(f"获取礼物价格失败: gift_id={gift_id}, error={str(e)}")
            return None

    async def check_operation_exists(self, operation_id: str) -> bool:
        """
        检查操作ID是否已存在
        
        Args:
            operation_id: 客户端生成的唯一操作ID
            
        Returns:
            bool: 是否存在
        """
        try:
            existing_gift = await self._repo.gifts.get_gift_by_operation_id(operation_id)
            return existing_gift is not None
        except Exception as e:
            logger.error(f"Error checking operation existence: {str(e)}")
            # 在发生数据库错误时，为了安全起见，返回True以防止重复操作
            return True

    async def get_channel_token_info(self, channel_name: str) -> Dict[str, Any]:
        """
        获取频道绑定的代币信息
        
        Args:
            channel_name: 频道名称
            
        Returns:
            Dict[str, Any]: 频道代币信息响应
        """
        try:
            # 先尝试从Redis缓存中获取频道信息
            redis_data = await self._get_channel_from_redis(channel_name)
            
            if redis_data:
                # 使用缓存的channel数据
                channel = type('CachedChannel', (), {
                    'token_address': redis_data.get('token_address')
                })()
            else:
                # 如果Redis中没有找到，查询数据库 - 传递 status=None 以获取任何状态的频道
                channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
                if not channel:
                    raise ValueError(f"Channel '{channel_name}' not found")
                
                # 将查询到的频道信息缓存到Redis
                await self._cache_channel_to_redis(channel)
            
            response = {
                "channel_name": channel_name,
                "has_token": False,
                "token_address": None,
                "token_info": None
            }
            
            # 如果频道绑定了代币地址
            if channel.token_address:
                response["has_token"] = True
                response["token_address"] = channel.token_address
                
                # 直接从Redis获取代币信息
                token_info = await self._get_token_info_from_redis(channel.token_address)
                if token_info:
                    response.update(token_info)
                    logger.debug(f"Retrieved token info from Redis for channel {channel_name}: {channel.token_address}")
                else:
                    # Redis中没有找到token信息
                    logger.debug(f"No token info found in Redis for {channel.token_address}")
                    response["token_info"] = {"message": "Token info not available in cache"}
            
            return response
            
        except ValueError:
            # 重新抛出频道不存在的错误
            raise
        except Exception as e:
            logger.error(f"Error getting channel token info for {channel_name}: {str(e)}")
            raise Exception(f"Failed to get channel token info: {str(e)}")

    async def get_token_channels(self, token_address: str) -> Dict[str, Any]:
        """
        获取绑定了指定代币的所有正在直播的频道
        
        Args:
            token_address: 代币地址
            
        Returns:
            Dict[str, Any]: 代币相关频道响应
        """
        try:
            # 查询绑定了指定token且正在直播的频道
            live_channels = await self._repo.channels.get_live_channels_by_token(token_address)
            
            if not live_channels:
                return {
                    "token_address": token_address,
                    "total_channels": 0,
                    "live_channels": []
                }
            
            # 批量获取所有需要的信息
            user_ids = [channel.user_id for channel in live_channels]
            
            # 获取token信息和作者信息
            token_info = await self._get_token_info_batch([token_address])
            authors_info = await self._get_authors_info_batch(user_ids)
            
            # 转换为ChannelInfo格式
            channel_infos = []
            for channel in live_channels:
                channel_info = self._channel_to_info(
                    channel,
                    cover=token_info.get(token_address, {}).get("cover"),
                    author_info=authors_info.get(channel.user_id, {}),
                    token_name=token_info.get(token_address, {}).get("name")
                )
                channel_infos.append(channel_info)
            
            response = {
                "token_address": token_address,
                "total_channels": len(channel_infos),
                "live_channels": channel_infos
            }
            
            logger.info(f"Found {len(channel_infos)} live channels for token {token_address}")
            return response
            
        except Exception as e:
            logger.error(f"Error getting token channels for {token_address}: {str(e)}")
            raise Exception(f"Failed to get token channels: {str(e)}")

    async def get_channel_gift_ranking(
        self,
        channel_name: str,
        page: int = 1,
        page_size: int = 10
    ) -> Dict[str, Any]:
        """
        获取频道打赏排行榜
        
        Args:
            channel_name: 频道名称
            page: 页码，从1开始
            page_size: 每页大小
            
        Returns:
            Dict[str, Any]: 打赏排行榜响应
        """
        try:
            # 验证频道是否存在
            channel = await self._repo.channels.get_channel_by_name(channel_name)
            if not channel:
                raise ValueError(f"Channel '{channel_name}' not found")
            
            offset = (page - 1) * page_size
            
            # 获取排行榜数据和总人数
            rankings, total_tippers = await self._repo.gifts.get_channel_gift_ranking(
                channel_name=channel_name,
                limit=page_size,
                offset=offset
            )
            
            # 获取频道总打赏价值（使用新的优化方法）
            total_gift_value = await self._repo.gifts.get_channel_total_gift_value(channel_name)
            
            # 获取用户信息并补充到排行榜数据
            enriched_rankings = []
            for ranking_item in rankings:
                user_id = ranking_item["user_id"]
                
                # 尝试从Author表获取用户信息
                username = None
                avatar = None
                try:
                    # 查询用户信息
                    stmt = select(Author).where(Author.id == user_id)
                    author_result = await self._repo.db.execute(stmt)
                    author = author_result.scalar_one_or_none()
                    
                    if author:
                        username = author.username
                        avatar = author.avatar
                except Exception as e:
                    logger.warning(f"Failed to get user info for {user_id}: {str(e)}")
                
                enriched_item = {
                    "rank": ranking_item["rank"],
                    "user_id": user_id,
                    "username": username or f"User_{user_id[:8]}",  # 提供默认用户名
                    "avatar": avatar,
                    "total_gift_value": ranking_item["total_gift_value"],
                    "total_gift_count": ranking_item["total_gift_count"],
                    "gift_types": ranking_item["gift_types"],
                    "latest_gift_time": ranking_item["latest_gift_time"]
                }
                enriched_rankings.append(enriched_item)
            
            # 计算是否有下一页
            has_next = (offset + page_size) < total_tippers
            
            response = {
                "channel_name": channel_name,
                "total_tippers": total_tippers,
                "total_gift_value": total_gift_value,
                "rankings": enriched_rankings,
                "page": page,
                "page_size": page_size,
                "has_next": has_next
            }
            
            logger.info(f"Retrieved gift ranking for channel {channel_name}: {total_tippers} tippers, page {page}")
            return response
            
        except ValueError:
            # 重新抛出频道不存在的错误
            raise
        except Exception as e:
            logger.error(f"Error getting channel gift ranking for {channel_name}: {str(e)}")
            raise Exception(f"Failed to get channel gift ranking: {str(e)}")

    def _extract_user_token_from_request(self, request: "Request") -> str:
        """
        从请求中提取用户认证token

        Args:
            request: FastAPI Request 对象

        Returns:
            str: 用户认证token
            
        Raises:
            HTTPException: 认证错误
        """
        authorization = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(status_code=401, detail="Missing authorization header")

        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=401, detail="Invalid authorization header format"
            )

        return authorization.split(" ", 1)[1]

    async def process_gift_purchase(
        self,
        user_id: str,
        request: Request,
        request_body: BuyGiftRequest
    ) -> BuyGiftResponse:
        """
        处理礼物购买的完整业务逻辑
        
        Args:
            user_id: 用户ID
            request: FastAPI Request 对象（用于提取用户token）
            request_body: 礼物购买请求
            
        Returns:
            BuyGiftResponse: 购买结果响应
            
        Raises:
            HTTPException: 各种业务异常
        """
        try:
            
            # 2. 检查操作ID是否已存在（去重）
            existing_operation = await self.check_operation_exists(request_body.operation_id)
            if existing_operation:
                logger.warning(
                    f"Duplicate operation attempt: operation_id={request_body.operation_id}, user={user_id}"
                )
                raise HTTPException(
                    status_code=409,
                    detail=f"Operation with ID {request_body.operation_id} already exists",
                )

            # 3. 获取礼物价格和总价
            price_info = await self._get_gift_price_and_total(
                request_body.gift_id, request_body.quantity
            )
            total_price_usd = float(price_info["total_price_usd"])

            # 4. 获取用户USD余额
            # 获取用户的认证token
            user_token = self._extract_user_token_from_request(request)
            balance_usd = await self._get_user_usd_balance(user_token)

            # 5. 检查余额是否足够
            if balance_usd < total_price_usd:
                error_msg = f"Insufficient balance. Required: ${total_price_usd:.2f} USD, Available: ${balance_usd:.2f} USD"
                logger.warning(
                    f"User {user_id} insufficient balance for gift purchase: {error_msg}"
                )
                raise HTTPException(status_code=402, detail=error_msg)

            logger.info(
                f"Balance check passed for user {user_id}: Required ${total_price_usd:.2f} USD, Available ${balance_usd:.2f} USD"
            )

            # 6. 获取频道ID（数据库主键）
            channel = await self._repo.channels.get_channel_by_name(request_body.channel_name)
            if not channel:
                raise HTTPException(
                    status_code=404, detail=f"Channel '{request_body.channel_name}' not found"
                )
            
            channel_id = channel.id

            # 7. 使用 Celery 客户端提交任务到 worker 队列
            from src.agora.task_client import agora_task_client
            task_result = agora_task_client.submit_gift_purchase_task(
                user_id=user_id,
                channel_name=request_body.channel_name,
                channel_id=channel_id,
                gift_id=request_body.gift_id,
                quantity=request_body.quantity,
                user_token=user_token,
                token_address=channel.token_address,
                message=request_body.message,
                operation_id=request_body.operation_id,
            )

            # 立即返回成功响应，包含任务ID和操作ID用于追踪
            logger.info(
                f"Gift purchase task submitted for user {user_id}, operation_id={request_body.operation_id}, {request_body.quantity}x gift {request_body.gift_id} in channel {request_body.channel_name}, task_id={task_result.id}"
            )

            # 检查礼物类型，如果是supreme类型则发送消息到全频道
            # 红包创建逻辑已移至 Celery 任务中处理
            try:
                # 优先从缓存获取礼物信息
                gift_info = await gift_cache.get_gift_by_id(request_body.gift_id)
                
                if gift_info and gift_info.category == GiftCategory.SUPREME:
                    # 仅当有非空文案时才发送全频道消息
                    self._broadcast_supreme_gift(user_id, request_body.message)
                    
                elif not gift_info:
                    # 缓存中没有找到，尝试从数据库获取
                    gift_detail = await self._repo.gift_list.get_gift_by_id(request_body.gift_id)
                    if gift_detail and gift_detail.category == GiftCategory.SUPREME:
                        self._broadcast_supreme_gift(user_id, request_body.message)
                
            except Exception as e:
                # 消息发送失败不影响礼物购买流程
                logger.warning(f"Failed to send supreme gift message for user {user_id}: {str(e)}")

            return BuyGiftResponse(
                success=True,
                message=f"Gift purchase request submitted successfully (task_id: {task_result.id}, operation_id: {request_body.operation_id})",
            )

        except HTTPException:
            # 重新抛出HTTP异常（包括余额不足的402错误）
            raise
        except Exception as e:
            logger.error(
                f"Failed to submit gift purchase request for user {user_id}: {str(e)}"
            )
            raise HTTPException(status_code=500, detail=str(e))

    async def _get_gift_price_and_total(
        self, gift_id: int, quantity: int
    ) -> Dict[str, float]:
        """
        获取礼物价格和计算总价（优先使用缓存）
        
        Args:
            gift_id: 礼物ID
            quantity: 购买数量
            
        Returns:
            Dict[str, float]: 包含价格信息的字典
            {
                "gift_price_usd": 单个礼物价格,
                "total_price_usd": 总价格
            }
            
        Raises:
            HTTPException:
                - 404: 礼物不存在
                - 500: 系统错误
        """
        try:
            # 优先从缓存获取礼物信息
            from src.agora.gift_cache import gift_cache
            gift_info = await gift_cache.get_gift_by_id(gift_id)

            if gift_info and gift_info.is_active:
                # 从缓存获取成功
                gift_price_usd = gift_info.price
                total_price_usd = gift_price_usd * quantity

                logger.debug(
                    f"从缓存获取礼物价格: gift_id={gift_id}, price=${gift_price_usd}"
                )

                return {
                    "gift_price_usd": gift_price_usd,
                    "total_price_usd": total_price_usd,
                }

            # 缓存中没有找到，使用数据库查询作为后备方案
            logger.info(f"缓存中未找到礼物 {gift_id}，使用数据库查询")
            gift_price_usd = await self.get_gift_price(gift_id)
            if gift_price_usd is None:
                raise HTTPException(
                    status_code=404, detail=f"Gift with ID {gift_id} not found"
                )

            total_price_usd = gift_price_usd * quantity

            return {
                "gift_price_usd": gift_price_usd,
                "total_price_usd": total_price_usd,
            }

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Failed to get gift price for gift_id {gift_id}: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to get gift price: {str(e)}"
            )

    async def _get_user_usd_balance(self, user_token: str) -> float:
        """
        获取用户的USD余额信息
        
        Args:
            user_token: 用户认证token
            
        Returns:
            Dict[str, float]: 包含余额信息的字典
            {
                "eth_balance": ETH余额,
                "eth_price_usd": ETH价格（USD）,
                "balance_usd": USD余额
            }
            
        Raises:
            HTTPException:
                - 503: 服务不可用
                - 500: 系统错误
        """
        try:
            from src.agora.memecoin_client import memecoin_client

            # 1. 获取用户ETH余额
            balance_response = await memecoin_client.get_user_balance(user_token)

            balance_usd = float(balance_response.get("cash_amount", "0"))

            return balance_usd

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Failed to get user USD balance: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to get user balance: {str(e)}"
            )

    async def convert_gift_to_token(self, gift_id: int, quantity: int, token_address: str) -> Dict[str, Any]:
        """
        将礼物转换为对应的代币数量
        
        Args:
            gift_id: 礼物ID
            quantity: 礼物数量
            token_address: 目标代币地址
            
        Returns:
            Dict[str, Any]: 转换结果（简化版）
        """
        try:
            # 1. 从缓存获取礼物信息
            gift_info = await gift_cache.get_gift_by_id(gift_id)
            
            if not gift_info:
                # 缓存中没有，尝试从数据库获取
                gift_detail = await self._repo.gift_list.get_gift_by_id(gift_id)
                if not gift_detail:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Gift with ID {gift_id} not found"
                    )
                
                # 从数据库模型提取信息
                gift_unit_price = gift_detail.price
                
                if not gift_detail.is_active:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Gift {gift_id} is not active"
                    )
            else:
                # 从缓存获取信息
                gift_unit_price = gift_info.price
                
                if not gift_info.is_active:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Gift {gift_id} is not active"
                    )
            
            # 2. 计算总价格
            gift_total_usd = gift_unit_price * quantity
            
            # 3. 调用memecoin服务进行转换
            conversion_result = await memecoin_client.convert_usd_to_token(
                token_address=token_address,
                usd_amount=str(gift_total_usd)
            )
            
            # 4. 提取转换结果
            token_amount = conversion_result.get("token_amount", "0")
            
            # 5. 计算转换汇率
            if gift_total_usd > 0:
                conversion_rate = float(token_amount) / gift_total_usd
                conversion_rate_str = f"{conversion_rate:.6f}"
            else:
                conversion_rate_str = "0"
            
            # 6. 构造简化响应
            result = {
                "token_amount": token_amount,
                "conversion_rate": conversion_rate_str
            }
            
            logger.info(f"Gift conversion successful: {quantity}x gift {gift_id} (${gift_total_usd:.2f}) -> {token_amount} tokens")
            
            return result
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"Gift conversion failed: gift_id={gift_id}, quantity={quantity}, token_address={token_address}, error={str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Gift conversion failed: {str(e)}"
            )

    async def _create_red_envelope_for_supreme_gift(
        self,
        user_id: str,
        user_token: str,
        channel_name: str,
        gift_total_usd: float,
        operation_id: str,
        gift_id: int = None
    ):
        """
        为至尊礼物创建红包
        
        Args:
            user_id: 购买礼物的用户ID
            user_token: 用户认证token
            channel_name: 频道名称
            gift_total_usd: 礼物总价值(USD)
            operation_id: 操作ID（用于去重和追踪）
            gift_id: 礼物ID（用于获取礼物名称和图标，可选）
        """
        try:
            # 获取礼物信息（如果提供了 gift_id）
            gift_name = None
            gift_image = None
            if gift_id:
                try:
                    # 优先从缓存获取礼物信息
                    from src.agora.gift_cache import gift_cache
                    gift_info = await gift_cache.get_gift_by_id(gift_id)
                    if gift_info and gift_info.is_active:
                        gift_name = gift_info.name
                        gift_image = gift_info.icon_url
                        logger.debug(f"从缓存获取礼物信息: gift_id={gift_id}, name={gift_name}, image={gift_image}")
                    else:
                        # 缓存不可用，从数据库获取
                        gift_detail = await self._repo.gift_list.get_gift_by_id(gift_id)
                        if gift_detail:
                            meta = gift_detail.meta or {}
                            gift_name = meta.get('name', f'Gift {gift_id}')
                            gift_image = gift_detail.cover
                            logger.debug(f"从数据库获取礼物信息: gift_id={gift_id}, name={gift_name}, image={gift_image}")
                        else:
                            logger.warning(f"未找到礼物信息: gift_id={gift_id}")
                            gift_name = f'Gift {gift_id}'
                except Exception as gift_error:
                    logger.warning(f"获取礼物信息失败: gift_id={gift_id}, error={str(gift_error)}")
                    gift_name = f'Gift {gift_id}' if gift_id else None
            
            # 计算红包金额 - 10%的礼物价值
            red_envelope_amount_usd = gift_total_usd * 0.1
            
            # 获取频道绑定的代币地址
            channel = await self._repo.channels.get_channel_by_name(channel_name)
            if not channel or not channel.token_address:
                logger.warning(f"Channel {channel_name} not found or has no token address, skipping red envelope creation")
                return
            
            # 将USD金额转换为代币数量
            conversion_result = await memecoin_client.convert_usd_to_token(
                token_address=channel.token_address,
                usd_amount=str(red_envelope_amount_usd)
            )
            
            token_amount = conversion_result.get("token_amount", "0")
            if float(token_amount) <= 0:
                logger.warning(f"Red envelope token amount is zero or negative: {token_amount}, skipping creation")
                return
            
            # 这里可以调用红包服务创建红包（目前使用简化方式记录）
            # TODO: 实现真正的红包创建逻辑，包括数据库存储、分配机制等
            
            # 发送websocket通知给移动端APP
            await self._send_red_envelope_notification(
                user_id=user_id,
                channel_name=channel_name,
                token_address=channel.token_address,
                token_amount=token_amount,
                usd_amount=red_envelope_amount_usd,
                operation_id=operation_id,
                gift_name=gift_name,
                gift_image=gift_image
            )
            
            logger.info(
                f"Red envelope created for supreme gift: user={user_id}, channel={channel_name}, "
                f"usd_amount=${red_envelope_amount_usd:.2f}, token_amount={token_amount}, operation_id={operation_id}"
            )
            
        except Exception as e:
            logger.error(f"Failed to create red envelope for supreme gift: user={user_id}, error={str(e)}")

    async def _send_red_envelope_notification(
        self,
        user_id: str,
        channel_name: str,
        token_address: str,
        token_amount: str,
        usd_amount: float,
        operation_id: str,
        gift_name: str = None,
        gift_image: str = None
    ):
        """
        发送红包创建的websocket通知
        
        Args:
            user_id: 创建红包的用户ID
            channel_name: 频道名称
            token_address: 代币地址
            token_amount: 代币数量
            usd_amount: USD金额
            operation_id: 操作ID
            gift_name: 礼物名称（可选）
            gift_image: 礼物图标URL（可选）
        """
        try:
            # 生成红包ID
            import uuid
            envelope_id = str(uuid.uuid4())
            
            # 计算创建时间和过期时间
            created_at = utc_now_naive()
            expires_at = created_at + timedelta(seconds=60)
            
            # 构造红包通知消息
            notification_message = {
                "type": "red_envelope_created",
                "data": {
                    "envelope_id": envelope_id,
                    "creator_user_id": user_id,
                    "channel_name": channel_name,
                    "token_address": token_address,
                    "token_amount": token_amount,
                    "usd_amount": usd_amount,
                    "operation_id": operation_id,
                    "gift_name": gift_name,
                    "gift_image": gift_image,
                    "total_parts": 88,
                    "claimed_parts": 0,
                    "status": "active",
                    "created_at": created_at.isoformat(),
                    "expires_at": expires_at.isoformat(),
                    "message": f"A red envelope worth ${usd_amount:.2f} has been created in {channel_name}!"
                }
            }
            
            # 发送RTM消息到频道，告知所有观众红包已创建
            await self.send_rtm_channel_message(
                user_id=user_id,
                request=RTMChannelMessageRequest(
                    channel_names=[channel_name],
                    message=json.dumps(notification_message),
                    broadcast=False
                )
            )
            
            logger.info(f"Red envelope notification sent to channel {channel_name}: {notification_message}")
            
        except Exception as e:
            logger.error(f"Failed to send red envelope notification: {str(e)}")

    async def get_channel_red_envelopes(self, channel_name: str) -> Dict[str, Any]:
        """
        获取频道中的活跃红包列表
        
        Args:
            channel_name: 频道名称
            
        Returns:
            Dict[str, Any]: 红包列表响应
        """
        try:
            await self.init_redis()
            
            # 获取频道的红包ID列表
            channel_envelopes_key = f"channel_red_envelopes:{channel_name}"
            envelope_ids = await self.redis_client.smembers(channel_envelopes_key)
            
            envelopes = []
            for envelope_id in envelope_ids:
                redis_key = f"red_envelope:{envelope_id}"
                envelope_data = await self.redis_client.get(redis_key)
                
                if envelope_data:
                    envelope = json.loads(envelope_data)
                    
                    # 只返回活跃的红包
                    if envelope.get('status') == 'active':
                        # 转换为响应格式
                        envelope_info = {
                            'envelope_id': envelope['id'],
                            'creator_user_id': envelope['creator_user_id'],
                            'creator_name': envelope.get('creator_name'),
                            'creator_avatar': envelope.get('creator_avatar'),
                            'channel_name': envelope['channel_name'],
                            'token_address': envelope['token_address'],
                            'total_amount': envelope['total_amount'],
                            'usd_amount': envelope['usd_amount'],
                            'total_parts': 88,
                            'claimed_parts': len(envelope.get('claimed_parts', [])),
                            'status': envelope['status'],
                            'created_at': envelope['created_at'],
                            'first_claim_at': envelope.get('first_claim_at'),
                            'expires_at': envelope.get('expires_at')
                        }
                        envelopes.append(envelope_info)
            
            # 按创建时间排序，最新的在前
            envelopes.sort(key=lambda x: x['created_at'], reverse=True)
            
            return {
                'channel_name': channel_name,
                'envelopes': envelopes,
                'total': len(envelopes)
            }
            
        except Exception as e:
            logger.error(f"Failed to get channel red envelopes: channel={channel_name}, error={str(e)}")
            raise

    async def get_all_active_red_envelopes(self) -> Dict[str, Any]:
        """
        获取所有频道的活跃红包列表
        
        Returns:
            Dict[str, Any]: 所有活跃红包的响应
        """
        try:
            await self.init_redis()
            
            # 获取所有红包键（使用模式匹配）
            all_envelope_keys = []
            cursor = 0
            while True:
                cursor, keys = await self.redis_client.scan(
                    cursor, match="red_envelope:*", count=100
                )
                all_envelope_keys.extend(keys)
                if cursor == 0:
                    break
            
            all_envelopes = []
            channels_with_envelopes = set()
            
            for redis_key in all_envelope_keys:
                envelope_data = await self.redis_client.get(redis_key)
                
                if envelope_data:
                    envelope = json.loads(envelope_data)
                    
                    # 只返回活跃的红包
                    if envelope.get('status') == 'active':
                        channels_with_envelopes.add(envelope['channel_name'])
                        
                        # 转换为响应格式
                        envelope_info = {
                            'envelope_id': envelope['id'],
                            'creator_user_id': envelope['creator_user_id'],
                            'creator_name': envelope.get('creator_name'),
                            'creator_avatar': envelope.get('creator_avatar'),
                            'channel_name': envelope['channel_name'],
                            'token_address': envelope['token_address'],
                            'total_amount': envelope['total_amount'],
                            'usd_amount': envelope['usd_amount'],
                            'total_parts': 88,
                            'claimed_parts': len(envelope.get('claimed_parts', [])),
                            'status': envelope['status'],
                            'created_at': envelope['created_at'],
                            'first_claim_at': envelope.get('first_claim_at'),
                            'expires_at': envelope.get('expires_at')
                        }
                        all_envelopes.append(envelope_info)
            
            # 按创建时间排序，最新的在前
            all_envelopes.sort(key=lambda x: x['created_at'], reverse=True)
            
            return {
                'envelopes': all_envelopes,
                'total': len(all_envelopes),
                'total_channels': len(channels_with_envelopes),
                'channels': list(channels_with_envelopes)
            }
            
        except Exception as e:
            logger.error(f"Failed to get all active red envelopes: {str(e)}")
            raise

    async def claim_red_envelope(
        self,
        user_id: str,
        envelope_id: str,
        channel_name: str
    ) -> Dict[str, Any]:
        """
        领取红包
        
        Args:
            user_id: 用户ID
            envelope_id: 红包ID
            channel_name: 频道名称
            
        Returns:
            Dict[str, Any]: 领取结果，包含status_code字段用于前端判断错误类型
                - status_code: 0 (成功)
                - status_code: 1 (红包不存在)
                - status_code: 2 (已经领取过)
                - status_code: 3 (红包已失效：已过期/已领完/状态无效)
                - status_code: 4 (系统繁忙)
                - status_code: 5 (请求参数错误，如频道不匹配)
                - status_code: 99 (系统异常)
        """
        try:
            await self.init_redis()
            
            # 使用分布式锁避免并发问题
            lock_key = f"red_envelope_lock:{envelope_id}"
            lock_acquired = await self.redis_client.set(
                lock_key, "1", nx=True, ex=5  # 5秒锁定时间
            )
            
            if not lock_acquired:
                return {
                    'success': False,
                    'status_code': 4,
                    'envelope_id': envelope_id,
                    'message': 'System busy, please try again later'
                }
            
            try:
                # 获取红包数据
                redis_key = f"red_envelope:{envelope_id}"
                envelope_data = await self.redis_client.get(redis_key)
                
                if not envelope_data:
                    return {
                        'success': False,
                        'status_code': 1,
                        'envelope_id': envelope_id,
                        'message': 'Red envelope not found'
                    }
                
                envelope = json.loads(envelope_data)
                
                # 验证频道
                if envelope['channel_name'] != channel_name:
                    return {
                        'success': False,
                        'status_code': 5,
                        'envelope_id': envelope_id,
                        'message': 'Red envelope does not belong to this channel'
                    }
                
                # 检查状态
                if envelope['status'] != 'active':
                    return {
                        'success': False,
                        'status_code': 3,
                        'envelope_id': envelope_id,
                        'message': f'Red envelope is {envelope["status"]}'
                    }
                
                # 检查是否已领取
                if user_id in envelope.get('claimed_by', {}):
                    return {
                        'success': False,
                        'status_code': 2,
                        'envelope_id': envelope_id,
                        'message': 'You have already claimed this red envelope',
                        'total_claimed_amount': envelope['claimed_by'][user_id]
                    }
                
                # 检查是否还有剩余份数
                claimed_parts = envelope.get('claimed_parts', [])
                if len(claimed_parts) >= 88:
                    envelope['status'] = 'completed'
                    await self.redis_client.setex(redis_key, 86400, json.dumps(envelope))
                    return {
                        'success': False,
                        'status_code': 3,
                        'envelope_id': envelope_id,
                        'message': 'Red envelope has been fully claimed'
                    }
                
                # 检查是否已过期（创建时已设置 expires_at 并调度任务，这里只做被动校验）
                current_time = datetime.utcnow()
                expires_at = None
                if envelope.get('expires_at'):
                    expires_at = datetime.fromisoformat(envelope['expires_at'])
                if expires_at and current_time > expires_at:
                    envelope['status'] = 'expired'
                    await self.redis_client.setex(redis_key, 86400, json.dumps(envelope))
                    return {
                        'success': False,
                        'status_code': 3,
                        'envelope_id': envelope_id,
                        'message': 'Red envelope has expired'
                    }
                
                # 分配一份金额
                available_parts = envelope['parts']
                part_index = len(claimed_parts)
                claimed_amount = available_parts[part_index]
                
                # 更新红包数据
                claimed_parts.append(part_index)
                envelope['claimed_parts'] = claimed_parts
                envelope['claimed_by'][user_id] = claimed_amount
                
                # 检查是否全部领完
                if len(claimed_parts) >= 88:
                    envelope['status'] = 'completed'
                    envelope['completed_at'] = current_time.isoformat()
                    
                    # 发送完成通知（包括Redis发布）
                    await self._send_red_envelope_completed_notification(
                        envelope_id=envelope_id,
                        channel_name=channel_name,
                        total_amount=envelope['total_amount'],
                        claimed_by=envelope['claimed_by']
                    )
                
                # 保存更新
                await self.redis_client.setex(redis_key, 86400, json.dumps(envelope))
                
                # TODO: 调用memecoin服务进行实际转账
                # txid = await memecoin_client.send_asset(
                #     from_system=True,
                #     to_user_id=user_id,
                #     token_address=envelope['token_address'],
                #     amount=claimed_amount
                # )
                txid = f"mock_claim_{envelope_id}_{user_id}"
                
                return {
                    'success': True,
                    'status_code': 0,
                    'envelope_id': envelope_id,
                    'claimed_amount': claimed_amount,
                    'claimed_part_index': part_index,
                    'message': f'Successfully claimed {claimed_amount} USDT',
                    'txid': txid,
                    'remaining_parts': 88 - len(claimed_parts),
                    'total_claimed_amount': claimed_amount
                }
                
            finally:
                # 释放锁
                await self.redis_client.delete(lock_key)
                
        except Exception as e:
            logger.error(f"Failed to claim red envelope: user={user_id}, envelope={envelope_id}, error={str(e)}")
            return {
                'success': False,
                'status_code': 99,
                'envelope_id': envelope_id,
                'message': 'System error, please try again later'
            }

    async def _send_red_envelope_completed_notification(
        self,
        envelope_id: str,
        channel_name: str,
        total_amount: str,
        claimed_by: Dict[str, str]
    ):
        """发送红包领完通知，包含领取详情"""
        try:
            # 发布Redis消息
            from src.agora.redis_messenger import redis_messenger
            await redis_messenger.publish_red_envelope_completed(
                envelope_id=envelope_id,
                channel_name=channel_name,
                total_amount=total_amount,
                claimed_by=claimed_by
            )
            
            # 获取用户信息以显示用户名
            user_ids = list(claimed_by.keys())
            authors_info = await self._get_authors_info_batch(user_ids)
            
            # 构建领取详情列表
            claim_details = []
            for user_id, amount in claimed_by.items():
                author_info = authors_info.get(user_id, {})
                claim_details.append({
                    "user_id": user_id,
                    "username": author_info.get("username", f"User_{user_id[:8]}"),
                    "avatar": author_info.get("avatar"),
                    "amount": amount
                })
            
            # 按金额降序排序
            claim_details.sort(key=lambda x: float(x["amount"]), reverse=True)
            
            notification = {
                "type": "red_envelope_completed",
                "data": {
                    "envelope_id": envelope_id,
                    "channel_name": channel_name,
                    "total_amount": total_amount,
                    "total_claimed": len(claimed_by),
                    "claim_details": claim_details,
                    "completed_at": utc_now_naive().isoformat(),
                    "message": f"Red envelope {envelope_id} has been fully claimed!"
                }
            }
            
            # 发送到所有频道（因为WebSocket监听所有频道）
            await self.send_rtm_channel_message(
                user_id="system",
                request=RTMChannelMessageRequest(
                    channel_names=[channel_name],
                    message=json.dumps(notification),
                    broadcast=False
                )
            )
            
            logger.info(f"Red envelope completed notification sent: envelope_id={envelope_id}")
        except Exception as e:
            logger.error(f"Failed to send completed notification: {str(e)}")

    async def get_all_live_channel_names(self) -> List[str]:
        """
        获取所有正在直播的频道名称列表
        
        Returns:
            List[str]: 频道名称列表
        """
        try:
            channels = await self._repo.channels.get_live_channels(limit=1000, offset=0)
            return [channel.channel_name for channel in channels]
        except Exception as e:
            logger.error(f"获取所有直播频道名称失败: {str(e)}")
            return []
    
    async def validate_channels_exist(self, channel_names: List[str]) -> Dict[str, List[str]]:
        """
        验证频道是否存在
        
        Args:
            channel_names: 需要验证的频道名称列表
            
        Returns:
            Dict[str, List[str]]: 包含 valid_channels 和 invalid_channels 两个键的字典
        """
        valid_channels = []
        invalid_channels = []
        
        try:
            for channel_name in channel_names:
                if not channel_name or not isinstance(channel_name, str):
                    invalid_channels.append(str(channel_name))
                    continue
                    
                # 检查频道是否存在（不限制状态）
                channel = await self._repo.channels.get_channel_by_name(channel_name, status=None)
                if channel:
                    valid_channels.append(channel_name)
                else:
                    invalid_channels.append(channel_name)
            
            logger.debug(f"频道验证结果: valid={len(valid_channels)}, invalid={len(invalid_channels)}")
            return {
                "valid_channels": valid_channels,
                "invalid_channels": invalid_channels
            }
            
        except Exception as e:
            logger.error(f"验证频道存在性时出错: {str(e)}")
            # 出错时保守处理，将所有频道标记为无效
            return {
                "valid_channels": [],
                "invalid_channels": channel_names
            }

    async def kick_user_from_channel(
        self,
        channel_name: str,
        user_id_to_kick: str,
        kicked_by_user_id: str,
        reason: Optional[str] = None,
        duration: int = 300
    ) -> Dict[str, Any]:
        """
        从频道中踢出用户
        
        仅在Redis中存储用户的踢出状态，不进行实际的踢出操作。
        在用户请求token时会检查此状态并拒绝生成新token。
        
        Args:
            channel_name: 频道名称
            user_id_to_kick: 要踢出的用户ID
            kicked_by_user_id: 执行踢出操作的用户ID（需要是主播）
            reason: 踢出原因（可选）
            duration: 踢出时长（秒），默认300秒
            
        Returns:
            Dict[str, Any]: 踢出结果
            
        Raises:
            ValueError: 频道不存在或权限不足
            HTTPException: API调用失败
        """
        try:
            # 1. 验证频道存在且正在直播
            channel = await self._repo.channels.get_channel_by_name(channel_name, status=ChannelStatus.LIVE)
            if not channel:
                raise ValueError("频道不存在或未在直播中")
            
            # 2. 验证操作者权限（必须是频道主播）
            if channel.user_id != kicked_by_user_id:
                raise ValueError("只有主播才能踢出用户")
            
            # 3. 不能踢出自己
            if user_id_to_kick == kicked_by_user_id:
                raise ValueError("不能踢出自己")
            
            # 4. 获取要踢出用户的UID
            uid_to_kick = token_manager.get_uid_by_user_id(user_id_to_kick)
            
            # 5. 存储踢出状态到Redis
            await self.init_redis()
            kick_key = f"channel_kick:{channel_name}:{user_id_to_kick}"
            kick_data = {
                "kicked_by": kicked_by_user_id,
                "kicked_at": utc_now_naive().isoformat(),
                "reason": reason or "Kicked by host",
                "channel_name": channel_name,
                "uid": uid_to_kick,
                "duration": duration
            }
            
            # 设置指定的过期时间
            await self.redis_client.setex(kick_key, duration, json.dumps(kick_data))
            kick_data["expires_at"] = (utc_now_naive() + timedelta(seconds=duration)).isoformat()
            
            # 6. 撤销该用户在该频道的所有现有token
            await token_manager.revoke_user_channel_tokens(user_id_to_kick, channel_name)
            
            # 7. 从本地数据库中移除用户
            await self._repo.audiences.remove_audience(channel.id, user_id_to_kick)
            # 提交事务，确保数据库侧移除立即可见
            await self._repo.db.commit()
            
            logger.info(f"User {user_id_to_kick} kicked from channel {channel_name} by {kicked_by_user_id}")
            
            return {
                "success": True,
                "channel_name": channel_name,
                "kicked_user_id": user_id_to_kick,
                "message": f"用户 {user_id_to_kick} 已被踢出频道"
            }
                    
        except ValueError as e:
            raise
        except Exception as e:
            logger.error(f"Failed to kick user: channel={channel_name}, user={user_id_to_kick}, error={str(e)}")
            raise HTTPException(status_code=500, detail=f"踢出用户失败: {str(e)}")


