from fastapi import Depends

from src.database.session import get_session_context
from src.database.models.AccessToken import AccessToken
from fastapi_users_db_sqlalchemy.access_token import SQLAlchemyAccessTokenDatabase
from fastapi_users.authentication.strategy.db import AccessTokenDatabase, DatabaseStrategy


async def get_access_token_db():
    async with get_session_context() as session:
        yield SQLAlchemyAccessTokenDatabase(session, AccessToken)


def get_auth_strategy(
    access_token_db: AccessTokenDatabase[AccessToken] = Depends(get_access_token_db),
) -> DatabaseStrategy:
    return DatabaseStrategy(access_token_db)
