from typing import Optional

import httpx
from fastapi import Depends, Request
from fastapi_users import Base<PERSON>ser<PERSON>ana<PERSON>, UUIDIDMixin, exceptions

from src.database.models.User import User
from src.common.exceptions import AlreadyExistsError
from .logger import logger
from .settings import settings

from .user_database import get_user_db
from .schemas import UserCreate
from src.common.elasticsearch.sync_client import create_es_sync_client


class UserManager(UUIDIDMixin, BaseUserManager[User, str]):
    def parse_id(self, value: str) -> str:
        return value

    async def create(
            self,
            user_create,
            safe: bool = False,
            request: Optional[Request] = None,
    ):
        user_create.password = ""
        existing_user = await self.user_db.get_by_email(user_create.email)
        if existing_user is not None:
            raise AlreadyExistsError(detail="User already exists")
        user_dict = (
            user_create.create_update_dict()
            if safe
            else user_create.create_update_dict_superuser()
        )
        
        # Get wallet data from KMS first
        wallet_data = await self._get_wallet_data()
        
        # Create user and wallet together without committing
        user = await self.user_db.create_user_with_wallet(user_dict, wallet_data)
        
        # 异步同步到 Elasticsearch
        es_sync_client = create_es_sync_client("auth_service")
        es_sync_client.sync_author(str(user.id), "create", priority=5)
        
        return user

    async def create_from_phone(self, user: UserCreate):
        user.password = ""
        user_dict = user.create_update_dict()
        
        # Create user and wallet in the same transaction
        created_user = await self.user_db.create_from_phone(user_dict)  # type: ignore
        await self.generate_wallet(created_user.id)
        
        # 异步同步到 Elasticsearch
        es_sync_client = create_es_sync_client("auth_service")
        es_sync_client.sync_author(str(created_user.id), "create", priority=5)
        
        return created_user

    async def _get_wallet_data(self):
        """Get wallet data from KMS service"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(f"{settings.KMS_ADDRESS}/generate")
            response.raise_for_status()
            wallet = response.json()
            
            # Validate wallet response
            required_keys = ['bsc_pubkey', 'sol_pubkey', 'bsc_secret', 'sol_secret']
            missing_keys = [key for key in required_keys if not wallet.get(key)]
            if missing_keys:
                raise ValueError(f"Wallet response missing keys: {missing_keys}")
            
            return wallet

    async def generate_wallet(self, user_id):
        try:
            wallet = await self._get_wallet_data()
            await self.user_db.create_user_wallet(user_id, wallet)
            # 不手动提交，让外层事务管理
            logger.info(f"Successfully created wallet for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to create wallet for {user_id}: {e}")
            raise  # 让外层事务处理回滚


async def get_user_manager(user_db=Depends(get_user_db)):
    yield UserManager(user_db)
