import os
import ssl
from enum import Enum
from string import Template
from urllib import parse

from smtplib import SMTP, SMTP_SSL
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText

from src.database.models import User

SMTP_URL = parse.urlparse(os.getenv("SMTP_URL"))
BASE_URL = os.getenv("BASE_URL")

# Try to get allowed emails from environment variable first
allowed_emails_env = os.getenv("ALLOWED_EMAILS", "")
if allowed_emails_env:
    allowed_emails = set(filter(lambda x: x, allowed_emails_env.split(",")))
    print(f"Loaded {len(allowed_emails)} allowed emails from environment variable")
else:
    # Fall back to reading from file
    try:
        with open(os.path.abspath('auth/allowed_emails.txt'), 'r') as file:
            allowed_emails = file.read().splitlines()
            allowed_emails = set(filter(lambda x: x, allowed_emails))
            print(f"Loaded {len(allowed_emails)} allowed emails from file")
    except FileNotFoundError:
        print("Warning: allowed_emails.txt not found and ALLOWED_EMAILS env var not set, using empty set")
        allowed_emails = set()
    except Exception as e:
        print(f"Error reading allowed_emails.txt: {e}")
        allowed_emails = set()


class Region(str, Enum):
    DEFAULT = "en"
    CHINA = "zh"
    RUSSIA = "ru"


class MailLanguage(str, Enum):
    ENGLISH = "en"
    CHINESE = "zh"
    RUSSIAN = "ru"
    OTHER = "oth"


def send_mail(
        mail_to,
        mail_html,
        mail_body,
        mail_subject,
        mail_from='<EMAIL>',
):
    context = ssl.create_default_context()
    hostname = SMTP_URL.hostname
    port = SMTP_URL.port
    scheme = SMTP_URL.scheme

    message = MIMEMultipart("alternative")
    message["Subject"] = mail_subject
    message["From"] = mail_from
    message["To"] = mail_to or '<EMAIL>'
    part1 = MIMEText(mail_body, "plain")
    part2 = MIMEText(mail_html, "html")
    message.attach(part1)
    message.attach(part2)

    if scheme == 'smtps':
        server = SMTP_SSL(hostname, port)
    else:
        server = SMTP(hostname, port)
    try:
        server.login(SMTP_URL.username or '', SMTP_URL.password or '')
        server.sendmail(mail_from, mail_to, message.as_string())
    except Exception as e:
        print(e)
    finally:
        server.quit()


def get_html_template(html_file_name: str) -> Template:
    with open(html_file_name, "r") as invite_html:
        return Template(invite_html.read())


def send_registration_mail(email: str, code: str, lang: MailLanguage, invitation_id: str | None):
    invitation_param = f"&invitation_id={invitation_id}" if invitation_id else ""
    if lang == MailLanguage.CHINESE:
        html = get_html_template("./auth/templates/verify_cn.html").safe_substitute(code=code)
    elif lang == MailLanguage.RUSSIAN:
        html = get_html_template("./auth/templates/verify_ru.html").safe_substitute(code=code)
    else:
        html = get_html_template("./auth/templates/signup_en.html").safe_substitute(code=code)
    body = f"Verify your mail {code}"
    subject = "Verify your mail"
    send_mail(mail_to=email, mail_html=html, mail_body=body, mail_subject=subject)
    return code


def send_authorization_mail(user: User, code: str):
    if user.region == Region.CHINA:
        html = get_html_template("./auth/templates/signin_zh.html").safe_substitute(code=code)
    elif user.region == Region.RUSSIA:
        html = get_html_template("./auth/templates/verify_ru.html").safe_substitute(code=code)
    else:
        html = get_html_template("./auth/templates/signin_en.html").safe_substitute(code=code)
    body = f"Verify your mail {code}"
    subject = "Verify your mail"
    send_mail(mail_to=user.email, mail_html=html, mail_body=body, mail_subject=subject)
    return code


def valid_email(email: str) -> bool:
    if '@' not in email:
        return False
    username, host = email.split('@')[:2]
    if '+' not in username:
        return True
    username = username.split('+')[0]
    if '@'.join([username, host]) in allowed_emails:
        return True
    return False
