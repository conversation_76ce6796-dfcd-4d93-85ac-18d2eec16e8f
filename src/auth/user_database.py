import logging
import random
from typing import Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status
from fastapi_users.db import SQLAlchemyUserDatabase

from src.auth.logger import logger
from src.auth.settings import settings
from src.database.models.MiniApp import AuthorAppInteraction
from src.database.models.UserWallet import UserWallet
from src.database.session import get_session
from src.database.models.User import User
from src.database.models.TociIm import TociIm
from src.database.models import Invitation, Author
from src.database.mixins.utils import generate_shortid

from src.auth.settings import settings
from src.common.im_service import ImService
from src.common.recommender import RecommenderClient
from src.common.exceptions import AlreadyExistsError, NotFoundError

# 安全地创建 recommender_client，如果配置缺失则为 None
try:
    recommender_client = RecommenderClient(recommender_token=settings.RECOMMENDER_TOKEN) if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN else None
except Exception:
    recommender_client = None


class UserDatabase(SQLAlchemyUserDatabase):
    def __init__(self, session: AsyncSession):
        super().__init__(session, User, None)

    async def create_dependant_models(
        self, user: User, create_data: dict, invitation_id: str | None = None
    ):
        username = create_data.get("username")
        avatar_url = user.avatar_url or ""

        params = {
            "id": user.id,
            "user_id": user.id,
            "name": create_data.get("name"),
            "username": username,
            "username_raw": username.lower() if username else None,
            "email": user.email,
            "phone_number": user.phone_number,
            "region": create_data.get("region"),
            "invitation_id": invitation_id,
            "invitation_id_owned": generate_shortid(),
            "vip_code": create_data.get("vip_code"),  # 添加内测码字段
            "citations_count": 0,
            "avatar": avatar_url,
        }
        model = Author(**params)  # type: ignore
        self.session.add(model)

        im_service = ImService(self.session)
        try:
            im_register_result = await im_service.register(user.id, username, avatar_url)
        except Exception as e:
            logger.error(f"IM service registration failed: {str(e)}")
            raise HTTPException(status.HTTP_400_BAD_REQUEST, f"Im unavailable: {str(e)}")

        toci_im = TociIm()
        toci_im.toci_id = im_register_result.toci_id
        toci_im.im_id = im_register_result.im_id
        toci_im.im_login = im_register_result.im_login
        toci_im.im_password = im_register_result.im_password
        self.session.add(toci_im)

        try:
            if recommender_client:
                await recommender_client.add_user(model)
        except HTTPException as e:
            logger.error(f"Recommender request failed with the following error:\n{e}")

        # Add record for recently-used mini-apps
        record = AuthorAppInteraction(
            author_id=user.id,
        )
        self.session.add(record)
        await self.session.flush()

    async def create(self, create_data: Dict[str, Any]) -> User:
        user: User = self.user_table()
        user.email = create_data.get("email")
        user.hashed_password = ""
        user.region = create_data.get("region")
        user.is_verified = True

        # Set OAuth fields if provided
        if create_data.get("google_id"):
            user.google_id = create_data.get("google_id")
            user.google_linked_at = create_data.get("google_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        if create_data.get("apple_id"):
            user.apple_id = create_data.get("apple_id")
            user.apple_linked_at = create_data.get("apple_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        avatar_url = create_data.get("avatar_url")
        if not avatar_url:
            avatar_url = random.choice(settings.default_user_avatar_list)
            logger.info(f"Assigned random default avatar for user: {avatar_url}")
        user.avatar_url = avatar_url

        if create_data.get("invitation_id") is not None:
            invitation = await self.session.get(
                Invitation, create_data.get("invitation_id")
            )
            if invitation is None:
                raise NotFoundError(detail="Invitation not found")
        try:
            self.session.add(user)
            await self.session.flush([user])
        except Exception:
            raise AlreadyExistsError(detail="User already exists")
        await self.create_dependant_models(
            user=user,
            create_data=create_data,
            invitation_id=create_data.get("invitation_id"),
        )
        await self.session.commit()
        return user

    async def create_from_phone(self, create_data: dict) -> User:
        user: User = self.user_table()
        user.phone_number = create_data.get("phone_number")
        user.email = create_data.get("email")
        user.hashed_password = ""
        user.region = create_data.get("region")
        user.is_verified = True

        # Set OAuth fields if provided
        if create_data.get("google_id"):
            user.google_id = create_data.get("google_id")
            user.google_linked_at = create_data.get("google_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        if create_data.get("apple_id"):
            user.apple_id = create_data.get("apple_id")
            user.apple_linked_at = create_data.get("apple_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        avatar_url = create_data.get("avatar_url")
        if not avatar_url:
            avatar_url = random.choice(settings.default_user_avatar_list)
            logger.info(f"Assigned random default avatar for user: {avatar_url}")
        user.avatar_url = avatar_url
        try:
            self.session.add(user)
            await self.session.flush([user])
        except Exception:
            raise AlreadyExistsError(detail="User already exists")
        await self.create_dependant_models(
            user=user,
            create_data=create_data,
            invitation_id=create_data.get("invitation_id"),
        )
        return user

    async def create_user_with_wallet(self, create_data: Dict[str, Any], wallet: dict) -> User:
        """Create user and wallet in the same session without committing"""
        from .logger import logger

        # Create user
        user: User = self.user_table()
        user.email = create_data.get("email")
        user.hashed_password = ""
        user.region = create_data.get("region")
        user.is_verified = True

        # Set OAuth fields if provided
        if create_data.get("google_id"):
            user.google_id = create_data.get("google_id")
            user.google_linked_at = create_data.get("google_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        if create_data.get("apple_id"):
            user.apple_id = create_data.get("apple_id")
            user.apple_linked_at = create_data.get("apple_linked_at")
            user.email_verified_via = create_data.get("email_verified_via")

        avatar_url = create_data.get("avatar_url")
        if not avatar_url:
            avatar_url = random.choice(settings.default_user_avatar_list)
            logger.info(f"Assigned random default avatar for user: {avatar_url}")
        user.avatar_url = avatar_url

        if create_data.get("invitation_id") is not None:
            invitation = await self.session.get(
                Invitation, create_data.get("invitation_id")
            )
            if invitation is None:
                raise NotFoundError(detail="Invitation not found")

        try:
            self.session.add(user)
            await self.session.flush([user])
        except Exception:
            raise AlreadyExistsError(detail="User already exists")

        # Create dependent models
        await self.create_dependant_models(
            user=user,
            create_data=create_data,
            invitation_id=create_data.get("invitation_id"),
        )

        # Create wallet
        bsc_pubkey = wallet.get("bsc_pubkey")
        sol_pubkey = wallet.get("sol_pubkey")
        bsc_secret = wallet.get("bsc_secret")
        sol_secret = wallet.get("sol_secret")

        logger.debug(f"Creating wallet for user {user.id}: bsc_pubkey={bsc_pubkey}, sol_pubkey={sol_pubkey}")

        if not all([bsc_pubkey, sol_pubkey, bsc_secret, sol_secret]):
            missing_fields = [field for field, value in [
                ("bsc_pubkey", bsc_pubkey),
                ("sol_pubkey", sol_pubkey),
                ("bsc_secret", bsc_secret),
                ("sol_secret", sol_secret)
            ] if not value]
            logger.error(f"Missing wallet fields for user {user.id}: {missing_fields}")
            raise ValueError(f"Missing required wallet fields: {missing_fields}")

        user_bsc_wallet = UserWallet(
            user_id=user.id,
            pubkey=bsc_pubkey,
            secret=bsc_secret,
            status="1",
            type="bsc",
        )
        user_sol_wallet = UserWallet(
            user_id=user.id,
            pubkey=sol_pubkey,
            secret=sol_secret,
            status="1",
            type="sol",
        )

        self.session.add(user_bsc_wallet)
        self.session.add(user_sol_wallet)
        logger.debug(f"Added wallet objects to session for user {user.id}")
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
        return user

    async def create_user_wallet(self, user_id: str, wallet: dict):
        from .logger import logger

        bsc_pubkey = wallet.get("bsc_pubkey")
        sol_pubkey = wallet.get("sol_pubkey")
        bsc_secret = wallet.get("bsc_secret")
        sol_secret = wallet.get("sol_secret")

        # Log the wallet data for debugging
        logger.debug(f"Creating wallet for user {user_id}: bsc_pubkey={bsc_pubkey}, sol_pubkey={sol_pubkey}")

        # Validate that all required fields are present
        if not all([bsc_pubkey, sol_pubkey, bsc_secret, sol_secret]):
            missing_fields = [field for field, value in [
                ("bsc_pubkey", bsc_pubkey),
                ("sol_pubkey", sol_pubkey),
                ("bsc_secret", bsc_secret),
                ("sol_secret", sol_secret)
            ] if not value]
            logger.error(f"Missing wallet fields for user {user_id}: {missing_fields}")
            raise ValueError(f"Missing required wallet fields: {missing_fields}")

        try:
            user_bsc_wallet = UserWallet(
                user_id=user_id,
                pubkey=bsc_pubkey,
                secret=bsc_secret,
                status="1",  # 1 - active, 0 - inactive
                type="bsc",
            )
            user_sol_wallet = UserWallet(
                user_id=user_id,
                pubkey=sol_pubkey,
                secret=sol_secret,
                status="1",  # 1 - active, 0 - inactive
                type="sol",
            )
            self.session.add(user_bsc_wallet)
            self.session.add(user_sol_wallet)
            logger.debug(f"Added wallet objects to session for user {user_id}")

        except Exception as e:
            logger.error(f"Failed to create wallet for user {user_id}: {e}")
            raise


async def get_user_db(session: AsyncSession = Depends(get_session)):
    yield UserDatabase(session)
