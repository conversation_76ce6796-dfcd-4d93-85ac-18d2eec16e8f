import asyncio

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

from .settings import settings
from .exceptions import TwilioRequestFailed
from .logger import logger


class TwilioClient:
    def __init__(self):
        self.service_id = settings.TWILIO_SERVICE_ID
        self.client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

    async def send_verification_code(self, phone_number: str) -> None:
        try:
            await asyncio.to_thread(
                self.client.verify.v2.services(self.service_id).verifications.create,
                to=phone_number,
                channel="sms",
                risk_check="disable"
            )
        except TwilioRestException as e:
            logger.error(e)
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid phone number")
        except Exception:
            raise TwilioRequestFailed

    async def verify(self, phone_number: str, code: str) -> bool:
        try:
            verification_check = await asyncio.to_thread(
                self.client.verify.v2.services(self.service_id).verification_checks.create,
                to=phone_number,
                code=code
            )
        except TwilioRestException as te:
            return False
        except Exception as e:
            logger.info(f"Request to twilio failed with the following exception:\n{e}")
            raise TwilioRequestFailed
        return verification_check.status == 'approved'
