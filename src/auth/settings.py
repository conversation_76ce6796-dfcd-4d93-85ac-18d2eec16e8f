from typing import Optional, List
from pydantic import HttpUrl
from pydantic_settings import BaseSettings, SettingsConfigDict

try:
    with open("auth/private.pem", "r") as private_key_file:
        private_key = private_key_file.read()
except:
    private_key = ""


class Settings(BaseSettings):
    model_config = SettingsConfigDict(extra="ignore")

    RECOMMENDER_TOKEN: str

    TWILIO_ACCOUNT_SID: str
    TWILIO_AUTH_TOKEN: str
    TWILIO_SERVICE_ID: str

    ALIYUN_SMS_ACCESS_KEY_ID: str
    ALIYUN_SMS_ACCESS_KEY_SECRET: str
    KMS_ADDRESS: str
    FSERVER_URL: Optional[HttpUrl] = None
    FSERVER_TOKEN: Optional[str] = None

    TURNSTILE_SECRET: str

    SMS_API_USERNAME: str
    SMS_API_PASSWORD: str
    SMS_API_BASE_URL: str

    # NXCloud
    SMS_NX_APPKEY: str
    SMS_NX_SECRET: str

    # SMS providers: "twilio", "nxcloud"
    SMS_OPTION: str = "nxcloud"

    # Google OAuth
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_AUD_ID: str = ""

    # Apple Sign In
    APPLE_CLIENT_ID: str = ""

    # OAuth security settings
    OAUTH_TOKEN_CACHE_MINUTES: int = 5  # Token verification result cache time (minutes)
    OAUTH_CLOCK_SKEW_SECONDS: int = 60  # Allowed clock skew (seconds)

    # VIP内测码功能开关
    ENABLE_VIP_CODE: bool = False

    DEFAULT_USER_AVATARS: str

    @property
    def default_user_avatar_list(self) -> List[str]:
        """Get default user avatars as a list"""
        if not self.DEFAULT_USER_AVATARS:
            return []
        return [url.strip() for url in self.DEFAULT_USER_AVATARS.split(',') if url.strip()]


settings = Settings()
