import logging
from typing import Optional
from datetime import <PERSON><PERSON><PERSON>

import uvicorn

from fastapi import Fast<PERSON><PERSON>, Depends, status, Request, BackgroundTasks, HTTPException
from fastapi.openapi.utils import get_openapi
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import select

from src.database.session import AsyncSession, get_session
from src.database.models import User
from src.database.models.TociIm import TociIm

from src.auth import current_user
from src.auth.schemas import AuthSchema, UserCreate, PhoneOrEmail, RegistrationCodeSchema, RegisterUserRead, \
    UpdateEmailOrPhoneResponse, UpdateVerifyCodeRequest, UpdateEmailCodeRequest, UpdateEmailOrPhoneRequest, \
    UpdatePhoneCodeRequest, HasEmailOrPhoneResponse, VerifyRegistrationCodeSchema, GoogleAuthRequest, AppleAuthRequest, \
    OAuthRegistrationRequiredResponse, LoginResponse, <PERSON>ce<PERSON><PERSON>, AppleEmailSendCodeRequest, AppleEmailVerifyCodeRequest, \
    VipCodeVerifyRequest, VipCodeVerifyResponse

from src.infra.logger import get_logger
from src.infra.app import create_app
from src.common.im_service import ImService
from src.common.exceptions import HTTPError, NotFoundError

from src.auth.service import AuthService
from src.auth.settings import settings

logger = get_logger("auth", level="INFO", file_path="latest.log")


app = create_app(title="Auth Service", version="1.0.1", description="Auth Service API", request_logger=logger)


def check_phone(phone_number: str):
    if phone_number.startswith("+CN") or phone_number.startswith("+86"):
        return True
    return False


@app.exception_handler(HTTPError)
async def http_error_handler(request: Request, exc: HTTPError):
    return JSONResponse(
        status_code=exc.error_code,
        content={"message": exc.detail},
    )

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get(
    "/vip_code_status",
    status_code=status.HTTP_200_OK,
    summary="VIP内测码功能状态",
    description="返回VIP内测码功能是否开启的状态信息"
)
async def vip_code_status():
    """
    返回VIP内测码功能的开启状态
    
    **返回说明:**
    - **enabled**: VIP内测码功能是否开启
    - **message**: 状态描述信息
    """
    return {
        "enabled": settings.ENABLE_VIP_CODE,
        "message": "VIP内测码功能已开启" if settings.ENABLE_VIP_CODE else "VIP内测码功能已关闭"
    }

@app.post(
    "/register",
    response_model=RegisterUserRead,
    status_code=status.HTTP_201_CREATED,
    summary="User Registration",
    description="Register a new user. Supports both traditional registration (with verification) and OAuth registration (no verification required)."
)
async def register(
    user: UserCreate,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Register a new user
    
    This endpoint supports two types of registration:
    
    **Traditional Registration:**
    - Requires email or phone number verification
    - User must call /register/code and /register/verify_code first
    - Fields: email OR phone_number, name, username, region, invitation_id (optional), vip_code (optional)
    
    **OAuth Registration:**
    - No verification required (OAuth providers handle verification)
    - Used after OAuth login returns "registration required" response
    - Fields: email OR phone_number, name, username, region, google_id OR apple_id, avatar_url (optional), vip_code (optional)
    
    The system automatically detects which type of registration based on the presence of OAuth fields (google_id/apple_id).
    
    **vip_code**: 内测码，可选参数，用于内测用户注册验证。当VIP内测码功能关闭时，此参数将被忽略。
    """
    # Check if this is OAuth registration (has OAuth ID)
    is_oauth_registration = bool(user.google_id) or bool(user.apple_id)
    
    # 如果VIP内测码功能关闭，清空VIP码参数
    if not settings.ENABLE_VIP_CODE:
        user.vip_code = None
        logger.info("VIP内测码功能已关闭，跳过VIP码验证")
    
    if is_oauth_registration:
        # OAuth registration - no verification needed
        return await service.register_with_oauth(user)
    
    # Traditional registration - requires verification
    if user.email:
        user.email = user.email.strip()
        return await service.register_with_email(user)
    if user.phone_number:
        user.phone_number = user.phone_number.strip()
        if check_phone(user.phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        return await service.register_with_phone(user)
    raise HTTPError


@app.post(
    "/register/code",
    status_code=status.HTTP_200_OK
)
async def get_register_key(
        schema: RegistrationCodeSchema,
        request: Request,
        service: AuthService = Depends(AuthService.get_new_instance)
):
    # Verify Turnstile token
    client_ip = request.client.host
    is_valid_turnstile = await service.verify_turnstile(schema.turnstile_token, client_ip)
    if not is_valid_turnstile:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Human verification failed. Please try again."
        )
    
    if schema.email:
        schema.email = schema.email.strip()
        return await service.send_registration_letter(
            lang=schema.lang,
            email=schema.email,
            invitation_id=schema.invitation_id
        )
    if schema.phone_number:
        schema.phone_number = schema.phone_number.strip()
        if check_phone(schema.phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        return await service.send_verification_code(phone_number=schema.phone_number)
    raise HTTPError


@app.post(
    "/register/verify_code",
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def verify_register_code(
        schema: VerifyRegistrationCodeSchema,
        service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Verify if the register verification code is correct
    """
    if schema.email:
        schema.email = schema.email.strip()
        is_correct = await service.verify_registration_code(
            verification_code=schema.verification_code,
            email=schema.email
        )
    elif schema.phone_number:
        schema.phone_number = schema.phone_number.strip()
        if check_phone(schema.phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        is_correct = await service.verify_registration_code(
            verification_code=schema.verification_code,
            phone_number=schema.phone_number
        )
    else:
        raise HTTPError
    
    if is_correct:
        return UpdateEmailOrPhoneResponse(
            success=True, 
            message="Verification code is correct."
        )
    else:
        return UpdateEmailOrPhoneResponse(
            success=False, 
            message="Verification code is incorrect."
        )


@app.post(
    "/login/code",
    status_code=status.HTTP_200_OK
)
async def get_login_key(
        phone_or_email: PhoneOrEmail,
        service: AuthService = Depends(AuthService.get_new_instance)
):
    if phone_or_email.email:
        phone_or_email.email = phone_or_email.email.strip()
        return await service.send_login_email(email=phone_or_email.email)
    if phone_or_email.phone_number:
        phone_or_email.phone_number = phone_or_email.phone_number.strip()
        if check_phone(phone_or_email.phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        return await service.send_verification_code(phone_number=phone_or_email.phone_number)
    raise HTTPError


@app.post(
    "/login",
    status_code=status.HTTP_200_OK,
    response_model=LoginResponse,
    summary="User Login",
    description="Login using email or phone number with verification code"
)
async def login(
        auth_schema: AuthSchema,
        service: AuthService = Depends(AuthService.get_new_instance)
):
    if auth_schema.email:
        auth_schema.email = auth_schema.email.strip()
        return await service.email_login(email=auth_schema.email, verification_code=auth_schema.verification_code)
    if auth_schema.phone_number:
        auth_schema.phone_number = auth_schema.phone_number.strip()
        if check_phone(auth_schema.phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        return await service.phone_login(
            phone_number=auth_schema.phone_number,
            verification_code=auth_schema.verification_code
        )
    raise HTTPError


@app.get(
    "/account_info",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=HasEmailOrPhoneResponse
)
async def has_email_or_phone(
    user: User = Depends(current_user),
):
    
    return HasEmailOrPhoneResponse(
        has_email=True if user.email else False,
        has_phone=True if user.phone_number else False,
    )


@app.post(
    "/add/email/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def send_add_email_verification_code(
    request: UpdateEmailCodeRequest = None,
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    if request:
        new_email = request.email.strip()
        return await service.send_add_email_code(user, new_email, background_tasks)
    else:
        return await service.verification_send_code(False, user, AuthService.add_email_endpoint, background_tasks)

@app.post(
    "/add/email/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def verify_add_email_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Send verification code to user's current email address
    """
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    return await service.verification_verify_code(False, user, AuthService.add_email_endpoint, AuthService.add_email_verified_endpoint, request.verification_code)

@app.post(
    "/add/email",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def add_email(
    request: UpdateEmailOrPhoneRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    return await service.verify_add_email_code(user, request.email.strip(), request.verification_code)


@app.post(
    "/add/phone/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def send_add_phone_verification_code(
    request: UpdatePhoneCodeRequest = None,
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    if request:
        new_phone_number = request.phone_number.strip()
        if check_phone(new_phone_number):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Your phone number's country or region is not supported."
            )
        return await service.send_add_phone_code(user, new_phone_number, background_tasks)
    else:
        return await service.verification_send_code(True, user, AuthService.add_phone_endpoint, background_tasks)

@app.post(
    "/add/phone/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def verify_add_phone_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    if check_phone(request.phone_number.strip()):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your phone number's country or region is not supported."
        )
    return await service.verification_verify_code(True, user, AuthService.add_phone_endpoint, AuthService.add_phone_verified_endpoint, request.verification_code)

@app.post(
    "/add/phone",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def add_phone(
    request: UpdateEmailOrPhoneRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has email and phone number."
        )
    if check_phone(request.phone_number.strip()):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your phone number's country or region is not supported."
        )
    return await service.verify_add_phone_code(user, request.phone_number.strip(), request.verification_code)

@app.post(
    "/remove/phone/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_phone_send_code(
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.verification_send_code(False, user, AuthService.remove_phone_endpoint, background_tasks)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can't remove email or phone, at least one of email or phone number is required."
        )

@app.post(
    "/remove/phone/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_phone_verify_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.verification_verify_code(False, user, AuthService.remove_phone_endpoint, AuthService.remove_phone_verified_endpoint, request.verification_code)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of email or phone number is required."
        )
@app.post(
    "/remove/email/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_email_send_code(
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.verification_send_code(True, user, AuthService.remove_email_endpoint, background_tasks)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can't remove email or phone, at least one of email or phone number is required."
        )

@app.post(
    "/remove/email/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_email_verify_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.verification_verify_code(True, user, AuthService.remove_email_endpoint, AuthService.remove_email_verified_endpoint, request.verification_code)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of email or phone number is required."
        )
@app.post(
    "/remove/phone",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_phone(
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.remove_phone(user)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of email or phone number is required."
        )

@app.post(
    "/remove/email",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def remove_phone(
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if user.phone_number and user.email:
        return await service.remove_email(user)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of email or phone number is required."
        )
    

@app.post(
    "/update/email/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def send_update_email_verification_code(
    request: UpdateEmailCodeRequest = None,
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    email = request.email.strip() if request else None
    return await service.send_email_update_verification_code(user, email, background_tasks)

@app.post(
    "/update/email/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def verify_current_email_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Send verification code to user's current email address
    """
    return await service.verify_email_update_verification_code(user, request.verification_code)

@app.post(
    "/update/email",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def update_email(
    request: UpdateEmailOrPhoneRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.update_email_or_phone(
        user=user,
        new_email=request.email.strip(),
        new_phone_number=None,
        verification_code=request.verification_code
    )


@app.post(
    "/update/phone/send_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def send_update_phone_verification_code(
    request: UpdatePhoneCodeRequest = None,
    background_tasks: BackgroundTasks = None,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    phone_number = request.phone_number.strip() if request else None
    if check_phone(phone_number):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your phone number's country or region is not supported."
        )
    return await service.send_phone_update_verification_code(user, phone_number, background_tasks)


@app.post(
    "/update/phone/verify_code",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def verify_current_phone_code(
    request: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Send verification code to user's current email address
    """
    return await service.verify_phone_update_verification_code(user, request.verification_code)

@app.post(
    "/update/phone",
    tags=["Update Contact Info"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def update_phone(
    request: UpdateEmailOrPhoneRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    if check_phone(request.phone_number.strip()):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your phone number's country or region is not supported."
        )
    return await service.update_email_or_phone(
        user=user,
        new_email=None,
        new_phone_number=request.phone_number.strip(),
        verification_code=request.verification_code
    )

@app.get("/im_update_token")
async def update_token(
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session)
):
    stmt = select(TociIm).where(TociIm.toci_id == user.id)
    im_user = (await session.execute(stmt)).scalar()
    if im_user is None:
        logger.error(f"OpenIM user not found (id={user.id})")
        raise NotFoundError(detail="OpenIM user not found")

    im_service = ImService(session)
    try:
        im_login_result = await im_service.login(
            login=im_user.im_login,
            password=im_user.im_password
        )
    except Exception as e:
        logger.error(f"OpenIM login failed")
        logger.error(e)
        raise HTTPError

    return im_login_result

@app.post(
    "/export_key/phone/send_code",
    tags=["Export Key"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def export_key_send(
    background_tasks: BackgroundTasks,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.verification_send_code(False, user, AuthService.export_key_endpoint, background_tasks)


@app.post(
    "/export_key/email/send_code",
    tags=["Export Key"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def export_key_send(
    background_tasks: BackgroundTasks,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.verification_send_code(True, user, AuthService.export_key_endpoint, background_tasks)


@app.post(
    "/export_key/email/verify_code",
    tags=["Export Key"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def export_key_verify(
    req: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.verification_verify_code(True, user, AuthService.export_key_endpoint, AuthService.export_key_email_verified_endpoint, req.verification_code)

@app.post(
    "/export_key/phone/verify_code",
    tags=["Export Key"],
    status_code=status.HTTP_200_OK,
    response_model=UpdateEmailOrPhoneResponse
)
async def export_key_verify(
    req: UpdateVerifyCodeRequest,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.verification_verify_code(False, user, AuthService.export_key_endpoint, AuthService.export_key_phone_verified_endpoint, req.verification_code)

@app.post(
    "/login/google",
    status_code=status.HTTP_200_OK,
    summary="Google OAuth Login",
    description="Login using Google ID token. If user doesn't exist, returns registration required response.",
    responses={
        200: {"description": "Login successful, returns access token and user ID", "model": LoginResponse},
        202: {"description": "User not found, registration required", "model": OAuthRegistrationRequiredResponse},
        401: {"description": "Invalid Google token"},
        500: {"description": "Internal server error"}
    }
)
async def google_login(
    auth_request: GoogleAuthRequest,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Login using Google ID token
    
    This endpoint handles Google OAuth login with the following flow:
    - If user exists with Google ID: Standard login
    - If user exists with same email: Links Google account to existing user and logs in
    - If user doesn't exist: Returns registration required response (status 202)
    
    When registration is required, frontend should:
    1. Collect additional user information (username, region, etc.)
    2. Call the /register endpoint with the OAuth user info
    
    - **id_token**: Google provided ID token
    
    Returns either login success (same format as /login) or registration required response
    """
    try:
        result = await service.google_login(auth_request.id_token)

        if isinstance(result, OAuthRegistrationRequiredResponse):
            return JSONResponse(
                status_code=status.HTTP_202_ACCEPTED,
                content=result.model_dump()
            )

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in Google login endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login service temporarily unavailable"
        )

@app.post(
    "/login/apple",
    status_code=status.HTTP_200_OK,
    summary="Apple Sign In Login",
    description="Login using Apple ID token. If user doesn't exist, returns registration required response.",
    responses={
        200: {"description": "Login successful, returns access token and user ID", "model": LoginResponse},
        202: {"description": "User not found, registration required", "model": OAuthRegistrationRequiredResponse},
        400: {"description": "Missing required email information"},
        401: {"description": "Invalid Apple token"},
        500: {"description": "Internal server error"}
    }
)
async def apple_login(
    auth_request: AppleAuthRequest,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Login using Apple ID token
    
    This endpoint handles Apple OAuth login with the following flow:
    - If user exists with Apple ID: Standard login
    - If user exists with same email: Links Apple account to existing user and logs in
    - If user doesn't exist: Returns registration required response (status 202)
    
    When registration is required, frontend should:
    1. Collect additional user information (username, region, etc.)
    2. Call the /register endpoint with the OAuth user info
    
    - **id_token**: Apple provided ID token
    - **name**: User name (optional, only provided by Apple on first login)
    
    Returns either login success (same format as /login) or registration required response
    """
    try:
        result = await service.apple_login(auth_request.id_token, auth_request.name)

        if isinstance(result, OAuthRegistrationRequiredResponse):
            return JSONResponse(
                status_code=status.HTTP_202_ACCEPTED,
                content=result.model_dump()
            )

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in Apple login endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login service temporarily unavailable"
        )

@app.post(
    "/login/apple/email/send_code",
    status_code=status.HTTP_200_OK,
    summary="Send Apple email verification code",
    response_model=UpdateEmailOrPhoneResponse
)
async def apple_email_send_code(
    req: AppleEmailSendCodeRequest,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Send a verification code to the email associated with the Apple account (from token or request body).
    """
    return await service.send_apple_email_code(req.apple_id, req.email, req.lang)


@app.post(
    "/login/apple/email/verify_code",
    status_code=status.HTTP_200_OK,
    summary="Verify Apple email verification code",
    response_model=UpdateEmailOrPhoneResponse
)
async def apple_email_verify_code(
    req: AppleEmailVerifyCodeRequest,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    Verify the verification code sent to the Apple email.
    """
    return await service.verify_apple_email_code(req.apple_id, req.verification_code, req.email)

@app.post(
    "/devices",
    status_code=status.HTTP_201_CREATED,
    summary="Register new device",
    description="Register a new device to the user's account."
)
async def add_device(
    device_data: DeviceCreate,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.register_device(device_data, user.id)

@app.delete(
    "/devices/{device_id}",
    status_code=status.HTTP_200_OK,
    summary="Unregister device",
    description="Unregister a device from the user's account."
)
async def remove_device(
    device_id: str,
    user: User = Depends(current_user),
    service: AuthService = Depends(AuthService.get_new_instance)
):
    return await service.unregister_device(device_id, user.id)


@app.post(
    "/verify_vip_code",
    response_model=dict,
    summary="验证VIP码",
    description="""
    验证用户输入的VIP码是否有效。
    
    **参数说明:**
    - **vip_code**: VIP码（必填）
    - **device_id**: 设备ID（必填）
    - **device_brand**: 设备品牌（必填）
    
    **验证规则:**
    - VIP码必须处于有效期内（未过期且未达到最大使用次数）
    - 如果VIP码已绑定设备ID，则入参的 device_id 必须与数据库记录中绑定的设备ID一致
    - 验证成功时，会将 device_id 和 device_brand 保存到数据库进行绑定
    - 验证失败超过3次后，设备将被锁定1小时
    
    **返回说明:**
    - **success**: 验证是否成功
    - **message**: 验证结果消息
    - **bind_device_brand**: 绑定的设备品牌（成功时）
    
    **注意:**
    - 验证失败超过3次后，设备将被锁定1小时
    - 锁定期间无法进行任何VIP码验证
    - 验证成功时会清除失败记录
    """,
    responses={
        200: {"description": "验证成功"},
        400: {"description": "验证失败"},
        500: {"description": "服务器内部错误"}
    }
)
async def verify_vip_code(
    request: VipCodeVerifyRequest,
    service: AuthService = Depends(AuthService.get_new_instance)
):
    """
    验证VIP码
    """
    result = await service.verify_vip_code(request.vip_code, request.device_id, request.device_brand)
    return result