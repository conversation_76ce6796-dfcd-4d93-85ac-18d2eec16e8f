import os
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_dysmsapi20170525.models import SendSmsResponse
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models
from .logger import logger
from src.auth.exceptions import AliyunSMSException
from src.auth.settings import settings


class AliyunSMS:
    def __init__(self):
        config = open_api_models.Config(
            access_key_id=settings.ALIYUN_SMS_ACCESS_KEY_ID,
            access_key_secret=settings.ALIYUN_SMS_ACCESS_KEY_SECRET
        )
        # Endpoint refers to https://api.aliyun.com/product/Dysmsapi
        config.endpoint = f'dysmsapi.aliyuncs.com'
        self.client = Dysmsapi20170525Client(config)

    async def send_verification_code(self, phone_number: str, code: str) -> bool:
        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=phone_number,
            sign_name='动悉智能',  # Don't change, it's a fixed value.
            template_code='SMS_478610596',  # Don't change, it's a fixed value.
            template_param=f'{{"code":"{code}"}}'
        )
        try:
            response: SendSmsResponse = await self.client.send_sms_with_options_async(send_sms_request, util_models.RuntimeOptions())
            if response.body.code != 'OK':
                logger.info(f"Request to aliyun failed with the following message:\n{response.body.message}")
                raise AliyunSMSException(response.body.message)
            return True
        except Exception as e:
            logger.info(f"Request to aliyun failed with the following exception:\n{e}")
            return False
