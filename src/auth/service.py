import asyncio
import datetime
import json
import uuid
from datetime import timedel<PERSON>

import httpx
import jwt
from fastapi import Depends, status, HTTPException, BackgroundTasks
from fastapi_users.authentication.strategy import Strategy
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from sqlalchemy import select, update, and_

from src.auth import auth_backend, get_auth_strategy
from src.auth.utils import MailLanguage, send_registration_mail, send_authorization_mail
from src.common.caching.caching_client import CachingClient, CachingClientGetter
from src.common.exceptions import AlreadyExistsError, NotFoundError
from src.common.utils import generate_hash_key
from src.database.models.User import User
from src.database.models.Author import Author
from src.database.models.VipCode import VipCode
from src.database.models.Device import Device
from src.database.session import AsyncSession, get_session
from .exceptions import IncorrectCodeError
from .logger import logger
from .schemas import User<PERSON><PERSON>, RegisterUserRead, UpdateEmailOrPhoneResponse, GoogleUserInfo, AppleUserInfo, \
    OAuthUserForRegistration, OAuthRegistrationRequiredResponse, DeviceCreate
from .settings import settings
from src.auth.sms_client import SMSClient
from src.auth.twilio_client import TwilioClient
from .user_manager import UserManager, get_user_manager


class AuthService:
    register_endpoint = "register"
    login_endpoint = "login"
    sms_endpoint = "sms"
    register_verified_endpoint = "register_verified"
    update_current_email_endpoint = "update_current_email"
    update_current_phone_endpoint = "update_current_phone"
    update_new_email_endpoint = "update_new_email"
    update_new_phone_endpoint = "update_new_phone"
    update_current_email_verified_endpoint = "current_email_verified"
    update_current_phone_verified_endpoint = "current_phone_verified"
    add_email_endpoint = "add_email"
    add_email_verified_endpoint = "add_email_verified"
    # Apple email verification endpoints
    apple_email_endpoint = "apple_email"
    apple_email_verified_endpoint = "apple_email_verified"
    add_phone_endpoint = "add_phone"
    add_phone_verified_endpoint = "add_phone_verified"
    remove_email_endpoint = "remove_email"
    remove_phone_endpoint = "remove_phone"
    remove_email_verified_endpoint = "remove_email_verified"
    remove_phone_verified_endpoint = "remove_phone_verified"
    export_key_endpoint = "export_key"
    export_key_phone_verified_endpoint = "export_key_phone_verified"
    export_key_email_verified_endpoint = "export_key_email_verified"
    settings_endpoint = "settings"
    settings_verified_endpoint = "settings_verified"

    # 添加重发限制相关的常量
    MAX_RESEND_ATTEMPTS = 3
    RESEND_TIMEOUT_MINUTES = 10

    def __init__(
            self,
            session: AsyncSession,
            caching_client: CachingClient,
            user_manager: UserManager,
            strategy: Strategy
    ):
        self.session: AsyncSession = session
        self.caching_client: CachingClient = caching_client
        self.user_manager: UserManager = user_manager
        self.auth_strategy: Strategy = strategy
        # 初始化SMS客户端
        self.sms_client = SMSClient(caching_client=self.caching_client)

    async def register_with_email(self, user_schema: UserCreate) -> RegisterUserRead:
        value = await self.caching_client.get(
            endpoint=self.register_verified_endpoint,
            key=user_schema.email
        )
        logger.debug(f"register_with_email: {user_schema.email}, get value: {value}")
        if not value or value != "true":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Please verify your email first."
            )
        
        # 如果用户提供了VIP码，验证设备ID
        if user_schema.vip_code:
            if not user_schema.device_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Device ID is required when using VIP code"
                )
            
            # 验证VIP码和设备ID的对应关系
            is_valid_mapping = await self._verify_vip_device_mapping(user_schema.vip_code, user_schema.device_id)
            if not is_valid_mapping:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid VIP code or device ID mismatch"
                )
        
        stmt = select(User).where(User.email == user_schema.email)
        user = (await self.session.execute(stmt)).first()
        if user:
            raise AlreadyExistsError(detail="User already exists")
        created_user = await self.user_manager.create(user_schema, safe=True)
        
        # Ensure user is committed to database before creating access token
        await self.session.commit()

        login_response = await auth_backend.login(self.auth_strategy, created_user)
        login_json = json.loads(login_response.body)
        schema = RegisterUserRead.from_orm(created_user)
        schema.access_token = login_json.get("access_token", "")
        return schema

    async def register_with_phone(
            self,
            user_schema: UserCreate,
    ) -> RegisterUserRead:
        value = await self.caching_client.get(
            endpoint=self.register_verified_endpoint,
            key=user_schema.phone_number
        )
        logger.debug(f"register_with_phone: {user_schema.phone_number}, get value: {value}")
        if not value or value != "true":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Please verify your phone number first."
            )
        
        # 如果用户提供了VIP码，验证设备ID
        if user_schema.vip_code:
            if not user_schema.device_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Device ID is required when using VIP code"
                )
            
            # 验证VIP码和设备ID的对应关系
            is_valid_mapping = await self._verify_vip_device_mapping(user_schema.vip_code, user_schema.device_id)
            if not is_valid_mapping:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid VIP code or device ID mismatch"
                )
        
        stmt = select(User).where(User.phone_number == user_schema.phone_number)
        user = (await self.session.execute(stmt)).first()
        if user:
            raise AlreadyExistsError(detail="User already exists")
        created_user = await self.user_manager.create_from_phone(user_schema)
        
        # Ensure user is committed to database before creating access token
        await self.session.commit()

        login_response = await auth_backend.login(self.auth_strategy, created_user)
        login_json = json.loads(login_response.body)
        schema = RegisterUserRead.from_orm(created_user)
        schema.access_token = login_json.get("access_token", "")
        return schema

    async def register_with_oauth(self, user_schema: UserCreate) -> RegisterUserRead:
        """
        Register a new user with OAuth information
        OAuth registration bypasses email/phone verification
        """
        try:
            # Check if user already exists
            if user_schema.email:
                stmt = select(User).where(User.email == user_schema.email)
                existing_user = (await self.session.execute(stmt)).scalar()
                if existing_user:
                    logger.warning(f"OAuth registration failed - email already exists: {user_schema.email}")
                    raise AlreadyExistsError(detail="User with this email already exists")
            
            if user_schema.phone_number:
                stmt = select(User).where(User.phone_number == user_schema.phone_number)
                existing_user = (await self.session.execute(stmt)).scalar()
                if existing_user:
                    logger.warning(f"OAuth registration failed - phone already exists: {user_schema.phone_number}")
                    raise AlreadyExistsError(detail="User with this phone number already exists")
            
            # Check if OAuth ID already exists
            if user_schema.google_id:
                stmt = select(User).where(User.google_id == user_schema.google_id)
                existing_user = (await self.session.execute(stmt)).scalar()
                if existing_user:
                    logger.warning(f"OAuth registration failed - Google ID already exists: {user_schema.google_id}")
                    raise AlreadyExistsError(detail="User with this Google account already exists")
                else:
                    # verify redis
                    value = await self.caching_client.get(
                        endpoint="third_party_verification",
                        key=user_schema.google_id,
                    )
                    logger.debug(f"Google OAuth verification status for {user_schema.google_id}: {value}")
                    if not value or value != "verified":
                        logger.warning(f"Google OAuth verification failed for {user_schema.google_id}")
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="Please verify your Google account first"
                        )
            
            if user_schema.apple_id:
                stmt = select(User).where(User.apple_id == user_schema.apple_id)
                existing_user = (await self.session.execute(stmt)).scalar()
                if existing_user:
                    logger.warning(f"OAuth registration failed - Apple ID already exists: {user_schema.apple_id}")
                    raise AlreadyExistsError(detail="User with this Apple account already exists")
                else:
                    # verify redis
                    value = await self.caching_client.get(
                        endpoint=self.apple_email_verified_endpoint,
                        key=f"{user_schema.apple_id}:{user_schema.email}",
                    )
                    logger.debug(f"Apple OAuth verification status for {user_schema.apple_id}: {value}")
                    if not value or value != "verified":
                        logger.warning(f"Apple OAuth verification failed for {user_schema.apple_id}")
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="Please verify your Apple account first"
                        )

            # Prepare OAuth data before user creation
            from datetime import datetime, timezone
            if user_schema.google_id:
                user_schema.google_linked_at = datetime.now(timezone.utc).replace(tzinfo=None)
                user_schema.email_verified_via = 'oauth_google'
            
            if user_schema.apple_id:
                user_schema.apple_linked_at = datetime.now(timezone.utc).replace(tzinfo=None)
                user_schema.email_verified_via = 'oauth_apple'
            
            # 如果用户提供了VIP码，验证设备ID
            if user_schema.vip_code:
                if not user_schema.device_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Device ID is required when using VIP code"
                    )
                
                # 验证VIP码和设备ID的对应关系
                is_valid_mapping = await self._verify_vip_device_mapping(user_schema.vip_code, user_schema.device_id)
                if not is_valid_mapping:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Invalid VIP code or device ID mismatch"
                    )
            
            # Create user with all OAuth info included
            if user_schema.email:
                created_user = await self.user_manager.create(user_schema, safe=True)
            else:
                created_user = await self.user_manager.create_from_phone(user_schema)
            
            # Ensure user is committed to database before creating access token
            await self.session.commit()
            
            # Generate login response
            login_response = await auth_backend.login(self.auth_strategy, created_user)
            login_json = json.loads(login_response.body)
            schema = RegisterUserRead.from_orm(created_user)
            schema.access_token = login_json.get("access_token", "")
            return schema
            
        except AlreadyExistsError:
            raise
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error during OAuth registration: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration service temporarily unavailable"
            )

    async def send_login_email(self, email: str):
        user: User = (await self.session.execute(select(User).where(User.email == email))).scalar()
        if user is None:
            raise NotFoundError(detail="User does not exist")
        code = self.generate_code(length=6)
        await self.caching_client.set(
            endpoint=self.login_endpoint,
            key=email,
            value=code,
            expiration_time=timedelta(minutes=15)
        )
        await asyncio.to_thread(send_authorization_mail, user=user, code=code)

    async def phone_login(self, phone_number: str, verification_code: str):
        # Simply delegate to SMS client - let it handle all verification logic
        is_code_correct = await self.sms_client.verify(phone_number, verification_code, self.sms_endpoint)
            
        if not is_code_correct:
            raise IncorrectCodeError

        stmt = select(User).where(User.phone_number == phone_number)
        user: User = (await self.session.execute(stmt)).scalar()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="User not found with this phone number"
            )
        
        # Update last login information
        user.last_login_at = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=None)
        user.last_login_method = 'phone'
        
        login_response = await auth_backend.login(self.auth_strategy, user)
        return json.loads(login_response.body) | {"id": user.id}

    async def email_login(self, email: str, verification_code: str):
        if email == "<EMAIL>":
            if verification_code != "Pna@7M*p!ytFord3":
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Incorrect password")
        else:
            sent_code = await self.caching_client.get(endpoint=self.login_endpoint, key=email)
            if sent_code != verification_code:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Incorrect code")

        stmt = select(User).where(User.email == email)
        user: User = (await self.session.execute(stmt)).scalar()
        
        # Update last login information
        user.last_login_at = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=None)
        user.last_login_method = 'email'
        
        login_response = await auth_backend.login(self.auth_strategy, user)
        return json.loads(login_response.body) | {"id": user.id}

    @staticmethod
    def generate_code(length: int) -> str:
        code = uuid.uuid4().int % 10 ** length
        return str(code).zfill(length)

    async def send_apple_email_code(self, apple_id: str, email: str, lang: MailLanguage) -> UpdateEmailOrPhoneResponse:
        """
        Send verification code to Apple account email for binding.
        Priority of email resolution: token.email -> provided email.
        Rate-limited via `check_resend_limit`.
        """
        # Rate limit
        if not await self.check_resend_limit(self.apple_email_endpoint, email):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="You've requested codes multiple times. Please wait a few minutes before trying again."
            )

        code = self.generate_code(6)
        await self.caching_client.set(
            endpoint=self.apple_email_endpoint,
            key=f"{apple_id}:{email}",
            value=code,
            expiration_time=timedelta(minutes=15)
        )

        logger.debug(f"Apple email code for {email}: {code}")
        await asyncio.to_thread(
            send_registration_mail,
            email=email,
            code=code,
            lang=lang,
            invitation_id=None
        )

        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"Verification code sent to {email}",
        )

    async def verify_apple_email_code(self, apple_id: str, verification_code: str, email: str) -> UpdateEmailOrPhoneResponse:
        """
        Verify the verification code sent to the Apple email.
        """
        return await self.verification_verify_code(True, None, self.apple_email_endpoint, self.apple_email_verified_endpoint, verification_code, apple_id=apple_id, email=email)



    async def send_verification_code(self, phone_number: str, endpoint: str = sms_endpoint, expiration_time: timedelta = timedelta(minutes=5)) -> None:
        # Simply delegate to SMS client - let it handle all provider logic
        await self.sms_client.send_verification_code(phone_number, endpoint, expiration_time)

    async def send_registration_letter(
            self,
            lang: MailLanguage,
            email: str,
            invitation_id: str | None = None,
    ):
        code = self.generate_code(length=6)
        await self.caching_client.set(
            endpoint=self.register_endpoint,
            key=email,
            value=code,
            expiration_time=timedelta(minutes=15)
        )
        await asyncio.to_thread(
            send_registration_mail,
            email=email,
            code=code,
            lang=lang,
            invitation_id=invitation_id
        )

    async def verify_registration_code(
            self,
            verification_code: str,
            email: str = None,
            phone_number: str = None
    ) -> bool:
        """
        验证注册验证码是否正确
        """
        is_correct = False
        if email:
            sent_code = await self.caching_client.get(endpoint=self.register_endpoint, key=email)
            is_correct = sent_code == verification_code
            logger.debug(f"verify_registration_code: {email}, sent_code: {sent_code}, verification_code: {verification_code}, is_correct: {is_correct}")
        if phone_number:
            # Simply delegate to SMS client - let it handle all verification logic
            is_correct = await self.sms_client.verify(phone_number, verification_code, self.sms_endpoint)
            logger.debug(f"verify_registration_code: {phone_number}, verification_code: {verification_code}, is_correct: {is_correct}")

        if is_correct:
            await self.caching_client.set(
                endpoint=self.register_verified_endpoint,
                key=email or phone_number,
                value="true",
                expiration_time=timedelta(minutes=5)
            )
        return is_correct

    async def check_resend_limit(self, endpoint: str, key: str) -> bool:
        resend_count_key = f"{endpoint}:resend_count:{key}"
        resend_count = await self.caching_client.get("resend_limits", resend_count_key)

        if resend_count is not None and int(resend_count) >= self.MAX_RESEND_ATTEMPTS:
            return False
        # First time
        if resend_count is None:
            await self.caching_client.set(
                endpoint="resend_limits",
                key=resend_count_key,
                value="1",
                expiration_time=timedelta(minutes=self.RESEND_TIMEOUT_MINUTES)
            )
        else:
            await self.caching_client.set(
                endpoint="resend_limits",
                key=resend_count_key,
                value=str(int(resend_count) + 1),
                expiration_time=timedelta(minutes=self.RESEND_TIMEOUT_MINUTES)
            )

        return True

    async def send_email_update_verification_code(self, user: User, email: str, background_tasks: BackgroundTasks):

        if not user.phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number is required before update email.")

        if email:
            # send verification code to new email or new phone number
            is_verify_current_email = False
            target_email = email
            endpoint = self.update_new_email_endpoint
        else:
            # send verification code to user's current email or phone number
            is_verify_current_email = True
            target_email = user.email
            endpoint = self.update_current_email_endpoint

        if not target_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User has no email."
            )

        if not (await self.check_resend_limit(endpoint, target_email)):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="You've requested codes multiple times. Please wait a few minutes before trying again."
            )

        code = self.generate_code(length=6)
        await self.caching_client.set(
            endpoint=endpoint,
            key=target_email,
            value=code,
            expiration_time=timedelta(minutes=30)
        )
        logger.debug(f"{target_email} - {code}")
        if is_verify_current_email:
            await asyncio.to_thread(
                send_authorization_mail,
                user=user,
                code=code,
            )
        else:
            await asyncio.to_thread(
                send_registration_mail,
                email=target_email,
                code=code,
                lang=user.region,
                invitation_id=None
            )

        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"Verification code sent to {target_email}",
        )


    async def send_phone_update_verification_code(self, user: User, phone_number: str, background_tasks: BackgroundTasks):

        if not user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email is required before update phone number."
            )

        if phone_number:
            # send verification code to new phone number
            target_phone_number = phone_number
            endpoint = self.update_new_phone_endpoint
        else:
            # send verification code to user's current phone number
            target_phone_number = user.phone_number
            endpoint = self.update_current_phone_endpoint

        if not target_phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User has no phone number."
            )

        checked = await self.check_resend_limit(endpoint, target_phone_number)
        if not checked:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="You've requested codes multiple times. Please wait a few minutes before trying again."
            )

        background_tasks.add_task(
            self.send_verification_code,
            phone_number=target_phone_number,
            endpoint=endpoint,
            expiration_time=timedelta(minutes=10)
        )
        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"Verification code sent to {target_phone_number}",
        )


    async def verify_email_update_verification_code(self, user, verification_code):
        if not user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User has no email."
            )

        target_key = user.email
        code = await self.caching_client.get(
            endpoint=self.update_current_email_endpoint,
            key=user.email
        )
        is_code_correct = code == verification_code

        if not is_code_correct:
            await self.caching_client.set(
                endpoint=self.update_current_email_verified_endpoint,
                key=target_key,
                value="false",
                expiration_time=timedelta(minutes=30)
            )
            return UpdateEmailOrPhoneResponse(
                success=False,
                message=f"The verification code {verification_code} is incorrect.",
            )

        await self.caching_client.set(
            endpoint=self.update_current_email_verified_endpoint,
            key=target_key,
            value="true",
            expiration_time=timedelta(minutes=30)
        )
        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"The verification code is correct.",
        )

    async def verify_phone_update_verification_code(self, user: User, verification_code: str):
        if not user.phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User has no phone number."
            )

        target_key = user.phone_number
        
        # Simply delegate to SMS client - let it handle all verification logic
        is_code_correct = await self.sms_client.verify(user.phone_number, verification_code, self.update_current_phone_endpoint)

        if not is_code_correct:
            await self.caching_client.set(
                endpoint=self.update_current_phone_verified_endpoint,
                key=target_key,
                value="false",
                expiration_time=timedelta(minutes=10)
            )
            return UpdateEmailOrPhoneResponse(
                success=False,
                message=f"The verification code {verification_code} is incorrect.",
            )
        await self.caching_client.set(
            endpoint=self.update_current_phone_verified_endpoint,
            key=target_key,
            value="true",
            expiration_time=timedelta(minutes=10)
        )
        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"The verification code is correct.",
        )

    async def update_email_or_phone(self, user, new_email, new_phone_number, verification_code):
        is_email_verified = await self.caching_client.get(
            endpoint=self.update_current_email_verified_endpoint,
            key=user.email
        )
        if not is_email_verified or is_email_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The user's current email or phone number has not been verified."
            )

        is_phone_verified = await self.caching_client.get(
            endpoint=self.update_current_phone_verified_endpoint,
            key=user.phone_number if user.phone_number else user.email
        )
        if not is_phone_verified or is_phone_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The user's current email or phone number has not been verified."
            )

        if new_email:
            code = await self.caching_client.get(
                endpoint=self.update_new_email_endpoint,
                key=new_email
            )

            if code != verification_code:
                raise IncorrectCodeError

            if new_email == user.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="The new email is the same as the current email."
                )

            if await self.update_email(user, new_email):
                return UpdateEmailOrPhoneResponse(success=True, message="Email updated successfully.")
            else:
                return UpdateEmailOrPhoneResponse(success=False, message="Failed to update email.")

        elif new_phone_number:
            # Simply delegate to SMS client - let it handle all verification logic
            is_code_correct = await self.sms_client.verify(new_phone_number, verification_code, self.update_new_phone_endpoint)

            if not is_code_correct:
                raise IncorrectCodeError

            if new_phone_number == user.phone_number:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="The new phone number is the same as the current phone number."
                )

            if await self.update_phone(user, new_phone_number):
                return UpdateEmailOrPhoneResponse(success=True, message="Phone number updated successfully.")
            else:
                return UpdateEmailOrPhoneResponse(success=False, message="Failed to update phone number.")

    async def update_email(self, user: User, new_email: str) -> bool:
        # 再次检查邮箱是否已被使用
        stmt = select(User).where(User.email == new_email)
        existing_user = (await self.session.execute(stmt)).scalar()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This email is already in use. Please enter a different email address."
            )

        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(email=new_email)
        )
        await self.session.execute(stmt_user)

        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(email=new_email)
        )
        await self.session.execute(stmt_author)

        return True

    async def update_phone(self, user: User, new_phone_number: str):
        # 再次检查手机号是否已被使用
        stmt = select(User).where(User.phone_number == new_phone_number)
        existing_user = (await self.session.execute(stmt)).scalar()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This phone number is already in use. Please enter a different phone number."
            )

        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(phone_number=new_phone_number)
        )
        await self.session.execute(stmt_user)

        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(phone_number=new_phone_number)
        )
        await self.session.execute(stmt_author)

        return True

    @classmethod
    def get_new_instance(
            cls,
            session: AsyncSession = Depends(get_session),
            caching_client: CachingClient = Depends(CachingClientGetter(logger)),
            user_manager: UserManager = Depends(get_user_manager),
            strategy: Strategy = Depends(get_auth_strategy),
    ):
        return cls(session=session, caching_client=caching_client, user_manager=user_manager, strategy=strategy)

    async def verification_send_code(self, is_email: bool, user: User, endpoint: str, background_tasks: BackgroundTasks):
        if is_email:
            if not user.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Please add a email first."
                )

            code = self.generate_code(length=6)
            await self.caching_client.set(
                endpoint=endpoint,
                key=user.email,
                value=code,
                expiration_time=timedelta(minutes=10)
            )
            logger.debug(f"{user.email} - {code}")
            await asyncio.to_thread(
                send_authorization_mail,
                user=user,
                code=code
            )
        else:
            if not user.phone_number:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Please add a phone number first."
                )

            background_tasks.add_task(
                self.send_verification_code,
                phone_number=user.phone_number,
                endpoint=endpoint,
                expiration_time=timedelta(minutes=10)
            )

        return UpdateEmailOrPhoneResponse(success=True, message="Verification code sent successfully.")

    async def verification_verify_code(self, is_email: bool, user: User, endpoint: str, verified_endpoint: str, verification_code: str, apple_id: str | None = None, email: str | None = None):
        # 处理 Apple 相关的验证逻辑
        if apple_id and email:
            target_key = email
            sent_code = await self.caching_client.get(endpoint=endpoint, key=f"{apple_id}:{email}")
            is_code_correct = sent_code == verification_code
        else:
            # 常规的用户验证逻辑
            target_key = user.email if is_email else user.phone_number
            if is_email:
                sent_code = await self.caching_client.get(endpoint=endpoint, key=target_key)
                is_code_correct = sent_code == verification_code
            else:
                # Simply delegate to SMS client - let it handle all verification logic
                is_code_correct = await self.sms_client.verify(target_key, verification_code, endpoint)
        
        if not is_code_correct:
            raise IncorrectCodeError
            
        # 设置验证成功标记
        if apple_id and email:
            await self.caching_client.set(
                endpoint=verified_endpoint,
                key=f"{apple_id}:{email}",
                value="true",
                expiration_time=timedelta(minutes=15)
            )
        else:
            await self.caching_client.set(
                endpoint=verified_endpoint,
                key=target_key,
                value="true",
                expiration_time=timedelta(minutes=10)
            )
        return UpdateEmailOrPhoneResponse(success=True, message="Verification code is correct.")
        
    async def send_add_email_code(self, user: User, new_email: str, background_tasks: BackgroundTasks):
        """
        发送添加邮箱的验证码
        """
        # 检查重发限制
        if not await self.check_resend_limit(self.add_email_endpoint, new_email):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="You've requested codes multiple times. Please wait a few minutes before trying again."
            )
        
        # 生成验证码并存储
        code = self.generate_code(length=6)
        await self.caching_client.set(
            endpoint=self.add_email_endpoint,
            key=new_email,
            value=code,
            expiration_time=timedelta(minutes=30)
        )
        
        # 发送验证码邮件
        logger.debug(f"{new_email} - {code}")
        await asyncio.to_thread(
            send_registration_mail,
            email=new_email,
            code=code,
            lang=user.region,
            invitation_id=None
        )
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"Verification code sent to {new_email}",
        )

    async def verify_add_email_code(self, user: User, new_email: str, verification_code: str):
        """
        验证添加邮箱的验证码并更新用户邮箱
        """
        is_phone_verified = await self.caching_client.get(
            endpoint=self.add_phone_verified_endpoint,
            key=user.phone_number
        )
        if not is_phone_verified or is_phone_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The user's current phone number has not been verified."
            )

        # 首先检查验证码是否正确
        sent_code = await self.caching_client.get(
            endpoint=self.add_email_endpoint,
            key=new_email
        )
        
        if sent_code != verification_code:
            return UpdateEmailOrPhoneResponse(
                success=False,
                message=f"The verification code {verification_code} is incorrect.",
            )
        
        # 检查邮箱是否已被使用
        stmt = select(User).where(User.email == new_email)
        existing_user = (await self.session.execute(stmt)).scalar()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This email is already in use. Please enter a different email address."
            )
        
        # 更新用户邮箱
        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(email=new_email)
        )
        await self.session.execute(stmt_user)
        
        # 同时更新作者表的邮箱
        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(email=new_email)
        )
        await self.session.execute(stmt_author)
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message="Email added successfully.",
        )
        
    async def send_add_phone_code(self, user: User, new_phone_number: str, background_tasks: BackgroundTasks):
        """
        发送添加手机号的验证码
        """
        # 检查重发限制
        if not await self.check_resend_limit(self.add_phone_endpoint, new_phone_number):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="You've requested codes multiple times. Please wait a few minutes before trying again."
            )
        
        # 检查手机号是否已被使用
        stmt = select(User).where(User.phone_number == new_phone_number)
        existing_user = (await self.session.execute(stmt)).scalar()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This phone number is already in use. Please enter a different phone number."
            )
            
        # 发送验证码
        background_tasks.add_task(
            self.send_verification_code,
            phone_number=new_phone_number,
            endpoint=self.add_phone_endpoint,
            expiration_time=timedelta(minutes=10)
        )
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message=f"Verification code sent to {new_phone_number}",
        )
        
    async def verify_add_phone_code(self, user: User, new_phone_number: str, verification_code: str):
        """
        验证添加手机号的验证码并更新用户手机号
        """
        is_email_verified = await self.caching_client.get(
            endpoint=self.add_phone_verified_endpoint,
            key=user.email
        )
        if not is_email_verified or is_email_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The user's current email has not been verified."
            )
        # Simply delegate to SMS client - let it handle all verification logic
        is_code_correct = await self.sms_client.verify(new_phone_number, verification_code, self.add_phone_endpoint)
            
        if not is_code_correct:
            return UpdateEmailOrPhoneResponse(
                success=False,
                message=f"The verification code {verification_code} is incorrect.",
            )
        
        # 再次检查手机号是否已被使用
        stmt = select(User).where(User.phone_number == new_phone_number)
        existing_user = (await self.session.execute(stmt)).scalar()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This phone number is already in use. Please enter a different phone number."
            )
        
        # 更新用户手机号
        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(phone_number=new_phone_number)
        )
        await self.session.execute(stmt_user)
        
        # 同时更新作者表的手机号
        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(phone_number=new_phone_number)
        )
        await self.session.execute(stmt_author)
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message="Phone number added successfully.",
        )

    async def remove_email(self, user: User):
        """
        删除用户邮箱
        """
        # 检查邮箱验证状态
        email_is_verified = await self.caching_client.get(
            endpoint=self.remove_email_verified_endpoint,
            key=user.email
        )
        
        if not email_is_verified or email_is_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email removal has not been verified."
            )
        
        # 检查电话验证状态
        phone_is_verified = await self.caching_client.get(
            endpoint=self.remove_phone_verified_endpoint,
            key=user.phone_number
        )
        
        if not phone_is_verified or phone_is_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number verification is also required."
            )
        
        # 检查用户是否有电话号码，确保至少保留一种联系方式
        if not user.phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove email as user has no phone number. At least one contact method is required."
            )
        
        # 更新用户表，删除邮箱
        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(email=None)
        )
        await self.session.execute(stmt_user)
        
        # 同时更新作者表
        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(email=None)
        )
        await self.session.execute(stmt_author)
        
        # TODO: 删除缓存中的验证状态
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message="Email removed successfully."
        )

    async def remove_phone(self, user: User):
        """
        删除用户电话号码
        """
        # 检查电话验证状态
        phone_is_verified = await self.caching_client.get(
            endpoint=self.remove_phone_verified_endpoint,
            key=user.phone_number
        )
        
        if not phone_is_verified or phone_is_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone removal has not been verified."
            )
        
        # 检查邮箱验证状态
        email_is_verified = await self.caching_client.get(
            endpoint=self.remove_email_verified_endpoint,
            key=user.email
        )
        
        if not email_is_verified or email_is_verified != "true":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email verification is also required."
            )
        
        # 检查用户是否有邮箱，确保至少保留一种联系方式
        if not user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove phone number as user has no email. At least one contact method is required."
            )
        
        # 缓存手机号用于后续删除验证状态
        phone_number = user.phone_number
        
        # 更新用户表，删除电话号码
        stmt_user = (
            update(User)
            .where(User.id == user.id)
            .values(phone_number=None)
        )
        await self.session.execute(stmt_user)
        
        # 同时更新作者表
        stmt_author = (
            update(Author)
            .where(Author.id == user.id)
            .values(phone_number=None)
        )
        await self.session.execute(stmt_author)
        
        # TODO: 删除缓存中的验证状态
        
        return UpdateEmailOrPhoneResponse(
            success=True,
            message="Phone number removed successfully."
        )


    async def verify_turnstile(self, token: str, ip: str) -> bool:
        """
        异步校验 Cloudflare Turnstile token。
        注：可复用外部 client 以减少 TCP/TLS 握手开销。
        """
        token_key = generate_hash_key(token)
        cached_result = await self.caching_client.get(
            endpoint="turnstile_verification",
            key=token_key
        )
        
        if cached_result == "true":
            # 如果已验证过，直接返回true
            return True
            
        # 未验证过，执行完整验证流程
        VERIFY_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify"
        SITEHOSTS = [
            "api.dev.memefans.ai",
            "api.memefans.ai",
            "dev.memefans.ai",
            "localhost",
            "memefans.ai",
        ]
        ACTION = "register"  # data-action
        MAX_AGE = 300  # 5 分钟

        form = {
            "secret": settings.TURNSTILE_SECRET,
            "response": token,
            "remoteip": ip,
            "idempotency_key": str(uuid.uuid4()),
        }

        # 1. 发送验证请求
        try:
            async with httpx.AsyncClient(timeout=5) as c:
                resp = await c.post(VERIFY_URL, data=form)
            resp.raise_for_status()
            outcome = resp.json()
        except Exception as exc:
            logger.warning("Turnstile request error: %s", exc)
            return False

        # 2. 校验响应
        if not outcome.get("success"):
            logger.info("Turnstile validation failed: %s", outcome.get("error-codes"))
            return False

        # 3. 额外强校验
        # 3-a hostname
        if outcome.get("hostname") not in SITEHOSTS:
            logger.info("Hostname mismatch: %s", outcome.get("hostname"))
            return False

        # 3-b action
        if outcome.get("action") != ACTION:
            logger.info("Action mismatch: %s", outcome.get("action"))
            return False

        # 3-c challenge_ts ≤ 5 min
        try:
            issued_time = datetime.datetime.fromisoformat(
                outcome["challenge_ts"].replace("Z", "+00:00")
            )
            age = (datetime.datetime.now(datetime.timezone.utc) - issued_time).total_seconds()
            if age > MAX_AGE:
                logger.info("Token expired: %.1fs", age)
                return False
        except Exception:
            logger.info("Failed to parse challenge_ts")
            return False

        await self.caching_client.set(
            endpoint="turnstile_verification",
            key=token_key,
            value="true",
            expiration_time=timedelta(seconds=MAX_AGE)
        )
        return True  # 全部通过

    async def verify_google_token(self, token: str) -> GoogleUserInfo:
        try:
            token_hash = generate_hash_key(token)
            cached_result = await self.caching_client.get(
                endpoint="google_token_verification",
                key=token_hash
            )
            
            if cached_result:
                cached_info = json.loads(cached_result)
                return GoogleUserInfo(**cached_info)

            info = await asyncio.to_thread(
                id_token.verify_oauth2_token, 
                token, 
                google_requests.Request()
            )

            if info['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Invalid issuer')

            if info.get('aud') not in [settings.GOOGLE_AUD_ID, settings.GOOGLE_CLIENT_ID]:
                raise ValueError('Invalid audience')
            
            if info.get('azp') and info.get('azp') not in [settings.GOOGLE_AUD_ID, settings.GOOGLE_CLIENT_ID]:
                raise ValueError('Invalid authorized party')

            current_time = datetime.datetime.now(datetime.timezone.utc).timestamp()
            if info.get('exp', 0) < current_time:
                raise ValueError('Token expired')
                
            user_info = GoogleUserInfo(
                sub=info['sub'],
                email=info['email'],
                name=info.get('name', ''),
                picture=info.get('picture')
            )

            await self.caching_client.set(
                endpoint="google_token_verification",
                key=token_hash,
                value=user_info.model_dump_json(),
                expiration_time=timedelta(minutes=settings.OAUTH_TOKEN_CACHE_MINUTES)
            )
            
            return user_info
            
        except ValueError as e:
            logger.warning(f"Google token validation failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Google token"
            )
        except Exception as e:
            logger.error(f"Unexpected error during Google token verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service temporarily unavailable"
            )

            
    async def google_login(self, token: str):
        user_info = await self.verify_google_token(token)
        
        try:
            stmt = select(User).where(User.google_id == user_info.sub)
            user = (await self.session.execute(stmt)).scalar()
            
            if not user:
                email_check = select(User).where(User.email == user_info.email)
                existing_email_user = (await self.session.execute(email_check)).scalar()
                
                if existing_email_user:
                    # If email exists but Google ID not linked, link accounts
                    existing_email_user.google_id = user_info.sub
                    existing_email_user.google_linked_at = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=None)
                    existing_email_user.email_verified_via = 'oauth_google'
                    if user_info.picture and not existing_email_user.avatar_url:
                        existing_email_user.avatar_url = user_info.picture
                    user = existing_email_user
                else:
                    await self.caching_client.set(
                        endpoint="third_party_verification",
                        key=user_info.sub,
                        value="verified",
                        expiration_time=timedelta(minutes=5)
                    )
                    return OAuthRegistrationRequiredResponse(
                        oauth_user_info=OAuthUserForRegistration(
                            email=user_info.email,
                            name=user_info.name,
                            avatar_url=user_info.picture,
                            google_id=user_info.sub
                        )
                    )

            user.last_login_at = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=None)
            user.last_login_method = 'google'

            login_response = await auth_backend.login(self.auth_strategy, user)
            return json.loads(login_response.body) | {"id": user.id}
            
        except Exception as e:
            logger.error(f"Error during Google login: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login service temporarily unavailable"
            )

    async def verify_apple_token(self, token: str) -> AppleUserInfo:
        try:
            token_hash = generate_hash_key(token)
            cached_result = await self.caching_client.get(
                endpoint="apple_token_verification",
                key=token_hash
            )
            
            if cached_result:
                cached_info = json.loads(cached_result)
                return AppleUserInfo(**cached_info)
            
            # Fetch Apple's JWKs and verify
            apple_key_url = 'https://appleid.apple.com/auth/keys'
            pubkeys = jwt.PyJWKClient(apple_key_url)

            info = jwt.decode(
                token,
                key=pubkeys.get_signing_key_from_jwt(token).key,
                audience=settings.APPLE_CLIENT_ID,
                algorithms=["RS256"],
                issuer="https://appleid.apple.com",
                options={
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_aud": True,
                    "verify_iss": True
                }
            )

            current_time = datetime.datetime.now(datetime.timezone.utc).timestamp()
            if info.get('exp', 0) < current_time:
                raise jwt.ExpiredSignatureError('Token expired')

            if info.get('iat', 0) > current_time + settings.OAUTH_CLOCK_SKEW_SECONDS:
                raise jwt.InvalidIssuedAtError('Token issued in the future')

            if info.get('aud') != settings.APPLE_CLIENT_ID:
                raise jwt.InvalidAudienceError('Invalid audience')
            
            user_info = AppleUserInfo(
                sub=info['sub'],
                email=info.get('email'),
                name=None  # Apple only provides name on first login, needs to be passed from frontend
            )

            await self.caching_client.set(
                endpoint="apple_token_verification",
                key=token_hash,
                value=user_info.model_dump_json(),
                expiration_time=timedelta(minutes=settings.OAUTH_TOKEN_CACHE_MINUTES)
            )
            
            return user_info
            
        except jwt.ExpiredSignatureError:
            logger.warning("Apple token expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except jwt.InvalidAudienceError:
            logger.warning("Apple token has invalid audience")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except jwt.InvalidIssuedAtError:
            logger.warning("Apple token has invalid issued time")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except jwt.PyJWTError as e:
            logger.warning(f"Apple token validation failed: {type(e).__name__}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Apple token"
            )
        except Exception as e:
            logger.error(f"Unexpected error during Apple token verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service temporarily unavailable"
            )
    
    async def apple_login(self, token: str, name: str = None):
        user_info = await self.verify_apple_token(token)
        try:
            stmt = select(User).where(User.apple_id == user_info.sub)
            user = (await self.session.execute(stmt)).scalar()
            
            if not user:
                await self.caching_client.set(
                    endpoint="third_party_verification",
                    key=user_info.sub,
                    value="verified",
                    expiration_time=timedelta(minutes=5)
                )

                display_name = name or user_info.name or "Apple User"
                return OAuthRegistrationRequiredResponse(
                    oauth_user_info=OAuthUserForRegistration(
                        email=user_info.email,
                        name=display_name,
                        apple_id=user_info.sub
                    )
                )

            user.last_login_at = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=None)
            user.last_login_method = 'apple'

            login_response = await auth_backend.login(self.auth_strategy, user)
            return json.loads(login_response.body) | {"id": user.id}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error during Apple login: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login service temporarily unavailable"
            )
    
    async def register_device(self, device_data: DeviceCreate, user_id: str):
        try:
            stmt = select(Device).where(Device.device_id == device_data.device_id)
            device = (await self.session.execute(stmt)).scalar()
            if device:
                stmt_user = (
                    update(Device)
                    .where(Device.id == device.id)
                    .values(player_id=device_data.player_id, device_info=device_data.device_info, author_id=user_id)
                )
                await self.session.execute(stmt_user)
                await self.session.commit()
                return device
            
            device = Device(**device_data.model_dump())
            device.author_id = user_id
            self.session.add(device)
            await self.session.commit()
            return device
        except Exception as e:
            logger.error(f"Error during register device: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid device id"
            )
    
    async def unregister_device(self, device_id: str, user_id: str):
        try:
            stmt = select(Device).where(Device.device_id == device_id)
            device = (await self.session.execute(stmt)).scalar_one_or_none()
            if not device:
                raise NotFoundError(detail="Device not found")
            await self.session.delete(device)
            await self.session.commit()
        except Exception as e:
            logger.error(f"Error during unregister device: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device not found"
            )

    async def verify_vip_code(self, vip_code: str, device_id: str, device_brand: str):
        """
        验证VIP码
        
        Args:
            vip_code: 用户输入的VIP码
            device_id: 设备ID（用于验证和绑定）
            device_brand: 设备品牌（用于记录）
            
        Returns:
            dict: 包含验证结果的字典
            
        验证逻辑:
            1. 检查VIP码是否存在且有效
            2. 检查VIP码是否过期
            3. 如果VIP码已绑定设备ID，验证device_id是否匹配
            4. 验证成功时，将device_id和device_brand保存到数据库
        """
        try:
            # 检查设备是否被锁定
            lock_info = await self._check_device_lock(device_id)
            if lock_info["is_locked"]:
                return {
                    "success": False,
                    "message": f"设备已被锁定，请等待 {lock_info['remaining_time']} 分钟后重试",
                    "bind_device_brand": None,
                    "remaining_attempts": 0,
                    "locked_until": lock_info.get("lock_until")
                }
            
            # 从数据库查询VIP码
            stmt = select(VipCode).where(
                and_(
                    VipCode.code == vip_code,
                    VipCode.is_active == True
                )
            )
            vip_code_record = (await self.session.execute(stmt)).scalar()
            
            if not vip_code_record:
                # VIP码不存在或已失效
                await self._record_failed_attempt(device_id)
                remaining_attempts = await self._get_remaining_attempts(device_id)
                return {
                    "success": False,
                    "message": "VIP码不存在或已失效",
                    "bind_device_brand": None,
                    "remaining_attempts": remaining_attempts
                }
            
            # 检查VIP码是否已过期
            if vip_code_record.expires_at and vip_code_record.expires_at < datetime.datetime.now():
                await self._record_failed_attempt(device_id)
                remaining_attempts = await self._get_remaining_attempts(device_id)
                return {
                    "success": False,
                    "message": "VIP码已过期",
                    "bind_device_brand": None,
                    "remaining_attempts": remaining_attempts
                }
            
            
            # 检查设备ID是否一致（如果VIP码已绑定设备ID）
            if vip_code_record.device_id and vip_code_record.device_id != device_id and vip_code_record.code != "KLK98":
                await self._record_failed_attempt(device_id)
                remaining_attempts = await self._get_remaining_attempts(device_id)
                return {
                    "success": False,
                    "message": f"设备ID不匹配，该VIP码已绑定到其他设备",
                    "bind_device_brand": vip_code_record.device_brand,
                    "remaining_attempts": remaining_attempts
                }
            
            # VIP码验证成功，清除失败记录
            await self._clear_device_failed_attempts(device_id)
            
            # 将device_id和device_brand保存到数据库（如果尚未绑定）
            if not vip_code_record.device_id:
                vip_code_record.device_id = device_id
                if not vip_code_record.device_brand:
                    vip_code_record.device_brand = device_brand
                await self.session.commit()
                logger.info(f"Bound VIP code {vip_code} to device_id: {device_id}, device_brand: {device_brand}")
            
            # 将device_id和VIP信息存储到Redis，用于注册时验证
            logger.info(f"Storing VIP device mapping to Redis for {vip_code}")
            await self._store_vip_device_mapping(vip_code, device_id, vip_code_record)
            
            return {
                "success": True,
                "message": "VIP码验证成功",
                "bind_device_brand": vip_code_record.device_brand,
                "remaining_attempts": 3  # 验证成功后重置为3次
            }
            
        except Exception as e:
            logger.error(f"Error during VIP code verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="VIP码验证服务暂时不可用"
            )

    async def _store_vip_device_mapping(self, vip_code: str, device_id: str, vip_code_record):
        """
        将VIP码和设备ID的映射关系存储到Redis
        
        Args:
            vip_code: VIP码
            device_id: 设备ID
            vip_code_record: VIP码记录对象
        """
        try:
            # 存储映射关系，key格式: vip_device_mapping:{vip_code}
            mapping_data = {
                "device_id": device_id,
                "vip_code": vip_code,
                "max_uses": vip_code_record.max_uses,
                "current_uses": vip_code_record.current_uses,
                "expires_at": vip_code_record.expires_at.isoformat() if vip_code_record.expires_at else None,
                "verified_at": datetime.datetime.now().isoformat()
            }
            
            # 设置过期时间为24小时，防止长期占用Redis空间
            expiration_time = datetime.timedelta(hours=24)
            
            logger.debug(f"Attempting to store VIP device mapping to Redis: {mapping_data}")
            
            await self.caching_client.set(
                "vip_device_mapping",
                vip_code,
                json.dumps(mapping_data),
                expiration_time
            )
            
            # 验证存储是否成功
            stored_data = await self.caching_client.get("vip_device_mapping", vip_code)
            if stored_data:
                logger.info(f"Successfully stored VIP device mapping for vip_code: {vip_code}, device_id: {device_id}")
                logger.debug(f"Stored data: {stored_data}")
            else:
                logger.error(f"Failed to verify VIP device mapping storage for vip_code: {vip_code}")
            
        except Exception as e:
            logger.error(f"Failed to store VIP device mapping: {str(e)}")
            logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
            # 不抛出异常，因为这不是关键操作

    async def _verify_vip_device_mapping(self, vip_code: str, device_id: str) -> bool:
        """
        验证VIP码和设备ID的映射关系
        
        Args:
            vip_code: VIP码
            device_id: 设备ID
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 从Redis获取映射关系
            mapping_data = await self.caching_client.get("vip_device_mapping", vip_code)
            
            if not mapping_data:
                logger.warning(f"No VIP device mapping found for vip_code: {vip_code}")
                return False
            
            # 解析映射数据
            mapping = json.loads(mapping_data)
            stored_device_id = mapping.get("device_id")
            
            if not stored_device_id:
                logger.warning(f"Invalid VIP device mapping data for vip_code: {vip_code}")
                return False
            
            # 验证设备ID是否匹配
            if stored_device_id != device_id:
                logger.warning(f"Device ID mismatch for vip_code: {vip_code}. Expected: {stored_device_id}, Got: {device_id}")
                return False
            
            # 验证成功后，从Redis中删除该映射（防止重复使用）
            await self.caching_client.delete("vip_device_mapping", vip_code)
            
            logger.info(f"Successfully verified VIP device mapping for vip_code: {vip_code}, device_id: {device_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error during VIP device mapping verification: {str(e)}")
            return False

    async def _check_device_lock(self, device_id: str) -> dict:
        """
        检查设备是否被锁定
        
        Args:
            device_id: 设备ID
            
        Returns:
            dict: 包含锁定状态和剩余时间的字典
        """
        try:
            lock_key = f"vip_lock:{device_id}"
            lock_data = await self.caching_client.get("vip_locks", lock_key)
            
            if not lock_data:
                return {"is_locked": False, "remaining_time": 0}
            
            lock_info = json.loads(lock_data)
            lock_until = datetime.datetime.fromisoformat(lock_info["lock_until"])
            now = datetime.datetime.now()
            
            if now < lock_until:
                # 设备仍被锁定
                remaining_minutes = int((lock_until - now).total_seconds() / 60)
                return {
                    "is_locked": True, 
                    "remaining_time": remaining_minutes,
                    "lock_until": lock_until.isoformat()
                }
            else:
                # 锁定时间已过，清除锁定记录
                await self.caching_client.delete("vip_locks", lock_key)
                return {"is_locked": False, "remaining_time": 0}
                
        except Exception as e:
            logger.error(f"Error checking device lock: {str(e)}")
            return {"is_locked": False, "remaining_time": 0}

    async def _record_failed_attempt(self, device_id: str):
        """
        记录验证失败次数
        
        Args:
            device_id: 设备ID
        """
        try:
            failed_key = f"vip_failed:{device_id}"
            failed_data = await self.caching_client.get("vip_failed_attempts", failed_key)
            
            if failed_data:
                failed_info = json.loads(failed_data)
                failed_count = failed_info["count"] + 1
            else:
                failed_count = 1
            
            # 更新失败次数
            failed_info = {
                "count": failed_count,
                "last_attempt": datetime.datetime.now().isoformat()
            }
            
            await self.caching_client.set(
                "vip_failed_attempts",
                failed_key,
                json.dumps(failed_info),
                datetime.timedelta(hours=24)  # 24小时后自动清除
            )
            
            # 如果失败次数达到3次，锁定设备1小时
            if failed_count >= 3:
                await self._lock_device_until_midnight(device_id)
                logger.warning(f"Device {device_id} locked for 1 hour due to 3 failed VIP code attempts")
                
        except Exception as e:
            logger.error(f"Error recording failed attempt: {str(e)}")

    async def _lock_device_until_midnight(self, device_id: str):
        """
        锁定设备直到1小时后
        
        Args:
            device_id: 设备ID
        """
        try:
            lock_until = datetime.datetime.now() + datetime.timedelta(hours=1)
            lock_info = {
                "device_id": device_id,
                "lock_until": lock_until.isoformat(),
                "locked_at": datetime.datetime.now().isoformat()
            }
            
            lock_key = f"vip_lock:{device_id}"
            await self.caching_client.set(
                "vip_locks",
                lock_key,
                json.dumps(lock_info),
                datetime.timedelta(hours=2)  # 设置2小时过期，确保1小时锁定期
            )
            
        except Exception as e:
            logger.error(f"Error locking device: {str(e)}")

    async def _clear_device_failed_attempts(self, device_id: str):
        """
        清除设备的失败记录（验证成功时调用）
        
        Args:
            device_id: 设备ID
        """
        try:
            failed_key = f"vip_failed:{device_id}"
            await self.caching_client.delete("vip_failed_attempts", failed_key)
        except Exception as e:
            logger.error(f"Error clearing failed attempts: {str(e)}")

    async def _get_remaining_attempts(self, device_id: str) -> int:
        """
        获取设备剩余尝试次数
        
        Args:
            device_id: 设备ID
            
        Returns:
            int: 剩余尝试次数
        """
        try:
            failed_key = f"vip_failed:{device_id}"
            failed_data = await self.caching_client.get("vip_failed_attempts", failed_key)
            
            if failed_data:
                failed_info = json.loads(failed_data)
                return max(0, 3 - failed_info["count"])
            else:
                return 3
                
        except Exception as e:
            logger.error(f"Error getting remaining attempts: {str(e)}")
            return 3
