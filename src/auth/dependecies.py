from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Coroutine

from fastapi import HTT<PERSON>Exception, status, Depends, Header, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.database.models.AccessToken import AccessToken
from src.database.session import get_session
from src.database.models import User
from src.common.caching.caching_client import CachingClient, CachingClientGetter
from src.common.constants import UserStatus

from .utils import valid_email as valid_email_str
from .logger import logger


def valid_email(email: str) -> str:
    if not valid_email_str(email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid email"
        )
    return email


async def current_user(
    request: Request,
    authorization: str | None = Header(None),
    caching_client: CachingClient = Depends(CachingClientGetter(logger)),
    session: AsyncSession = Depends(get_session),
) -> User:

    if not authorization:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    token = authorization[7:]

    user = await verify_token(request.method, token, session, caching_client)

    return user


async def optional_user(
    request: Request,
    authorization: str | None = Header(None),
    caching_client: CachingClient = Depends(CachingClientGetter(logger)),
    session: AsyncSession = Depends(get_session),
) -> User | None:
    try:
        user = await current_user(request, authorization, caching_client, session)
        return user
    except HTTPException:
        return None


async def verify_token(
    method: str,
    token: str,
    session: AsyncSession,
    caching_client: CachingClient,
) -> Any | None:

    cached_user: dict | None = await caching_client.get(endpoint="auth", key=token)
    if cached_user:
        user = User.from_dict(cached_user)
    else:
        stmt = select(User).join(AccessToken).where(AccessToken.token == token)  # type: ignore
        user = (await session.execute(stmt)).first()
        if not user or not (user[0].is_active and user[0].is_verified):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

        await caching_client.set(
            endpoint="auth",
            key=token,
            value=user[0].dict(),
            expiration_time=timedelta(hours=2),
        )
        user = user[0]

    if user.status == UserStatus.BANNED and method == "POST":
        raise HTTPException(
            status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
            detail="POST requests are not allowed.",
        )

    return user
