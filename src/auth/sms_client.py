import asyncio
import uuid
from datetime import timedelta

import requests
import json
import hashlib
import time
from typing import List, Dict, Optional, Union
from enum import Enum
from urllib.parse import quote

from src.auth.logger import logger
from src.auth.settings import settings


class SMSProvider(Enum):
    """短信服务商枚举"""
    TWILIO = "twilio"  # 原有服务商
    NXCLOUD = "nxcloud"  # nxcloud服务商
    DREAMNET = "dreamnet"  # 梦网服务商


class BaseSMSClient:
    """短信客户端基类"""
    
    def __init__(self, provider: SMSProvider, caching_client=None, **kwargs):
        """
        初始化短信客户端基类
        
        Args:
            provider: 服务商类型
            caching_client: 缓存客户端，用于存储和验证验证码
            **kwargs: 各服务商特定的配置参数
        """
        self.provider = provider
        self.caching_client = caching_client
        
    @staticmethod
    def _md5(text: str) -> str:
        """计算字符串的MD5值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def _generate_code(length: int = 6) -> str:
        """生成验证码"""
        code = uuid.uuid4().int % 10 ** length
        return str(code).zfill(length)
    
    async def _cache_code(self, endpoint: str, key: str, code: str, expiration_time: timedelta = timedelta(minutes=5)):
        """缓存验证码"""
        if self.caching_client:
            await self.caching_client.set(
                endpoint=endpoint,
                key=key,
                value=code,
                expiration_time=expiration_time
            )
    
    async def _get_cached_code(self, endpoint: str, key: str) -> Optional[str]:
        """获取缓存的验证码"""
        if self.caching_client:
            return await self.caching_client.get(endpoint=endpoint, key=key)
        return None
    
    def send_message(self, message_list: List[Dict], **kwargs) -> Dict:
        """
        发送短信的统一接口
        
        Args:
            message_list: 短信消息列表
            **kwargs: 额外参数
            
        Returns:
            API响应结果
        """
        raise NotImplementedError("子类必须实现此方法")


class TwilioSMSClient(BaseSMSClient):
    """Twilio短信服务商客户端（原有服务商）"""
    
    def __init__(self, username: str, password: str, base_url: str = None, caching_client=None):
        """
        初始化Twilio短信客户端
        
        Args:
            username: API账号用户名
            password: API账号密码
            base_url: API基础URL
            caching_client: 缓存客户端
        """
        super().__init__(SMSProvider.TWILIO, caching_client=caching_client)
        if not username or not password:
            raise ValueError("[TwilioSMSClient] Username and password are required.")
        self.username = username
        self.password_md5 = self._md5(password)  # 保存密码的MD5值
        self.base_url = base_url or settings.SMS_API_BASE_URL
        self.send_url = f"{self.base_url}/sms/api/sendMessageOne"
        
        # Initialize Twilio REST client for verification service
        self._twilio_rest_client = None
    
    @property
    def twilio_rest_client(self):
        """Lazy initialization of Twilio REST client for verification service"""
        if self._twilio_rest_client is None:
            from twilio.rest import Client
            self._twilio_rest_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        return self._twilio_rest_client
    
    def _generate_sign(self, timestamp: int) -> str:
        """
        生成API签名
        
        Args:
            timestamp: 当前时间戳（毫秒）
            
        Returns:
            生成的签名字符串
        """
        # MD5(userName + timestamp + MD5(password))
        sign_str = f"{self.username}{timestamp}{self.password_md5}"
        return self._md5(sign_str)
    
    def send_message(self, 
                    message_list: List[Dict], 
                    send_time: Optional[str] = None) -> Dict:
        """
        发送短信
        
        Args:
            message_list: 短信消息列表，每项包含手机号和内容
            send_time: 定时发送时间，格式：yyyy-MM-dd HH:mm:ss
            
        Returns:
            API响应结果
        """
        try:
            timestamp = int(time.time() * 1000)
            payload = {
                "userName": self.username,
                "messageList": message_list,
                "timestamp": timestamp,
                "sign": self._generate_sign(timestamp)
            }
            if send_time:
                payload["sendTime"] = send_time
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json;charset=utf-8"
            }
            response = requests.post(
                self.send_url, 
                headers=headers,
                data=json.dumps(payload)
            )
            return response.json()
        except Exception as e:
            logger.error(f"发送短信请求失败: {str(e)}")
            return {"code": -1, "message": str(e)}
    
    async def send_verification_code(self, phone_number: str, endpoint: str = "sms", expiration_time: timedelta = timedelta(minutes=5)) -> bool:
        """
        发送验证码短信 - 使用Twilio验证服务
        
        Args:
            phone_number: 手机号码，需要包含国际区号，例如 "+7xxx1234567"
            endpoint: 缓存端点（Twilio不使用）
            expiration_time: 验证码过期时间（Twilio不使用）
            
        Returns:
            True: 发送成功
            False: 发送失败
        """
        try:
            # Use Twilio verification service directly
            import asyncio
            from twilio.base.exceptions import TwilioRestException
            
            await asyncio.to_thread(
                self.twilio_rest_client.verify.v2.services(settings.TWILIO_SERVICE_ID).verifications.create,
                to=phone_number,
                channel="sms",
                risk_check="disable"
            )
            logger.debug(f"Twilio verification code sent to {phone_number}")
            return True
        except TwilioRestException as e:
            logger.error(f"Twilio error sending verification code: {e}")
            return False
        except Exception as e:
            logger.error(f"Error sending Twilio verification code: {str(e)}")
            return False
    
    async def verify(self, phone_number: str, code: str, endpoint: str = "sms") -> bool:
        """
        验证短信验证码 - 使用Twilio验证服务
        
        Args:
            phone_number: 手机号码
            code: 用户输入的验证码
            endpoint: 缓存端点（Twilio不使用）
            
        Returns:
            True: 验证成功
            False: 验证失败
        """
        try:
            # Use Twilio verification service directly
            import asyncio
            from twilio.base.exceptions import TwilioRestException
            
            verification_check = await asyncio.to_thread(
                self.twilio_rest_client.verify.v2.services(settings.TWILIO_SERVICE_ID).verification_checks.create,
                to=phone_number,
                code=code
            )
            result = verification_check.status == 'approved'
            logger.debug(f"Twilio verification for {phone_number}: {result}")
            return result
        except TwilioRestException as e:
            logger.warning(f"Twilio verification failed for {phone_number}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error verifying Twilio code for {phone_number}: {str(e)}")
            return False


class NxcloudSMSClient(BaseSMSClient):
    """nxcloud短信服务商客户端"""
    
    def __init__(self, appkey: str, secretkey: str, base_url: str = "http://api2.nxcloud.com", caching_client=None):
        """
        初始化nxcloud短信客户端
        
        Args:
            appkey: API应用密钥
            secretkey: API秘密密钥
            base_url: API基础URL，默认为"http://api2.nxcloud.com"
            caching_client: 缓存客户端
        """
        super().__init__(SMSProvider.NXCLOUD, caching_client=caching_client)
        self.appkey = appkey
        self.secretkey = secretkey
        self.base_url = base_url
        self.send_url = f"{base_url}/api/sms/mtsend"
    
    def send_message(self, message_list: List[Dict], **kwargs) -> Dict:
        """
        发送短信
        
        Args:
            message_list: 短信消息列表，每项应包含：
                         - mobile/phone: 手机号
                         - content: 短信内容
            **kwargs: 额外参数（当前不使用）
            
        Returns:
            API响应结果
        """
        # nxcloud API是单条发送，这里处理第一条消息
        if not message_list:
            raise ValueError("消息列表不能为空")
        
        message = message_list[0]  # 只处理第一条消息
        
        # 支持两种字段名：mobile 或 phone
        phone = message.get("mobile") or message.get("phone")
        content = message.get("content")
        
        if not phone or not content:
            raise ValueError("消息必须包含phone/mobile和content字段")

        # 标准POST with form data
        return self._try_standard_post(phone, content)
    
    def _try_standard_post(self, phone: str, content: str) -> Dict:
        """尝试标准POST请求"""
        try:
            form_data = {
                "appkey": self.appkey,
                "secretkey": self.secretkey,
                "phone": phone,
                "content": content
            }
            
            logger.debug(f"NxCloud SMS - 发送到 {phone}: {content}")
            
            response = requests.post(
                self.send_url,
                data=form_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=10
            )
            
            logger.debug(f"NxCloud SMS Response - Status: {response.status_code}, Text: '{response.text}'")
            
            if response.status_code == 200:
                try:
                    return response.json()
                except:
                    return {"status_code": 200, "text": response.text, "success": True}
            else:
                return {
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "success": False,
                    "status_code": response.status_code
                }
                    
        except Exception as e:
            logger.error(f"NxCloud SMS发送失败: {e}")
            return {
                "error": str(e),
                "success": False,
                "status_code": 500
            }
    
    async def send_verification_code(self, phone_number: str, endpoint: str = "sms", expiration_time: timedelta = timedelta(minutes=5)) -> bool:
        """
        发送验证码短信，失败时自动回退到Twilio
        
        Args:
            phone_number: 手机号码，需要包含国际区号，例如 "+7xxx1234567"
            endpoint: 缓存端点
            expiration_time: 验证码过期时间
            
        Returns:
            True: 发送成功
            False: 发送失败
        """
        try:
            code = self._generate_code()
            message_list = [
                {
                    "phone": phone_number,
                    "content": f"[Memefans] Your verification code is: {code}"
                }
            ]
            result = self.send_message(message_list)
            success = (
                result.get('code') == "0" or  # NxCloud API returns string "0" for success
                result.get('code') == 0 or    # Fallback for integer format
                result.get('status_code') == 200 or
                result.get('success') == True
            )
            
            if success:
                # Cache the code for verification
                await self._cache_code(endpoint, phone_number, code, expiration_time)
                logger.debug(f"NxCloud verification code sent and cached for {phone_number}")
                return True
            else:
                logger.warning(f"NxCloud SMS failed, attempting Twilio fallback for {phone_number}")
                
                # Fallback to Twilio
                return await self._fallback_to_twilio(phone_number, endpoint, expiration_time)
                
        except Exception as e:
            logger.error(f"Error sending NxCloud SMS, attempting Twilio fallback for {phone_number}: {str(e)}")
            return await self._fallback_to_twilio(phone_number, endpoint, expiration_time)
    
    async def _fallback_to_twilio(self, phone_number: str, endpoint: str, expiration_time: timedelta) -> bool:
        """回退到Twilio发送验证码"""
        try:
            # Use Twilio verification service directly
            import asyncio
            from twilio.rest import Client
            from twilio.base.exceptions import TwilioRestException
            
            twilio_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            
            await asyncio.to_thread(
                twilio_client.verify.v2.services(settings.TWILIO_SERVICE_ID).verifications.create,
                to=phone_number,
                channel="sms",
                risk_check="disable"
            )
            
            # Mark that this phone number should use Twilio for verification
            if self.caching_client:
                await self.caching_client.set(
                    endpoint="twilio_fallback",
                    key=phone_number,
                    value="true",
                    expiration_time=expiration_time
                )
            
            logger.info(f"Twilio fallback successful for {phone_number}")
            return True
            
        except TwilioRestException as e:
            logger.error(f"Twilio fallback also failed for {phone_number}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error in Twilio fallback for {phone_number}: {str(e)}")
            return False
    
    async def verify(self, phone_number: str, code: str, endpoint: str = "sms") -> bool:
        """
        验证短信验证码，自动检查是否需要使用Twilio回退验证
        
        Args:
            phone_number: 手机号码
            code: 用户输入的验证码
            endpoint: 缓存端点
            
        Returns:
            True: 验证成功
            False: 验证失败
        """
        try:
            # Check if Twilio fallback was used for this phone number
            twilio_fallback_used = None
            if self.caching_client:
                twilio_fallback_used = await self.caching_client.get(endpoint="twilio_fallback", key=phone_number)
            
            if twilio_fallback_used == "true":
                # Use Twilio verification service
                return await self._verify_with_twilio(phone_number, code)
            else:
                # Use cached code verification
                cached_code = await self._get_cached_code(endpoint, phone_number)
                if cached_code == code:
                    logger.debug(f"NxCloud verification successful for {phone_number}")
                    return True
                else:
                    logger.warning(f"NxCloud verification failed for {phone_number}: code mismatch")
                    return False
        except Exception as e:
            logger.error(f"Error verifying NxCloud code for {phone_number}: {str(e)}")
            return False
    
    async def _verify_with_twilio(self, phone_number: str, code: str) -> bool:
        """使用Twilio验证服务验证验证码"""
        try:
            import asyncio
            from twilio.rest import Client
            from twilio.base.exceptions import TwilioRestException
            
            twilio_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            
            verification_check = await asyncio.to_thread(
                twilio_client.verify.v2.services(settings.TWILIO_SERVICE_ID).verification_checks.create,
                to=phone_number,
                code=code
            )
            
            result = verification_check.status == 'approved'
            logger.debug(f"Twilio fallback verification for {phone_number}: {result}")
            return result
            
        except TwilioRestException as e:
            logger.warning(f"Twilio fallback verification failed for {phone_number}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error in Twilio fallback verification for {phone_number}: {str(e)}")
            return False


class DreamnetSMSClient(BaseSMSClient):
    """梦网短信服务商客户端"""
    
    def __init__(self, userid: str, password: str, base_url: str, caching_client=None):
        """
        初始化梦网短信客户端
        
        Args:
            userid: 用户账号，长度最大6-16个字符，如"J10003"
            password: API账号密码（原始密码）
            base_url: API基础URL，如"http://ip:port"
            caching_client: 缓存客户端
        """
        super().__init__(SMSProvider.DREAMNET, caching_client=caching_client)
        self.userid = userid
        self.raw_password = password
        self.base_url = base_url
        self.send_url = f"{base_url}/sms/v2/std/send_single"
    
    def _generate_timestamp(self) -> str:
        """
        生成时间戳，24小时制格式：MMDDHHMMSSS
        
        Returns:
            10位时间戳字符串，如"**********"
        """
        now = time.localtime()
        # 格式：月日时分秒，定长10位
        return time.strftime("%m%d%H%M%S", now).ljust(10, '0')
    
    def _generate_password_md5(self, timestamp: str) -> str:
        """
        生成MD5加密的密码
        密码生成规则详见"3.1鉴权规则"
        
        Args:
            timestamp: 时间戳
            
        Returns:
            32位MD5加密字符串
        """
        fixed_string = "00000000"
        userid_upper = self.userid.upper()
        pre_md5_string = f"{userid_upper}{fixed_string}{self.raw_password}{timestamp}"
        md5_string = self._md5(pre_md5_string)
        return md5_string
    
    def _url_encode_content(self, content: str) -> str:
        """
        对短信内容进行URL编码
        使用UrlEncode编码UTF-8的消息内容
        
        Args:
            content: 原始短信内容
            
        Returns:
            URL编码后的内容
        """
        return quote(content, encoding='utf-8')
    
    def send_message(self, 
                     message_list: List[Dict], 
                     exno: Optional[str] = None,
                     custid: Optional[str] = None,
                     exdata: Optional[str] = None,
                     **kwargs) -> Dict:
        """
        发送短信（单条发送接口）
        
        Args:
            message_list: 短信消息列表，每项应包含：
                         - mobile: 收信接收的手机号，只能填一个手机号
                         - content: 短信内容，最大支持3000个字，使用UrlEncode编码UTF-8
            exno: 可选，扩展号本条短信的OA或SenderID，可留空
            custid: 可选，用户自定义流水号，在您业务系统内的ID，比如订单号或短信发送记录流水号
            exdata: 可选，自定义扩展资料，额外提供的最大512位长度的ASCII字符串
            **kwargs: 额外参数
            
        Returns:
            API响应结果
        """
        # 梦网API是单条发送，这里处理第一条消息
        if not message_list:
            raise ValueError("消息列表不能为空")
        
        message = message_list[0]  # 只处理第一条消息
        if "mobile" not in message or "content" not in message:
            raise ValueError("消息必须包含mobile和content字段")
        
        # 生成时间戳
        timestamp = self._generate_timestamp()
        
        # 构建请求数据
        payload = {
            "userid": self.userid,
            "pwd": self._generate_password_md5(timestamp),
            "mobile": message["mobile"],
            "content": self._url_encode_content(message["content"]),
            "timestamp": timestamp
        }
        
        # 添加可选参数
        if exno:
            payload["exno"] = exno
        if custid:
            payload["custid"] = custid
        if exdata:
            payload["exdata"] = exdata
        
        # 设置请求头
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        logger.debug(f"Dreamnet SMS - 发送到 {message['mobile']}: {message['content']}")
        
        try:
            # 发送请求
            response = requests.post(
                self.send_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            logger.debug(f"Dreamnet SMS Response - Status: {response.status_code}, Text: '{response.text}'")
            
            # 解析响应
            result = response.json()
            
            # 对desc字段进行URL解码，方便查看中文信息
            if 'desc' in result and isinstance(result['desc'], str):
                from urllib.parse import unquote
                result['desc'] = unquote(result['desc'], encoding='utf-8')
            
            return result
            
        except Exception as e:
            logger.error(f"Dreamnet SMS发送失败: {e}")
            return {
                "error": str(e),
                "success": False,
                "status_code": 500
            }
    
    async def send_verification_code(self, phone_number: str, endpoint: str = "sms", expiration_time: timedelta = timedelta(minutes=5)) -> bool:
        """
        发送验证码短信
        
        Args:
            phone_number: 手机号码，需要包含国际区号，例如 "+7xxx1234567"
            endpoint: 缓存端点
            expiration_time: 验证码过期时间
            
        Returns:
            True: 发送成功
            False: 发送失败
        """
        try:
            code = self._generate_code()
            message_list = [
                {
                    "mobile": phone_number,
                    "content": f"[Memefans] Your verification code is: {code}"
                }
            ]
            result = self.send_message(message_list)
            # Dreamnet通常返回code字段，0表示成功
            success = result.get('code') == 0 or result.get('result') == 0
            
            if success:
                # Cache the code for verification
                await self._cache_code(endpoint, phone_number, code, expiration_time)
                logger.debug(f"Dreamnet verification code sent and cached for {phone_number}")
            else:
                logger.warning(f"Failed to send verification code via Dreamnet: {result.get('desc', result.get('error', 'Unknown error'))}")
            return success
        except Exception as e:
            logger.error(f"Error sending verification code via Dreamnet: {str(e)}")
            return False
    
    async def verify(self, phone_number: str, code: str, endpoint: str = "sms") -> bool:
        """
        验证短信验证码（通过缓存比较）
        
        Args:
            phone_number: 手机号码
            code: 用户输入的验证码
            endpoint: 缓存端点
            
        Returns:
            True: 验证成功
            False: 验证失败
        """
        try:
            cached_code = await self._get_cached_code(endpoint, phone_number)
            if cached_code == code:
                logger.debug(f"Dreamnet verification successful for {phone_number}")
                return True
            else:
                logger.warning(f"Dreamnet verification failed for {phone_number}: code mismatch")
                return False
        except Exception as e:
            logger.error(f"Error verifying Dreamnet code for {phone_number}: {str(e)}")
            return False


class SMSClient:
    """
    统一的短信客户端工厂类
    提供向后兼容性和多服务商支持
    """
    
    def __init__(self, caching_client=None):
        """
        初始化短信客户端，默认使用NxCloud服务商
        
        Args:
            caching_client: 缓存客户端
        """
        # 默认使用NxCloud服务商，带有Twilio回退功能
        self._client = NxcloudSMSClient(
            appkey=settings.SMS_NX_APPKEY,
            secretkey=settings.SMS_NX_SECRET,
            caching_client=caching_client
        )
    
    def send_message(self, message_list: List[Dict], **kwargs) -> Dict:
        """
        发送短信统一接口
        
        Args:
            message_list: 短信消息列表
            **kwargs: 额外参数
            
        Returns:
            API响应结果
        """
        return self._client.send_message(message_list, **kwargs)
    
    async def send_verification_code(self, phone_number: str, endpoint: str = "sms", expiration_time: timedelta = timedelta(minutes=5)) -> bool:
        """
        发送验证码短信
        
        Args:
            phone_number: 手机号码，需要包含国际区号，例如 "+7xxx1234567"
            endpoint: 缓存端点
            expiration_time: 验证码过期时间
            
        Returns:
            True: 发送成功
            False: 发送失败
        """
        return await self._client.send_verification_code(phone_number, endpoint, expiration_time)
    
    async def verify(self, phone_number: str, code: str, endpoint: str = "sms") -> bool:
        """
        验证短信验证码
        
        Args:
            phone_number: 手机号码
            code: 用户输入的验证码
            endpoint: 缓存端点
            
        Returns:
            True: 验证成功
            False: 验证失败
        """
        return await self._client.verify(phone_number, code, endpoint)
    
    # 为了向后兼容，保留原有的类方法
    @classmethod
    def create_twilio_client(cls, username: str, password: str, base_url: str = None, caching_client=None):
        """创建Twilio服务商客户端（向后兼容）"""
        return cls(SMSProvider.TWILIO, caching_client=caching_client, username=username, password=password, base_url=base_url)
    
    @classmethod
    def create_nxcloud_client(cls, appkey: str, secretkey: str, base_url: str = "http://api2.nxcloud.com", caching_client=None):
        """创建nxcloud服务商客户端"""
        return cls(SMSProvider.NXCLOUD, caching_client=caching_client, appkey=appkey, secretkey=secretkey, base_url=base_url)
    
    @classmethod
    def create_dreamnet_client(cls, userid: str, password: str, base_url: str, caching_client=None):
        """创建梦网服务商客户端"""
        return cls(SMSProvider.DREAMNET, caching_client=caching_client, userid=userid, password=password, base_url=base_url)
