from typing import Optional
import datetime
import re

from pydantic import BaseModel, field_validator, model_validator
from fastapi_users import schemas

from src.auth.utils import MailLanguage

from .utils import valid_email


class HasPhoneNumberOrEmail(BaseModel):
    email: str | None = None
    phone_number: str | None = None

    @field_validator('email')
    @classmethod
    def allowed_email(cls, v: str | None) -> str | None:
        if v is not None and not valid_email(email=v):
            raise ValueError('Invalid email')
        return v

    @model_validator(mode="after")
    def check_fields(self):
        if bool(self.phone_number) == bool(self.email):
            raise ValueError("Either email or phone number must be present exclusively")
        return self


class PhoneOrEmail(HasPhoneNumberOrEmail):
    pass


class RegistrationCodeSchema(HasPhoneNumberOrEmail):
    lang: MailLanguage
    invitation_id: str | None = None
    turnstile_token: str
    vip_code: str | None = None
    
    @field_validator('vip_code')
    @classmethod
    def validate_vip_code(cls, v: str | None) -> str | None:
        if v is not None:
            if not v or v.strip() == "":
                raise ValueError("VIP码不能为空")
            if len(v) < 4:
                raise ValueError('VIP码长度不能少于4个字符')
            if len(v) > 20:
                raise ValueError('VIP码长度不能超过20个字符')
            if not re.match(r'^[a-zA-Z0-9]+$', v):
                raise ValueError('VIP码只能包含字母和数字')
            return v.strip()
        return v


class VerifyRegistrationCodeSchema(HasPhoneNumberOrEmail):
    verification_code: str


class AuthSchema(HasPhoneNumberOrEmail):
    verification_code: str


class AuthResponse(BaseModel):
    access_token: str
    token_type: str
    id: str


class LoginResponse(BaseModel):
    """
    Standard login response format used by all login endpoints
    (/login, /login/google, /login/apple)
    """
    access_token: str
    token_type: str = "bearer"
    id: str


class Captcha(BaseModel):
    token: str = ""
    ekey: str = ""


class UserBase(schemas.BaseUserCreate):
    pass


class UserCreate(UserBase, HasPhoneNumberOrEmail):
    email: str | None = None
    name: str
    username: str
    region: str
    invitation_id: str | None = None
    password: str = ""
    # OAuth 相关字段（可选）
    google_id: str | None = None
    apple_id: str | None = None
    avatar_url: str | None = None
    google_linked_at: datetime.datetime | None = None
    apple_linked_at: datetime.datetime | None = None
    email_verified_via: str | None = None
    # 内测码（可选）
    vip_code: str | None = None
    # 设备ID（使用VIP码时必填）
    device_id: str | None = None
    
    @field_validator('username')
    @classmethod
    def not_empty(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("This field cannot be empty")
        return v
    
    @field_validator('vip_code')
    @classmethod
    def validate_vip_code(cls, v: str | None) -> str | None:
        if v is not None:
            if not v or v.strip() == "":
                raise ValueError("VIP码不能为空")
            if len(v) < 4:
                raise ValueError('VIP码长度不能少于4个字符')
            if len(v) > 20:
                raise ValueError('VIP码长度不能超过20个字符')
            if not re.match(r'^[a-zA-Z0-9]+$', v):
                raise ValueError('VIP码只能包含字母和数字')
            return v.strip()
        return v
    
    @model_validator(mode="after")
    def check_oauth_requirements(self):
        # 如果有 OAuth ID，说明是 OAuth 注册，不需要验证联系方式
        has_oauth = bool(self.google_id) or bool(self.apple_id)
        has_contact = bool(self.email) or bool(self.phone_number)
        
        if not has_contact:
            raise ValueError("Either email or phone number is required")
            
        # OAuth 注册时，不需要遵循 HasPhoneNumberOrEmail 的互斥规则
        if not has_oauth:
            # 传统注册：必须只有 email 或 phone_number 之一
            if bool(self.phone_number) == bool(self.email):
                raise ValueError("Either email or phone number must be present exclusively")
        
        # 如果使用VIP码，设备ID是必填的
        if self.vip_code and not self.device_id:
            raise ValueError("Device ID is required when using VIP code")
        
        return self


class UserRead(schemas.BaseUser[str], HasPhoneNumberOrEmail):
    email: str | None = None


class RegisterUserRead(UserRead):
    access_token: str | None = None


class UserUpdate(schemas.BaseUserUpdate):
    pass


class UserPasswordUpdate(BaseModel):
    password: str
    updated_password: str


class UpdateEmailCodeRequest(BaseModel):
    email: str


class UpdatePhoneCodeRequest(BaseModel):
    phone_number: str


class UpdateVerifyCodeRequest(BaseModel):
    verification_code: str


class UpdateEmailOrPhoneRequest(HasPhoneNumberOrEmail):
    verification_code: str


class UpdateEmailOrPhoneResponse(BaseModel):
    success: bool
    message: str

class HasEmailOrPhoneResponse(BaseModel):
    has_email: bool
    has_phone: bool

# Google登录相关Schema
class GoogleAuthRequest(BaseModel):
    id_token: str
    
    @field_validator('id_token')
    @classmethod
    def validate_id_token(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("ID token cannot be empty")
        # 基本的JWT格式检查（应该有3个部分，用.分隔）
        parts = v.split('.')
        if len(parts) != 3:
            raise ValueError("Invalid token format")
        return v.strip()

class GoogleUserInfo(BaseModel):
    sub: str  # Google的用户ID
    email: str
    name: str
    picture: str | None = None

# Apple登录相关Schema
class AppleAuthRequest(BaseModel):
    id_token: str
    name: str | None = None  # Apple只在首次登录时提供name
    
    @field_validator('id_token')
    @classmethod
    def validate_id_token(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("ID token cannot be empty")
        return v.strip()
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str | None) -> str | None:
        if v is not None and v.strip() == "":
            return None  # 空字符串转换为None
        return v

class AppleUserInfo(BaseModel):
    sub: str  # Apple的用户ID
    email: str | None = None
    name: str | None = None

# OAuth响应相关Schema
# 新增：OAuth 用户信息（用于注册）
class OAuthUserForRegistration(BaseModel):
    email: str | None = None
    name: str | None = None
    avatar_url: str | None = None
    google_id: str | None = None
    apple_id: str | None = None

# 新增：OAuth 登录需要注册的响应
class OAuthRegistrationRequiredResponse(BaseModel):
    new_user: bool = True
    message: str = "User not found. Please complete registration."
    oauth_user_info: OAuthUserForRegistration

# Apple 邮箱绑定与校验
class AppleEmailSendCodeRequest(BaseModel):
    apple_id: str
    email: str
    lang: MailLanguage = MailLanguage.ENGLISH

class AppleEmailVerifyCodeRequest(BaseModel):
    apple_id: str
    verification_code: str
    email: str

class DeviceCreate(BaseModel):
    player_id: str
    device_id: str
    device_info: Optional[str]

    class Config:
        from_attributes = True


# VIP码验证相关Schema
class VipCodeVerifyRequest(BaseModel):
    vip_code: str
    device_id: str
    device_brand: str
    
    @field_validator('vip_code')
    @classmethod
    def validate_vip_code(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("VIP码不能为空")
        if len(v) < 4:
            raise ValueError('VIP码长度不能少于4个字符')
        if len(v) > 20:
            raise ValueError('VIP码长度不能超过20个字符')
        if not re.match(r'^[a-zA-Z0-9]+$', v):
            raise ValueError('VIP码只能包含字母和数字')
        return v.strip()
    
    @field_validator('device_id')
    @classmethod
    def validate_device_id(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("Device ID cannot be empty")
        return v.strip()
    
    @field_validator('device_brand')
    @classmethod
    def validate_device_brand(cls, v: str) -> str:
        if not v or v.strip() == "":
            raise ValueError("Device brand cannot be empty")
        return v.strip()


class VipCodeVerifyResponse(BaseModel):
    success: bool
    message: str
    bind_device_brand: str | None = None
    remaining_attempts: int | None = None  # 剩余尝试次数
    locked_until: str | None = None  # 锁定到期时间（ISO格式）