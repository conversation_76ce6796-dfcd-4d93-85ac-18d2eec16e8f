import logging
from httpx import AsyncClient

from src.common.constants import Language
from src.common.exceptions import TranslationError


logger = logging.getLogger("Translation-client")


class TranslationClient:
    def __init__(self, auth_token: str):
        self.client = AsyncClient(
            trust_env=False,
            headers={"X-Token": auth_token},
            base_url="http://toci-suggester:8000/",
            timeout=300000
        )

    async def translate(self, texts: list[str], language: Language) -> list[str]:
        data = {"texts": texts, "target_lang": language}
        translations = await self.post(url='translate', data=data)
        if not isinstance(translations, list):
            logging.error(f"Request data:\n{data}")
            raise TranslationError
        return translations

    async def translate_prompt(self, text: str, language: Language) -> str:
        data = {"text": text, "target_lang": language}
        translations = await self.post(url='translate/simple', data=data)
        if not isinstance(translations, str):
            logging.error(f"Request data:\n{data}")
            raise TranslationError
        return translations

    async def post(self, url: str, data: dict | None = None) -> list | dict | str | None:
        try:
            response = await self.client.post(url=url, json=data, headers={"Content-Type": "application/json"})
            if response.status_code >= 300:
                logging.error(f"Request to suggester failed with status code {response.status_code}\n"
                              f"Response details:\n{response}")
            return response.json()
        except Exception as e:
            logging.error("Suggester request failed")
            logging.error(e)
