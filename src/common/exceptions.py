from fastapi import status


class TranslationError(Exception):
    pass


class HTTPError(Exception):
    error_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail: str = "Unexpected error occurred"

    def __init__(self, detail: str | None = None):
        if detail:
            self.detail = detail


class AlreadyExistsError(HTTPError):
    error_code: int = status.HTTP_400_BAD_REQUEST
    detail: str = "Entity already exists"


class NotFoundError(HTTPError):
    error_code: int = status.HTTP_404_NOT_FOUND
    detail: str = "Entity not found"


class NotAllowedError(HTTPError):
    error_code: int = status.HTTP_403_FORBIDDEN
    detail: str = "Action not allowed"


class IncorrectDataError(HTTPError):
    error_code: int = status.HTTP_400_BAD_REQUEST
    detail = "Incorrect data"
