import json
import hashlib
from logging import Logger
from datetime import timed<PERSON><PERSON>
from typing import List, Tuple, Dict, Any

from fastapi import Depends
from redis.asyncio import Redis

from src.common.caching.redis import get_redis_client


class CachingClient:
    def __init__(self, client: Redis, logger: Logger):
        self.client = client
        self.logger = logger

    async def get(self, endpoint: str, key: str) -> str | list | dict | None:
        cache: str | None = None
        try:
            cache = await self.client.get(f"{endpoint}-{key}")
        except Exception as e:
            self.logger.error(f"Redis request failed with the following exception:\n{e}")
        if cache:
            self.logger.debug(f"Cache hit: {endpoint}-{key}")
        else:
            self.logger.debug(f"Cache miss: {endpoint}-{key}")
        return json.loads(cache) if cache else None

    async def set(
            self,
            endpoint: str,
            key: str,
            value: str | list | dict,
            expiration_time: timedelta = timedelta(seconds=10)
    ) -> None:
        try:
            await self.client.set(name=f"{endpoint}-{key}", value=json.dumps(value), ex=expiration_time)
        except Exception as e:
            self.logger.error(f"Redis request failed with the following exception:\n{e}")

    async def delete(self, endpoint: str, key: str) -> None:
        try:
            await self.client.delete(f"{endpoint}-{key}")
        except Exception as e:
            self.logger.error(f"Redis delete failed with the following exception:\n{e}")

    def _convert_to_offset(self, id_value: str | int) -> int:
        """
        将字符串ID转换为整数offset
        
        Args:
            id_value: 字符串或整数ID
            
        Returns:
            整数offset
        """
        if isinstance(id_value, int):
            return id_value
        
        # 对字符串ID进行哈希处理
        hash_value = int(hashlib.md5(str(id_value).encode()).hexdigest(), 16)
        # 取模以避免offset过大（使用较大的素数）
        return hash_value % (2**32 - 1)

    async def set_bitmap(
            self,
            namespace: str,
            key: str,
            offset: str | int,
            value: int,
            expiration_time: timedelta | None = None
    ) -> int:
        """
        设置 bitmap 中指定位置的值
        
        Args:
            namespace: bitmap 的命名空间
            key: bitmap 的键
            offset: 位偏移量（可以是字符串ID或整数）
            value: 要设置的值（0或1）
            expiration_time: 过期时间
            
        Returns:
            之前该位置的值
        """
        bitmap_key = f"bitmap:{namespace}:{key}"
        numeric_offset = self._convert_to_offset(offset)
        
        try:
            previous = await self.client.setbit(bitmap_key, numeric_offset, value)
            if expiration_time:
                await self.client.expire(bitmap_key, int(expiration_time.total_seconds()))
            return previous
        except Exception as e:
            self.logger.error(f"Redis set_bitmap failed with the following exception:\n{e}")
            return 0
    
    async def batch_set_bitmap(
            self,
            namespace: str,
            key: str,
            offset_values: List[Tuple[str | int, int]] | List[str | int],
            expiration_seconds: int = 60 * 60 * 24
    ) -> bool:
        """
        批量设置 bitmap 中多个位置的值
        
        Args:
            namespace: bitmap 的命名空间
            key: bitmap 的键
            offset_values: 位偏移量和值的元组列表 [(offset1, value1), (offset2, value2), ...] 
                          或者直接是偏移量列表 [offset1, offset2, ...] (默认值为1)
            expiration_seconds: 过期时间
            
        Returns:
            是否成功设置
        """
        bitmap_key = f"bitmap:{namespace}:{key}"
        
        try:
            # 获取 pipeline 对象
            pipeline = self.client.pipeline()
            
            # 检查传入的是否是元组列表
            if offset_values and isinstance(offset_values[0], tuple):
                # 元组列表情况：[(offset1, value1), ...]
                for offset, value in offset_values:
                    self.logger.debug(f"Setting bitmap {key} for offset (tuple): {offset}, value: {value}")
                    numeric_offset = self._convert_to_offset(offset)
                    pipeline.setbit(bitmap_key, numeric_offset, value)
            else:
                # 单值列表情况：[offset1, offset2, ...] - 默认值为1
                for offset in offset_values:
                    self.logger.debug(f"Setting bitmap {key} for offset (single): {offset}, value: 1")
                    numeric_offset = self._convert_to_offset(offset)
                    pipeline.setbit(bitmap_key, numeric_offset, 1)
            
            # 设置过期时间
            pipeline.expire(bitmap_key, expiration_seconds)
            
            # 执行所有操作
            await pipeline.execute()
            return True
        except Exception as e:
            self.logger.error(f"Redis batch_set_bitmap failed with the following exception:\n{e}")
            return False
    
    async def get_bitmap(self, namespace: str, key: str, offset: str | int) -> int:
        """
        获取 bitmap 中指定位置的值
        
        Args:
            namespace: bitmap 的命名空间
            key: bitmap 的键
            offset: 位偏移量（可以是字符串ID或整数）
            
        Returns:
            该位置的值（0或1）
        """
        bitmap_key = f"bitmap:{namespace}:{key}"
        numeric_offset = self._convert_to_offset(offset)
        
        try:
            return await self.client.getbit(bitmap_key, numeric_offset)
        except Exception as e:
            self.logger.error(f"Redis get_bitmap failed with the following exception:\n{e}")
            return 0


class CachingClientGetter:
    def __init__(self, logger: Logger, client: Redis | None = None):
        self.logger = logger
        self.client = client

    def __call__(self, client: Redis = Depends(get_redis_client)) -> CachingClient:
        return CachingClient(client=self.client or client, logger=self.logger)
