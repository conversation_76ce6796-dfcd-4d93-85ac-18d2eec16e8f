from redis.asyncio import Redis as AsyncRedis, ConnectionPool as AsyncConnectionPool
import redis as sync_redis
from src.common.settings import settings

# Unified connection pools (async and sync) derived from REDIS_URL
async_redis_pool = AsyncConnectionPool.from_url(
    settings.REDIS_URL,
    **settings.REDIS_POOL,
)

sync_redis_pool = sync_redis.ConnectionPool.from_url(
    settings.REDIS_URL,
    **settings.REDIS_POOL,
)


def get_redis_client():
    """FastAPI dependency: yield a single async Redis client bound to shared pool."""
    yield AsyncRedis(connection_pool=async_redis_pool)


def get_sync_redis_client() -> sync_redis.Redis:
    """Factory for sync Redis client bound to shared pool."""
    return sync_redis.Redis(connection_pool=sync_redis_pool)
