"""
基于 redis-py 内置 Lock 的幂等性处理模块

使用 Redis 官方客户端的分布式锁防止并发请求重复。
redis-py 有 12.2k+ GitHub stars，是最稳妥的选择。
"""

import logging
from typing import Optional
from functools import wraps

from fastapi import HTTPException, status
from redis.asyncio.lock import Lock

logger = logging.getLogger(__name__)


class DuplicateOrderError(HTTPException):
    """并发处理错误"""
    def __init__(self, order_id: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"订单 {order_id} 正在处理中，请稍后重试"
        )


async def acquire_distributed_lock(
    redis_client,
    user_id: str,
    order_id: str,
    timeout: int = 10
) -> Optional[Lock]:
    """获取分布式锁，使用 redis-py 内置 Lock"""
    if not order_id:
        return None
        
    try:
        lock_name = f"processing:{user_id}:{order_id}"
        lock = Lock(redis_client, name=lock_name, timeout=timeout)
        
        # 非阻塞获取锁
        if await lock.acquire(blocking=False):
            logger.info(f"Lock acquired: {order_id} for user {user_id}")
            return lock
        else:
            logger.warning(f"Lock already exists: {order_id} for user {user_id}")
            return None
            
    except Exception as e:
        logger.error(f"Error acquiring lock: {str(e)}")
        return None


def idempotent(timeout: int = 10):
    """
    幂等性装饰器 - 基于 redis-py 内置 Lock
    
    Args:
        timeout: 锁的超时时间（秒）
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 提取参数
            user_id = None
            order_id = None
            redis_client = None
            
            for arg in args:
                if hasattr(arg, 'id') and 'User' in str(type(arg)):
                    user_id = arg.id
                elif hasattr(arg, 'order_id'):
                    order_id = arg.order_id
                elif hasattr(arg, '_caching_client') and hasattr(arg._caching_client, 'client'):
                    redis_client = arg._caching_client.client
            
            # 从 kwargs 提取
            order_id = kwargs.get('order_id', order_id)
            
            # 检查并获取锁
            if user_id and order_id and redis_client:
                lock = await acquire_distributed_lock(redis_client, user_id, order_id, timeout)
                if not lock:
                    raise DuplicateOrderError(order_id)
                
                try:
                    # 执行函数
                    return await func(*args, **kwargs)
                finally:
                    # 释放锁
                    try:
                        await lock.release()
                        logger.info(f"Lock released: {order_id} for user {user_id}")
                    except Exception as e:
                        logger.error(f"Error releasing lock: {str(e)}")
            else:
                # 如果无法提取必要参数，直接执行函数
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# 预定义装饰器
create_meme_idempotent = idempotent(timeout=10)
buy_token_idempotent = idempotent(timeout=10)
sell_token_idempotent = idempotent(timeout=10)
send_asset_idempotent = idempotent(timeout=10)
send_token_idempotent = idempotent(timeout=10)
send_native_idempotent = idempotent(timeout=10)
withdraw_idempotent = idempotent(timeout=10)


# 直接使用锁的工具函数
async def with_distributed_lock(redis_client, lock_name: str, timeout: int = 10):
    """
    上下文管理器方式使用分布式锁
    
    Usage:
        async with with_distributed_lock(redis_client, "my_lock"):
            # 执行需要加锁的代码
            pass
    """
    return Lock(redis_client, name=lock_name, timeout=timeout) 