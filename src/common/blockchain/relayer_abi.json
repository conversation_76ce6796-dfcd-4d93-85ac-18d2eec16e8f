{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "BASIS_POINTS_DIVISOR", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "FEE_BASIS_POINTS", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_ACC_BUY_AMOUNT_PER_USER", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "buyWithUSDT", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "usdtAmount", "type": "uint256", "internalType": "uint256"}, {"name": "amountOutMin", "type": "uint256", "internalType": "uint256"}, {"name": "ethAmountOutMin", "type": "uint256", "internalType": "uint256"}, {"name": "gasUSDTAmount", "type": "uint256", "internalType": "uint256"}, {"name": "permitDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "permitSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "dex<PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_meme", "type": "address", "internalType": "address"}, {"name": "_usdt", "type": "address", "internalType": "address"}, {"name": "_dexRouter", "type": "address", "internalType": "address"}, {"name": "_relayer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "launch", "inputs": [{"name": "creator", "type": "address", "internalType": "address"}, {"name": "L2TokenAddress", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "logo", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "tokenAmountMin", "type": "uint256", "internalType": "uint256"}, {"name": "initBuyWithUSDAmount", "type": "uint256", "internalType": "uint256"}, {"name": "initBuyETHAmountOutMin", "type": "uint256", "internalType": "uint256"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "gasUSDTAmount", "type": "uint256", "internalType": "uint256"}, {"name": "permitDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "permitSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "meme", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "purchase", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "purchaseUsdtAmount", "type": "uint256", "internalType": "uint256"}, {"name": "permitTokenDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "permitTokenSig", "type": "bytes", "internalType": "bytes"}, {"name": "gasUSDTAmount", "type": "uint256", "internalType": "uint256"}, {"name": "permitUSDTDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "permitUSDTSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "purchaseFeeTo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "relayer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sellForUSDT", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "sellAmount", "type": "uint256", "internalType": "uint256"}, {"name": "tokenPermitDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "tokenPermitSig", "type": "bytes", "internalType": "bytes"}, {"name": "ethAmountOutMin", "type": "uint256", "internalType": "uint256"}, {"name": "usdtAmountOutMin", "type": "uint256", "internalType": "uint256"}, {"name": "gasUSDTAmount", "type": "uint256", "internalType": "uint256"}, {"name": "permitDeadline", "type": "uint256", "internalType": "uint256"}, {"name": "permitSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "tokenPermitSig", "type": "bytes", "internalType": "bytes"}, {"name": "gasUSDTAmount", "type": "uint256", "internalType": "uint256"}, {"name": "gasUSDTPermitSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "usdt", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "BuyWithUsdt", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "relayer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "gasUSDTAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "ethAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "usdtAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Launch", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "launcher", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "repoUrl", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Purchase", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "purchaseUsdtAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "purchaseTokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SellForUsdt", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "usdtAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TransferToken", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}]}