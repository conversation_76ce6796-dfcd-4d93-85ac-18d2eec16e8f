{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "LIQUIDITY_TOKEN_AMOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_TRADE_AMOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "MFN_L2_BRIDGE", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "REWRAD_AMOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "REWRAD_GITHUB_AMOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "TOKEN_TOTAL_SUPPLY", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "X0", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "Y0", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "buy", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "buyAmountIn", "type": "uint256", "internalType": "uint256"}, {"name": "amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createL2Token", "inputs": [{"name": "creator", "type": "address", "internalType": "address"}, {"name": "L1TokenAddress", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "logo", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "L2TokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "dex<PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "erc20InitialSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feeTo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "feeToAddress", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getInBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getInBnbAmountNotYetLaunched", "inputs": [{"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "getOutBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutBnbAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "ethAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "graduate", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "graduateFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}, {"name": "_feeTo", "type": "address", "internalType": "address"}, {"name": "_dexRouter", "type": "address", "internalType": "address"}, {"name": "_rewardAdmin", "type": "address", "internalType": "address"}, {"name": "_signer", "type": "address", "internalType": "address"}, {"name": "_relayer", "type": "address", "internalType": "address"}, {"name": "_tokenLockup", "type": "address", "internalType": "address"}, {"name": "_mfnL2Bridge", "type": "address", "internalType": "address"}, {"name": "_usdtAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "launch", "inputs": [{"name": "L2TokenAddress", "type": "address", "internalType": "address"}, {"name": "creator", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "initBuyAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "memeTokenCount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "memeTokenList", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "progress", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "relayer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "sell", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDexRouter", "inputs": [{"name": "newDexRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeTo", "inputs": [{"name": "newFeeTo", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setGraduateFee", "inputs": [{"name": "newGraduateFee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "newOperator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "newRelayer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardAdmin", "inputs": [{"name": "newRewardAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTokenLockup", "inputs": [{"name": "newTokenLockup", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTradingFeeRate", "inputs": [{"name": "newFeeRate", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "tokenInfos", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IMemeUsdBase.TokenInfo", "components": [{"name": "virtualX", "type": "uint256", "internalType": "uint256"}, {"name": "virtualY", "type": "uint256", "internalType": "uint256"}, {"name": "K", "type": "uint256", "internalType": "uint256"}, {"name": "state", "type": "uint8", "internalType": "enum IMemeUsdBase.TokenState"}]}], "stateMutability": "view"}, {"type": "function", "name": "tokenLockup", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "tradingFeeRate", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateSigner", "inputs": [{"name": "_newS<PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "usdt", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "userEligibilityCheckEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "userEligible", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verifySignature", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Buy", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BuyWithUsdt", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "relayer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "gasUSDTAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "gasETHAmountOutMin", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "usdtAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Complete", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "CreateL2Token", "inputs": [{"name": "L2TokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "creator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "repoUrl", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "Graduate", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Launch", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "launcher", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "repoUrl", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON>ll", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "bnbAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SellForUsdt", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "usdtAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetDexRouter", "inputs": [{"name": "dex<PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFeeTo", "inputs": [{"name": "feeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetGraduateFee", "inputs": [{"name": "graduateFee", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetOperator", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "relayer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetRewardAdmin", "inputs": [{"name": "rewardAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTokenLockup", "inputs": [{"name": "tokenLockup", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTradingFeeRate", "inputs": [{"name": "tradingFeeRate", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetUsdtToken", "inputs": [{"name": "usdtToken", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SignerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}]}