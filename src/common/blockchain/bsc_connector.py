import json
import time
from decimal import Decimal
from logging import Logger
from typing import Any, Tu<PERSON>, List, Optional

import eth_utils
from eth_typing import <PERSON>sumAddress
from web3 import AsyncHT<PERSON><PERSON>rovider, AsyncWeb3

from src.common.blockchain.blockchain_connector import (
    BlockchainConnector,
    BlockchainConnectorGetter,
)
from src.memecoin.settings import settings
from src.common.blockchain.pancake_contracts import PancakeContracts, PANCAKE_ROUTER_ABI


class BSCConnector(BlockchainConnector):

    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        kms_address: str = None,
        chain_id: int = None,
        contract_address: str = None,
        meme_abi_path: str = None,
        erc20_abi_path: str = None,
        reward_contract_address: str = None,
        reward_abi_path: str = None,
    ):
        """
          MAINNET
        - rpc_url: 'https://bsc.drpc.org'
        - chain_id: 56

          TESTNET
        - rpc_url: 'https://bsc-testnet.drpc.org'
        - chain_id: 97
        """
        super().__init__(logger, rpc_url, kms_address)
        self.chain_id = chain_id or 56  # Default to mainnet if not specified
        self.w3 = AsyncWeb3(AsyncHTTPProvider(rpc_url))
        self.contract_address = (
            self.w3.to_checksum_address(contract_address) if contract_address else None
        )
        self.reward_contract_address = (
            self.w3.to_checksum_address(reward_contract_address) if reward_contract_address else None
        )

        if contract_address and meme_abi_path:
            self.contract = self.w3.eth.contract(
                address=self.contract_address,
                abi=self._load_abi(meme_abi_path),
            )

        if reward_contract_address and reward_abi_path:
            self.reward_contract = self.w3.eth.contract(
                address=self.reward_contract_address,
                abi=self._load_abi(reward_abi_path)
            )

        self.erc20_abi = self._load_abi(erc20_abi_path) if erc20_abi_path else None

        self.pancake_constants = PancakeContracts.MAINNET if self.chain_id == 56 else PancakeContracts.TESTNET
        self.pancake_router_address = self.w3.to_checksum_address(self.pancake_constants["PANCAKESWAP_ROUTER"]["address"])
        self.pancake_contract = self.w3.eth.contract(
            address=self.pancake_router_address,
            abi=PANCAKE_ROUTER_ABI
        )

        self.wbnb_address = self.w3.to_checksum_address(self.pancake_constants["WBNB"]["address"])

    def _load_abi(self, abi_path: str) -> Any:
        with open(abi_path, "r", encoding="utf-8") as f:
            raw_abi = json.load(f)
            return raw_abi.get("abi")

    async def init_connection(self):
        """
        Call this method to check the connection status
        """
        connected = await self.w3.is_connected()
        if not connected:
            raise ConnectionError(
                f"Unable to connect to the blockchain network, please check the RPC URL ({self.rpc_url}) and network status."
            )

    # -----------------------------
    # View functions
    # -----------------------------

    async def userCreateTokenEligible(self, pubkey: str) -> bool:
        account = self.w3.to_checksum_address(pubkey)
        return not await self.contract.functions.userIneligible(account).call()

    async def get_native_balance_in_wei(self, pubkey: str) -> Decimal:
        return await self.get_bnb_balance_in_wei(pubkey)

    async def get_liquidity(self, token_address: str) -> float:
        try:
            address = self.w3.to_checksum_address(token_address)
            token_info = await self.contract.functions.tokenInfos(address).call()
            virtual_x, virtual_y, _, _ = token_info
            pool_bnb_amount = virtual_y / 1e18 - 30
            return pool_bnb_amount if pool_bnb_amount > 0 else 0
        except Exception as e:
            self.logger.warning(f"Failed to get liquidity for token {token_address}: {str(e)}")
            return 0

    async def meme_initial_supply(self) -> Decimal:
        return self.contract.functions.erc20InitialSupply().call()

    async def get_out_token_amount(
        self, token_address: str, bnb_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutTokenAmount(
            token_addr, self.w3.to_wei(bnb_amount, "ether")
        ).call()
        return (
            amount[1],
            self.w3.from_wei(amount[1], "ether"),
        )

    async def get_in_native_amount(
        self, token_address: str, amount_out: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getInBnbAmount(
            token_addr, self.w3.to_wei(amount_out, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def get_out_native_amount(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmount(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def get_out_native_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmountAfterFee(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def progress(self, token_address: str) -> int:
        token_addr = self.w3.to_checksum_address(token_address)
        try:
            return await self.contract.functions.progress(token_addr).call()
        except Exception as e:
            self.logger.warning(f"Failed to get progress for token {token_address}: {str(e)}")
            return -1

    async def get_token_balance(self, pubkey: str, token_address: str) -> Decimal:
        """Get token balance"""
        balance_wei = await self.get_token_balance_in_wei(pubkey, token_address)
        balance_token = self.w3.from_wei(balance_wei, "ether")
        return balance_token

    async def get_native_balance(self, pubkey: str) -> Decimal:
        """Get BNB balance"""
        return await self.get_bnb_balance(pubkey)

    async def get_reward_nonce(self, token_address: ChecksumAddress, sender: ChecksumAddress):

        return await self.reward_contract.functions.nonces(token_address, sender).call()

    async def pancake_get_out_token_amount(
        self, token_address: str, bnb_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        path = [self.wbnb_address, token_addr]
        in_bnb_amount, out_token_amount = await self.pancake_contract.functions.getAmountsOut(self.w3.to_wei(bnb_amount, "ether"), path).call()
        return out_token_amount, self.w3.from_wei(out_token_amount, "ether")

    async def pancake_get_in_native_amount(
        self, token_address: str, amount_out: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        path = [self.wbnb_address, token_addr]
        in_bnb_amount, out_token_amount = await self.pancake_contract.functions.getAmountsIn(
            self.w3.to_wei(amount_out, "ether"), path).call()
        return (
            in_bnb_amount,
            self.w3.from_wei(in_bnb_amount, "ether"),
        )

    async def pancake_get_out_native_amount(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        path = [token_addr, self.wbnb_address]
        in_token_amount, out_bnb_amount = await self.pancake_contract.functions.getAmountsOut(
            self.w3.to_wei(token_amount, "ether"), path).call()
        return (
            out_bnb_amount,
            self.w3.from_wei(out_bnb_amount, "ether"),
        )

    async def pancake_get_out_native_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        path = [token_addr, self.wbnb_address]
        in_token_amount, out_bnb_amount = await self.pancake_contract.functions.getAmountsOut(
            self.w3.to_wei(token_amount, "ether"), path).call()
        return (
            out_bnb_amount,
            self.w3.from_wei(out_bnb_amount, "ether"),
        )

    async def pancake_query_price(self, token_path: list) -> Decimal:
        """
        查询代币交换价格

        :param token_path: 代币路径列表，每个元素包含address和decimals
        :return: 价格比例
        """
        try:
            # 构建代币路径
            path = [self.w3.to_checksum_address(token["address"]) for token in token_path]

            # 查询价格
            input_decimals = token_path[0]["decimals"]
            input_amount = 10 ** input_decimals  # 1个代币

            amounts = await self.pancake_contract.functions.getAmountsOut(input_amount, path).call()

            # 计算价格比例
            output_decimals = token_path[-1]["decimals"]
            input_amount_decimal = Decimal(amounts[0]) / (10 ** input_decimals)
            output_amount_decimal = Decimal(amounts[-1]) / (10 ** output_decimals)

            return output_amount_decimal / input_amount_decimal
        except Exception as e:
            self.logger.error(f"Error querying price on PancakeSwap: {e}")
            return Decimal("0")

    # -----------------------------
    # Transaction methods
    # -----------------------------

    async def _build_launch_tx(
        self,
        name,
        symbol,
        repo_url,
        logo,
        desc,
        nonce,
        amount_to_buy,
        gas,
        gas_price,
        signature,
    ):
        tx = await self.contract.functions.launch(
            name, symbol, repo_url, logo, desc, bytes.fromhex(signature)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(amount_to_buy, "ether"),
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        self.logger.debug(f"launch gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_approve_tx(
        self, token_addr, amount_to_sell, gas, gas_price, nonce
    ):
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        tx = await token_contract.functions.approve(
            self.contract_address, self.w3.to_wei(amount_to_sell, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        self.logger.debug(f"approve gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_sell_tx(
        self, token_addr, amount_to_sell, amount_out_min, gas, gas_price, nonce
    ):
        tx = await self.contract.functions.sell(
            token_addr, self.w3.to_wei(amount_to_sell, "ether"), amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        self.logger.debug(f"sell gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_buy_tx(
        self, token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
    ):
        tx = await self.contract.functions.buy(
            token_addr, amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(amount_to_buy, "ether"),
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        self.logger.debug(f"buy gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_transfer_tx(
        self, token_address, to_address, amount, gas, gas_price, nonce
    ):
        token_addr = self.w3.to_checksum_address(token_address)
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        tx = await token_contract.functions.transfer(
            to_address, self.w3.to_wei(amount, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "nonce": nonce,
                "gas": gas,  # Adjust the gas limit as needed
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
            }
        )
        return tx

    # Implementation of abstract methods
    async def transfer_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
        **kwargs,
    ) -> str:
        """
        Transfer ERC20 tokens
        """
        return await self.send_token(
            token_address, from_pubkey, from_secret, to_address, amount, gas, gas_price
        )

    async def transfer_native(
        self,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
        **kwargs,
    ) -> str:
        """
        Transfer BNB
        """
        return await self.send(
            from_pubkey, from_secret, to_address, amount, gas, gas_price
        )

    async def launch(
        self,
        name: str,
        symbol: str,
        repo_url: str,
        logo: str,
        desc: str,
        pubkey: str,
        secret: str,
        amount_to_buy: Decimal = 0,
        gas: int = 5_000_000,
        gas_price: int = 3,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        """
        Create a new token on BSC

        :param creator_pubkey: Creator public key
        :param creator_secret: Creator secret
        :param name: Token name
        :param symbol: Token symbol
        :param repo_url: Repository URL
        :param logo: Logo URL
        :param desc: Description
        :param amount_to_buy: Amount to buy initially
        :param gas: Gas limit
        :param gas_price: Gas price
        :param gas_payer_secret: Optional gas payer secret, if provided will be used to pay for gas
        :return: Transaction hash
        """
        # Security check: when using gas payer, amount_to_buy must be 0
        if gas_payer_secret is not None and amount_to_buy > 0:
            raise ValueError("When using gas_payer_secret, amount_to_buy must be 0")
            
        account = self.w3.to_checksum_address(pubkey)
        signature = await self.generate_signature(pubkey, repo_url)
        nonce = await self.w3.eth.get_transaction_count(account)
        tx = await self._build_launch_tx(
            name,
            symbol,
            repo_url,
            logo,
            desc,
            nonce,
            amount_to_buy,
            gas if gas else 5_000_000,
            gas_price if gas_price else 3,
            signature,
        )

        # Use gas_payer_secret if provided, otherwise use user's secret
        signing_secret = gas_payer_secret if gas_payer_secret else secret
        signed_tx_hex = await self.sign(signing_secret, tx, "bsc")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def generate_signature(self, sender: str, repo_url: str, signer_secret: str = settings.LAUNCH_SIGN_SECRET) -> str | None:
        if sender.startswith("0x"):
            sender = sender[2:]
        sender_bytes = bytes.fromhex(sender)
        repo_url_bytes = repo_url.encode("utf-8")
        message_hash = eth_utils.keccak(sender_bytes + repo_url_bytes)
        tx = message_hash.hex()
        signature = await self.sign(signer_secret, tx, "bsc")
        return signature

    async def generate_reward_signature(self, sender: str, amount: int, nonce: int, signer_secret: str = settings.LAUNCH_SIGN_SECRET) -> bytes | None:
        message_hash = self.w3.solidity_keccak(['address', 'uint256', 'uint256'], [sender, amount, nonce])
        tx = message_hash.hex()
        signature = await self.sign(signer_secret, tx, "bsc")
        return bytes.fromhex(signature)

    async def buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        amount_to_buy: Decimal,
        amount_out_min: int = 0,
        gas: int = 21000,
        gas_price: int = 1,
    ) -> str:
        """
        Buy tokens

        :param token_address: Token address
        :param buyer_pubkey: Buyer's public key
        :param buyer_secret: Buyer's secret key
        :param amount_to_buy: Amount of BNB to spend
        :param amount_out_min: Minimum amount of tokens expected
        :param gas: Gas limit
        :param gas_price: Gas price in gwei
        :return: Transaction hash
        """
        token_addr = self.w3.to_checksum_address(token_address)
        buyer_addr = self.w3.to_checksum_address(buyer_pubkey)
        nonce = await self.w3.eth.get_transaction_count(buyer_addr)
        tx = await self._build_buy_tx(
            token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(buyer_secret, tx, "bsc")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def sell(
        self,
        token_address: str,
        amount_to_sell: Decimal,
        pubkey: str,
        secret: str,
        amount_out_min: int = 0,
        gas: int = 24000,
        gas_price: int = 1,
    ) -> str:
        """
        Sell tokens

        :param token_address: Token address
        :param amount_to_sell: Amount of tokens to sell
        :param pubkey: Seller's public key
        :param secret: Seller's secret
        :param amount_out_min: Minimum BNB expected to receive
        :param gas: Gas limit
        :param gas_price: Gas price
        :return: Transaction hash
        """
        token_addr = self.w3.to_checksum_address(token_address)
        account_pubkey = self.w3.to_checksum_address(pubkey)

        nonce = await self.w3.eth.get_transaction_count(account_pubkey)

        approve_tx = await self._build_approve_tx(
            token_addr, amount_to_sell, gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(secret, approve_tx, "bsc")
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"Approve transaction sent: {approve_tx_hash.hex()}")
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"Approve transaction receipt: {receipt}")
        nonce += 1

        tx = await self._build_sell_tx(
            token_addr, amount_to_sell, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(secret, tx, "bsc")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def send(
        self,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
    ) -> str:
        """
        Send BNB from one address to another

        :param from_pubkey: Sender's public key
        :param from_secret: Sender's secret
        :param to_address: Recipient's address
        :param amount: Amount to send in BNB
        :param gas: Gas limit
        :param gas_price: Gas price
        :return: Transaction hash
        """
        from_addr = self.w3.to_checksum_address(from_pubkey)
        nonce = await self.w3.eth.get_transaction_count(from_addr)
        to_addr = self.w3.to_checksum_address(to_address)
        tx = {
            "nonce": nonce,
            "to": to_addr,
            "value": self.w3.to_wei(amount, "ether"),
            "gas": gas,
            "gasPrice": self.w3.to_wei(gas_price, "gwei"),
            "chainId": self.chain_id,
        }
        self.logger.debug(f"Send, gas: {gas}, gas_price: {gas_price}")
        signed_tx = await self.sign(from_secret, tx, "bsc")
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def send_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
    ) -> str:
        """
        Send ERC20 tokens from one address to another

        :param token_address: Token address
        :param from_pubkey: Sender's public key
        :param from_secret: Sender's secret
        :param to_address: Recipient's address
        :param amount: Amount to send
        :param gas: Gas limit
        :param gas_price: Gas price
        :return: Transaction hash
        """
        from_addr = self.w3.to_checksum_address(from_pubkey)
        nonce = await self.w3.eth.get_transaction_count(from_addr)
        to_addr = self.w3.to_checksum_address(to_address)
        # Build transaction
        tx = await self._build_transfer_tx(
            token_address, to_addr, amount, gas, gas_price, nonce
        )
        self.logger.debug(f"send_token, gas: {gas}, gas_price: {gas_price}")
        # Sign transaction
        signed_tx = await self.sign(from_secret, tx, "bsc")
        # Send transaction
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def graduate(self, token_address: str) -> str:
        """Graduate a token and launch it to PancakeSwap.

        This function builds and sends a transaction to graduate the token from the
        bonding curve to PancakeSwap exchange. It requires the sender's credentials
        to sign the transaction.

        :param token_address: The contract address of the token to graduate
        :type token_address: str
        :return: Transaction hash
        :rtype: str
        :raises Exception: When transaction fails or network connection issues occur
        """
        gas: int = 3000000
        gas_price: int = 3
        token_addr = self.w3.to_checksum_address(token_address)
        sender_addr = self.w3.to_checksum_address("******************************************")

        # Get the current nonce for the sender's address
        nonce = await self.w3.eth.get_transaction_count(sender_addr)

        # Build the transaction
        tx = await self.contract.functions.graduate(token_addr).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )

        # Sign the transaction
        signed_tx = await self.sign(settings.LAUNCH_SIGN_SECRET, tx, "bsc")

        # Send the transaction
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def claim(self, pubkey: str, secret: str, token_address: str, amount: Decimal, gas: int, gas_price: int):
        token_addr = self.w3.to_checksum_address(token_address)
        user_addr = self.w3.to_checksum_address(pubkey)
        reward_nonce = await self.get_reward_nonce(token_addr, user_addr)
        signature = await self.generate_reward_signature(pubkey, int(amount), reward_nonce, settings.REWARD_SIGNER_SECRET)
        tx = await self.reward_contract.functions.claim(token_addr, int(amount), reward_nonce, signature).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": await self.w3.eth.get_transaction_count(user_addr),
            }
        )

        signed_tx = await self.sign(secret, tx, "bsc")

        # Send the transaction
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def batch_distribute_reward(self, token_address: str, recipients: List[str], amounts: List[Decimal], gas: int, gas_price: int):
        token_addr = self.w3.to_checksum_address(token_address)
        recipients_addr = [self.w3.to_checksum_address(recipient) for recipient in recipients]
        operator_addr = self.w3.to_checksum_address(settings.REWARD_OPERATOR_PUBKEY)
        nonce = await self.w3.eth.get_transaction_count(operator_addr)
        tx = await self.reward_contract.functions.batchDistributeReward(token_addr, recipients_addr, amounts).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )

        signed_tx = await self.sign(settings.REWARD_OPERATOR_SECRET, tx, "bsc")

        # Send the transaction
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def get_bnb_balance(self, pubkey: str) -> Decimal:
        """Get BNB balance"""
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        balance_bnb = self.w3.from_wei(balance_wei, "ether")
        return balance_bnb

    async def get_bnb_balance_in_wei(self, pubkey: str) -> Decimal:
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        return Decimal(balance_wei)

    async def get_token_balance_in_wei(self, pubkey: str, token_address: str) -> int:
        address = self.w3.to_checksum_address(pubkey)
        token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address), abi=self.erc20_abi
        )
        balance_wei = await token_contract.functions.balanceOf(address).call()
        return balance_wei

    async def pancake_approve_token(self, token_address: ChecksumAddress, token_contract, wallet_address: ChecksumAddress, secret: str, amount: int, gas: int, gas_price: int):
        """
        授权PancakeSwap使用代币

        :param token_address: 代币地址
        :param wallet_address: 钱包地址
        :param secret: 用于KMS签名的密钥
        :return: 交易收据
        """
        try:

            # 检查当前授权额度
            current_allowance = await token_contract.functions.allowance(
                wallet_address,
                self.pancake_router_address
            ).call()

            if current_allowance >= amount:
                self.logger.info(f"Token {token_address} already approved for PancakeSwap")
                return None

            # 构建授权交易
            nonce = await self.w3.eth.get_transaction_count(wallet_address)

            tx = await token_contract.functions.approve(
                self.pancake_router_address,
                amount
            ).build_transaction({
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            })

            # 使用KMS签名交易
            signed_tx_hex = await self.sign(secret, tx, "bsc")
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_hex))

            # 等待交易完成
            tx_receipt = await self.w3.eth.wait_for_transaction_receipt(tx_hash)

            return tx_receipt
        except Exception as e:
            self.logger.error(f"Error approving token for PancakeSwap: {e}")
            return None

    async def swap_token(
        self,
        token_in: str,           # 输入代币地址 (使用"WBNB"标识符表示BNB/WBNB)
        token_out: str,          # 输出代币地址 (使用"WBNB"标识符表示BNB/WBNB)
        amount_in: Decimal,      # 输入金额
        user_pubkey: str,        # 用户公钥
        user_secret: str,        # 用户私钥
        slippage_percent: float = 0.5,    # 滑点百分比
        gas: int = 200_000,      # Gas限制
        gas_price: int = 3,      # Gas价格(gwei)
        deadline_minutes: int = 20,       # 交易期限(分钟)
    ) -> str:
        """
        统一的代币交换函数，支持BNB<->Token和Token<->BNB交换 (PancakeSwap实现)
        
        :param token_in: 输入代币地址，使用"WBNB"标识符表示BNB(实际转换为WBNB地址)
        :param token_out: 输出代币地址，使用"WBNB"标识符表示BNB(实际转换为WBNB地址)
        :param amount_in: 输入金额
        :param user_pubkey: 用户公钥
        :param user_secret: 用户私钥
        :param slippage_percent: 滑点百分比，默认0.5%
        :param gas: Gas限制，默认200000
        :param gas_price: Gas价格(gwei)，默认3
        :param deadline_minutes: 交易期限(分钟)，默认20
        :return: 交易哈希
        """
        try:
            wallet_address = self.w3.to_checksum_address(user_pubkey)
            
            # 转换"WBNB"标识符为实际的WBNB地址(BSC网络上原生代币BNB的包装版本)
            if token_in == "WBNB":
                token_in_addr = self.wbnb_address
            else:
                token_in_addr = self.w3.to_checksum_address(token_in)
                
            if token_out == "WBNB":
                token_out_addr = self.wbnb_address
            else:
                token_out_addr = self.w3.to_checksum_address(token_out)
            
            # 转换滑点格式
            slippage = Decimal(str(slippage_percent / 100))

            # 判断交易类型
            is_bnb_to_token = token_in_addr.lower() == self.wbnb_address.lower()
            is_token_to_bnb = token_out_addr.lower() == self.wbnb_address.lower()

            deadline = int(time.time()) + (deadline_minutes * 60)

            # 判断是BNB到代币还是代币到BNB的交易
            if is_bnb_to_token:
                # 从BNB到代币的交易
                self.logger.info(f"交易类型: BNB到代币")
                path = [self.wbnb_address, token_out_addr]
                # 检查BNB余额
                bnb_balance = await self.get_native_balance(wallet_address)
                if bnb_balance < amount_in:
                    self.logger.warning(f"BNB余额不足. 需要 {amount_in} 但只有 {bnb_balance}")
                    raise ValueError(f"BNB余额不足. 需要 {amount_in} 但只有 {bnb_balance}")

                # 计算输入金额
                amount_in_wei = self.w3.to_wei(amount_in, "ether")

                # 获取预期输出金额
                amounts = await self.pancake_contract.functions.getAmountsOut(amount_in_wei, path).call()

                # 计算最小输出金额（考虑滑点）
                min_output = int(amounts[-1] * (1 - slippage))

                self.logger.info(f"使用swapExactETHForTokens直接将BNB兑换为代币")
                tx = await self.pancake_contract.functions.swapExactETHForTokens(
                    min_output,
                    path,
                    wallet_address,
                    deadline
                ).build_transaction({
                    "chainId": self.chain_id,
                    "gas": gas,
                    "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                    "nonce": await self.w3.eth.get_transaction_count(wallet_address),
                    "value": amount_in_wei
                })
            elif is_token_to_bnb:
                # 从代币到BNB的交易
                self.logger.info(f"交易类型: 代币到BNB")
                path = [token_in_addr, self.wbnb_address]
                # 获取代币合约
                token_contract = self.w3.eth.contract(
                    address=token_in_addr,
                    abi=self.erc20_abi
                )

                # 检查代币余额
                decimals = settings.DEFAULT_TOKEN_DECIMALS
                balance = await token_contract.functions.balanceOf(wallet_address).call()
                balance_decimal = Decimal(balance) / (10 ** decimals)

                if balance_decimal < amount_in:
                    self.logger.warning(f"代币余额不足. 需要 {amount_in} 但只有 {balance_decimal}")
                    raise ValueError(f"代币余额不足. 需要 {amount_in} 但只有 {balance_decimal}")

                amount_in_wei = self.w3.to_wei(amount_in, "ether")

                # 授权代币
                approval = await self.pancake_approve_token(token_in_addr, token_contract, wallet_address, user_secret, amount_in_wei, gas, gas_price)

                # 获取预期输出金额
                amounts = await self.pancake_contract.functions.getAmountsOut(amount_in_wei, path).call()

                # 计算最小输出金额（考虑滑点）
                min_output = int(amounts[-1] * (1 - slippage))

                self.logger.info(f"使用swapExactTokensForETH直接将代币兑换为BNB")
                tx = await self.pancake_contract.functions.swapExactTokensForETH(
                    amount_in_wei,
                    min_output,
                    path,
                    wallet_address,
                    deadline
                ).build_transaction({
                    "chainId": self.chain_id,
                    "gas": gas,
                    "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                    "nonce": await self.w3.eth.get_transaction_count(wallet_address),
                })
            else:
                # Token -> Token 交换（暂不支持，需要多跳路径）
                raise ValueError("暂不支持Token到Token的直接交换，请分步操作")
            
            # 使用KMS签名交易
            signed_tx_hex = await self.sign(user_secret, tx, "bsc")
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_hex))
            
            self.logger.info(f"DEX交易完成，哈希: {tx_hash.hex()}")
            return self.w3.to_hex(tx_hash)
        except Exception as e:
            self.logger.error(f"DEX token swap failed: {str(e)}")
            raise e

    # -----------------------------
    # DEX Amount Query Methods (Implementation of Abstract Interface)
    # -----------------------------

    async def get_dex_out_token_amount(self, token_address: str, native_amount: Decimal) -> Tuple:
        """获取DEX中用原生代币(BNB)可以换取的代币数量"""
        return await self.pancake_get_out_token_amount(token_address, native_amount)

    async def get_dex_in_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中换取指定代币数量需要的原生代币(BNB)数量"""
        return await self.pancake_get_in_native_amount(token_address, token_amount)

    async def get_dex_out_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中用代币可以换取的原生代币(BNB)数量"""
        return await self.pancake_get_out_native_amount(token_address, token_amount)

    async def get_dex_out_native_amount_after_fee(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中用代币可以换取的原生代币(BNB)数量(扣除手续费)"""
        return await self.pancake_get_out_native_amount_after_fee(token_address, token_amount)

    async def pancake_swap_token(
        self,
        amount_in: Decimal,
        token_path: list,
        wallet_address: str,
        secret: str,
        gas: int,
        gas_price: int,
        slippage: Decimal = Decimal("0.005"),
        auto_wrap_unwrap: bool = True
    ) -> str:
        """
        在PancakeSwap上交换代币
        
        @deprecated: 建议使用 swap_token 函数替代，这个函数保留用于向后兼容

        :param amount_in: 输入代币数量
        :param token_path: 代币路径列表
        :param wallet_address: 钱包地址
        :param secret: 用于KMS签名的密钥
        :param slippage: 滑点，默认0.5%
        :param auto_wrap_unwrap: 是否自动包装BNB为WBNB或解包WBNB为BNB，默认为True
        :return: 交易收据
        """
        self.logger.warning("pancake_swap_token is deprecated, please use swap_token instead")
        
        # 从token_path提取token_in和token_out
        if len(token_path) < 2:
            raise ValueError("token_path must contain at least 2 tokens")
        
        token_in = token_path[0] if isinstance(token_path[0], str) else token_path[0]
        token_out = token_path[-1] if isinstance(token_path[-1], str) else token_path[-1]
        
        # 转换滑点格式
        slippage_percent = float(slippage * 100)
        
        # 调用新的swap_token函数
        return await self.swap_token(
            token_in=token_in,
            token_out=token_out,
            amount_in=amount_in,
            user_pubkey=wallet_address,
            user_secret=secret,
            slippage_percent=slippage_percent,
            gas=gas,
            gas_price=gas_price
        )

    async def wrap_bnb(self, amount: Decimal, wallet_address: str, secret: str):
        """
        将BNB包装为WBNB
        
        :param amount: BNB数量
        :param wallet_address: 钱包地址
        :param secret: 用于KMS签名的密钥
        :return: 交易收据
        """
        try:
            wallet_address = self.w3.to_checksum_address(wallet_address)
            
            # 检查BNB余额
            bnb_balance = await self.get_native_balance(wallet_address)
            if bnb_balance < amount:
                self.logger.warning(f"Insufficient BNB balance. Need {amount} but have {bnb_balance}")
                return None
                
            # 获取WBNB合约
            contracts = PancakeContracts.MAINNET if self.chain_id == 56 else PancakeContracts.TESTNET
            wbnb_address = self.w3.to_checksum_address(contracts["WBNB"]["address"])
            wbnb_contract = self.w3.eth.contract(
                address=wbnb_address,
                abi=self.erc20_abi  # 使用ERC20 ABI
            )
            
            # 构建交易
            nonce = await self.w3.eth.get_transaction_count(wallet_address)
            amount_wei = self.w3.to_wei(amount, "ether")
            
            tx = await wbnb_contract.functions.deposit().build_transaction({
                "chainId": self.chain_id,
                "gas": 50000,
                "gasPrice": self.w3.to_wei(3, "gwei"),
                "nonce": nonce,
                "value": amount_wei,
            })
            
            # 使用KMS签名交易
            signed_tx_hex = await self.sign(secret, tx, "bsc")
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_hex))
            
            # 等待交易完成
            tx_receipt = await self.w3.eth.wait_for_transaction_receipt(tx_hash)
            
            return tx_receipt
        except Exception as e:
            self.logger.error(f"Error wrapping BNB: {e}")
            return None

    async def unwrap_wbnb(self, amount: Decimal, wallet_address: str, secret: str):
        """
        将WBNB解包为BNB
        
        :param amount: WBNB数量
        :param wallet_address: 钱包地址
        :param secret: 用于KMS签名的密钥
        :return: 交易收据
        """
        try:
            wallet_address = self.w3.to_checksum_address(wallet_address)
            
            # 获取WBNB合约
            contracts = PancakeContracts.MAINNET if self.chain_id == 56 else PancakeContracts.TESTNET
            wbnb_address = self.w3.to_checksum_address(contracts["WBNB"]["address"])
            wbnb_contract = self.w3.eth.contract(
                address=wbnb_address,
                abi=self.erc20_abi  # 使用ERC20 ABI
            )
            
            # 检查WBNB余额
            wbnb_balance = await self.get_token_balance(wallet_address, wbnb_address)
            
            if wbnb_balance < amount:
                self.logger.warning(f"Insufficient WBNB balance. Need {amount} but have {wbnb_balance}")
                return None
                
            # 构建交易
            nonce = await self.w3.eth.get_transaction_count(wallet_address)
            amount_wei = self.w3.to_wei(amount, "ether")
            
            tx = await wbnb_contract.functions.withdraw(amount_wei).build_transaction({
                "chainId": self.chain_id,
                "gas": 50000,
                "gasPrice": self.w3.to_wei(3, "gwei"),
                "nonce": nonce,
            })
            
            # 使用KMS签名交易
            signed_tx_hex = await self.sign(secret, tx, "bsc")
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_hex))
            
            # 等待交易完成
            tx_receipt = await self.w3.eth.wait_for_transaction_receipt(tx_hash)
            
            return tx_receipt
        except Exception as e:
            self.logger.error(f"Error unwrapping WBNB: {e}")
            return None
        
    async def generic_estimate_evm_onchain_gas(self, abi_path: str, contract_address: str, function_name: str,  estimate_gas_params: dict, function_params: list = None,) -> int:
        """
        通用合约函数调用 gas 预估
        
        :param abi_path: ABI 路径
        :param contract_address: 合约地址
        :param function_name: 函数名称
        :param function_params: 函数参数列表
        :return: 估算的gas费用
        """
        try:

            
            # 处理普通转账的情况
            if function_name.lower() == 'native_token_transfer' and not contract_address:   
                # 设置默认值
                default_from_address = "******************************************"
                default_to_address = "******************************************"  # 零地址作为默认目标地址
                default_amount = 0  # 0 作为默认金额
                
                # 普通转账的 gas 预估       
                gas_estimate = await self.w3.eth.estimate_gas({
                    'from': default_from_address,
                    'to': default_to_address,
                    'value': default_amount
                })
                return gas_estimate
            
            # 加载 ABI
            abi = self._load_abi(abi_path)
            contract = self.w3.eth.contract(
                address=contract_address,
                abi=abi
            )
            
            # 处理合约调用的情况
            if function_params is None:
                function_params = []

    
            gas_estimate = await contract.functions[function_name](*function_params).estimate_gas({'from': estimate_gas_params['from'], 'value': estimate_gas_params['value']})
            return gas_estimate
        except Exception as e:
            self.logger.error(f"Error estimating gas: {e}") 
            return None
        
        

# Register BSCConnector with the BlockchainConnectorGetter
BlockchainConnectorGetter.register_connector("bsc", BSCConnector)
