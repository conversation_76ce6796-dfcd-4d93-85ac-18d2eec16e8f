from src.common.blockchain.blockchain_connector import BlockchainConnector, BlockchainConnectorGetter

# 尝试导入各种连接器，如果依赖缺失则跳过
_available_connectors = ["BlockchainConnector", "BlockchainConnectorGetter"]

try:
    from src.common.blockchain.solana_connector import SolanaConnector
    _available_connectors.append("SolanaConnector")
except ImportError:
    # Solana依赖缺失，跳过
    pass

try:
    from src.common.blockchain.bsc_connector import BSCConnector
    _available_connectors.append("BSCConnector")
except ImportError:
    # BSC依赖缺失，跳过
    pass

try:
    from src.common.blockchain.eth_connector import ETHConnector
    _available_connectors.append("ETHConnector")
except ImportError:
    # ETH依赖缺失，跳过
    pass

__all__ = _available_connectors
