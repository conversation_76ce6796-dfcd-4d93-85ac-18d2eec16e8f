import json
import time
from decimal import Decimal
from logging import Logger
from typing import Any, Tu<PERSON>, List, Optional, Dict
import sys
import uuid
import asyncio
import eth_utils
from eth_typing import <PERSON>sumAddress
from web3 import AsyncHTTPProvider, AsyncWeb3
from web3.exceptions import Web3RPCError

from src.common.blockchain.blockchain_connector import (
    BlockchainConnector,
    BlockchainConnectorGetter,
)
from src.common.blockchain.memeswap_contracts import (
    MemeSwapContracts,
    MEMESWAP_ROUTER_ABI,
    ERC20_ABI,
    WETH_ABI,
    MEMESWAP_PAIR_ABI,
)
from src.memecoin.settings import settings
from src.common.redis_cli import RedisCli

# 增加整数字符串转换限制
sys.set_int_max_str_digits(100000000)

class ETHConnector(BlockchainConnector):

    L1_CHAIN_ID: int
    FEE_TO_ADDRESS: Optional[str]

    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        kms_address: str = None,
        chain_id: int = None,
        contract_address: str = None,
        meme_abi_path: str = None,
        relayer_abi_path: str = None,
        relayer_contract_address: str = None,
        l1_launcher_abi_path: str = None,
        l1_launcher_contract_address: str = None,
        erc20_abi_path: str = None,
        reward_contract_address: str = None,
        reward_abi_path: str = None,
    ):
        """
        ETH Connector for Ethereum Mainnet
        - rpc_url: 'https://eth.drpc.org' or similar
        - chain_id: 1 for mainnet, 5918 for Sepolia testnet
        """
        super().__init__(logger, rpc_url, kms_address)
        self.chain_id = chain_id or 1  # Default to mainnet if not specified
        self.w3 = AsyncWeb3(AsyncHTTPProvider(rpc_url))
        self.l1_rpc_url = settings.ETH_L1_RPC_URL
        self.l1_w3 = AsyncWeb3(AsyncHTTPProvider(self.l1_rpc_url))
        # 注意：这里不能直接获取chain_id，因为l1_w3是异步的
        # 需要在异步方法中调用 await self.l1_w3.eth.chain_id
        self.contract_address = (
            self.w3.to_checksum_address(contract_address) if contract_address else None
        )
        self.reward_contract_address = (
            self.w3.to_checksum_address(reward_contract_address) if reward_contract_address else None
        )
        self.relayer_contract_address = (
            self.w3.to_checksum_address(relayer_contract_address) if relayer_contract_address else None
        )
        self.l1_launcher_contract_address = (
            self.l1_w3.to_checksum_address(l1_launcher_contract_address) if l1_launcher_contract_address else None
        )

        if l1_launcher_contract_address and l1_launcher_abi_path:
            self.l1_launcher_contract = self.l1_w3.eth.contract(
                address=self.l1_launcher_contract_address,
                abi=self._load_abi(l1_launcher_abi_path)
            )
        
        if relayer_contract_address and relayer_abi_path:
            self.relayer_contract = self.w3.eth.contract(
                address=self.relayer_contract_address,
                abi=self._load_abi(relayer_abi_path)
            )

        if contract_address and meme_abi_path:
            self.contract = self.w3.eth.contract(
                address=self.contract_address,
                abi=self._load_abi(meme_abi_path),
            )

        if reward_contract_address and reward_abi_path:
            self.reward_contract = self.w3.eth.contract(
                address=self.reward_contract_address,
                abi=self._load_abi(reward_abi_path)
            )

        self.erc20_abi = self._load_abi(erc20_abi_path) if erc20_abi_path else ERC20_ABI
        
        # MemeSwap (Uniswap V2) integration
        self.memeswap_constants = MemeSwapContracts.MAINNET if self.chain_id == 1 else MemeSwapContracts.TESTNET
            
        self.memeswap_router_address = self.w3.to_checksum_address(self.memeswap_constants["MEMESWAP_ROUTER"]["address"])
        self.memeswap_contract = self.w3.eth.contract(
            address=self.memeswap_router_address,
            abi=MEMESWAP_ROUTER_ABI
        )
        
        # Update WETH address to use MemeSwap constants
        self.weth_address = self.w3.to_checksum_address(self.memeswap_constants["WETH"]["address"])
        self.usdt_address = self.w3.to_checksum_address(self.memeswap_constants["USDT"]["address"])
        decimals = self.memeswap_constants["USDT"]["decimals"]
        self.usdt_decimals = 10 ** decimals if decimals else settings.USDT_DECIMALS

        # feeToAddress 采用类级别缓存，懒加载后进程内复用

    def _load_abi(self, abi_path: str) -> Any:
        with open(abi_path, "r", encoding="utf-8") as f:
            raw_abi = json.load(f)
            return raw_abi.get("abi")

    async def init_connection(self):
        """
        Call this method to check the connection status
        """
        connected = await self.w3.is_connected()
        if not connected:
            raise ConnectionError(
                f"Unable to connect to Ethereum network, please check the RPC URL ({self.rpc_url}) and network status."
            )
        
        # 初始化L1 chain_id
        try:
            ETHConnector.L1_CHAIN_ID = await self.get_l1_chain_id()
        except Exception as e:
            self.logger.warning(f"Failed to get L1 chain_id: {e}")
            raise e
        
        # 初始化 feeToAddress（类级懒加载），失败直接报错
        fee_addr = await self.fee_to_address()
        if not fee_addr:
            raise ConnectionError("Unable to fetch feeToAddress from contract during initialization")
        
    async def get_block_timestamp(self) -> int:
        """获取当前区块时间戳"""
        try:
            latest_block = await self.w3.eth.get_block('latest')
            return latest_block['timestamp']
        except Exception as e:
            self.logger.warning(f"Failed to get block timestamp: {e}")
            # 回退到系统时间
            return int(time.time())
    
    async def get_l1_chain_id(self) -> int:
        """获取L1网络的chain_id"""
        try:
            return await self.l1_w3.eth.chain_id
        except Exception as e:
            self.logger.error(f"Failed to get L1 chain_id: {e}")
            raise e
    # -----------------------------
    # View functions
    # -----------------------------

    async def get_token_decimals(self, token_address: str) -> int:
        try:
            token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address),
            abi=self.erc20_abi
            )
            decimals = await token_contract.functions.decimals().call()
            return int(decimals)
        except Exception as e:
            self.logger.error(f"Failed to get token decimals from token {token_address}: {str(e)}")
            raise e

    async def get_permit_erc20_domain_separator(self, token_address: str) -> bytes:
        """Get ERC20 permit domain separator by calling DOMAIN_SEPARATOR()"""
        try:
            # 创建 ERC20 合约实例
            token_contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(token_address),
                abi=self.erc20_abi
            )
            
            # 调用 DOMAIN_SEPARATOR() 接口
            domain_separator = await token_contract.functions.DOMAIN_SEPARATOR().call()
            return domain_separator
        except Exception as e:
            self.logger.error(f"Failed to get DOMAIN_SEPARATOR from token {token_address}: {str(e)}")
            raise e
        
    async def get_permit_erc20_nonce(self, token_address: str, owner: str) -> int:
        """Get ERC20 permit nonce by calling nonces(address)"""
        try:
            token_contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(token_address),
                abi=self.erc20_abi
            )   
            nonce = await token_contract.functions.nonces(self.w3.to_checksum_address(owner)).call()
            return nonce
        except Exception as e:
            self.logger.warning(f"Failed to get nonce from token {token_address}: {str(e)}")
            self.logger.warning("Token may not support permit functionality, returning 0 as default")
            return 0  # 返回默认值而不是抛出异常


    async def userCreateTokenEligible(self, pubkey: str) -> bool:
        """Check if user is eligible to create a token"""
        if not self.contract:
            return True  # Default to True if no contract is set
        account = self.w3.to_checksum_address(pubkey)
        return await self.contract.functions.userEligible(account).call()

    async def get_native_balance_in_wei(self, pubkey: str) -> Decimal:
        """Get ETH balance in wei"""
        return await self.get_eth_balance_in_wei(pubkey)

    async def get_cash_balance_in_wei(self, pubkey: str) -> Decimal:
        """Get cash balance in wei"""
        balance_wei = await self.get_token_balance_in_wei(pubkey, self.usdt_address)
        return Decimal(balance_wei)

    async def get_eth_balance_in_wei(self, pubkey: str) -> Decimal:
        """Get ETH balance in wei"""
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        return Decimal(balance_wei)

    async def get_token_balance_in_wei(self, pubkey: str, token_address: str) -> int:
        """Get ERC20 token balance in wei"""
        address = self.w3.to_checksum_address(pubkey)
        token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address), abi=self.erc20_abi
        )
        balance_wei = await token_contract.functions.balanceOf(address).call()
        return balance_wei

    def check_if_native_token(self, token_address: str) -> bool:
        """Check if the token is the native ETH token"""
        return token_address == "native_token" or token_address.lower() == "eth"

    async def get_liquidity(self, token_address: str) -> float:
        try:
            address = self.w3.to_checksum_address(token_address)
            token_info = await self.contract.functions.tokenInfos(address).call()
            virtual_x, virtual_y, _, _ = token_info
            pool_usdt_amount = virtual_y / 1e6 - 14000 # 14000 USDT
            return pool_usdt_amount if pool_usdt_amount > 0 else 0
        except Exception as e:
            self.logger.warning(f"Failed to get liquidity for token {token_address}: {str(e)}")
            return 0

    async def progress(self, token_address: str) -> int:
        """Get token progress percentage"""
        if not self.contract:
            return 0
        token_addr = self.w3.to_checksum_address(token_address)
        try:
            return await self.contract.functions.progress(token_addr).call()
        except Exception as e:
            self.logger.warning(f"Failed to get progress for token {token_address}: {str(e)}")
            return -1

    async def get_token_price(self, token_address: str) -> float:
        """获取基于 bonding curve 的当前中间价 mid（USDT/Token）

        仅返回 mid，为浮点数（UI 单位）。
        """
        try:
            if not getattr(self, "contract", None):
                return 0.0

            address = self.w3.to_checksum_address(token_address)

            # 读取虚拟储备
            token_info = await self.contract.functions.tokenInfos(address).call()
            virtual_x, virtual_y, _, _ = token_info

            # 防止除零
            if virtual_x is None or int(virtual_x) <= 0:
                return 0.0

            # 中间价（USDT/Token，不含费）。
            # 单位换算：virtual_y(USDT最小单位) / virtual_x(Token最小单位) × (1e18 / 1e6) = × 1e12
            scale = Decimal(1e12)
            mid = (Decimal(virtual_y) / Decimal(virtual_x)) * scale

            # 手续费（bps）
            # 以下手续费相关计算保留为注释
            # fee_bps = await self.contract.functions.tradingFeeRate().call()
            # fee_rate = Decimal(int(fee_bps)) / Decimal(10000)
            # one = Decimal(1)
            # buy_after_fee = mid / (one - fee_rate) if (one - fee_rate) > 0 else Decimal(0)
            # sell_after_fee = mid * (one - fee_rate) if fee_rate < one else Decimal(0)

            return float(mid) if mid > 0 else 0.0
        except Exception as e:
            self.logger.warning(f"Failed to get token price for {token_address}: {str(e)}")
            return 0.0

    # -----------------------------
    # MemeSwap (Uniswap V2) methods
    # -----------------------------

    async def memeswap_get_amounts_out(
        self, amount_in: Decimal, path: List[str]
    ) -> List[int]:
        """Get amounts out for MemeSwap trade"""
        try:
            path_checksummed = [self.w3.to_checksum_address(addr) for addr in path]
            amounts = await self.memeswap_contract.functions.getAmountsOut(
                self.w3.to_wei(amount_in, "ether"), path_checksummed
            ).call()
            return amounts
        except Exception as e:
            self.logger.warning(f"Failed to get amounts out from MemeSwap: {str(e)}")
            return []
    
    async def memeswap_get_amounts_out_universal(self, amount_in_uint256: Decimal, path: List[str]) -> List[int]:
        """Get amounts out for MemeSwap trade"""
        try:
            path_checksummed = [self.w3.to_checksum_address(addr) for addr in path]
            amounts = await self.memeswap_contract.functions.getAmountsOut(amount_in_uint256, path_checksummed).call()
            return amounts
        except Exception as e:  
            self.logger.warning(f"Failed to get memeswap_get_amounts_out_universal: {str(e)}")
            return []

    async def memeswap_get_amounts_in(
        self, amount_out: Decimal, path: List[str]
    ) -> List[int]:
        """Get amounts in for MemeSwap trade"""
        try:
            path_checksummed = [self.w3.to_checksum_address(addr) for addr in path]
            amounts = await self.memeswap_contract.functions.getAmountsIn(
                self.w3.to_wei(amount_out, "ether"), path_checksummed
            ).call()
            return amounts
        except Exception as e:
            self.logger.warning(f"Failed to get amounts in from MemeSwap: {str(e)}")
            return []
    async def memeswap_get_out_mount_universal(self, path: List[str], amount_in_uint256: int) -> int:
        """Get amounts out for MemeSwap trade
        amount_int_uint256: 这里使用原始单位，比如USDT的1 * 10^6
        """
        try:
            path_checksummed = [self.w3.to_checksum_address(addr) for addr in path]
            amounts = await self.memeswap_contract.functions.getAmountsOut(
                amount_in_uint256, path_checksummed
            ).call()
            # return last one
            if len(amounts) >= 2:
                return amounts[-1]
            return 0
        except Exception as e:
            self.logger.warning(f"Failed to memeswap_get_out_mount_universal from MemeSwap: {str(e)}")
            return 0
        
    async def memeswap_get_out_token_amount_with_usdt(self, token_address: str, usdt_amount: Decimal) -> tuple:
        """Get output token amount for MemeSwap USDT->Token swap"""
        try:
            path = [self.usdt_address, self.w3.to_checksum_address(token_address)]
            ustd_amount_decimal = int(Decimal(usdt_amount) * Decimal(self.usdt_decimals))
            amounts = await self.memeswap_get_amounts_out_universal(ustd_amount_decimal, path)
            if len(amounts) >= 2:
                return (amounts[-1], self.w3.from_wei(amounts[-1], "ether"))  
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap token amount: {str(e)}")
            return (0, Decimal("0"))
        
    async def memeswap_get_out_token_amount(
        self, token_address: str, eth_amount: Decimal
    ) -> tuple:
        """Get output token amount for MemeSwap ETH->Token swap"""
        try:
            path = [self.weth_address, self.w3.to_checksum_address(token_address)]
            amounts = await self.memeswap_get_amounts_out(eth_amount, path)
            if len(amounts) >= 2:
                return (amounts[1], self.w3.from_wei(amounts[1], "ether"))
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap token amount: {str(e)}")
            return (0, Decimal("0"))
        
    async def memeswap_get_out_usdt_amount(self, token_address: str, token_amount: Decimal) -> tuple:
        """Get output USDT amount for MemeSwap Token->WETH->USDT swap"""
        try:
            path = [self.w3.to_checksum_address(token_address), self.usdt_address]
            amounts = await self.memeswap_get_amounts_out(token_amount, path)
            if len(amounts) >= 2:
                return (amounts[-1], Decimal(amounts[-1]) / Decimal(self.usdt_decimals))  
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap USDT amount: {str(e)}")
            return (0, Decimal("0"))

    async def memeswap_get_out_eth_amount(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get output ETH amount for MemeSwap Token->ETH swap"""
        try:
            path = [self.w3.to_checksum_address(token_address), self.weth_address]
            amounts = await self.memeswap_get_amounts_out(token_amount, path)
            if len(amounts) >= 2:
                return (amounts[1], self.w3.from_wei(amounts[1], "ether"))
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap ETH amount: {str(e)}")
            return (0, Decimal("0"))

    async def memeswap_get_in_eth_amount(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get input ETH amount needed for MemeSwap ETH->Token swap"""
        try:
            path = [self.weth_address, self.w3.to_checksum_address(token_address)]
            amounts = await self.memeswap_get_amounts_in(token_amount, path)
            if len(amounts) >= 2:
                return (amounts[0], self.w3.from_wei(amounts[0], "ether"))
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap ETH amount for input: {str(e)}")
            return (0, Decimal("0"))

    async def memeswap_get_out_eth_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get output ETH amount for MemeSwap Token->ETH swap (after fees)"""
        try:
            # MemeSwap基于Uniswap V2，通常没有额外的协议费用
            # 直接使用getAmountsOut获取预期输出（已包含交换手续费）
            path = [self.w3.to_checksum_address(token_address), self.weth_address]
            amounts = await self.memeswap_get_amounts_out(token_amount, path)
            if len(amounts) >= 2:
                return (amounts[1], self.w3.from_wei(amounts[1], "ether"))
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap ETH amount after fee: {str(e)}")
            return (0, Decimal("0"))

    async def get_out_token_amount_with_usdt(self, token_address: str, usdt_amount: Decimal) -> tuple:
        """Get output token amount from bonding curve"""
        if not self.contract:
            return (0, Decimal("0"))
       
        usdt_amount_decimal = int(Decimal(usdt_amount) * Decimal(self.usdt_decimals))
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutTokenAmount(
            token_addr, usdt_amount_decimal
        ).call()
        return (
            amount[1],
            self.w3.from_wei(amount[1], "ether"),
        )

    async def get_out_token_amount(
        self, token_address: str, eth_amount: Decimal
    ) -> tuple:
        """Get output token amount from bonding curve"""
        if not self.contract:
            return (0, Decimal("0"))
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutTokenAmount(
            token_addr, self.w3.to_wei(eth_amount, "ether")
        ).call()
        return (
            amount[1],
            self.w3.from_wei(amount[1], "ether"),
        )

    async def get_in_native_amount(
        self, token_address: str, amount_out: Decimal
    ) -> tuple:
        """Get input ETH amount needed for specific token amount"""
        if not self.contract:
            return (0, Decimal("0"))
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getInBnbAmount(
            token_addr, self.w3.to_wei(amount_out, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def get_out_usdt_amount(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get output USDT amount from token amount"""
        if not self.contract:
            return (0, Decimal("0"))
        token_addr = self.w3.to_checksum_address(token_address)
        try:    
            eth_amount = await self.contract.functions.getOutBnbAmount(
                token_addr, self.w3.to_wei(token_amount, "ether")
            ).call()

            self.logger.debug(f"get_out_usdt_amount eth_amount: {eth_amount}")

            path = [self.weth_address, self.usdt_address]
            amounts = await self.memeswap_get_amounts_out(self.w3.from_wei(eth_amount, "ether"), path)
            self.logger.debug(f"get_out_usdt_amount amounts: {amounts}")
            if len(amounts) >= 1:
                return (amounts[1], Decimal(amounts[1]) / Decimal(self.usdt_decimals))  
            return (0, Decimal("0"))
        except Exception as e:
            self.logger.warning(f"Failed to get get_out_usdt_amount USDT amount: {str(e)}")
            return (0, Decimal("0"))

        return usdt_amount

    async def get_out_native_amount(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get output ETH amount from token amount"""
        if not self.contract:
            return (0, Decimal("0"))
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmount(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, amount / Decimal(self.usdt_decimals)

    async def get_out_native_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> tuple:
        """Get output ETH amount after fees"""
        if not self.contract:
            return (0, Decimal("0"))
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmountAfterFee(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, amount / Decimal(self.usdt_decimals)

    # -----------------------------
    # MemeSwap transaction builders
    # -----------------------------

    async def _build_memeswap_buy_with_USDT_tx(
        self, token_address: str, usdt_amount_to_buy: Decimal, amount_out_min: int, 
        buyer_address: str, gas: int, gas_price: int, nonce: int, deadline: int
    ):
        """Build MemeSwap USDT->Token swap transaction"""
        path = [self.usdt_address, self.w3.to_checksum_address(token_address)]
        tx = await self.memeswap_contract.functions.swapExactTokensForTokens(
            int(usdt_amount_to_buy * Decimal(self.usdt_decimals)),
            amount_out_min,
            path,
            buyer_address,
            deadline
        ).build_transaction(
            {   
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )   
        self.logger.debug(f"MemeSwap buy with USDT gas:{gas}, gas_price: {gas_price}")
        return tx
    
    async def _build_memeswap_sell_for_USDT_tx(
        self, token_address: str, token_amount: Decimal, amount_out_min: int, 
        seller_address: str, gas: int, gas_price: int, nonce: int, deadline: int
    ):
        """Build MemeSwap Token->WETH->USDT swap transaction"""
        path = [self.w3.to_checksum_address(token_address), self.usdt_address]
        tx = await self.memeswap_contract.functions.swapExactTokensForTokens(
            self.w3.to_wei(token_amount, "ether"),
            amount_out_min,
            path,
            seller_address,
            deadline
        ).build_transaction(
            {
                "chainId": self.chain_id,   
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"MemeSwap sell for USDT gas:{gas}, gas_price: {gas_price}")
        return tx
    
    
    async def _build_memeswap_buy_tx(
        self, token_address: str, eth_amount: Decimal, amount_out_min: int, 
        buyer_address: str, gas: int, gas_price: int, nonce: int, deadline: int
    ):
        """Build MemeSwap ETH->Token swap transaction"""
        path = [self.weth_address, self.w3.to_checksum_address(token_address)]
        tx = await self.memeswap_contract.functions.swapExactETHForTokens(
            amount_out_min,
            path,
            buyer_address,
            deadline
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(eth_amount, "ether"),
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"MemeSwap buy gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_memeswap_sell_tx(
        self, token_address: str, token_amount: Decimal, amount_out_min: int, 
        seller_address: str, gas: int, gas_price: int, nonce: int, deadline: int
    ):
        """Build MemeSwap Token->ETH swap transaction"""
        path = [self.w3.to_checksum_address(token_address), self.weth_address]
        tx = await self.memeswap_contract.functions.swapExactTokensForETH(
            self.w3.to_wei(token_amount, "ether"),
            amount_out_min,
            path,
            seller_address,
            deadline
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"MemeSwap sell gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_memeswap_approve_tx(
        self, token_address: str, amount: Decimal, gas: int, gas_price: int, nonce: int
    ):
        """Build MemeSwap token approval transaction"""
        token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address), 
            abi=self.erc20_abi
        )
        tx = await token_contract.functions.approve(
            self.memeswap_router_address, 
            self.w3.to_wei(amount, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"MemeSwap approve gas:{gas}, gas_price: {gas_price}")
        return tx
    async def _build_memeswap_approve_tx_universal(
        self, token_address: str, amount: int, gas: int, gas_price: int, nonce: int
    ):
        """Build MemeSwap token approval transaction"""
        token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address), 
            abi=self.erc20_abi
        )
        tx = await token_contract.functions.approve(
            self.memeswap_router_address, 
            amount
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"MemeSwap approve gas:{gas}, gas_price: {gas_price}")
        return tx

    # -----------------------------
    # Helper transaction builders
    # -----------------------------

    async def _build_launch_by_relayer_tx(
        self,
        creator,
        l2_token_address,
        name,
        symbol,
        repo_url,
        logo,
        desc,
        usdt_ui_amount_to_buy,
        user_gas_usdt_amount,
        launch_signer_signature,
        permit_deadline,
        permit_sig,
        relayer_gas,
        relayer_gas_price,
        relayer_nonce,
    ):
        """Build token launch transaction"""
        if not self.relayer_contract:
            raise ValueError("Relayer contract not initialized")
        amount_to_buy_with_usdt = int(usdt_ui_amount_to_buy * self.usdt_decimals)
        user_gas_usdt_amount = int(user_gas_usdt_amount * self.usdt_decimals)
        tx = await self.relayer_contract.functions.launch(
            creator, l2_token_address, name, symbol, repo_url, logo, desc, 0, amount_to_buy_with_usdt, 0,bytes.fromhex(launch_signer_signature) ,user_gas_usdt_amount, permit_deadline,bytes.fromhex(permit_sig)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": relayer_gas,
                "gasPrice": relayer_gas_price,
                "nonce": relayer_nonce,
            }
        )
        self.logger.debug(f"launch by relayer gas:{relayer_gas}, gas_price: {relayer_gas_price} creator: {creator} name: {name} symbol: {symbol} repo_url: {repo_url} logo: {logo} desc: {desc} signature: {launch_signer_signature}")
        return tx

    async def _build_launch_tx(
        self,
        creator,
        name,
        symbol,
        repo_url,
        logo,
        desc,
        nonce,
        amount_to_buy,
        gas,
        gas_price,
        signature,
        is_with_usdt: bool = False,
    ):
        """Build token launch transaction"""
        if not self.contract:
            raise ValueError("Contract not initialized")
        
        if is_with_usdt:
            amount_to_buy_with_usdt = int(amount_to_buy * self.usdt_decimals)
            tx = await self.contract.functions.launch(
                creator, name, symbol, repo_url, logo, desc, amount_to_buy_with_usdt, bytes.fromhex(signature)
            ).build_transaction(
                {
                    "chainId": self.chain_id,
                    "gas": gas,
                    "gasPrice": gas_price,
                    "nonce": nonce,
                }
            )
        else:
            tx = await self.contract.functions.launch(
                creator, name, symbol, repo_url, logo, desc, 0, bytes.fromhex(signature)
            ).build_transaction(
                {
                    "chainId": self.chain_id,
                    "gas": gas,
                    "value": self.w3.to_wei(amount_to_buy, "ether"),
                    "gasPrice": gas_price,
                    "nonce": nonce,
                }
            )
        self.logger.debug(f"launch gas:{gas}, gas_price: {gas_price} creator: {creator} name: {name} symbol: {symbol} repo_url: {repo_url} logo: {logo} desc: {desc} signature: {signature}")
        return tx

    async def _build_approve_tx(
        self, token_addr, amount_to_sell, gas, gas_price, nonce
    ):
        """Build token approval transaction"""
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        tx = await token_contract.functions.approve(
            self.contract_address, self.w3.to_wei(amount_to_sell, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"approve gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_sell_tx(
        self, token_addr, amount_to_sell, amount_out_min, gas, gas_price, nonce
    ):
        """Build token sell transaction"""
        if not self.contract:
            raise ValueError("Contract not initialized")
        tx = await self.contract.functions.sell(
            token_addr, self.w3.to_wei(amount_to_sell, "ether"), amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"sell gas:{gas}, gas_price: {gas_price}")
        return tx
    
    async def _build_buy_with_USDT_by_relayer_tx(
        self,
        buyer_address,
        token_addr,
        usdt_amount_to_buy,
        amount_out_min,
        eth_amount_out_min,
        gas,
        gas_price,
        usdt_ui_gas_amount,
        relayer_nonce,
        permit_deadline,
        permit_sig,
    ):
        """Build token buy transaction"""
        if not self.relayer_contract:
            raise ValueError("Relayer contract not initialized")
        
        tx = await self.relayer_contract.functions.buyWithUSDT(
            buyer_address, 
            token_addr, 
            int(usdt_amount_to_buy * self.usdt_decimals), 
            int(amount_out_min * settings.DEFAULT_DECIMALS), 
            int(eth_amount_out_min * settings.DEFAULT_DECIMALS), 
            int(usdt_ui_gas_amount * self.usdt_decimals), 
            permit_deadline,
            bytes.fromhex(permit_sig)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        self.logger.debug(f"_build_buy_with_USDT_by_relayer_tx gas:{gas}, gas_price: {gas_price}")
        return tx
    async def _build_buy_with_USDT_tx(
        self, token_addr, usdt_amount_to_buy, amount_out_min,eth_amount_out_min, gas, gas_price, nonce
    ):
        """Build token buy transaction"""
        if not self.contract:
            raise ValueError("Contract not initialized")
        
        tx = await self.contract.functions.buyWithUSDT(
            token_addr, int(usdt_amount_to_buy * self.usdt_decimals), amount_out_min, eth_amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"_build_buy_with_USDT_tx gas:{gas}, gas_price: {gas_price}")
        return tx
    
    async def _build_sell_for_USDT_by_relayer_tx(
        self,
        seller_address,
        token_addr,
        amount_to_sell,
        token_permit_deadline,
        token_permit_sig,
        eth_amount_out_min,
        usdt_amount_out_min,
        gas,
        gas_price,
        usdt_ui_gas_amount,
        usdt_permit_deadline,
        usdt_permit_sig,
        relayer_nonce,
    ):
        """Build token sell transaction"""
        if not self.relayer_contract:
            raise ValueError("Relayer contract not initialized")
        
        tx = await self.relayer_contract.functions.sellForUSDT(
            seller_address,
            token_addr,
            int(amount_to_sell * settings.DEFAULT_DECIMALS),
            token_permit_deadline,
            bytes.fromhex(token_permit_sig),
            int(eth_amount_out_min * settings.DEFAULT_DECIMALS),
            int(usdt_amount_out_min * self.usdt_decimals),
            int(usdt_ui_gas_amount * self.usdt_decimals),  
            usdt_permit_deadline,
            bytes.fromhex(usdt_permit_sig)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas, 
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        self.logger.debug(f"_build_sell_for_USDT_by_relayer_tx gas:{gas}, gas_price: {gas_price}")
        return tx
    async def _build_sell_for_USDT_tx(
        self, token_addr, amount_to_sell, amount_out_min, usdt_amount_out_min, gas, gas_price, nonce
    ):
        """Build token sell transaction"""
        if not self.contract:
            raise ValueError("Contract not initialized")
        tx = await self.contract.functions.sellForUSDT(
            token_addr, int(amount_to_sell * settings.DEFAULT_DECIMALS), amount_out_min, usdt_amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"_build_sell_for_USDT_tx gas:{gas}, gas_price: {gas_price}")
        return tx
    
    async def _build_buy_tx(
        self, token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
    ):
        """Build token buy transaction"""
        if not self.contract:
            raise ValueError("Contract not initialized")
        tx = await self.contract.functions.buy(
            token_addr, amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(amount_to_buy, "ether"),
                "gasPrice": gas_price,
                "nonce": nonce,
            }
        )
        self.logger.debug(f"buy gas:{gas}, gas_price: {gas_price}")
        return tx

    async def _build_transfer_tx(
        self, token_address, to_address, amount, gas, gas_price, nonce, token_decimal: int = 18
    ):
        """Build token transfer transaction"""
        token_addr = self.w3.to_checksum_address(token_address)
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        # Convert amount to token's decimal precision
        amount_wei = int(amount * (10 ** token_decimal))
        tx = await token_contract.functions.transfer(
            to_address, amount_wei
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "nonce": nonce,
                "gas": gas,
                "gasPrice": gas_price,
            }
        )
        return tx

    async def _send_relayer_tx_with_retry(
        self,
        tx_builder,
        builder_kwargs: dict,
        signer_secret: str,
        web3_instance,
        relayer_pubkey: str,
        initial_gas_price: int | None = None,
        gas_price_key: str = "gas_price",
        nonce_key: str = "relayer_nonce",
        use_redis_nonce: bool = True,
        chain_label: str = "l2",
    ) -> str:
        """签名并发送 relayer 交易，带加价重试与 pending nonce 刷新。

        - 第一次失败（underpriced/nonce too low）时：同 nonce 提升 ≥12.5% 重试
        - 再失败：刷新 pending nonce 后重试
        - 最多尝试 3 次
        """
        relayer_address = web3_instance.to_checksum_address(relayer_pubkey)
        gas_price = initial_gas_price if initial_gas_price is not None else await web3_instance.eth.gas_price
        op_id = str(uuid.uuid4())
        builder_name = getattr(tx_builder, "__name__", str(tx_builder))
        self.logger.info(
            f"[relayer_op:{op_id}] start send, builder:{builder_name}, chain:{chain_label}, "
            f"relayer:{relayer_address}, use_redis_nonce:{use_redis_nonce}, initial_gas_price:{initial_gas_price}, "
            f"effective_gas_price:{gas_price}"
        )
        attempts = 0
        max_attempts = 3
        last_error_message = None

        # 初始分配 nonce（优先使用 Redis 锁）
        if use_redis_nonce:
            nonce = await self._allocate_relayer_nonce(redis_chain_label=chain_label, web3_instance=web3_instance, relayer_address=relayer_address)
        else:
            nonce = await web3_instance.eth.get_transaction_count(relayer_address, "pending")
        nonce_source = "redis" if use_redis_nonce else "pending"
        self.logger.debug(
            f"[relayer_op:{op_id}] allocated nonce from {nonce_source}: {nonce} for relayer:{relayer_address}"
        )

        while attempts < max_attempts:
            attempts += 1
            kwargs = dict(builder_kwargs)
            kwargs[gas_price_key] = gas_price
            kwargs[nonce_key] = nonce
            self.logger.debug(
                f"[relayer_op:{op_id}] attempt {attempts}/{max_attempts} builder:{builder_name} gas_price:{gas_price} nonce:{nonce}"
            )

            tx = await tx_builder(**kwargs)
            signed_tx_hex = await self.sign(signer_secret, tx, "eth")
            try:
                signed_preview = signed_tx_hex[:12] if isinstance(signed_tx_hex, (str, bytes)) else str(type(signed_tx_hex))
                signed_len = len(signed_tx_hex) if hasattr(signed_tx_hex, "__len__") else -1
            except Exception:
                signed_preview, signed_len = "N/A", -1
            self.logger.debug(
                f"[relayer_op:{op_id}] attempt {attempts} signed_tx preview:{signed_preview} len:{signed_len}"
            )
            try:
                # 与现有大多数路径保持一致，直接传入返回值
                tx_hash = await web3_instance.eth.send_raw_transaction(signed_tx_hex)
                tx_hash_hex = web3_instance.to_hex(tx_hash)
                self.logger.info(
                    f"[relayer_op:{op_id}] success attempt {attempts} tx_hash:{tx_hash_hex} gas_price:{gas_price} nonce:{nonce}"
                )
                return tx_hash_hex
            except Web3RPCError as e:
                message = str(e)
                last_error_message = message
                if ("replacement transaction underpriced" in message) or ("nonce too low" in message):
                    bumped_gas_price = max(int(Decimal(gas_price) * Decimal("1.125")), int(gas_price) + 1)
                    if attempts == 1:
                        self.logger.warning(
                            f"[relayer_op:{op_id}] retryable error attempt {attempts} reason:{message}; "
                            f"gas_bump {gas_price}->{bumped_gas_price}; nonce:{nonce}; action=gas_bump"
                        )
                        gas_price = bumped_gas_price
                        continue
                    if attempts == 2:
                        prev_nonce = nonce
                        if use_redis_nonce:
                            nonce = await self._allocate_relayer_nonce(redis_chain_label=chain_label, web3_instance=web3_instance, relayer_address=relayer_address)
                        else:
                            nonce = await web3_instance.eth.get_transaction_count(relayer_address, "pending")
                        self.logger.warning(
                            f"[relayer_op:{op_id}] retryable error attempt {attempts} reason:{message}; "
                            f"gas_bump {gas_price}->{bumped_gas_price}; nonce_refresh {prev_nonce}->{nonce}; action=nonce_refresh"
                        )
                        gas_price = bumped_gas_price
                        continue
                self.logger.error(
                    f"[relayer_op:{op_id}] non-retriable error attempt {attempts} builder:{builder_name} chain:{chain_label} "
                    f"gas_price:{gas_price} nonce:{nonce} error:{message}"
                )
                raise e
        self.logger.error(
            f"[relayer_op:{op_id}] failed after {attempts} attempts builder:{builder_name} chain:{chain_label} "
            f"relayer:{relayer_address} last_error:{last_error_message}"
        )
        raise RuntimeError("Failed to send transaction after retries")

    async def _allocate_relayer_nonce(self, redis_chain_label: str, web3_instance, relayer_address: str, lock_ttl_ms: int = 5000) -> int:
        """基于 Redis 的分布式 nonce 分配。

        算法：
        - 获取分布式锁（SET NX PX）
        - 读取 last_nonce，与当前 pending nonce 取 max(last_nonce+1, pending)
        - 写回 last_nonce = 分配值
        - 释放锁
        - 如无法获得锁，退避重试，最多 5 次；仍失败则退化为直接使用 pending
        """
        client = RedisCli.async_()
        addr_lower = relayer_address.lower()
        last_key = f"nonce:last:{redis_chain_label}:{addr_lower}"
        lock_key = f"nonce:lock:{redis_chain_label}:{addr_lower}"
        token = str(uuid.uuid4())

        async def try_lock() -> bool:
            try:
                return bool(await client.set(lock_key, token, nx=True, px=lock_ttl_ms))
            except Exception:
                return False

        acquired = await try_lock()
        retries = 0
        while not acquired and retries < 5:
            retries += 1
            await asyncio.sleep(0.2 * retries)
            acquired = await try_lock()

        if not acquired:
            # 退化：直接返回 pending
            self.logger.warning(
                f"[nonce] lock not acquired for {relayer_address} on {redis_chain_label}, fallback to pending nonce"
            )
            return await web3_instance.eth.get_transaction_count(relayer_address, "pending")

        try:
            last_val = await client.get(last_key)
            if isinstance(last_val, (bytes, bytearray)):
                try:
                    last_val = last_val.decode("utf-8")
                except Exception:
                    last_val = None
            last_nonce = int(last_val) if last_val is not None else None

            pending_nonce = await web3_instance.eth.get_transaction_count(relayer_address, "pending")
            if last_nonce is None:
                allocated = pending_nonce
            else:
                allocated = max(pending_nonce, last_nonce + 1)

            await client.set(last_key, str(allocated))
            if retries > 0:
                self.logger.debug(
                    f"[nonce] lock acquired after {retries} retries for addr:{relayer_address} chain:{redis_chain_label}"
                )
            self.logger.debug(
                f"[nonce] allocated:{allocated} last:{last_nonce} pending:{pending_nonce} addr:{relayer_address} chain:{redis_chain_label}"
            )
            return allocated
        finally:
            # 安全释放
            lua = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end"
            try:
                await client.eval(lua, 1, lock_key, token)
            except Exception:
                pass
    
    async def _build_transfer_by_relayer_tx(
        self, 
        token_address,
        from_address,
        to_address, 
        amount: Decimal,  
        permit_deadline,
        token_permit_sig,
        gas,
        gas_price,
        usdt_ui_gas_amount: Decimal,
        usdt_permit_sig,
        relayer_nonce
    ):
        """Build token transfer transaction"""
        if not self.relayer_contract:
            raise ValueError("Relayer contract not initialized")
        
        token_decimal = await self.get_token_decimals(token_address)
        self.logger.info(f"transfer_token token_decimal: {token_decimal}")
        self.logger.info(f"transfer_token start building tx")
        
        # 使用字符串计算避免科学计数法
        amount_in_decimals = int(amount * (10 ** token_decimal))
        gas_usdt_amount_in_decimals = int(usdt_ui_gas_amount * self.usdt_decimals)
        
        self.logger.info(f"amount_in_decimals: {amount_in_decimals}")
        self.logger.info(f"gas_usdt_amount_in_decimals: {gas_usdt_amount_in_decimals}")
        # 规范化地址为校验和，避免 web3 InvalidAddress
        token_address_cs = self.w3.to_checksum_address(token_address)
        from_address_cs = self.w3.to_checksum_address(from_address)
        to_address_cs = self.w3.to_checksum_address(to_address)

        tx = await self.relayer_contract.functions.transferToken(
            token_address_cs, 
            amount_in_decimals,
            from_address_cs,
            to_address_cs,
            permit_deadline,
            bytes.fromhex(token_permit_sig),
            gas_usdt_amount_in_decimals,
            bytes.fromhex(usdt_permit_sig)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        return tx

    async def generate_signature(self, sender: str, repo_url: str, signer_secret: str = settings.LAUNCH_SIGN_SECRET) -> str | None:
        """Generate signature for token launch"""
        if sender.startswith("0x"):
            sender = sender[2:]
        sender_bytes = bytes.fromhex(sender)
        repo_url_bytes = repo_url.encode("utf-8")
        message_hash = eth_utils.keccak(sender_bytes + repo_url_bytes)
        tx = message_hash.hex()
        signature = await self.sign(signer_secret, tx, "bsc")
        return signature

    async def generate_reward_signature(self, sender: str, amount: int, nonce: int, signer_secret: str = settings.LAUNCH_SIGN_SECRET) -> bytes | None:
        """Generate reward signature"""
        message_hash = self.w3.solidity_keccak(['address', 'uint256', 'uint256'], [sender, amount, nonce])
        tx = message_hash.hex()
        signature = await self.sign(signer_secret, tx, "eth")
        return bytes.fromhex(signature)

    # -----------------------------
    # Transaction functions
    # -----------------------------

    async def _build_permit_signature(self, token_address: str, owner:str, spender:str, nonce: int, value: int,deadline: int, signer_secret: str) -> str | None:
        """Generate permit signature"""


        permit_type_hash = self.w3.solidity_keccak(
            ["string"],
            ["Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)"]
        )
        
        from eth_abi import encode
        # 手动进行 ABI 编码
        encoded = encode(['bytes32', 'address', 'address', 'uint256', 'uint256', 'uint256'], 
                        [permit_type_hash, owner, spender, value, nonce, deadline])

        # 然后计算 keccak256
        from eth_utils import keccak
        struct_hash = keccak(encoded)
        # 通过合约接口获取 domain separator
        domain_separator = await self.get_permit_erc20_domain_separator(token_address)
        
        # 计算 message_hash
        message_hash = self.w3.solidity_keccak(
            ["bytes", "bytes32", "bytes32"],
            [b"\x19\x01", domain_separator, struct_hash]
        )
        tx = "0x" + message_hash.hex()
        
        signature = await self.sign(signer_secret, tx, "eth", raw_sign=True)
        return signature
    
    async def _build_launch_l1_tx(self, name: str, symbol: str, repo_url: str, logo: str, desc: str, pubkey: str, gas: int = 5_000_000, gas_price: Optional[int] = None, relayer_nonce: int = 0) -> str:
        if not self.l1_launcher_contract:
            raise ValueError("L1 launcher contract not initialized")
        
        creator = self.w3.to_checksum_address(pubkey)
        signature = await self.generate_signature(creator, repo_url)

        tx = await self.l1_launcher_contract.functions.launchOnL1(
            creator,
            name,
            symbol,
            repo_url,
            logo,
            desc,
            bytes.fromhex(signature),
        ).build_transaction(
            {
                "chainId": ETHConnector.L1_CHAIN_ID,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        return tx
        

    async def launch_l1(self, name: str, symbol: str, repo_url: str, logo: str, desc: str, pubkey: str, gas: int = 5_000_000, gas_price: Optional[int] = None, gas_payer_secret: Optional[str] = None) -> str:
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        
        creator = self.l1_w3.to_checksum_address(pubkey)
        
        relayer_pubkey = settings.GAS_PAYER_PUBKEY

        builder_kwargs = dict(
            name=name,
            symbol=symbol,
            repo_url=repo_url,
            logo=logo,
            desc=desc,
            pubkey=creator,
            gas=gas,
        )
        initial_gas_price = gas_price if gas_price is not None else None
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_launch_l1_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.l1_w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=initial_gas_price,
            use_redis_nonce=True,
            chain_label="l1",
        )
        self.logger.info(f"launch_l1 by relayer sent: {tx_hash_hex}")
        return tx_hash_hex
    
    async def _build_bridge_l1_to_l2_tx(self, l1_token_address: str, l2_token_address: str, gas: int = 5_000_000, gas_price: Optional[int] = None, relayer_nonce: int = 0) -> str:
        if not self.l1_launcher_contract:
            raise ValueError("contract not initialized")
        tx = await self.l1_launcher_contract.functions.bridgeTokenToL2(l1_token_address, l2_token_address).build_transaction(
            {
                "chainId": ETHConnector.L1_CHAIN_ID,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        return tx
    
    async def bridge_l1_to_l2(self, l1_token_address: str, l2_token_address: str, gas: int = 5_000_000, gas_price: Optional[int] = None, gas_payer_secret: Optional[str] = None,) -> str:
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        l1_token_address = self.l1_w3.to_checksum_address(l1_token_address)
        l2_token_address = self.l1_w3.to_checksum_address(l2_token_address)
        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        initial_gas_price = (gas_price if gas_price is not None else await self.l1_w3.eth.gas_price) * 2

        builder_kwargs = dict(
            l1_token_address=l1_token_address,
            l2_token_address=l2_token_address,
            gas=gas,
        )
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_bridge_l1_to_l2_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.l1_w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=initial_gas_price,
            use_redis_nonce=True,
            chain_label="l1",
        )
        self.logger.info(f"bridge_l1_to_l2 by relayer sent: {tx_hash_hex}")
        return tx_hash_hex    
    
    async def _build_create_l2_token_tx(self, l1_token_address: str, name: str, symbol: str, repo_url: str, logo: str, desc: str, sender: str, gas: int = 5_000_000, gas_price: Optional[int] = None, relayer_nonce: int = 0) -> str:
        if not self.contract:
            raise ValueError("create_l2_token_contract not initialized")
        
        creator = self.w3.to_checksum_address(sender)
        l1_token_address = self.w3.to_checksum_address(l1_token_address)
        signature = await self.generate_signature(sender=sender, repo_url=repo_url)
        
        tx = await self.contract.functions.createL2Token(
            creator,
            l1_token_address,
            name,
            symbol,
            repo_url,
            logo,
            desc,
            bytes.fromhex(signature),
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        return tx
    
    async def create_l2_token(self, l1_token_address: str, name: str, symbol: str, repo_url: str, logo: str, desc: str, pubkey: str, gas: int = 5_000_000, gas_price: Optional[int] = None, gas_payer_secret: Optional[str] = None,) -> str:
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        l1_token_address = self.w3.to_checksum_address(l1_token_address)
        creator = self.w3.to_checksum_address(pubkey)
        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        builder_kwargs = dict(
            l1_token_address=l1_token_address,
            name=name,
            symbol=symbol,
            repo_url=repo_url,
            logo=logo,
            desc=desc,
            sender=creator,
            gas=gas,
        )
        initial_gas_price = gas_price if gas_price is not None else None
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_create_l2_token_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=initial_gas_price,
            use_redis_nonce=True,
            chain_label="l2",
        )
        self.logger.info(f"create_l2_token by relayer sent: {tx_hash_hex}")
        return tx_hash_hex
        

    async def launch(
        self,
        l2_token_address: str,
        name: str,
        symbol: str,
        repo_url: str,
        logo: str,
        desc: str,
        pubkey: str,
        secret: str,
        amount_to_buy: Decimal = 0,
        gas_usdt_amount: Decimal = 0.02,
        gas: int = 5_000_000,
        gas_price: Optional[int] = None,  # ETH typically uses higher gas prices
        gas_payer_secret: Optional[str] = None,
        is_with_usdt: Optional[bool] = False,
    ) -> str:
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        if not is_with_usdt:
            raise ValueError("is_with_usdt must be True")
        
        gas_price = gas_price if gas_price else await self.w3.eth.gas_price
        
        creator = self.w3.to_checksum_address(pubkey)
        signature = await self.generate_signature(pubkey, repo_url)

        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        creator_usdt_nonce = await self.get_permit_erc20_nonce(self.usdt_address, creator)
        self.logger.info(f"creator_usdt_nonce: {creator_usdt_nonce}")
        relayer_secret = gas_payer_secret
        permit_deadline = await self.get_block_timestamp() + 600
        permit_sig = await self._build_permit_signature(
            token_address=self.usdt_address, 
            owner=creator, 
            spender=self.relayer_contract_address, 
            nonce=creator_usdt_nonce, 
            value=int((amount_to_buy + gas_usdt_amount) * Decimal(self.usdt_decimals)), 
            deadline=permit_deadline, 
            signer_secret=secret
        )
        builder_kwargs = dict(
            creator=creator,
            l2_token_address=self.w3.to_checksum_address(l2_token_address),
            name=name,
            symbol=symbol,
            repo_url=repo_url,
            logo=logo,
            desc=desc,
            usdt_ui_amount_to_buy=amount_to_buy,
            user_gas_usdt_amount=gas_usdt_amount,
            launch_signer_signature=signature,
            permit_deadline=permit_deadline,
            permit_sig=permit_sig,
            relayer_gas=gas,
        )
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_launch_by_relayer_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=relayer_secret,
            web3_instance=self.w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=gas_price,
            gas_price_key="relayer_gas_price",
            use_redis_nonce=True,
            chain_label="l2",
        )
        self.logger.info(f"launch by relayer sent: {tx_hash_hex}")
        return tx_hash_hex
    

    async def buy_with_USDT(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        usdt_ui_amount_to_buy: Decimal,
        usdt_ui_gas_amount: Decimal,
        eth_amount_out_min: Decimal = Decimal(0),
        amount_out_min: Decimal = Decimal(0),
        gas: int = 1_000_000,
        gas_price: Optional[int] = None,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        gas_price = gas_price if gas_price else await self.w3.eth.gas_price
        buyer_addr = self.w3.to_checksum_address(buyer_pubkey)
        token_addr = self.w3.to_checksum_address(token_address)
        buyer_usdt_nonce = await self.get_permit_erc20_nonce(self.usdt_address, buyer_addr)
        permit_deadline = await self.get_block_timestamp() + 600
        permit_sig = await self._build_permit_signature(
            token_address=self.usdt_address, 
            owner=buyer_addr, 
            spender=self.relayer_contract_address, 
            nonce=buyer_usdt_nonce, 
            value=int((usdt_ui_amount_to_buy + usdt_ui_gas_amount) * Decimal(self.usdt_decimals)), 
            deadline=permit_deadline, 
            signer_secret=buyer_secret
        )

        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        builder_kwargs = dict(
            buyer_address=buyer_addr,
            token_addr=token_addr,
            usdt_amount_to_buy=usdt_ui_amount_to_buy,
            amount_out_min=amount_out_min,
            eth_amount_out_min=eth_amount_out_min,
            usdt_ui_gas_amount=usdt_ui_gas_amount,
            gas=gas,
            permit_deadline=permit_deadline,
            permit_sig=permit_sig,
        )
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_buy_with_USDT_by_relayer_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=gas_price,
            use_redis_nonce=True,
            chain_label="l2",
        )
        self.logger.info(f"buy_with_USDT by relayer sent: {tx_hash_hex}")
        return tx_hash_hex
    
    async def sell_for_USDT(
        self,
        token_address: str,
        seller_pubkey: str,
        seller_secret: str,
        amount_to_sell: Decimal,
        eth_amount_out_min: Decimal,
        usdt_ui_amount_out_min: Decimal,
        usdt_ui_gas_amount: Decimal,
        gas: int = 1_000_000,
        gas_price: Optional[int] = None,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        gas_price = gas_price if gas_price else await self.w3.eth.gas_price
        seller_addr = self.w3.to_checksum_address(seller_pubkey)
        token_addr = self.w3.to_checksum_address(token_address)
        seller_usdt_nonce = await self.get_permit_erc20_nonce(self.usdt_address, seller_addr)
        seller_token_nonce = await self.get_permit_erc20_nonce(token_addr, seller_addr)
        permit_deadline = await self.get_block_timestamp() + 600
        gas_permit_sig = await self._build_permit_signature(
            token_address=self.usdt_address, 
            owner=seller_addr, 
            spender=self.relayer_contract_address, 
            nonce=seller_usdt_nonce, 
            value=int((usdt_ui_gas_amount) * Decimal(self.usdt_decimals)), 
            deadline=permit_deadline, 
            signer_secret=seller_secret
        )
        token_permit_sig = await self._build_permit_signature(
            token_address=token_address, 
            owner=seller_addr, 
            spender=self.relayer_contract_address, 
            nonce=seller_token_nonce, 
            value=int(amount_to_sell * Decimal(settings.DEFAULT_DECIMALS)), 
            deadline=permit_deadline, 
            signer_secret=seller_secret
        )

        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        builder_kwargs = dict(
            seller_address=seller_addr,
            token_addr=token_addr,
            amount_to_sell=amount_to_sell,
            token_permit_deadline=permit_deadline,
            token_permit_sig=token_permit_sig,
            eth_amount_out_min=eth_amount_out_min,
            usdt_amount_out_min=usdt_ui_amount_out_min,
            gas=gas,
            usdt_ui_gas_amount=usdt_ui_gas_amount,
            usdt_permit_deadline=permit_deadline,
            usdt_permit_sig=gas_permit_sig,
        )
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_sell_for_USDT_by_relayer_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=gas_price,
            use_redis_nonce=True,
            chain_label="l2",
        )
        self.logger.info(f"sell_for_USDT by relayer sent: {tx_hash_hex}")
        return tx_hash_hex

    async def _build_purchase_tx(
        self,
        from_secret: str,
        from_address: str,
        token_address: str,
        to_address: str,
        purchase_usdt_amount: Decimal,
        gas_usdt_amount: Decimal,
        gas: int,
        gas_price: int,
        relayer_nonce: int,
    ):
        """
        Build purchase transaction
        """
        if not self.relayer_contract:
            raise ValueError("relayer contract not initialized")

        permit_deadline = await self.get_block_timestamp() + 600
        purchaser_usdt_nonce = await self.get_permit_erc20_nonce(self.usdt_address, from_address)
        purchaser_token_nonce = await self.get_permit_erc20_nonce(token_address, from_address)
        purchaser_token_amount = await self.get_token_balance_in_wei(from_address, token_address)
        usdt_permit_sig = await self._build_permit_signature(
            token_address=self.usdt_address, 
            owner=from_address, 
            spender=self.relayer_contract_address, 
            nonce=purchaser_usdt_nonce, 
            value=int(gas_usdt_amount + purchase_usdt_amount), 
            deadline=permit_deadline, 
            signer_secret=from_secret
        )
        token_permit_sig = await self._build_permit_signature(
            token_address=token_address, 
            owner=from_address, 
            spender=self.relayer_contract_address, 
            nonce=purchaser_token_nonce, 
            value=int(purchaser_token_amount), 
            deadline=permit_deadline, 
            signer_secret=from_secret
        )
        tx = await self.relayer_contract.functions.purchase(
            from_address,
            token_address,
            to_address,
            int(purchase_usdt_amount),
            permit_deadline,
            bytes.fromhex(token_permit_sig),
            int(gas_usdt_amount),
            permit_deadline,
            bytes.fromhex(usdt_permit_sig),
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": relayer_nonce,
            }
        )
        self.logger.debug(f"_build_buy_with_USDT_by_relayer_tx gas:{gas}, gas_price: {gas_price}")
        return tx


    async def purchase(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        to_address: str,
        purchase_usdt_ui_amount: Decimal,
        gas_usdt_ui_amount: Decimal,
        gas: int,
        gas_price: int,
        gas_payer_secret: str,
    ) -> str:
        """
        Purchase tokens 
        """
        token_addr = self.w3.to_checksum_address(token_address)
        buyer_addr = self.w3.to_checksum_address(buyer_pubkey)
        to_addr = self.w3.to_checksum_address(to_address)
        relayer_pubkey = settings.GAS_PAYER_PUBKEY
        builder_kwargs = dict(
            from_secret=buyer_secret,
            from_address=buyer_addr,
            token_address=token_addr,
            to_address=to_addr,
            purchase_usdt_amount=int(purchase_usdt_ui_amount * Decimal(self.usdt_decimals)),
            gas_usdt_amount=int(gas_usdt_ui_amount * Decimal(self.usdt_decimals)),
            gas=gas,
        )
        tx_hash_hex = await self._send_relayer_tx_with_retry(
            tx_builder=self._build_purchase_tx,
            builder_kwargs=builder_kwargs,
            signer_secret=gas_payer_secret,
            web3_instance=self.w3,
            relayer_pubkey=relayer_pubkey,
            initial_gas_price=gas_price,
            use_redis_nonce=True,
            chain_label="l2",
        )
        return tx_hash_hex


    async def buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        amount_to_buy: Decimal,
        amount_out_min: int = 0,
        gas: int = 200_000,  # ETH typically needs more gas
        gas_price: int = settings.DEFAULT_GAS_PRICE,
    ) -> str:
        """
        Buy tokens on Ethereum
        """
        token_addr = self.w3.to_checksum_address(token_address)
        buyer_addr = self.w3.to_checksum_address(buyer_pubkey)
        nonce = await self.w3.eth.get_transaction_count(buyer_addr)
        tx = await self._build_buy_tx(
            token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(buyer_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return self.w3.to_hex(tx_hash)

    async def sell(
        self,
        token_address: str,
        amount: Decimal,
        seller_pubkey: str,
        seller_secret: str,
        amount_out_min: int = 0,
        gas: int = 200_000,
        gas_price: int = settings.DEFAULT_GAS_PRICE,
    ) -> str:
        """
        Sell tokens on Ethereum
        """
        token_addr = self.w3.to_checksum_address(token_address)
        account_pubkey = self.w3.to_checksum_address(seller_pubkey)

        nonce = await self.w3.eth.get_transaction_count(account_pubkey)

        approve_tx = await self._build_approve_tx(
            token_addr, amount, gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(seller_secret, approve_tx, "eth")
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"Approve transaction sent: {approve_tx_hash.hex()}")
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"Approve transaction receipt: {receipt}")
        nonce += 1

        tx = await self._build_sell_tx(
            token_addr, amount, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(seller_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return self.w3.to_hex(tx_hash)

    async def transfer_native(
        self,
        from_pubkey: str,
        from_secret: str,
        to_pubkey: str,
        amount: Decimal,
        gas: int = 21000,
        gas_price: int = settings.DEFAULT_GAS_PRICE,
    ) -> str:
        """
        Transfer ETH
        """
        from_addr = self.w3.to_checksum_address(from_pubkey)
        nonce = await self.w3.eth.get_transaction_count(from_addr)
        to_addr = self.w3.to_checksum_address(to_pubkey)
        tx = {
            "nonce": nonce,
            "to": to_addr,
            "value": self.w3.to_wei(amount, "ether"),
            "gas": gas,
            "gasPrice": gas_price,
            "chainId": self.chain_id,
        }
        self.logger.debug(f"Send ETH, gas: {gas}, gas_price: {gas_price}")
        signed_tx = await self.sign(from_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)
    
    async def transfer_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_pubkey: str,
        amount: Decimal,
        usdt_ui_gas_amount: Decimal,
        gas: int = 60_000,
        gas_price: int = None,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        """
        Transfer ERC20 tokens
        """
        if not gas_payer_secret:
            raise ValueError("gas_payer_secret is required")
        gas_price = gas_price if gas_price else await self.w3.eth.gas_price
        token_decimal = await self.get_token_decimals(token_address)
        from_addr = self.w3.to_checksum_address(from_pubkey)
        to_addr = self.w3.to_checksum_address(to_pubkey)
        relayer_pubkey = self.w3.to_checksum_address(settings.GAS_PAYER_PUBKEY)
        relayer_nonce = await self.w3.eth.get_transaction_count(relayer_pubkey, 'pending')
        from_usdt_nonce = await self.get_permit_erc20_nonce(self.usdt_address, from_addr)
        if self.w3.to_checksum_address(token_address) == self.w3.to_checksum_address(self.usdt_address):
            from_token_nonce = from_usdt_nonce + 1
        else:   
            from_token_nonce = await self.get_permit_erc20_nonce(token_address, from_addr)
        permit_deadline = await self.get_block_timestamp() + 600
        token_permit_sig = await self._build_permit_signature(
            token_address=token_address, 
            owner=from_addr, 
            spender=self.relayer_contract_address, 
            nonce=from_token_nonce,   
            value=int(amount * (10 ** token_decimal)), 
            deadline=permit_deadline, 
            signer_secret=from_secret
        )
        self.logger.info(f"transfer_token token_permit_sig: {token_permit_sig}")
        usdt_permit_sig = await self._build_permit_signature(
            token_address=self.usdt_address, 
            owner=from_addr, 
            spender=self.relayer_contract_address, 
            nonce=from_usdt_nonce, 
            value=int(usdt_ui_gas_amount * self.usdt_decimals), 
            deadline=permit_deadline, 
            signer_secret=from_secret
        )
        self.logger.info(f"transfer_token usdt_permit_sig: {usdt_permit_sig}")
        tx = await self._build_transfer_by_relayer_tx(
            token_address=token_address,
            from_address=from_addr,
            to_address=to_addr,
            amount=amount,
            permit_deadline=permit_deadline,
            token_permit_sig=token_permit_sig,
            gas=gas,
            gas_price=gas_price,
            usdt_ui_gas_amount=usdt_ui_gas_amount,
            usdt_permit_sig=usdt_permit_sig,
            relayer_nonce=relayer_nonce,
        )
        self.logger.info(f"transfer_token tx: {tx}")
        self.logger.info(f"transfer_token params - relayer_nonce: {relayer_nonce}, gas_price: {gas_price}, to_addr: {to_addr}, amount: {amount}")
        signed_tx = await self.sign(gas_payer_secret, tx, "eth")
        self.logger.info(f"transfer_token signed_tx: {signed_tx}")
        try:
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
            return self.w3.to_hex(tx_hash)
        except Web3RPCError as e:
            error_message = str(e)
            self.logger.warning(f"transfer_token send failed: {error_message}")
            if ("replacement transaction underpriced" in error_message) or ("nonce too low" in error_message):
                bumped_gas_price = max(int(Decimal(gas_price) * Decimal('1.125')), int(gas_price) + 1)
                self.logger.warning(f"transfer_token retry with bumped gas price - from {gas_price} to {bumped_gas_price}, nonce: {relayer_nonce}")
                tx_retry = await self._build_transfer_by_relayer_tx(
                    token_address=token_address,
                    from_address=from_addr,
                    to_address=to_addr,
                    amount=amount,
                    permit_deadline=permit_deadline,
                    token_permit_sig=token_permit_sig,
                    gas=gas,
                    gas_price=bumped_gas_price,
                    usdt_ui_gas_amount=usdt_ui_gas_amount,
                    usdt_permit_sig=usdt_permit_sig,
                    relayer_nonce=relayer_nonce,
                )
                signed_tx_retry = await self.sign(gas_payer_secret, tx_retry, "eth")
                try:
                    tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_retry))
                    return self.w3.to_hex(tx_hash)
                except Web3RPCError as e2:
                    error_message2 = str(e2)
                    self.logger.warning(f"transfer_token retry with bumped gas price failed: {error_message2}")
                    try:
                        relayer_nonce2 = await self.w3.eth.get_transaction_count(relayer_pubkey, 'pending')
                        self.logger.warning(f"transfer_token retry with refreshed pending nonce: {relayer_nonce2}")
                        tx_retry2 = await self._build_transfer_by_relayer_tx(
                            token_address=token_address,
                            from_address=from_addr,
                            to_address=to_addr,
                            amount=amount,
                            permit_deadline=permit_deadline,
                            token_permit_sig=token_permit_sig,
                            gas=gas,
                            gas_price=bumped_gas_price,
                            usdt_ui_gas_amount=usdt_ui_gas_amount,
                            usdt_permit_sig=usdt_permit_sig,
                            relayer_nonce=relayer_nonce2,
                        )
                        signed_tx_retry2 = await self.sign(gas_payer_secret, tx_retry2, "eth")
                        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx_retry2))
                        return self.w3.to_hex(tx_hash)
                    except Web3RPCError as e3:
                        self.logger.error(f"transfer_token retry with refreshed pending nonce failed: {str(e3)}")
                        raise e3
            raise e

    # -----------------------------
    # MemeSwap transaction functions
    # -----------------------------
    async def swap_token(
        self,
        token_in: str,           # 输入代币地址 (使用"WETH"表示ETH)
        token_out: str,          # 输出代币地址 (使用"WETH"表示ETH) 
        amount_in: Decimal,      # 输入金额
        user_pubkey: str,        # 用户公钥
        user_secret: str,        # 用户私钥
        slippage_percent: float = 0.5,    # 滑点百分比
        gas: int = 200_000,      # Gas限制
        gas_price: int = settings.DEFAULT_GAS_PRICE,     # Gas价格(wei)
        deadline_minutes: int = 20,       # 交易期限(分钟)
    ) -> str:
        """
        统一的代币交换函数，支持ETH<->Token和Token<->ETH交换 (Uniswap V2实现)
        
        :param token_in: 输入代币地址，使用"WETH"表示ETH
        :param token_out: 输出代币地址，使用"WETH"表示ETH
        :param amount_in: 输入金额
        :param user_pubkey: 用户公钥
        :param user_secret: 用户私钥
        :param slippage_percent: 滑点百分比，默认0.5%
        :param gas: Gas限制，默认200000
        :param gas_price: Gas价格(wei)，默认10_000_000
        :param deadline_minutes: 交易期限(分钟)，默认20
        :return: 交易哈希
        """
        try:
            user_addr = self.w3.to_checksum_address(user_pubkey)
            
            # 转换"WETH"标识符为实际的WETH地址
            if token_in == "USDT":
                token_in_addr = self.usdt_address
            else:
                token_in_addr = self.w3.to_checksum_address(token_in)
                
            if token_out == "USDT":
                token_out_addr = self.usdt_address
            else:
                token_out_addr = self.w3.to_checksum_address(token_out)
            
            # 设置交易期限
            deadline = int(time.time()) + (deadline_minutes * 60)
            
            # 判断交易类型：USDT->Token 还是 Token->USDT
            is_buy_operation = token_in_addr.lower() == self.usdt_address.lower()
            is_sell_operation = token_out_addr.lower() == self.usdt_address.lower()
            
            if is_buy_operation:
                # USDT -> Token 买入操作
                return await self._execute_buy_with_USDT_swap(
                    token_out_addr, amount_in, user_addr, user_secret,
                    slippage_percent, gas, gas_price, deadline
                )
            elif is_sell_operation:
                # Token -> USDT 卖出操作
                return await self._execute_sell_for_USDT_swap(
                    token_in_addr, amount_in, user_addr, user_secret,
                    slippage_percent, gas, gas_price, deadline
                )
            else:
                # Token -> Token 交换（暂不支持，需要多跳路径）
                raise ValueError("暂不支持Token到Token的直接交换，请分步操作")
                
        except Exception as e:
            self.logger.error(f"DEX token swap failed: {str(e)}")
            raise e
    async def _execute_buy_with_USDT_swap(
        self,
        token_address: str,
        usdt_ui_amount_to_buy: Decimal,
        user_addr: str,
        user_secret: str,
        slippage_percent: float,
        gas: int,
        gas_price: int,
        deadline: int,
    ) -> str:
        """执行USDT->WETH->Token买入交换"""
        # 获取预期输出数量并计算最小值

        usdt_decimal_amount_to_buy = usdt_ui_amount_to_buy * Decimal(self.usdt_decimals)
        expected_out = await self.memeswap_get_out_mount_universal(
            [self.usdt_address, token_address],
            int(usdt_decimal_amount_to_buy)
        )
        amount_out_min = int(expected_out * (1 - slippage_percent / 100))
        self.logger.info(f"DEX buy with USDT usdt_ui_amount_to_buy: {usdt_ui_amount_to_buy}, usdt_decimal_amount: {usdt_decimal_amount_to_buy}, expected_out: {expected_out}, amount_out_min: {amount_out_min}")

         # 获取nonce
        nonce = await self.w3.eth.get_transaction_count(user_addr)

         # 首先进行USDT代币授权
        approve_tx = await self._build_memeswap_approve_tx_universal(
            self.usdt_address, int(usdt_decimal_amount_to_buy), gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(user_secret, approve_tx, "eth")
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"DEX buy with USDT approve transaction sent: {approve_tx_hash.hex()}")
        
        # 等待授权确认
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"DEX buy with USDT approve transaction confirmed: {receipt}")
        
        # 递增nonce用于卖出交易
        nonce += 1

         # 构建交易
        tx = await self._build_memeswap_buy_with_USDT_tx(
            token_address, usdt_ui_amount_to_buy, amount_out_min, user_addr, 
            gas, gas_price, nonce, deadline
        )
        
        # 签名并发送交易
        signed_tx_hex = await self.sign(user_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        
        self.logger.info(f"DEX buy with USDT transaction sent: {tx_hash.hex()}")
        return tx_hash.hex()

    async def _execute_sell_for_USDT_swap(
        self,
        token_address: str,
        token_amount: Decimal,
        user_addr: str,
        user_secret: str,
        slippage_percent: float,
        gas: int,
        gas_price: int,
        deadline: int,
    ) -> str:
        """执行Token->WETH->USDT卖出交换"""
         # 获取预期输出数量并计算最小值
        expected_out = await self.memeswap_get_out_mount_universal(
            [token_address,self.usdt_address],
            int(self.w3.to_wei(token_amount, "ether"))
        )
        amount_out_min = int(expected_out * (1 - slippage_percent / 100))
        
        # 获取nonce
        nonce = await self.w3.eth.get_transaction_count(user_addr)

         # 首先进行代币授权
        approve_tx = await self._build_memeswap_approve_tx_universal(
            token_address, int(self.w3.to_wei(token_amount, "ether")), gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(user_secret, approve_tx, "eth")
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"DEX sell for USDT approve transaction sent: {approve_tx_hash.hex()}")
        
        # 等待授权确认
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"DEX sell for USDT approve transaction confirmed: {receipt}")
        
        # 递增nonce用于卖出交易
        nonce += 1

         # 构建卖出交易
        tx = await self._build_memeswap_sell_for_USDT_tx(
            token_address, token_amount, amount_out_min, user_addr,
            gas, gas_price, nonce, deadline
        )
        
        # 签名并发送交易
        signed_tx_hex = await self.sign(user_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        
        self.logger.info(f"DEX sell transaction sent: {tx_hash.hex()}")
        return tx_hash.hex()
        

    async def _execute_buy_swap(
        self,
        token_address: str,
        eth_amount: Decimal,
        user_addr: str,
        user_secret: str,
        slippage_percent: float,
        gas: int,
        gas_price: int,
        deadline: int,
    ) -> str:
        """执行ETH->Token买入交换"""
        # 获取预期输出数量并计算最小值
        expected_out_wei, expected_out = await self.memeswap_get_out_token_amount(
            token_address, eth_amount
        )
        amount_out_min = int(expected_out_wei * (1 - slippage_percent / 100))
        
        # 获取nonce
        nonce = await self.w3.eth.get_transaction_count(user_addr)
        
        # 构建交易
        tx = await self._build_memeswap_buy_tx(
            token_address, eth_amount, amount_out_min, user_addr,
            gas, gas_price, nonce, deadline
        )
        
        # 签名并发送交易
        signed_tx_hex = await self.sign(user_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        
        self.logger.info(f"DEX buy transaction sent: {tx_hash.hex()}")
        return self.w3.to_hex(tx_hash)

    async def _execute_sell_swap(
        self,
        token_address: str,
        token_amount: Decimal,
        user_addr: str,
        user_secret: str,
        slippage_percent: float,
        gas: int,
        gas_price: int,
        deadline: int,
    ) -> str:
        """执行Token->ETH卖出交换"""
        # 获取预期输出数量并计算最小值
        expected_out_wei, expected_out = await self.memeswap_get_out_eth_amount(
            token_address, token_amount
        )
        amount_out_min = int(expected_out_wei * (1 - slippage_percent / 100))
        
        # 获取nonce
        nonce = await self.w3.eth.get_transaction_count(user_addr)
        
        # 首先进行代币授权
        approve_tx = await self._build_memeswap_approve_tx(
            token_address, token_amount, gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(user_secret, approve_tx, "eth")
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"DEX approve transaction sent: {approve_tx_hash.hex()}")
        
        # 等待授权确认
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"DEX approve transaction confirmed: {receipt}")
        
        # 递增nonce用于卖出交易
        nonce += 1
        
        # 构建卖出交易
        tx = await self._build_memeswap_sell_tx(
            token_address, token_amount, amount_out_min, user_addr,
            gas, gas_price, nonce, deadline
        )
        
        # 签名并发送交易
        signed_tx_hex = await self.sign(user_secret, tx, "eth")
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        
        self.logger.info(f"DEX sell transaction sent: {tx_hash.hex()}")
        return self.w3.to_hex(tx_hash)

    async def memeswap_buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        eth_amount: Decimal,
        slippage_percent: float = 0.5,
        gas: int = 200_000,
        gas_price: int = settings.DEFAULT_GAS_PRICE,
        deadline_minutes: int = 20,
    ) -> str:
        """
        Buy tokens using MemeSwap (ETH -> Token)
        
        @deprecated: 建议使用 swap_token 函数替代
        """
        self.logger.warning("memeswap_buy is deprecated, please use swap_token instead")
        return await self.swap_token(
            token_in=self.weth_address,
            token_out=token_address,
            amount_in=eth_amount,
            user_pubkey=buyer_pubkey,
            user_secret=buyer_secret,
            slippage_percent=slippage_percent,
            gas=gas,
            gas_price=gas_price,
            deadline_minutes=deadline_minutes,
        )

    async def memeswap_sell(
        self,
        token_address: str,
        seller_pubkey: str,
        seller_secret: str,
        token_amount: Decimal,
        slippage_percent: float = 0.5,
        gas: int = 200_000,
        gas_price: int = settings.DEFAULT_GAS_PRICE,
        deadline_minutes: int = 20,
    ) -> str:
        """
        Sell tokens using MemeSwap (Token -> ETH)
        
        @deprecated: 建议使用 swap_token 函数替代
        """
        self.logger.warning("memeswap_sell is deprecated, please use swap_token instead")
        return await self.swap_token(
            token_in=token_address,
            token_out=self.weth_address,
            amount_in=token_amount,
            user_pubkey=seller_pubkey,
            user_secret=seller_secret,
            slippage_percent=slippage_percent,
            gas=gas,
            gas_price=gas_price,
            deadline_minutes=deadline_minutes,
        )

    # -----------------------------
    # DEX Amount Query Methods (Implementation of Abstract Interface)
    # -----------------------------
    async def get_dex_out_token_amount_with_usdt(self, token_address: str, usdt_amount: Decimal) -> Tuple:
        """获取DEX中用USDT可以换取的代币数量"""
        return await self.memeswap_get_out_token_amount_with_usdt(token_address, usdt_amount)

    async def get_dex_out_token_amount(self, token_address: str, native_amount: Decimal) -> Tuple:
        """获取DEX中用原生代币(ETH)可以换取的代币数量"""
        return await self.memeswap_get_out_token_amount(token_address, native_amount)

    async def get_dex_in_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中换取指定代币数量需要的原生代币(ETH)数量"""
        return await self.memeswap_get_in_eth_amount(token_address, token_amount)

    async def get_dex_out_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中用代币可以换取的原生代币(ETH)数量"""
        return await self.memeswap_get_out_eth_amount(token_address, token_amount)
    
    async def get_dex_out_usdt_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中用代币可以换取的USDT数量"""
        return await self.memeswap_get_out_usdt_amount(token_address, token_amount)

    async def get_dex_out_native_amount_after_fee(self, token_address: str, token_amount: Decimal) -> Tuple:
        """获取DEX中用代币可以换取的原生代币(ETH)数量(扣除手续费)"""
        return await self.memeswap_get_out_eth_amount_after_fee(token_address, token_amount)

    async def claim(
        self,
        pubkey: str,
        secret: str,
        token_address: str,
        amount: Decimal,
        gas: int,
        gas_price: int,
    ) -> str:
        """
        Claim rewards
        """
        if not self.reward_contract:
            raise ValueError("Reward contract not initialized")
        token_addr = self.w3.to_checksum_address(token_address)
        user_addr = self.w3.to_checksum_address(pubkey)
        reward_nonce = await self.get_reward_nonce(token_addr, user_addr)
        signature = await self.generate_reward_signature(pubkey, int(amount), reward_nonce, settings.REWARD_SIGNER_SECRET)
        tx = await self.reward_contract.functions.claim(token_addr, int(amount), reward_nonce, signature).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": gas_price,
                "nonce": await self.w3.eth.get_transaction_count(user_addr),
            }
        )

        signed_tx = await self.sign(secret, tx, "eth")

        # Send the transaction
        tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
        return self.w3.to_hex(tx_hash)

    async def graduate(self, token_address: str) -> bool:
        """
        Graduate token to main DEX
        """
        if not self.contract:
            raise ValueError("Contract not initialized")
        try:
            gas: int = settings.DEFAULT_LAUNCH_GAS_LIMIT
            gas_price: int = settings.DEFAULT_GAS_PRICE  # Higher gas price for ETH
            token_addr = self.w3.to_checksum_address(token_address)
            sender_addr = self.w3.to_checksum_address(settings.REWARD_OPERATOR_PUBKEY)

            # Get the current nonce for the sender's address
            nonce = await self.w3.eth.get_transaction_count(sender_addr)

            # Build the transaction
            tx = await self.contract.functions.graduate(token_addr).build_transaction(
                {
                    "chainId": self.chain_id,
                    "gas": gas,
                    "gasPrice": gas_price,
                    "nonce": nonce,
                }
            )

            # Sign the transaction
            signed_tx = await self.sign(settings.REWARD_OPERATOR_SECRET, tx, "eth")

            # Send the transaction
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
            self.logger.info(f"Token {token_address} graduation transaction sent: {self.w3.to_hex(tx_hash)}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to graduate token {token_address}: {str(e)}")
            return False

    async def setUserEligibilityCheck(self, enabled: bool, gas: int = 200000, gas_price: int = 3) -> str:
        """Set user eligibility check status to enable/disable user creation restrictions.

        This function builds and sends a transaction to set the user eligibility check
        status in the smart contract. When enabled=False, it removes the restriction
        that only allows users to create one meme token.

        :param enabled: Whether to enable user eligibility check (True) or disable it (False)
        :type enabled: bool
        :param gas: Gas limit for the transaction, defaults to 200000
        :type gas: int
        :param gas_price: Gas price in wei, defaults to 10_000_000 (higher for ETH)
        :type gas_price: int
        :return: Transaction hash
        :rtype: str
        :raises Exception: When transaction fails or network connection issues occur
        """
        if not self.contract:
            raise ValueError("Contract not initialized")
        
        try:
            sender_addr = self.w3.to_checksum_address("******************************************")  # TODO: Use proper operator address

            # Get the current nonce for the sender's address
            nonce = await self.w3.eth.get_transaction_count(sender_addr)

            # Build the transaction
            tx = await self.contract.functions.setUserEligibilityCheck(enabled).build_transaction(
                {
                    "chainId": self.chain_id,
                    "gas": gas,
                    "gasPrice": gas_price,
                    "nonce": nonce,
                }
            )

            # Sign the transaction
            signed_tx = await self.sign(settings.LAUNCH_SIGN_SECRET, tx, "eth")

            # Send the transaction
            tx_hash = await self.w3.eth.send_raw_transaction(bytes.fromhex(signed_tx))
            self.logger.info(f"User eligibility check set to {enabled}, transaction hash: {self.w3.to_hex(tx_hash)}")
            return self.w3.to_hex(tx_hash)
        except Exception as e:
            self.logger.error(f"Failed to set user eligibility check to {enabled}: {str(e)}")
            raise e

    # Helper methods
    async def get_reward_nonce(self, token_address, sender):
        """Get reward nonce for user"""
        if not self.reward_contract:
            return 0
        return await self.reward_contract.functions.nonces(token_address, sender).call()

    async def get_token_balance(self, pubkey: str, token_address: str) -> Decimal:
        """Get token balance"""
        balance_wei = await self.get_token_balance_in_wei(pubkey, token_address)
        balance_token = self.w3.from_wei(balance_wei, "ether")
        return balance_token

    async def get_native_balance(self, pubkey: str) -> Decimal:
        """Get ETH balance"""
        return await self.get_eth_balance(pubkey)

    async def get_eth_balance(self, pubkey: str) -> Decimal:
        """Get ETH balance in ether"""
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        balance_eth = self.w3.from_wei(balance_wei, "ether")
        return balance_eth

    async def get_cash_balance(self, pubkey: str) -> Decimal:
        """Get cash balance"""
        cash_balance_wei = await self.get_cash_balance_in_wei(pubkey)
        cash_balance = cash_balance_wei / self.usdt_decimals
        return cash_balance

    async def fee_to_address(self) -> Optional[str]:
        """获取合约的 feeToAddress，类级别懒加载缓存。"""
        try:
            if getattr(ETHConnector, "FEE_TO_ADDRESS", None):
                return ETHConnector.FEE_TO_ADDRESS

            if not getattr(self, "contract", None):
                return None

            addr = await self.contract.functions.feeToAddress().call()
            ETHConnector.FEE_TO_ADDRESS = self.w3.to_checksum_address(addr)
            return ETHConnector.FEE_TO_ADDRESS
        except Exception as e:
            self.logger.warning(f"Failed to get feeToAddress: {str(e)}")
            return None

    async def meme_initial_supply(self) -> Decimal:
        """Get initial supply of meme tokens"""
        if not self.contract:
            return Decimal("0")
        return self.contract.functions.erc20InitialSupply().call()

    # -----------------------------
    # MemeSwap utility methods
    # -----------------------------

    async def get_memeswap_price(self, token_address: str, amount_eth: Decimal = Decimal("1")) -> Decimal:
        """Get token price in ETH from MemeSwap"""
        try:
            _, token_amount = await self.memeswap_get_out_token_amount(token_address, amount_eth)
            if token_amount > 0:
                return amount_eth / token_amount
            return Decimal("0")
        except Exception as e:
            self.logger.warning(f"Failed to get MemeSwap price: {str(e)}")
            return Decimal("0")

    async def check_memeswap_liquidity(self, token_address: str) -> bool:
        """Check if token has liquidity on MemeSwap"""
        try:
            # Try to get a small amount to test liquidity
            test_amount = Decimal("0.01")  # 0.01 ETH
            _, out_amount = await self.memeswap_get_out_token_amount(token_address, test_amount)
            return out_amount > 0
        except Exception as e:
            self.logger.warning(f"Failed to check MemeSwap liquidity: {str(e)}")
            return False

    async def get_pool_liquidity_onchain(self, pair_address: str) -> float:
        """
        通过链上读取 MemeSwap (Uniswap V2) Pair 储备量并返回按 USD 计价的单边流动性（float）。

        规则：
        - 若池子包含 USDT：直接返回该边 USDT 的 UI 数量（单边美元价值）。
        - 若池子包含 WETH：按 1 WETH -> USDT 的报价，将 WETH 储备折算为 USD 返回。
        - 否则：尝试以 Router 路径将 token -> USDT 报价（优先直达 [token, USDT]，否则 [token, WETH, USDT]），
          用该 token 的储备量折算为 USD 返回。
        - 失败或无法报价时返回 0.0。
        """
        try:
            pair_addr = self.w3.to_checksum_address(pair_address)
            pair = self.w3.eth.contract(address=pair_addr, abi=MEMESWAP_PAIR_ABI)

            token0_addr = await pair.functions.token0().call()
            token1_addr = await pair.functions.token1().call()
            reserve0, reserve1, _ = await pair.functions.getReserves().call()

            t0 = self.w3.eth.contract(address=token0_addr, abi=self.erc20_abi)
            t1 = self.w3.eth.contract(address=token1_addr, abi=self.erc20_abi)

            try:
                t0_decimals = int(await t0.functions.decimals().call())
            except Exception:
                t0_decimals = 18
            try:
                t1_decimals = int(await t1.functions.decimals().call())
            except Exception:
                t1_decimals = 18

            usdt_addr_lower = self.usdt_address.lower()
            weth_addr_lower = self.weth_address.lower()

            # 1) 如果包含 USDT，直接返回 USDT 边（单边流动性）
            if token0_addr.lower() == usdt_addr_lower:
                return float(Decimal(reserve0) / Decimal(self.usdt_decimals))
            if token1_addr.lower() == usdt_addr_lower:
                return float(Decimal(reserve1) / Decimal(self.usdt_decimals))

            # 获取 1 WETH 的 USDT 报价
            async def get_eth_usdt_price() -> Decimal:
                try:
                    amounts = await self.memeswap_get_amounts_out(Decimal(1), [self.weth_address, self.usdt_address])
                    if len(amounts) >= 2 and int(amounts[1]) > 0:
                        return Decimal(amounts[1]) / Decimal(self.usdt_decimals)
                except Exception as err:
                    self.logger.warning(f"Failed to quote WETH->USDT: {err}")
                return Decimal(0)

            # 2) 如果包含 WETH，用 WETH 边估值
            if token0_addr.lower() == weth_addr_lower:
                eth_price = await get_eth_usdt_price()
                if eth_price > 0:
                    eth_amount = Decimal(reserve0) / Decimal(10 ** 18)
                    return float(eth_amount * eth_price)
            if token1_addr.lower() == weth_addr_lower:
                eth_price = await get_eth_usdt_price()
                if eth_price > 0:
                    eth_amount = Decimal(reserve1) / Decimal(10 ** 18)
                    return float(eth_amount * eth_price)

            # 3) 一般情况：尝试 token -> USDT 折算，优先 token0
            try:
                amount_in_unit0 = 10 ** int(t0_decimals)
                out0 = await self.memeswap_get_amounts_out_universal(amount_in_unit0, [token0_addr, self.usdt_address])
                if isinstance(out0, list) and len(out0) >= 2 and int(out0[-1]) > 0:
                    price0 = Decimal(out0[-1]) / Decimal(self.usdt_decimals)
                    token0_ui = Decimal(reserve0) / Decimal(10 ** t0_decimals)
                    return float(token0_ui * price0)
            except Exception:
                pass

            # 3b) token0 经由 WETH 折算到 USDT
            try:
                amount_in_unit0 = 10 ** int(t0_decimals)
                out0_eth = await self.memeswap_get_amounts_out_universal(amount_in_unit0, [token0_addr, self.weth_address])
                if isinstance(out0_eth, list) and len(out0_eth) >= 2 and int(out0_eth[-1]) > 0:
                    eth_price = await get_eth_usdt_price()
                    if eth_price > 0:
                        price0 = (Decimal(out0_eth[-1]) / Decimal(10 ** 18)) * eth_price
                        token0_ui = Decimal(reserve0) / Decimal(10 ** t0_decimals)
                        return float(token0_ui * price0)
            except Exception:
                pass

            # 4) 尝试 token1 直接到 USDT
            try:
                amount_in_unit1 = 10 ** int(t1_decimals)
                out1 = await self.memeswap_get_amounts_out_universal(amount_in_unit1, [token1_addr, self.usdt_address])
                if isinstance(out1, list) and len(out1) >= 2 and int(out1[-1]) > 0:
                    price1 = Decimal(out1[-1]) / Decimal(self.usdt_decimals)
                    token1_ui = Decimal(reserve1) / Decimal(10 ** t1_decimals)
                    return float(token1_ui * price1)
            except Exception:
                pass

            # 4b) token1 经由 WETH 折算到 USDT
            try:
                amount_in_unit1 = 10 ** int(t1_decimals)
                out1_eth = await self.memeswap_get_amounts_out_universal(amount_in_unit1, [token1_addr, self.weth_address])
                if isinstance(out1_eth, list) and len(out1_eth) >= 2 and int(out1_eth[-1]) > 0:
                    eth_price = await get_eth_usdt_price()
                    if eth_price > 0:
                        price1 = (Decimal(out1_eth[-1]) / Decimal(10 ** 18)) * eth_price
                        token1_ui = Decimal(reserve1) / Decimal(10 ** t1_decimals)
                        return float(token1_ui * price1)
            except Exception:
                pass

            return 0.0
        except Exception as e:
            self.logger.warning(f"get_pool_liquidity_onchain failed for {pair_address}: {str(e)}")
            return 0.0

    async def estimate_memeswap_gas(self, operation: str, token_address: str = None) -> int:
        """Estimate gas for MemeSwap operations"""
        try:
            if operation == "buy":
                return 200_000  # Typical gas for ETH->Token swap
            elif operation == "sell":
                return 250_000  # Higher gas for Token->ETH swap (includes approval)
            elif operation == "approve":
                return 50_000   # Gas for token approval
            else:
                return 200_000  # Default estimate
        except Exception as e:
            self.logger.warning(f"Failed to estimate MemeSwap gas: {str(e)}")
            return 200_000

    async def generic_estimate_evm_onchain_gas(self, abi_path: str, contract_address: str, function_name: str, estimate_gas_params: dict, function_params: list = None) -> int:
        """
        通用合约函数调用 gas 预估
        
        :param abi_path: ABI 路径
        :param contract_address: 合约地址
        :param function_name: 函数名称
        :param estimate_gas_params: gas 估算参数
        :param function_params: 函数参数列表
        :return: 估算的gas费用
        """
        try:
            # 处理普通转账的情况
            if function_name.lower() == 'native_token_transfer' and not contract_address:   
                # 设置默认值
                default_from_address = "******************************************"
                default_to_address = "******************************************"  # 零地址作为默认目标地址
                default_amount = 0  # 0 作为默认金额
                
                # 普通转账的 gas 预估       
                gas_estimate = await self.w3.eth.estimate_gas({
                    'from': default_from_address,
                    'to': default_to_address,
                    'value': default_amount
                })
                return gas_estimate
            
            # 处理合约调用的情况
            if function_params is None:
                function_params = []

            gas_estimate = await self.contract.functions[function_name](*function_params).estimate_gas({
                'from': estimate_gas_params['from'], 
                'value': estimate_gas_params['value']
            })
            return gas_estimate
        except Exception as e:
            self.logger.error(f"Error estimating gas: {e}") 
            return None


# Register ETHConnector with the BlockchainConnectorGetter
BlockchainConnectorGetter.register_connector("eth", ETHConnector) 
