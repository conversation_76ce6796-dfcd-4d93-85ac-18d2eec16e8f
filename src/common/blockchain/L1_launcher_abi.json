{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "BRIDGE_MIN_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "L2_MEME", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "MFN_BRIDGE", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "bridgeTokenToL2", "inputs": [{"name": "L1TokenAddress", "type": "address", "internalType": "address"}, {"name": "L2TokenAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "erc20InitialSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_mfnBridge", "type": "address", "internalType": "address"}, {"name": "_signer", "type": "address", "internalType": "address"}, {"name": "_relayer", "type": "address", "internalType": "address"}, {"name": "_l2Meme", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isLaunched", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "launchOnL1", "inputs": [{"name": "creator", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "logo", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "relayer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signer", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateRelayer", "inputs": [{"name": "_newR<PERSON>yer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateSigner", "inputs": [{"name": "_newS<PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "verifySignature", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "L1TokenCreated", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "creator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RelayerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newRelayer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SignerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TokenBridged", "inputs": [{"name": "l1Token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "l2Token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}]}