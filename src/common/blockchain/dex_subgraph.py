import time
from typing import Any, Dict, List, Optional

import httpx

from src.memecoin.settings import settings
from src.memecoin.logger import logger


def _get_subgraph_url() -> str:
    """
    Return MemeSwap (Uniswap V2-compatible) subgraph endpoint.
    Prefer MEMESWAP_SUBGRAPH_URL; fallback to UNISWAP_V2_SUBGRAPH_URL if not set.
    """
    return settings.MEMESWAP_SUBGRAPH_URL or settings.UNISWAP_V2_SUBGRAPH_URL or ""


async def _post_graphql(endpoint: str, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
    if not endpoint:
        raise ValueError("Subgraph endpoint is empty. Configure it in settings.")
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            resp = await client.post(endpoint, json={"query": query, "variables": variables})
            resp.raise_for_status()
            data = resp.json()
            if "errors" in data and data["errors"]:
                logger.warning("Subgraph returned errors: %s", data["errors"])
            return data.get("data", {}) or {}
    except Exception as e:
        logger.warning("GraphQL request failed: %s", str(e))
        return {}


async def get_pool_liquidity(pool_addr: str, dex: str = "memeswap") -> Dict[str, Any]:
    """
    仅通过 MemeSwap（Uniswap V2 兼容）子图获取池流动性信息。
    返回 reserve0/reserve1 以及 token0/token1 元信息。

    Returns a dict with keys:
      - type: "v2"
      - pair address (lowercase)
      - reserve0/reserve1
      - token0, token1 { id, symbol, decimals }
      - source, dex, timestamp
    """
    endpoint = _get_subgraph_url()
    pool_id = (pool_addr or "").lower()

    # V2 (MemeSwap/Uniswap V2 compatible)
    query_v2 = (
        """
        query PairReserves($id: ID!) {
          pair(id: $id) {
            id
            reserve0
            reserve1
            token0 { id symbol decimals }
            token1 { id symbol decimals }
          }
        }
        """
    )
    data_v2 = await _post_graphql(endpoint, query_v2, {"id": pool_id})
    pair = data_v2.get("pair") if data_v2 else None
    if pair:
        return {
            "type": "v2",
            "pair": pool_id,
            "reserve0": pair.get("reserve0"),
            "reserve1": pair.get("reserve1"),
            "token0": pair.get("token0"),
            "token1": pair.get("token1"),
            "source": "subgraph",
            "dex": dex,
            "timestamp": int(time.time()),
        }

    # No data
    return {
        "type": "unknown",
        "address": pool_id,
        "source": "subgraph",
        "dex": dex,
        "timestamp": int(time.time()),
    }


async def get_pool_volume(
    pool_addr: str,
    dex: str = "memeswap",
    window_hours: int = 24,
) -> Dict[str, Any]:
    """
    仅通过 MemeSwap（Uniswap V2 兼容）子图获取最近 window_hours 小时成交量（USD）。
    通过 pairHourDatas 的 hourlyVolumeUSD 求和。

    Returns a dict:
      - volume_usd: float
      - from, to timestamps
      - buckets: list of hourly buckets (ts, volume)
      - dex, source, timestamp
    """
    endpoint = _get_subgraph_url()
    pool_id = (pool_addr or "").lower()
    now_ts = int(time.time())
    since_ts = now_ts - window_hours * 3600
    # V2 / MemeSwap hourly
    query_v2 = (
        """
        query PairVolume24h($pair: String!, $since: Int!) {
          pairHourDatas(
            where: { pair: $pair, hourStartUnix_gte: $since }
            orderBy: hourStartUnix
            orderDirection: asc
          ) {
            hourStartUnix
            hourlyVolumeUSD
          }
        }
        """
    )
    data_v2 = await _post_graphql(endpoint, query_v2, {"pair": pool_id, "since": since_ts})
    hours_v2: List[Dict[str, Any]] = (data_v2 or {}).get("pairHourDatas", [])
    total_v2 = 0.0
    buckets_v2: List[Dict[str, Any]] = []
    for h in hours_v2:
        try:
            vol = float(h.get("hourlyVolumeUSD") or 0)
        except Exception:
            vol = 0.0
        ts = int(h.get("hourStartUnix") or 0)
        total_v2 += vol
        buckets_v2.append({"ts": ts, "volume_usd": vol})

    return {
        "pair": pool_id,
        "volume_usd": total_v2,
        "from": since_ts,
        "to": now_ts,
        "buckets": buckets_v2,
        "dex": dex,
        "source": "subgraph",
        "timestamp": now_ts,
    }


