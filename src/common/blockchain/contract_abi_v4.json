{"abi": [{"type": "function", "name": "buy", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "getInBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getInBnbAmountNotYetLaunched", "inputs": [{"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "getOutBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutBnbAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "ethAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "graduate", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "launch", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "logo", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "progress", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sell", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeTo", "inputs": [{"name": "newFeeTo", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setGraduateFee", "inputs": [{"name": "newGraduateFee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "newOperator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardAdmin", "inputs": [{"name": "newRewardAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTokenLockup", "inputs": [{"name": "newTokenLockup", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTradingFeeRate", "inputs": [{"name": "newTradingFeeRate", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenInfos", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IMeme.TokenInfo", "components": [{"name": "virtualX", "type": "uint256", "internalType": "uint256"}, {"name": "virtualY", "type": "uint256", "internalType": "uint256"}, {"name": "K", "type": "uint256", "internalType": "uint256"}, {"name": "state", "type": "uint8", "internalType": "enum IMeme.TokenState"}]}], "stateMutability": "view"}, {"type": "function", "name": "userIneligible", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Buy", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Complete", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Graduate", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Launch", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "launcher", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "repoUrl", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "<PERSON>ll", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "bnbAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetFeeTo", "inputs": [{"name": "feeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetGraduateFee", "inputs": [{"name": "graduateFee", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetOperator", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetRewardAdmin", "inputs": [{"name": "rewardAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTokenLockup", "inputs": [{"name": "tokenLockup", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTradingFeeRate", "inputs": [{"name": "tradingFeeRate", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SignerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"buy(address,uint256)": "cce7ec13", "getInBnbAmount(address,uint256)": "5a328fda", "getInBnbAmountNotYetLaunched(uint256)": "772e3bbb", "getOutBnbAmount(address,uint256)": "d5e42595", "getOutBnbAmountAfterFee(address,uint256)": "fd3ad650", "getOutTokenAmount(address,uint256)": "58d7baeb", "getOutTokenAmountAfterFee(address,uint256)": "5212f9d9", "graduate(address)": "ff6d8d05", "launch(string,string,string,string,string,bytes)": "3ad3c462", "progress(address)": "da8589a2", "sell(address,uint256,uint256)": "6a272462", "setFeeTo(address)": "f46901ed", "setGraduateFee(uint256)": "bc8b30da", "setOperator(address)": "b3ab15fb", "setRewardAdmin(address)": "a1b7e2e2", "setTokenLockup(address)": "69567f84", "setTradingFeeRate(uint256)": "1091f67c", "setUserEligibilityCheck(bool)": "bd6010d6", "tokenInfos(address)": "ba46ae72", "userEligible(address)": "268d3a2f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"buyer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmountOut\",\"type\":\"uint256\"}],\"name\":\"Buy\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"Complete\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"Graduate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"launcher\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"repoUrl\",\"type\":\"string\"}],\"name\":\"Launch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"seller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmountOut\",\"type\":\"uint256\"}],\"name\":\"Sell\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeTo\",\"type\":\"address\"}],\"name\":\"SetFeeTo\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"graduateFee\",\"type\":\"uint256\"}],\"name\":\"SetGraduateFee\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"SetOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardAdmin\",\"type\":\"address\"}],\"name\":\"SetRewardAdmin\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenLockup\",\"type\":\"address\"}],\"name\":\"SetTokenLockup\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tradingFeeRate\",\"type\":\"uint256\"}],\"name\":\"SetTradingFeeRate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"SetUserEligibilityCheck\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldSigner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newSigner\",\"type\":\"address\"}],\"name\":\"SignerUpdated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"}],\"name\":\"buy\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"getInBnbAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"getInBnbAmountNotYetLaunched\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"getOutBnbAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"getOutBnbAmountAfterFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"}],\"name\":\"getOutTokenAmount\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"ethAmount\",\"type\":\"uint256\"}],\"name\":\"getOutTokenAmountAfterFee\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"graduate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"repoUrl\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"logo\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"launch\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"progress\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOutMin\",\"type\":\"uint256\"}],\"name\":\"sell\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"setFeeTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newGraduateFee\",\"type\":\"uint256\"}],\"name\":\"setGraduateFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newRewardAdmin\",\"type\":\"address\"}],\"name\":\"setRewardAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newTokenLockup\",\"type\":\"address\"}],\"name\":\"setTokenLockup\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newTradingFeeRate\",\"type\":\"uint256\"}],\"name\":\"setTradingFeeRate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"setUserEligibilityCheck\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"tokenInfos\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"virtualX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"K\",\"type\":\"uint256\"},{\"internalType\":\"enum IMeme.TokenState\",\"name\":\"state\",\"type\":\"uint8\"}],\"internalType\":\"struct IMeme.TokenInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"userEligible\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/IMeme.sol\":\"IMeme\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"src/IMeme.sol\":{\"keccak256\":\"0xe703b9705175bf670f541f7fdfdd09c6f9c5375471543cba3c600df43232d4ff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c0769aadf5a088f542018d169526123f338673d11bd6e4f5663b1c475e2efa52\",\"dweb:/ipfs/QmTrXRMRThcDF6nkCgwX7PbEiif2gfg6tpi693CWN5UzcF\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.22+commit.4fc1097e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "buyer", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "bnbAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "Buy", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}], "type": "event", "name": "Complete", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "Graduate", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "launcher", "type": "address", "indexed": true}, {"internalType": "string", "name": "name", "type": "string", "indexed": false}, {"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "string", "name": "repoUrl", "type": "string", "indexed": false}], "type": "event", "name": "Launch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "seller", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "bnbAmountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON>ll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeTo", "type": "address", "indexed": true}], "type": "event", "name": "SetFeeTo", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "graduateFee", "type": "uint256", "indexed": false}], "type": "event", "name": "SetGraduateFee", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address", "indexed": true}], "type": "event", "name": "SetOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardAdmin", "type": "address", "indexed": true}], "type": "event", "name": "SetRewardAdmin", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenLockup", "type": "address", "indexed": true}], "type": "event", "name": "SetTokenLockup", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "tradingFeeRate", "type": "uint256", "indexed": false}], "type": "event", "name": "SetTradingFeeRate", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool", "indexed": false}], "type": "event", "name": "SetUserEligibilityCheck", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}], "type": "event", "name": "SignerUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "buy"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getInBnbAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "getInBnbAmountNotYetLaunched", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutBnbAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutBnbAmountAfterFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutTokenAmount", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "ethAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutTokenAmountAfterFee", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "graduate"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "repoUrl", "type": "string"}, {"internalType": "string", "name": "logo", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "launch"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "progress", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "sell"}, {"inputs": [{"internalType": "address", "name": "newFeeTo", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeTo"}, {"inputs": [{"internalType": "uint256", "name": "newGraduateFee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setGraduateFee"}, {"inputs": [{"internalType": "address", "name": "newOperator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "address", "name": "newRewardAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardAdmin"}, {"inputs": [{"internalType": "address", "name": "newTokenLockup", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setTokenLockup"}, {"inputs": [{"internalType": "uint256", "name": "newTradingFeeRate", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setTradingFeeRate"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setUserEligibilityCheck"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "tokenInfos", "outputs": [{"internalType": "struct IMeme.TokenInfo", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "virtualX", "type": "uint256"}, {"internalType": "uint256", "name": "virtualY", "type": "uint256"}, {"internalType": "uint256", "name": "K", "type": "uint256"}, {"internalType": "enum IMeme.TokenState", "name": "state", "type": "uint8"}]}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "userEligible", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/IMeme.sol": "IMeme"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"src/IMeme.sol": {"keccak256": "0xe703b9705175bf670f541f7fdfdd09c6f9c5375471543cba3c600df43232d4ff", "urls": ["bzz-raw://c0769aadf5a088f542018d169526123f338673d11bd6e4f5663b1c475e2efa52", "dweb:/ipfs/QmTrXRMRThcDF6nkCgwX7PbEiif2gfg6tpi693CWN5UzcF"], "license": "MIT"}}, "version": 1}, "id": 21}