import hashlib
from decimal import Decima<PERSON>
from logging import Logger
from typing import <PERSON>ple, Optional

from anchorpy_core.idl import Idl

from solana.rpc.async_api import AsyncClient
from solana.transaction import Transaction
from anchorpy import Program, Provider, Context
from solders.pubkey import Pubkey
from solders.signature import Signature
from solders.system_program import transfer, TransferParams
from spl.token.constants import TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID
from spl.token.instructions import (
    get_associated_token_address,
    transfer_checked,
    TransferCheckedParams,
    create_associated_token_account,
)

from src.common.blockchain.blockchain_connector import BlockchainConnector, BlockchainConnectorGetter

TOKEN_METADATA_PROGRAM_ID = Pubkey.from_string(
    "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
)


class SolanaConnector(BlockchainConnector):
    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        meme_program_id: str,  # contract address
        meme_abi_path: str,  # meme solana json
        kms_address: str = None,
    ):
        """
          MAINNET
        - rpc_url: 'https://api.mainnet-beta.solana.com'

          DEVNET
        - rpc_url: 'https://api.devnet.solana.com'
        """
        super().__init__(logger, rpc_url, kms_address)
        self.client = AsyncClient(endpoint=rpc_url)
        self.meme_program_id = Pubkey.from_string(meme_program_id)
        self.meme_idl = self._load_idl(meme_abi_path)

    async def init_connection(self):
        """
        Call this method to check the connection status
        """
        connected = await self.client.is_connected()
        if not connected:
            raise ConnectionError(
                f"Unable to connect to the Solana network, please check the RPC URL ({self.rpc_url}) and network status."
            )

    def _load_idl(self, idl_path: str) -> Idl:
        with open(idl_path, "r", encoding="utf-8") as f:
            return Idl.from_json(f.read())

    # -----------------------------
    # View functions
    # -----------------------------

    async def get_token_balance(self, pubkey: str, token_address: str) -> Decimal:
        """
        Get token balance for a specific wallet and token
        """
        owner_pubkey = Pubkey.from_string(pubkey)
        token_pubkey = Pubkey.from_string(token_address)

        # Get associated token account
        token_account = get_associated_token_address(owner_pubkey, token_pubkey)

        # Get account info
        response = await self.client.get_token_account_balance(token_account)
        if response.value:
            balance = Decimal(response.value.amount) / Decimal(
                10**response.value.decimals
            )
            return balance
        return Decimal(0)

    async def get_token_balance_in_wei(self, pubkey: str, token_address: str) -> Decimal:
        return await self.get_token_balance_in_lamports(pubkey, token_address)

    async def get_native_balance(self, pubkey: str) -> Decimal:
        """
        Get SOL balance for a wallet
        """
        return await self.get_sol_balance(pubkey)

    async def get_native_balance_in_wei(self, pubkey: str) -> Decimal:
        return await self.get_sol_balance_in_lamports(pubkey)

    async def get_token_balance_in_lamports(
        self, pubkey: str, token_address: str
    ) -> Decimal:
        """
        Get token balance in lamports (smallest unit)
        """
        owner_pubkey = Pubkey.from_string(pubkey)
        token_pubkey = Pubkey.from_string(token_address)

        # Get associated token account
        token_account = get_associated_token_address(owner_pubkey, token_pubkey)

        # Get account info
        response = await self.client.get_token_account_balance(token_account)
        if response.value:
            return Decimal(response.value.amount)
        return Decimal(0)

    async def get_sol_balance(self, pubkey: str) -> Decimal:
        """
        Get SOL balance for a wallet
        """
        address = Pubkey.from_string(pubkey)
        response = await self.client.get_balance(address)
        if response.value:
            balance_sol = Decimal(response.value) / Decimal(10**9)  # 9 decimals for SOL
            return balance_sol
        return Decimal(0)

    async def get_sol_balance_in_lamports(self, pubkey: str) -> Decimal:
        """
        Get SOL balance in lamports
        """
        address = Pubkey.from_string(pubkey)
        response = await self.client.get_balance(address)
        if response.value:
            return Decimal(response.value)
        return Decimal(0)

    async def meme_initial_supply(self) -> Decimal:
        return Decimal(1_000_000_000)

    async def get_out_token_amount(self, token_address: str, bnb_amount: Decimal) -> Tuple:
        """
        Calculate how many tokens can be bought with a given amount of SOL.
        
        :param token_address: Token address
        :param bnb_amount: Amount of SOL to spend (in SOL)
        :return: Tuple of (token amount, token amount after fee)
        """
        virtual_sol_reserve, virtual_meme_reserve, real_sol_reserve, real_meme_reserve = await self.read_bonding_curve_account(token_address)

        sol_amount_lamports = int(bnb_amount * 10**9)

        fee_rate = 0.01
        
        # Using the bonding curve formula: dy = y * (dx / x)
        # where y = virtual_meme_reserve, x = virtual_sol_reserve, dx = sol_amount_lamports
        token_amount = int((sol_amount_lamports * virtual_meme_reserve) / virtual_sol_reserve)
        
        # Calculate the amount after fee (fee is taken from the tokens)
        token_amount_after_fee = int(token_amount * (1 - fee_rate))
        token_amount_after_fee_decimal = Decimal(token_amount_after_fee) / Decimal(10**6)
        
        return token_amount_after_fee, token_amount_after_fee_decimal

    async def get_in_native_amount(self, token_address: str, amount_out: Decimal) -> Tuple:
        """
        Calculate how much SOL is needed to buy a given amount of tokens.
        
        :param token_address: Token address
        :param amount_out: Amount of tokens to buy
        :return: Tuple of (SOL amount, SOL amount with fee)
        """
        virtual_sol_reserve, virtual_meme_reserve, real_sol_reserve, real_meme_reserve = await self.read_bonding_curve_account(token_address)

        token_amount_lamports = int(amount_out * 10**6)

        fee_rate = 0.01
        
        # Account for fee in the token amount calculation
        token_amount_before_fee = int(token_amount_lamports / (1 - fee_rate))
        
        # Calculate SOL amount needed using the bonding curve formula
        # dx = x * (dy / y)
        # where x = virtual_sol_reserve, y = virtual_meme_reserve, dy = token_amount_before_fee
        sol_amount = int((token_amount_before_fee * virtual_sol_reserve) / virtual_meme_reserve)
        sol_amount_decimal = Decimal(sol_amount) / Decimal(10**9)

        return sol_amount, sol_amount_decimal

    async def get_out_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        Calculate how much SOL can be received by selling a given amount of tokens.
        
        :param token_address: Token address
        :param token_amount: Amount of tokens to sell
        :return: Tuple of (SOL amount, SOL amount before fee)
        """
        virtual_sol_reserve, virtual_meme_reserve, real_sol_reserve, real_meme_reserve = await self.read_bonding_curve_account(token_address)
        
        # Convert token amount to smallest unit (assuming 6 decimals)
        token_amount_lamports = int(token_amount * 10**6)
        
        # Calculate SOL amount using the bonding curve formula
        # dx = x * (dy / y)
        # where x = virtual_sol_reserve, y = virtual_meme_reserve, dy = token_amount_lamports
        sol_amount = int((token_amount_lamports * virtual_sol_reserve) / virtual_meme_reserve)
        sol_amount_decimal = Decimal(sol_amount) / Decimal(10**9)
        
        return sol_amount, sol_amount_decimal

    async def get_out_native_amount_after_fee(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        Calculate how much SOL can be received after fees by selling a given amount of tokens.
        
        :param token_address: Token address
        :param token_amount: Amount of tokens to sell
        :return: Tuple of (SOL amount after fee, SOL amount before fee)
        """
        sol_amount, sol_amount_decimal = await self.get_out_native_amount(token_address, token_amount)
        fee_rate = 0.01
        sol_amount_after_fee = int(sol_amount * (1 - fee_rate))
        sol_amount_after_fee_decimal = Decimal(sol_amount_after_fee) / Decimal(10 ** 9)
        return sol_amount_after_fee, sol_amount_after_fee_decimal

    async def progress(self, token_address: str) -> int:
        _, _, real_sol_reserve, real_meme_reserve = await self.read_bonding_curve_account(token_address)
        
        total_supply = 1_000_000_000_000_000
        total_supply_for_bonding = total_supply * 0.7
        
        if real_meme_reserve >= total_supply_for_bonding:
            return 100

        progress = ((total_supply - real_meme_reserve) / total_supply_for_bonding) * 100
        return max(0, min(100, int(progress)))

    async def get_liquidity(self, token_address: str) -> float:
        _, _, real_sol_reserve, real_meme_reserve = await self.read_bonding_curve_account(token_address)
        return real_sol_reserve / 1e9

    async def graduate(self, token_address: str) -> bool:
        return False

    async def read_bonding_curve_account(self, token_address: str) -> Tuple[int, int]:
        program_id = Pubkey.from_string(self.meme_idl.metadata.get("address"))
        # Use Provider mainly to get Anchor's program object, but we'll handle signing manually
        provider = Provider(self.client, None)
        program = Program(self.meme_idl, program_id, provider)

        mint = Pubkey.from_string(token_address)
        # 指定 bonding curve 账户地址
        bonding_curve_pubkey = self.find_bonding_curve_address(mint, program_id)

        # 读取账户数据，假设在 IDL 中账户名称定义为 "bondingCurve"
        bonding_curve_account = await program.account["BondingCurve"].fetch(bonding_curve_pubkey)
        
        # 从bonding_curve_account中获取相关值
        virtual_sol_reserve = bonding_curve_account.virtual_sol_reserve
        virtual_meme_reserve = bonding_curve_account.virtual_meme_reserve
        real_sol_reserve = bonding_curve_account.real_sol_reserve
        real_meme_reserve = bonding_curve_account.real_meme_reserve
        return virtual_sol_reserve, virtual_meme_reserve, real_sol_reserve, real_meme_reserve
    # -----------------------------
    # Transaction methods
    # -----------------------------

    async def _build_send_transaction(self, instructions, fee_payer, secret):
        """
        Helper function to build, sign and send a transaction

        :param instructions: List of instructions to add to the transaction
        :param fee_payer: Public key of the account paying for the transaction
        :param secret: Key used for signing
        :return: Transaction signature
        """
        # Build transaction
        tx = Transaction()

        # Add all instructions
        for ix in instructions:
            tx.add(ix)

        # Set fee payer account
        tx.fee_payer = fee_payer

        # Get recent blockhash
        resp = await self.client.get_latest_blockhash()
        tx.recent_blockhash = resp.value.blockhash

        # Serialize transaction message for KMS signing
        message = tx.serialize_message()
        raw = await self.sign(secret, message, "sol")
        signature = bytes.fromhex(raw)
        print("kms signed....")
        tx.add_signature(fee_payer, Signature.from_bytes(signature))

        # Verify signature is valid
        if not tx.verify_signatures():
            raise Exception("KMS signature is invalid")

        # Serialize and send transaction to network
        raw_tx = tx.serialize()
        tx_sig = await self.client.send_raw_transaction(raw_tx)

        return str(tx_sig.value)

    async def transfer_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        decimals: int = 6,
        create_ata: bool = False,
        **kwargs,
    ) -> str:
        """
        Transfer SPL tokens from one account to another

        :param token_address: Token mint address
        :param from_pubkey: Sender's public key
        :param from_secret: Sender's secret for signing
        :param to_address: Recipient's address
        :param amount: Amount to transfer
        :param decimals: Token decimals (default 9)
        :param create_ata: whether create ATA
        :return: Transaction hash
        """
        token_pubkey = Pubkey.from_string(token_address)
        from_pubkey = Pubkey.from_string(from_pubkey)
        to_pubkey = Pubkey.from_string(to_address)

        # Get associated token accounts
        from_token_account = get_associated_token_address(from_pubkey, token_pubkey)
        to_token_account = get_associated_token_address(to_pubkey, token_pubkey)

        # Convert amount to lamports
        amount_lamports = int(amount * (10**decimals))

        # Prepare instruction list
        instructions = []

        if create_ata:
            to_token_account = self.find_associated_token_address(
                to_pubkey, token_pubkey
            )
            to_token_info = await self.client.get_account_info(to_token_account)
            # If recipient's ATA account doesn't exist, add instruction to create it
            if to_token_info.value is None:
                create_ata_ix = create_associated_token_account(
                    payer=from_pubkey, owner=to_pubkey, mint=token_pubkey
                )
                instructions.append(create_ata_ix)

        instructions.append(
            transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=from_token_account,
                    mint=token_pubkey,
                    dest=to_token_account,
                    owner=from_pubkey,
                    amount=amount_lamports,
                    decimals=decimals,
                )
            )
        )

        tx_hash = await self._build_send_transaction(
            instructions, from_pubkey, from_secret
        )
        return tx_hash

    async def transfer_native(
        self,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        **kwargs,
    ) -> str:
        """
        Transfer SOL from one account to another

        :param from_pubkey: Sender's public key
        :param from_secret: Sender's secret for signing
        :param to_address: Recipient's address
        :param amount: Amount to transfer in SOL
        :return: Transaction hash
        """

        # Convert SOL to lamports
        amount_lamports = int(amount * 10**9)

        # Create transaction
        instructions = []
        instructions.append(
            transfer(
                TransferParams(
                    from_pubkey=Pubkey.from_string(from_pubkey),
                    to_pubkey=Pubkey.from_string(to_address),
                    lamports=amount_lamports,
                )
            )
        )

        tx_hash = await self._build_send_transaction(
            instructions, from_pubkey, from_secret
        )
        return tx_hash

    async def launch(
        self,
        name: str,
        symbol: str,
        repo_url: str,
        logo: str,
        desc: str,
        pubkey: str,
        secret: str,
        amount_to_buy: Decimal = 0,
        gas: int = 5_000_000,
        gas_price: int = 3,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        """
        Launch a new token

        :param name: Token name
        :param symbol: Token symbol
        :param repo_url: Repository URL
        :param logo: Logo URL
        :param desc: Description
        :param pubkey: Creator's public key
        :param secret: Creator's secret for signing
        :param amount_to_buy: Amount to buy initially
        :param gas: Gas limit
        :param gas_price: Gas price
        :param gas_payer_secret: Gas payer's secret for signing
        :return: Transaction hash
        """
        # Security check: when using gas payer, amount_to_buy must be 0
        if gas_payer_secret is not None and amount_to_buy > 0:
            raise ValueError("When using gas_payer_secret, amount_to_buy must be 0")
            
        # Construct parameters
        create_params = {
            "name": name,
            "ticker": symbol,
            "uri": logo,
            "repo_uri": repo_url,
            "signature": "**********",
        }

        kms_public_key = Pubkey.from_string(pubkey)
        program_id = Pubkey.from_string(self.meme_idl.metadata.get("address"))
        # Use Provider mainly to get Anchor's program object, but we'll handle signing manually
        provider = Provider(self.client, None)
        program = Program(self.meme_idl, program_id, provider)
        # Find account addresses based on parameters and public key
        global_account = self.find_global_address(program_id)
        mint = self.find_mint_address(
            kms_public_key, create_params["repo_uri"], program_id
        )
        bonding_curve = self.find_bonding_curve_address(mint, program_id)
        bonding_curve_vault = self.find_bonding_curve_vault_address(
            bonding_curve, program_id
        )
        metadata_account = self.find_metadata_address(mint)
        bonding_curve_meme_token_account = self.find_associated_token_address(
            bonding_curve, mint
        )
        global_info = await program.account["Global"].fetch(global_account)
        fee_recipient = global_info.fee_recipient
        # fee_recipient = Pubkey.from_string("Di2iA87m4RrZh1WiqyBjE7RTATt2WzyZfzYFwTx9hG5a")

        ix = program.instruction["create"](
            create_params,
            ctx=Context(
                accounts={
                    "creator": kms_public_key,
                    "global": global_account,
                    "meme": mint,
                    "bonding_curve": bonding_curve,
                    "bonding_curve_vault": bonding_curve_vault,
                    "metadata_account": metadata_account,
                    "bonding_curve_meme_token_account": bonding_curve_meme_token_account,
                    "fee_recipient": fee_recipient,
                    "token_program": TOKEN_PROGRAM_ID,
                    "token_metadata_program": TOKEN_METADATA_PROGRAM_ID,
                    "associated_token_program": ASSOCIATED_TOKEN_PROGRAM_ID,
                    "system_program": Pubkey.from_string(
                        "11111111111111111111111111111111"
                    ),
                    "rent": Pubkey.from_string(
                        "SysvarRent111111111111111111111111111111111"
                    ),
                }
            ),
        )

        # Use helper function to build and send transaction
        signing_secret = gas_payer_secret if gas_payer_secret else secret
        tx_sig = await self._build_send_transaction([ix], kms_public_key, signing_secret)

        # Return transaction signature only to match parent class return type
        return tx_sig

    async def buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        amount_to_buy: Decimal,
        amount_out_min: int = 0,
        gas: int = 21000,
        gas_price: int = 1,
    ) -> str:
        """
        Buy tokens
        
        :param token_address: Token address
        :param buyer_pubkey: Buyer's public key
        :param buyer_secret: Buyer's secret (not private key)
        :param amount_to_buy: Amount to buy
        :param amount_out_min: Minimum amount expected to receive
        :param gas: Gas limit
        :param gas_price: Gas price
        :return: Transaction hash
        """
        # Construct parameters
        buy_params = {"amount_in": int(amount_to_buy) * 10 ** 9, "minimum_amount_out": amount_out_min}

        kms_public_key = Pubkey.from_string(buyer_pubkey)
        program_id = Pubkey.from_string(self.meme_idl.metadata.get("address"))
        # Use Provider mainly to get Anchor's program object, but we'll handle signing manually
        provider = Provider(self.client, None)
        program = Program(self.meme_idl, program_id, provider)
        # Find account addresses based on parameters and public key
        global_account = self.find_global_address(program_id)
        # mint = self.find_mint_address(kms_public_key, buy_params["repo_uri"], program_id)
        meme = Pubkey.from_string(token_address)
        bonding_curve = self.find_bonding_curve_address(meme, program_id)
        bonding_curve_vault = self.find_bonding_curve_vault_address(
            bonding_curve, program_id
        )
        metadata_account = self.find_metadata_address(meme)
        bonding_curve_meme_token_account = self.find_associated_token_address(
            bonding_curve, meme
        )
        buyer_token_account = self.find_associated_token_address(kms_public_key, meme)
        global_info = await program.account["Global"].fetch(global_account)
        fee_recipient = global_info.fee_recipient
        # fee_recipient = Pubkey.from_string("Di2iA87m4RrZh1WiqyBjE7RTATt2WzyZfzYFwTx9hG5a")

        # Check if buyer's ATA account exists, create it if not
        buyer_token_info = await self.client.get_account_info(buyer_token_account)

        # Prepare instruction list
        instructions = []

        # If buyer's ATA account doesn't exist, add instruction to create it
        if buyer_token_info.value is None:
            create_ata_ix = create_associated_token_account(
                payer=kms_public_key, owner=kms_public_key, mint=meme
            )
            instructions.append(create_ata_ix)

        # Add buy token instruction
        buy_ix = program.instruction["buy"](
            buy_params,
            ctx=Context(
                accounts={
                    "buyer": kms_public_key,
                    "buyer_token_account": buyer_token_account,
                    "global": global_account,
                    "meme": meme,
                    "bonding_curve": bonding_curve,
                    "bonding_curve_vault": bonding_curve_vault,
                    "bonding_curve_token_account": bonding_curve_meme_token_account,
                    "fee_recipient": fee_recipient,
                    "token_program": TOKEN_PROGRAM_ID,
                    "associated_token_program": ASSOCIATED_TOKEN_PROGRAM_ID,
                    "system_program": Pubkey.from_string(
                        "11111111111111111111111111111111"
                    ),
                }
            ),
        )
        instructions.append(buy_ix)

        # Use helper function to build and send transaction
        return await self._build_send_transaction(
            instructions, kms_public_key, buyer_secret
        )

    async def sell(
        self,
        token_address: str,
        amount_to_sell: Decimal,
        pubkey: str,
        secret: str,
        amount_out_min: int = 0,
        gas: int = 24000,
        gas_price: int = 1,
    ) -> str:
        """
        Sell tokens
        
        :param token_address: Token address
        :param amount_to_sell: Amount of tokens to sell
        :param pubkey: Seller's public key
        :param secret: Seller's secret (not private key)
        :param amount_out_min: Minimum SOL expected to receive
        :param gas: Gas limit
        :param gas_price: Gas price
        :return: Transaction hash
        """
        # Construct parameters
        sell_params = {
            "amount_in": int(amount_to_sell*10**6),
            "minimum_amount_out": amount_out_min,
        }

        seller_public_key = Pubkey.from_string(pubkey)
        program_id = Pubkey.from_string(self.meme_idl.metadata.get("address"))
        # Use Provider mainly to get Anchor's program object, but we'll handle signing manually
        provider = Provider(self.client, None)
        program = Program(self.meme_idl, program_id, provider)

        # Find account addresses based on parameters and public key
        global_account = self.find_global_address(program_id)
        meme = Pubkey.from_string(token_address)
        bonding_curve = self.find_bonding_curve_address(meme, program_id)
        bonding_curve_vault = self.find_bonding_curve_vault_address(
            bonding_curve, program_id
        )
        bonding_curve_token_account = self.find_associated_token_address(
            bonding_curve, meme
        )
        seller_token_account = self.find_associated_token_address(
            seller_public_key, meme
        )
        global_info = await program.account["Global"].fetch(global_account)
        fee_recipient = global_info.fee_recipient

        # Add sell token instruction
        sell_ix = program.instruction["sell"](
            sell_params,
            ctx=Context(
                accounts={
                    "seller": seller_public_key,
                    "global": global_account,
                    "meme": meme,
                    "bonding_curve": bonding_curve,
                    "bonding_curve_vault": bonding_curve_vault,
                    "bonding_curve_token_account": bonding_curve_token_account,
                    "seller_token_account": seller_token_account,
                    "fee_recipient": fee_recipient,
                    "token_program": TOKEN_PROGRAM_ID,
                    "system_program": Pubkey.from_string(
                        "11111111111111111111111111111111"
                    ),
                }
            ),
        )

        # Use helper function to build and send transaction
        return await self._build_send_transaction([sell_ix], seller_public_key, secret)

    # Find Global account address
    def find_global_address(self, program_id):
        return Pubkey.find_program_address([b"global"], program_id)[0]

    # Find Mint address (same as meme function in TypeScript example)
    def find_mint_address(self, creator_pubkey, repo_uri, program_id):
        # Create message data
        data = bytearray(bytes(creator_pubkey))
        data.extend(repo_uri.encode())

        # Calculate SHA256 hash
        message = hashlib.sha256(data).digest()

        # Find program address
        return Pubkey.find_program_address([b"mint", message], program_id)[0]

    # Find bonding curve address
    def find_bonding_curve_address(self, mint, program_id):
        return Pubkey.find_program_address([b"bonding_curve", bytes(mint)], program_id)[
            0
        ]

    # Find bonding curve vault address
    def find_bonding_curve_vault_address(self, bonding_curve, program_id):
        return Pubkey.find_program_address(
            [b"bonding_curve", bytes(bonding_curve)], program_id
        )[0]

    # Get Metadata account address
    def find_metadata_address(self, mint):
        # Metadata program ID (Metaplex)
        metadata_program_id = Pubkey.from_string(
            "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
        )
        return Pubkey.find_program_address(
            [b"metadata", bytes(metadata_program_id), bytes(mint)], metadata_program_id
        )[0]

    # Get associated token account address
    def find_associated_token_address(self, wallet, mint):
        # Associated token program ID
        associated_token_program_id = Pubkey.from_string(
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
        )
        return Pubkey.find_program_address(
            [bytes(wallet), bytes(TOKEN_PROGRAM_ID), bytes(mint)],
            associated_token_program_id,
        )[0]

# Register SolanaConnector with the BlockchainConnectorGetter
BlockchainConnectorGetter.register_connector("sol", SolanaConnector)
