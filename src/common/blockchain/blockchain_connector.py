import json
from abc import ABC, abstractmethod
from decimal import Decimal
from logging import Logger
from typing import Any, List, Literal, Dict, Optional, Type, Tuple

import httpx


class BlockchainConnector(ABC):
    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        kms_address: str = None,
    ):
        """
        Base blockchain connector

        :param logger: Logger instance
        :param rpc_url: RPC node URL
        :param kms_address: KMS service address
        """
        self.logger = logger
        self.rpc_url = rpc_url
        self.kms_address = kms_address

    @abstractmethod
    async def init_connection(self):
        """Check connection status with the blockchain"""
        pass

    def check_if_native_token(self, token_address: str) -> bool:
        """Check if the token is a native token"""
        return token_address == "native_token"

    async def sign(
        self, secret: str, tx: str | dict | bytes, type: str = None, raw_sign: bool = False
    ) -> Any | None:
        """
        Sign transaction using KMS

        :param secret: Signing key
        :param tx: Transaction data
        :param type: Chain type (sol or bsc)
        :return: Signed transaction
        """
        if isinstance(tx, dict):
            tx = json.dumps(tx)
        elif isinstance(tx, bytes):
            tx = tx.hex()

        payload = {"secret": secret, "transaction": tx, "raw_sign": raw_sign}
        if type:
            payload["type"] = type

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.kms_address}/sign", json=payload)
                js = response.json()
                if js.get("success"):
                    return js.get("signed_tx")
                else:
                    return None
        except httpx.HTTPError as e:
            self.logger.warning(f"HTTP error occurred: {e}")
            return None

    # -----------------------------
    # Abstract Methods - Query Functions
    # -----------------------------

    @abstractmethod
    async def get_token_balance(self, pubkey: str, token_address: str) -> Decimal:
        """Get token balance"""
        pass

    @abstractmethod
    async def get_native_balance(self, pubkey: str) -> Decimal:
        """Get native token balance (ETH/BNB/SOL etc.)"""
        pass

    @abstractmethod
    async def get_token_balance_in_wei(
        self, pubkey: str, token_address: str
    ) -> Decimal:
        """Get token balance"""
        pass

    @abstractmethod
    async def get_native_balance_in_wei(self, pubkey: str) -> Decimal:
        """Get native token balance (ETH/BNB/SOL etc.)"""
        pass

    @abstractmethod
    async def get_cash_balance(self, pubkey: str) -> Decimal:
        """Get cash balance"""
        pass

    @abstractmethod
    async def get_cash_balance_in_wei(self, pubkey: str) -> Decimal:
        """Get cash balance in wei"""
        pass

    @abstractmethod
    async def get_liquidity(self, token_address: str) -> float:
        pass

    @abstractmethod
    async def meme_initial_supply(self) -> Decimal:
        pass
    

    @abstractmethod
    async def get_out_token_amount_with_usdt(self, token_address: str, usdt_amount: Decimal) -> Tuple:
        pass

    @abstractmethod
    async def get_out_token_amount(
        self, token_address: str, bnb_amount: Decimal
    ) -> Tuple:
        pass

    @abstractmethod
    async def get_in_native_amount(
        self, token_address: str, amount_out: Decimal
    ) -> Tuple:
        pass
    
    @abstractmethod
    async def get_out_usdt_amount(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        pass

    @abstractmethod
    async def get_out_native_amount(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        pass

    @abstractmethod
    async def get_out_native_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        pass

    @abstractmethod
    async def progress(self, token_address: str) -> int:
        pass

    @abstractmethod
    async def graduate(self, token_address: str) -> bool:
        pass

    @abstractmethod
    async def generic_estimate_evm_onchain_gas(self, abi_path: str, contract_address: str, function_name: str, estimate_gas_params: dict, function_params: list) -> int:
        pass


    # -----------------------------
    # Abstract Methods - Transaction Functions
    # -----------------------------

    @abstractmethod
    async def transfer_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_pubkey: str,
        amount: Decimal,
        usdt_ui_gas_amount: Decimal,
        gas: int,
        gas_price: int,
        gas_payer_secret: str,
    ) -> str:
        """Transfer tokens"""
        pass

    @abstractmethod
    async def transfer_native(
        self,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        **kwargs,
    ) -> str:
        """Transfer native tokens (ETH/BNB/SOL etc.)"""
        pass

    @abstractmethod
    async def launch(
        self,
        l2_token_address: str,
        name: str,
        symbol: str,
        repo_url: str,
        logo: str,
        desc: str,
        pubkey: str,
        secret: str,
        amount_to_buy: Decimal,
        gas_usdt_amount: Decimal,
        gas: int,
        gas_price: Optional[int] = None,  # ETH typically uses higher gas prices
        gas_payer_secret: Optional[str] = None,
        is_with_usdt: Optional[bool] = True,
    ) -> str:
        """Launch a new token"""
        pass
    
    @abstractmethod
    async def userCreateTokenEligible(self, pubkey: str) -> bool:
        """
        Check if the user is eligible to create a token
        """
        pass
        

    @abstractmethod
    async def buy_with_USDT(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        usdt_ui_amount_to_buy: Decimal,
        usdt_ui_gas_amount: Decimal,
        eth_amount_out_min: Decimal,
        amount_out_min: Decimal,
        gas: Optional[int] = None,
        gas_price: Optional[int] = None,
        gas_payer_secret: Optional[str] = None,
    ) -> str:
        """Buy tokens with USDT"""
        pass
    
    @abstractmethod
    async def sell_for_USDT(
        self,
        token_address: str,
        seller_pubkey: str,
        seller_secret: str,
        amount_to_sell: Decimal,
        eth_amount_out_min: Decimal,
        usdt_ui_amount_out_min: Decimal,
        usdt_ui_gas_amount: Decimal,
        gas: int,
        gas_price: int,
        gas_payer_secret: str
    ) -> str:
        """Sell tokens for USDT"""
        pass

    @abstractmethod
    async def buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        amount_to_buy: Decimal,
        amount_out_min: int,
        gas: int,
        gas_price: int,
    ) -> str:
        """Buy tokens"""
        pass

    @abstractmethod
    async def sell(
        self,
        token_address: str,
        amount_to_sell: Decimal,
        pubkey: str,
        secret: str,
        amount_out_min: int,
        gas: int,
        gas_price: int,
    ) -> str:
        """Sell tokens"""
        pass

    @abstractmethod
    async def claim(
        self,
        pubkey: str,
        secret: str,
        token_address: str,
        amount: Decimal,
        gas: int,
        gas_price: int,
    ) -> str:
        pass

    # -----------------------------
    # Abstract Methods - DEX Swap Functions
    # -----------------------------

    @abstractmethod
    async def swap_token(
        self,
        token_in: str,           # 输入代币地址 (原生代币使用特定地址表示)
        token_out: str,          # 输出代币地址 (原生代币使用特定地址表示)
        amount_in: Decimal,      # 输入金额
        user_pubkey: str,        # 用户公钥
        user_secret: str,        # 用户私钥
        slippage_percent: float = 0.5,    # 滑点百分比
        gas: int = 200_000,      # Gas限制
        gas_price: int = 1,     # Gas价格
        deadline_minutes: int = 20,       # 交易期限(分钟)
    ) -> str:
        """
        统一的代币交换抽象接口，支持不同DEX实现
        
        不同链的实现：
        - ETH: Uniswap V2/V3, SushiSwap, 等
        - BSC: PancakeSwap, BiSwap, 等
        - SOL: Raydium, Orca, 等
        
        :param token_in: 输入代币地址，原生代币使用链特定地址表示
        :param token_out: 输出代币地址，原生代币使用链特定地址表示
        :param amount_in: 输入金额
        :param user_pubkey: 用户公钥
        :param user_secret: 用户私钥
        :param slippage_percent: 滑点百分比，默认0.5%
        :param gas: Gas限制
        :param gas_price: Gas价格
        :param deadline_minutes: 交易期限(分钟)，默认20
        :return: 交易哈希
        """
        pass

    @abstractmethod
    async def get_dex_out_token_amount_with_usdt(self, token_address: str, usdt_amount: Decimal) -> Tuple:
        """
        获取DEX中用USDT可以换取的代币数量
        """
        pass

    @abstractmethod
    async def get_dex_out_token_amount(self, token_address: str, native_amount: Decimal) -> Tuple:
        """
        获取DEX中用原生代币可以换取的代币数量
        
        :param token_address: 代币地址
        :param native_amount: 原生代币数量 (BNB/ETH)
        :return: (代币数量_wei, 代币数量_decimal)
        """
        pass

    @abstractmethod 
    async def get_dex_in_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        获取DEX中换取指定代币数量需要的原生代币数量
        
        :param token_address: 代币地址
        :param token_amount: 代币数量
        :return: (原生代币数量_wei, 原生代币数量_decimal)
        """
        pass
    
    @abstractmethod
    async def get_dex_out_usdt_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        获取DEX中用代币可以换取的USDT数量
        """
        pass

    @abstractmethod
    async def get_dex_out_native_amount(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        获取DEX中用代币可以换取的原生代币数量
        
        :param token_address: 代币地址
        :param token_amount: 代币数量
        :return: (原生代币数量_wei, 原生代币数量_decimal)
        """
        pass

    @abstractmethod
    async def get_dex_out_native_amount_after_fee(self, token_address: str, token_amount: Decimal) -> Tuple:
        """
        获取DEX中用代币可以换取的原生代币数量(扣除手续费)
        
        :param token_address: 代币地址
        :param token_amount: 代币数量
        :return: (原生代币数量_wei, 原生代币数量_decimal)
        """
        pass

    @abstractmethod
    async def memeswap_get_amounts_out_universal(self, amount_in_uint256: Decimal, path: List[str]) -> List[int]:
        """Get amounts out for MemeSwap trade"""
        pass

    @abstractmethod
    async def launch_l1(self, name, symbol, repo_url, avatar, about, wallet_address, gas, gas_price=None, gas_payer_secret=None) -> str:
        pass

    @abstractmethod
    async def create_l2_token(self,l1_token_address: str, name: str, symbol: str, repo_url: str, logo: str, desc: str, pubkey: str, gas: int, gas_price: Optional[int] = None, gas_payer_secret: Optional[str] = None):
        pass

    @abstractmethod
    async def bridge_l1_to_l2(self,l1_token_address: str, l2_token_address: str, gas: int, gas_price: Optional[int] = None, gas_payer_secret: Optional[str] = None):
        pass


class BlockchainConnectorGetter:
    """Factory class for blockchain connectors"""

    _connector_registry: Dict[str, Type[BlockchainConnector]] = {}

    @classmethod
    def register_connector(
        cls, chain_type: str, connector_class: Type[BlockchainConnector]
    ):
        """
        Register a blockchain connector class for a specific chain type

        :param chain_type: Chain type identifier (e.g., 'sol', 'bsc')
        :param connector_class: Connector class to register
        """
        cls._connector_registry[chain_type] = connector_class

    def __init__(
        self,
        logger: Logger,
        chain_type: Literal["sol", "bsc", "eth"],
        rpc_url: str,
        kms_address: str,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the blockchain connector factory

        :param logger: Logger instance
        :param chain_type: Type of blockchain ('sol', 'bsc', or 'eth')
        :param rpc_url: RPC node URL
        :param kms_address: KMS service address
        :param config: Additional configuration parameters specific to each blockchain
        """
        self.logger = logger
        self.chain_type = chain_type
        self.rpc_url = rpc_url
        self.kms_address = kms_address
        self.config = config or {}

    def __call__(self) -> BlockchainConnector:
        """
        Create and return an appropriate blockchain connector instance

        :return: BlockchainConnector instance
        :raises ValueError: If chain type is not supported
        """
        if self.chain_type == "sol":
            try:
                from src.common.blockchain.solana_connector import SolanaConnector
            except ImportError:
                raise ValueError("Solana connector is not available. Missing dependencies.")
                
            # Create a Solana connector
            if (
                "contract_address" not in self.config
                or "meme_abi_path" not in self.config
            ):
                raise ValueError(
                    "Solana connector requires 'contract_address' and 'meme_abi_path' in config"
                )

            return SolanaConnector(
                self.logger,
                self.rpc_url,
                self.config["contract_address"],
                self.config["meme_abi_path"],
                self.kms_address,
            )
        elif self.chain_type == "bsc":
            # Import here to avoid circular imports
            try:
                from src.common.blockchain.bsc_connector import BSCConnector

                # Create a BSC connector
                return BSCConnector(
                    self.logger, self.rpc_url, self.kms_address, **self.config
                )
            except ImportError:
                raise ValueError("BSC connector is not available")
        elif self.chain_type == "eth":
            # Import here to avoid circular imports
            try:
                from src.common.blockchain.eth_connector import ETHConnector

                # Create an ETH connector
                return ETHConnector(
                    self.logger, self.rpc_url, self.kms_address, **self.config
                )
            except ImportError:
                raise ValueError("ETH connector is not available")
        else:
            raise ValueError(f"Unsupported blockchain type: {self.chain_type}")
