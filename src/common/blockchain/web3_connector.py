import json
from decimal import Decimal
from logging import Logger
from typing import Any, Tuple

import eth_utils
import httpx
from web3 import AsyncHT<PERSON><PERSON><PERSON><PERSON>, AsyncWeb3

from src.memecoin.settings import settings


class Web3Connector:
    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        chain_id: int,
        contract_address: str,
        meme_abi_path: str,
        erc20_contract_address: str,
        erc20_abi_path: str,
        kms_address: str = None,
    ):
        """
          MAINNET
        - rpc_url: 'https://bsc.drpc.org'
        - chain_id: 56

          TESTNET
        - rpc_url: 'https://bsc-testnet.drpc.org'
        - chain_id: 97
        """
        self.logger = logger
        self.rpc_url = rpc_url
        self.chain_id = chain_id
        self.kms_address = kms_address
        self.w3 = AsyncWeb3(AsyncHTTPProvider(rpc_url))
        self.contract_address = self.w3.to_checksum_address(contract_address)
        self.contract = self.w3.eth.contract(
            address=self.contract_address,
            abi=self._load_abi(meme_abi_path),
        )
        self.erc20_abi = self._load_abi(erc20_abi_path)

    async def init_connection(self):
        """
        Call this method to check the connection status
        """
        connected = await self.w3.is_connected()
        if not connected:
            raise ConnectionError(
                f"Unable to connect to the blockchain network, please check the RPC URL ({self.rpc_url}) and network status."
            )

    def _load_abi(self, abi_path: str):
        raw_abi = json.load(open(abi_path, "r", encoding="utf-8"))
        return raw_abi.get("abi")

    # -----------------------------
    # View functions
    # -----------------------------

    async def erc20_initial_supply(self) -> int:
        return self.contract.functions.erc20InitialSupply().call()

    async def get_out_token_amount(
        self, token_address: str, bnb_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutTokenAmount(
            token_addr, self.w3.to_wei(bnb_amount, "ether")
        ).call()
        return (
            amount[1],
            self.w3.from_wei(amount[1], "ether"),
        )

    async def get_in_bnb_amount(self, token_address: str, amount_out: Decimal) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getInBnbAmount(
            token_addr, self.w3.to_wei(amount_out, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def get_out_bnb_amount(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmount(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def get_out_bnb_amount_after_fee(
        self, token_address: str, token_amount: Decimal
    ) -> Tuple:
        token_addr = self.w3.to_checksum_address(token_address)
        amount = await self.contract.functions.getOutBnbAmountAfterFee(
            token_addr, self.w3.to_wei(token_amount, "ether")
        ).call()
        return amount, self.w3.from_wei(amount, "ether")

    async def progress(self, token_address: str) -> int:
        token_addr = self.w3.to_checksum_address(token_address)
        try:
            return await self.contract.functions.progress(token_addr).call()
        except Exception as e:
            return -1

    # -----------------------------
    # Transaction methods
    # -----------------------------

    async def _build_launch_tx(
        self,
        name,
        symbol,
        repo_url,
        logo,
        desc,
        nonce,
        amount_to_buy,
        gas,
        gas_price,
        signature,
    ):
        tx = await self.contract.functions.launch(
            name, symbol, repo_url, logo, desc, bytes.fromhex(signature)
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(amount_to_buy, "ether"),
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        return tx

    async def _build_approve_tx(
        self, token_addr, amount_to_sell, gas, gas_price, nonce
    ):
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        tx = await token_contract.functions.approve(
            self.contract_address, self.w3.to_wei(amount_to_sell, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        return tx

    async def _build_sell_tx(
        self, token_addr, amount_to_sell, amount_out_min, gas, gas_price, nonce
    ):
        tx = await self.contract.functions.sell(
            token_addr, self.w3.to_wei(amount_to_sell, "ether"), amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        return tx

    async def _build_buy_tx(
        self, token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
    ):
        tx = await self.contract.functions.buy(
            token_addr, amount_out_min
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "gas": gas,
                "value": self.w3.to_wei(amount_to_buy, "ether"),
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
                "nonce": nonce,
            }
        )
        return tx

    async def _build_transfer_tx(
        self, token_address, to_address, amount, gas, gas_price, nonce
    ):
        token_addr = self.w3.to_checksum_address(token_address)
        token_contract = self.w3.eth.contract(address=token_addr, abi=self.erc20_abi)
        tx = await token_contract.functions.transfer(
            to_address, self.w3.to_wei(amount, "ether")
        ).build_transaction(
            {
                "chainId": self.chain_id,
                "nonce": nonce,
                "gas": gas,  # Adjust the gas limit as needed
                "gasPrice": self.w3.to_wei(gas_price, "gwei"),
            }
        )
        return tx

    async def sign(self, secret: str, tx: str | dict) -> Any | None:
        if isinstance(tx, dict):
            tx = json.dumps(tx)

        payload = {"secret": secret, "transaction": tx}

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.kms_address}/sign", json=payload)
                js = response.json()
                if js.get("success"):
                    return js.get("signed_tx")
                else:
                    return None
        except httpx.HTTPError as e:
            self.logger.warning(f"HTTP error occurred: {e}")
            return None

    async def buy(
        self,
        token_address: str,
        buyer_pubkey: str,
        buyer_secret: str,
        amount_to_buy: Decimal,
        amount_out_min: int = 0,
        gas: int = 21000,
        gas_price: int = 1,
    ):
        """
        buy token
        :param token_address: token address
        :param buyer_pubkey: account
        :param buyer_secret: secret (not private key) of buyer
        :param amount_out_min: amount out min
        :return:
        """
        buyer_address = self.w3.to_checksum_address(buyer_pubkey)
        token_addr = self.w3.to_checksum_address(token_address)
        nonce = await self.w3.eth.get_transaction_count(buyer_address)
        tx = await self._build_buy_tx(
            token_addr, amount_to_buy, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(buyer_secret, tx)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def generate_signature(self, sender: str, repo_url: str) -> str | None:
        if sender.startswith("0x"):
            sender = sender[2:]
        sender_bytes = bytes.fromhex(sender)
        repo_url_bytes = repo_url.encode("utf-8")
        message_hash = eth_utils.keccak(sender_bytes + repo_url_bytes)
        tx = message_hash.hex()
        signature = await self.sign(settings.LAUNCH_SIGN_SECRET, tx)
        return signature

    async def launch(
        self,
        name: str,
        symbol: str,
        repo_url: str,
        logo: str,
        desc: str,
        pubkey: str,
        secret: str,
        amount_to_buy: Decimal = 0,
        gas: int = 5_000_000,
        gas_price: int = 3,
    ):
        """
        create meme
        :param name: meme name
        :param symbol: meme symbol
        :param repo_url: collection repo url
        :param pubkey: public key
        :param private_key: private key
        :return:
        """
        account = self.w3.to_checksum_address(pubkey)
        signature = await self.generate_signature(pubkey, repo_url)
        nonce = await self.w3.eth.get_transaction_count(account)
        tx = await self._build_launch_tx(
            name,
            symbol,
            repo_url,
            logo,
            desc,
            nonce,
            amount_to_buy,
            gas,
            gas_price,
            signature,
        )

        signed_tx_hex = await self.sign(secret, tx)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def sell(
        self,
        token_address: str,
        amount_to_sell: Decimal,
        pubkey: str,
        secret: str,
        amount_out_min: int = 0,
        gas: int = 24000,
        gas_price: int = 1,
    ):
        """
        sell token
        :param token_address: token address
        :param amount_in: token amount to sell in wei (10**18)
        :param pubkey: pubkey
        :param private_key: private key
        :param amount_out_min: BNB expected to receive
        :return:
        """
        token_addr = self.w3.to_checksum_address(token_address)
        account_pubkey = self.w3.to_checksum_address(pubkey)

        nonce = await self.w3.eth.get_transaction_count(account_pubkey)

        approve_tx = await self._build_approve_tx(
            token_addr, amount_to_sell, gas, gas_price, nonce
        )
        signed_approve_tx = await self.sign(secret, approve_tx)
        approve_tx_hash = await self.w3.eth.send_raw_transaction(signed_approve_tx)
        self.logger.info(f"Approve transaction sent: {approve_tx_hash.hex()}")
        receipt = await self.w3.eth.wait_for_transaction_receipt(approve_tx_hash)
        self.logger.info(f"Approve transaction receipt: {receipt}")
        nonce += 1

        tx = await self._build_sell_tx(
            token_addr, amount_to_sell, amount_out_min, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(secret, tx)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def send(
        self,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
    ):
        from_address = self.w3.to_checksum_address(from_pubkey)
        nonce = await self.w3.eth.get_transaction_count(from_address)

        tx = {
            "nonce": nonce,
            "to": to_address,
            "value": self.w3.to_wei(amount, "ether"),
            "gas": gas,
            "gasPrice": self.w3.to_wei(gas_price, "gwei"),
            "chainId": 56,
        }

        signed_tx_hex = await self.sign(from_secret, tx)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def send_token(
        self,
        token_address: str,
        from_pubkey: str,
        from_secret: str,
        to_address: str,
        amount: Decimal,
        gas: int = 24000,
        gas_price: int = 1,
    ):
        """
        Determines the transfer type based on token_address:
          - If token_address is empty, "", or "BNB", it is considered a native currency (BNB) transfer.
          - Otherwise, it is treated as an ERC20 Token transfer.

        :param token_address: Token contract address or "BNB"
        :param from_pubkey: Sender's public key (address)
        :param from_secret: Sender's private key
        :param to_address: Recipient's address
        :param amount: Transfer amount (in wei for BNB, or in token's smallest unit for tokens)
        :return: Transaction receipt
        """
        from_address = self.w3.to_checksum_address(from_pubkey)
        to_address = self.w3.to_checksum_address(to_address)
        nonce = await self.w3.eth.get_transaction_count(from_address)

        tx = await self._build_transfer_tx(
            token_address, to_address, amount, gas, gas_price, nonce
        )

        signed_tx_hex = await self.sign(from_secret, tx)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx_hex)
        return tx_hash.hex()

    async def get_bnb_balance(self, pubkey: str) -> Decimal:
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        balance_bnb = self.w3.from_wei(balance_wei, "ether")
        return balance_bnb

    async def get_bnb_balance_in_wei(self, pubkey: str) -> Decimal:
        address = self.w3.to_checksum_address(pubkey)
        balance_wei = await self.w3.eth.get_balance(address)
        return Decimal(balance_wei)

    async def get_token_balance(self, pubkey: str, token_address: str) -> Decimal:
        balance_wei = await self.get_token_balance_in_wei(pubkey, token_address)
        balance_token = self.w3.from_wei(balance_wei, "ether")
        return balance_token

    async def get_token_balance_in_wei(self, pubkey: str, token_address: str) -> int:
        address = self.w3.to_checksum_address(pubkey)
        token_contract = self.w3.eth.contract(
            address=self.w3.to_checksum_address(token_address), abi=self.erc20_abi
        )
        balance_wei = await token_contract.functions.balanceOf(address).call()
        return balance_wei

    async def get_liquidity_bnb(self, token_address: str) -> float:
        address = self.w3.to_checksum_address(token_address)
        token_info = await self.contract.functions.tokenInfos(address).call()
        virtual_x, virtual_y, _, _ = token_info
        pool_bnb_amount = virtual_y / 1e18 - 30
        return pool_bnb_amount if pool_bnb_amount > 0 else 0


class Web3ConnectorGetter:
    def __init__(
        self,
        logger: Logger,
        rpc_url: str,
        chain_id: int,
        contract_address: str,
        meme_abi_path: str,
        erc20_contract_address: str,
        erc20_abi_path: str,
        kms_address: str,
    ):
        self.rpc_url = rpc_url
        self.chain_id = chain_id
        self.contract_address = contract_address
        self.meme_abi_path = meme_abi_path
        self.erc20_contract_address = erc20_contract_address
        self.erc20_abi_path = erc20_abi_path
        self.kms_address = kms_address
        self.logger = logger

    def __call__(self) -> Web3Connector:
        return Web3Connector(
            self.logger,
            self.rpc_url,
            self.chain_id,
            self.contract_address,
            self.meme_abi_path,
            self.erc20_contract_address,
            self.erc20_abi_path,
            self.kms_address,
        )
