{"abi": [{"type": "function", "name": "batchDistributeReward", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "recipients", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claim", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimedAmount", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "emergencyWithdraw", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "nonces", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateOperator", "inputs": [{"name": "newOperator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updatePaused", "inputs": [{"name": "newPaused", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateSigner", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BatchRewardDistributed", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "totalAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "recipientCount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NonceUsed", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nonce", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OperatorUpdated", "inputs": [{"name": "previousOperator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PausedUpdated", "inputs": [{"name": "previousPaused", "type": "bool", "indexed": true, "internalType": "bool"}, {"name": "newPaused", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "RewardDistributed", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SignerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"batchDistributeReward(address,address[],uint256[])": "d9f37207", "claim(address,uint256,uint256,bytes)": "2ada8a32", "claimedAmount(address,address)": "c6d5813e", "emergencyWithdraw(address,address,uint256)": "e63ea408", "nonces(address,address)": "9333fbda", "updateOperator(address)": "ac7475ed", "updatePaused(bool)": "08cdc2a8", "updateSigner(address)": "a7ecd37e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"recipientCount\",\"type\":\"uint256\"}],\"name\":\"BatchRewardDistributed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"}],\"name\":\"NonceUsed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOperator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"OperatorUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"previousPaused\",\"type\":\"bool\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"newPaused\",\"type\":\"bool\"}],\"name\":\"PausedUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"RewardDistributed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousSigner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newSigner\",\"type\":\"address\"}],\"name\":\"SignerUpdated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"}],\"name\":\"batchDistributeReward\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"claim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"claimedAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"updateOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"newPaused\",\"type\":\"bool\"}],\"name\":\"updatePaused\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newSigner\",\"type\":\"address\"}],\"name\":\"updateSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IMemeReward.sol\":\"IMemeReward\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":20000},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\"],\"viaIR\":true},\"sources\":{\"src/interfaces/IMemeReward.sol\":{\"keccak256\":\"0xdd93bd9d46d652ca32f17923c1b80ca246eb990ef48fc81b1fd6b88706f3203f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://39ef6e671d2ff561a7a4b748270b1b0e29ecaa89e0c4a1360fb023487309f0d7\",\"dweb:/ipfs/QmaiuwfNs9uRvCVBEhc7rX5Pyuo4EdQBzMb5fPu3M9PAt9\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "totalAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "recipientCount", "type": "uint256", "indexed": false}], "type": "event", "name": "BatchRewardDistributed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "nonce", "type": "uint256", "indexed": false}], "type": "event", "name": "NonceUsed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOperator", "type": "address", "indexed": true}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": true}], "type": "event", "name": "OperatorUpdated", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "previousPaused", "type": "bool", "indexed": true}, {"internalType": "bool", "name": "newPaused", "type": "bool", "indexed": true}], "type": "event", "name": "PausedUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardDistributed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}], "type": "event", "name": "SignerUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchDistributeReward"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "claim"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimedAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyWithdraw"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "newOperator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "updateOperator"}, {"inputs": [{"internalType": "bool", "name": "newPaused", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updatePaused"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "updateSigner"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/"], "optimizer": {"enabled": true, "runs": 20000}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IMemeReward.sol": "IMemeReward"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"src/interfaces/IMemeReward.sol": {"keccak256": "0xdd93bd9d46d652ca32f17923c1b80ca246eb990ef48fc81b1fd6b88706f3203f", "urls": ["bzz-raw://39ef6e671d2ff561a7a4b748270b1b0e29ecaa89e0c4a1360fb023487309f0d7", "dweb:/ipfs/QmaiuwfNs9uRvCVBEhc7rX5Pyuo4EdQBzMb5fPu3M9PAt9"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "src/interfaces/IMemeReward.sol", "id": 51599, "exportedSymbols": {"IMemeReward": [51598]}, "nodeType": "SourceUnit", "src": "32:1231:66", "nodes": [{"id": 51491, "nodeType": "PragmaDirective", "src": "32:24:66", "nodes": [], "literals": ["solidity", "^", "0.8", ".20"]}, {"id": 51598, "nodeType": "ContractDefinition", "src": "58:1204:66", "nodes": [{"id": 51497, "nodeType": "EventDefinition", "src": "86:85:66", "nodes": [], "anonymous": false, "eventSelector": "fbe5b6cbafb274f445d7fed869dc77a838d8243a22c460de156560e8857cad03", "name": "OperatorUpdated", "nameLocation": "92:15:66", "parameters": {"id": 51496, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51493, "indexed": true, "mutability": "mutable", "name": "previousOperator", "nameLocation": "124:16:66", "nodeType": "VariableDeclaration", "scope": 51497, "src": "108:32:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51492, "name": "address", "nodeType": "ElementaryTypeName", "src": "108:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51495, "indexed": true, "mutability": "mutable", "name": "newOperator", "nameLocation": "158:11:66", "nodeType": "VariableDeclaration", "scope": 51497, "src": "142:27:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51494, "name": "address", "nodeType": "ElementaryTypeName", "src": "142:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "107:63:66"}}, {"id": 51503, "nodeType": "EventDefinition", "src": "176:79:66", "nodes": [], "anonymous": false, "eventSelector": "2d025324f0a785e8c12d0a0d91a9caa49df4ef20ff87e0df7213a1d4f3157beb", "name": "SignerUpdated", "nameLocation": "182:13:66", "parameters": {"id": 51502, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51499, "indexed": true, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "212:14:66", "nodeType": "VariableDeclaration", "scope": 51503, "src": "196:30:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51498, "name": "address", "nodeType": "ElementaryTypeName", "src": "196:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51501, "indexed": true, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "244:9:66", "nodeType": "VariableDeclaration", "scope": 51503, "src": "228:25:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51500, "name": "address", "nodeType": "ElementaryTypeName", "src": "228:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "195:59:66"}}, {"id": 51511, "nodeType": "EventDefinition", "src": "260:90:66", "nodes": [], "anonymous": false, "eventSelector": "5303cb8fc2ab504b092b4799d2c00f2209ee4eee6b03a168171dc140ed7403ee", "name": "RewardDistributed", "nameLocation": "266:17:66", "parameters": {"id": 51510, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51505, "indexed": true, "mutability": "mutable", "name": "token", "nameLocation": "300:5:66", "nodeType": "VariableDeclaration", "scope": 51511, "src": "284:21:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51504, "name": "address", "nodeType": "ElementaryTypeName", "src": "284:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51507, "indexed": true, "mutability": "mutable", "name": "recipient", "nameLocation": "323:9:66", "nodeType": "VariableDeclaration", "scope": 51511, "src": "307:25:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51506, "name": "address", "nodeType": "ElementaryTypeName", "src": "307:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51509, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "342:6:66", "nodeType": "VariableDeclaration", "scope": 51511, "src": "334:14:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51508, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "334:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "283:66:66"}}, {"id": 51519, "nodeType": "EventDefinition", "src": "355:97:66", "nodes": [], "anonymous": false, "eventSelector": "7738883d37fd731f05937b44cf69e81d2a489af7c8a08ebec2d2d136f4d01978", "name": "BatchRewardDistributed", "nameLocation": "361:22:66", "parameters": {"id": 51518, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51513, "indexed": true, "mutability": "mutable", "name": "token", "nameLocation": "400:5:66", "nodeType": "VariableDeclaration", "scope": 51519, "src": "384:21:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51512, "name": "address", "nodeType": "ElementaryTypeName", "src": "384:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51515, "indexed": false, "mutability": "mutable", "name": "totalAmount", "nameLocation": "415:11:66", "nodeType": "VariableDeclaration", "scope": 51519, "src": "407:19:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51514, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "407:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 51517, "indexed": false, "mutability": "mutable", "name": "recipientCount", "nameLocation": "436:14:66", "nodeType": "VariableDeclaration", "scope": 51519, "src": "428:22:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51516, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "428:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "383:68:66"}}, {"id": 51527, "nodeType": "EventDefinition", "src": "457:76:66", "nodes": [], "anonymous": false, "eventSelector": "9fe9126258e6f3e5d5372e3ddb8a3821fea3030119c87ab7fec37dce0ed969f6", "name": "NonceUsed", "nameLocation": "463:9:66", "parameters": {"id": 51526, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51521, "indexed": true, "mutability": "mutable", "name": "token", "nameLocation": "489:5:66", "nodeType": "VariableDeclaration", "scope": 51527, "src": "473:21:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51520, "name": "address", "nodeType": "ElementaryTypeName", "src": "473:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51523, "indexed": true, "mutability": "mutable", "name": "user", "nameLocation": "512:4:66", "nodeType": "VariableDeclaration", "scope": 51527, "src": "496:20:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51522, "name": "address", "nodeType": "ElementaryTypeName", "src": "496:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51525, "indexed": false, "mutability": "mutable", "name": "nonce", "nameLocation": "526:5:66", "nodeType": "VariableDeclaration", "scope": 51527, "src": "518:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51524, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "518:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "472:60:66"}}, {"id": 51533, "nodeType": "EventDefinition", "src": "538:73:66", "nodes": [], "anonymous": false, "eventSelector": "9c13c5f6c628c1c82eb2b3fd8714da5c2857dbd0ad8ab6b2074abcf81015d396", "name": "PausedUpdated", "nameLocation": "544:13:66", "parameters": {"id": 51532, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51529, "indexed": true, "mutability": "mutable", "name": "previousPaused", "nameLocation": "571:14:66", "nodeType": "VariableDeclaration", "scope": 51533, "src": "558:27:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 51528, "name": "bool", "nodeType": "ElementaryTypeName", "src": "558:4:66", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 51531, "indexed": true, "mutability": "mutable", "name": "newPaused", "nameLocation": "600:9:66", "nodeType": "VariableDeclaration", "scope": 51533, "src": "587:22:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 51530, "name": "bool", "nodeType": "ElementaryTypeName", "src": "587:4:66", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "557:53:66"}}, {"id": 51544, "nodeType": "FunctionDefinition", "src": "617:96:66", "nodes": [], "functionSelector": "2ada8a32", "implemented": false, "kind": "function", "modifiers": [], "name": "claim", "nameLocation": "626:5:66", "parameters": {"id": 51542, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51535, "mutability": "mutable", "name": "token", "nameLocation": "640:5:66", "nodeType": "VariableDeclaration", "scope": 51544, "src": "632:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51534, "name": "address", "nodeType": "ElementaryTypeName", "src": "632:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51537, "mutability": "mutable", "name": "amount", "nameLocation": "655:6:66", "nodeType": "VariableDeclaration", "scope": 51544, "src": "647:14:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51536, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "647:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 51539, "mutability": "mutable", "name": "nonce", "nameLocation": "671:5:66", "nodeType": "VariableDeclaration", "scope": 51544, "src": "663:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51538, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "663:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 51541, "mutability": "mutable", "name": "signature", "nameLocation": "693:9:66", "nodeType": "VariableDeclaration", "scope": 51544, "src": "678:24:66", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 51540, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "678:5:66", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "631:72:66"}, "returnParameters": {"id": 51543, "nodeType": "ParameterList", "parameters": [], "src": "712:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51555, "nodeType": "FunctionDefinition", "src": "719:114:66", "nodes": [], "functionSelector": "d9f37207", "implemented": false, "kind": "function", "modifiers": [], "name": "batchDistributeReward", "nameLocation": "728:21:66", "parameters": {"id": 51553, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51546, "mutability": "mutable", "name": "token", "nameLocation": "758:5:66", "nodeType": "VariableDeclaration", "scope": 51555, "src": "750:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51545, "name": "address", "nodeType": "ElementaryTypeName", "src": "750:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51549, "mutability": "mutable", "name": "recipients", "nameLocation": "784:10:66", "nodeType": "VariableDeclaration", "scope": 51555, "src": "765:29:66", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_calldata_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 51547, "name": "address", "nodeType": "ElementaryTypeName", "src": "765:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 51548, "nodeType": "ArrayTypeName", "src": "765:9:66", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 51552, "mutability": "mutable", "name": "amounts", "nameLocation": "815:7:66", "nodeType": "VariableDeclaration", "scope": 51555, "src": "796:26:66", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_calldata_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 51550, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "796:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 51551, "nodeType": "ArrayTypeName", "src": "796:9:66", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "749:74:66"}, "returnParameters": {"id": 51554, "nodeType": "ParameterList", "parameters": [], "src": "832:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51564, "nodeType": "FunctionDefinition", "src": "839:79:66", "nodes": [], "functionSelector": "e63ea408", "implemented": false, "kind": "function", "modifiers": [], "name": "emergencyWithdraw", "nameLocation": "848:17:66", "parameters": {"id": 51562, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51557, "mutability": "mutable", "name": "token", "nameLocation": "874:5:66", "nodeType": "VariableDeclaration", "scope": 51564, "src": "866:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51556, "name": "address", "nodeType": "ElementaryTypeName", "src": "866:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51559, "mutability": "mutable", "name": "to", "nameLocation": "889:2:66", "nodeType": "VariableDeclaration", "scope": 51564, "src": "881:10:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51558, "name": "address", "nodeType": "ElementaryTypeName", "src": "881:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51561, "mutability": "mutable", "name": "amount", "nameLocation": "901:6:66", "nodeType": "VariableDeclaration", "scope": 51564, "src": "893:14:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51560, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "893:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "865:43:66"}, "returnParameters": {"id": 51563, "nodeType": "ParameterList", "parameters": [], "src": "917:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51569, "nodeType": "FunctionDefinition", "src": "924:54:66", "nodes": [], "functionSelector": "ac7475ed", "implemented": false, "kind": "function", "modifiers": [], "name": "updateOperator", "nameLocation": "933:14:66", "parameters": {"id": 51567, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51566, "mutability": "mutable", "name": "newOperator", "nameLocation": "956:11:66", "nodeType": "VariableDeclaration", "scope": 51569, "src": "948:19:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51565, "name": "address", "nodeType": "ElementaryTypeName", "src": "948:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "947:21:66"}, "returnParameters": {"id": 51568, "nodeType": "ParameterList", "parameters": [], "src": "977:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51574, "nodeType": "FunctionDefinition", "src": "984:50:66", "nodes": [], "functionSelector": "a7ecd37e", "implemented": false, "kind": "function", "modifiers": [], "name": "updateSigner", "nameLocation": "993:12:66", "parameters": {"id": 51572, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51571, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1014:9:66", "nodeType": "VariableDeclaration", "scope": 51574, "src": "1006:17:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51570, "name": "address", "nodeType": "ElementaryTypeName", "src": "1006:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1005:19:66"}, "returnParameters": {"id": 51573, "nodeType": "ParameterList", "parameters": [], "src": "1033:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51579, "nodeType": "FunctionDefinition", "src": "1040:47:66", "nodes": [], "functionSelector": "08cdc2a8", "implemented": false, "kind": "function", "modifiers": [], "name": "updatePaused", "nameLocation": "1049:12:66", "parameters": {"id": 51577, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51576, "mutability": "mutable", "name": "newPaused", "nameLocation": "1067:9:66", "nodeType": "VariableDeclaration", "scope": 51579, "src": "1062:14:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 51575, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1062:4:66", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1061:16:66"}, "returnParameters": {"id": 51578, "nodeType": "ParameterList", "parameters": [], "src": "1086:0:66"}, "scope": 51598, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 51588, "nodeType": "FunctionDefinition", "src": "1093:77:66", "nodes": [], "functionSelector": "9333fbda", "implemented": false, "kind": "function", "modifiers": [], "name": "nonces", "nameLocation": "1102:6:66", "parameters": {"id": 51584, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51581, "mutability": "mutable", "name": "token", "nameLocation": "1117:5:66", "nodeType": "VariableDeclaration", "scope": 51588, "src": "1109:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51580, "name": "address", "nodeType": "ElementaryTypeName", "src": "1109:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51583, "mutability": "mutable", "name": "user", "nameLocation": "1132:4:66", "nodeType": "VariableDeclaration", "scope": 51588, "src": "1124:12:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51582, "name": "address", "nodeType": "ElementaryTypeName", "src": "1124:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1108:29:66"}, "returnParameters": {"id": 51587, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51586, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 51588, "src": "1161:7:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51585, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1161:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1160:9:66"}, "scope": 51598, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 51597, "nodeType": "FunctionDefinition", "src": "1176:84:66", "nodes": [], "functionSelector": "c6d5813e", "implemented": false, "kind": "function", "modifiers": [], "name": "claimedAmount", "nameLocation": "1185:13:66", "parameters": {"id": 51593, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51590, "mutability": "mutable", "name": "token", "nameLocation": "1207:5:66", "nodeType": "VariableDeclaration", "scope": 51597, "src": "1199:13:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51589, "name": "address", "nodeType": "ElementaryTypeName", "src": "1199:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 51592, "mutability": "mutable", "name": "user", "nameLocation": "1222:4:66", "nodeType": "VariableDeclaration", "scope": 51597, "src": "1214:12:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 51591, "name": "address", "nodeType": "ElementaryTypeName", "src": "1214:7:66", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1198:29:66"}, "returnParameters": {"id": 51596, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 51595, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 51597, "src": "1251:7:66", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51594, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1251:7:66", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1250:9:66"}, "scope": 51598, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IMemeReward", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [51598], "name": "IMemeReward", "nameLocation": "68:11:66", "scope": 51599, "usedErrors": [], "usedEvents": [51497, 51503, 51511, 51519, 51527, 51533]}], "license": "MIT"}, "id": 66}