{"abi": [{"type": "function", "name": "buy", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "getInBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getInBnbAmountNotYetLaunched", "inputs": [{"name": "amountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "getOutBnbAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutBnbAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "tokenAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmount", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOutTokenAmountAfterFee", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "ethAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "graduate", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "launch", "inputs": [{"name": "creator", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "repoUrl", "type": "string", "internalType": "string"}, {"name": "logo", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "progress", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sell", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "amountOutMin", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeTo", "inputs": [{"name": "newFeeTo", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setGraduateFee", "inputs": [{"name": "newGraduateFee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "newOperator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardAdmin", "inputs": [{"name": "newRewardAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTokenLockup", "inputs": [{"name": "newTokenLockup", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTradingFeeRate", "inputs": [{"name": "newTradingFeeRate", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenInfos", "inputs": [{"name": "tokenAddress", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IMeme.TokenInfo", "components": [{"name": "virtualX", "type": "uint256", "internalType": "uint256"}, {"name": "virtualY", "type": "uint256", "internalType": "uint256"}, {"name": "K", "type": "uint256", "internalType": "uint256"}, {"name": "state", "type": "uint8", "internalType": "enum IMeme.TokenState"}]}], "stateMutability": "view"}, {"type": "function", "name": "userEligible", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Buy", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Complete", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Graduate", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "bnbAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Launch", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "launcher", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "symbol", "type": "string", "indexed": false, "internalType": "string"}, {"name": "repoUrl", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "<PERSON>ll", "inputs": [{"name": "tokenAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "bnbAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetDexRouter", "inputs": [{"name": "dex<PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFeeTo", "inputs": [{"name": "feeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetGraduateFee", "inputs": [{"name": "graduateFee", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetOperator", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetRewardAdmin", "inputs": [{"name": "rewardAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTokenLockup", "inputs": [{"name": "tokenLockup", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetTradingFeeRate", "inputs": [{"name": "tradingFeeRate", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "SetUserEligibilityCheck", "inputs": [{"name": "enabled", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SignerUpdated", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"buy(address,uint256)": "cce7ec13", "getInBnbAmount(address,uint256)": "5a328fda", "getInBnbAmountNotYetLaunched(uint256)": "772e3bbb", "getOutBnbAmount(address,uint256)": "d5e42595", "getOutBnbAmountAfterFee(address,uint256)": "fd3ad650", "getOutTokenAmount(address,uint256)": "58d7baeb", "getOutTokenAmountAfterFee(address,uint256)": "5212f9d9", "graduate(address)": "ff6d8d05", "launch(address,string,string,string,string,string,bytes)": "565d06ba", "progress(address)": "da8589a2", "sell(address,uint256,uint256)": "6a272462", "setFeeTo(address)": "f46901ed", "setGraduateFee(uint256)": "bc8b30da", "setOperator(address)": "b3ab15fb", "setRewardAdmin(address)": "a1b7e2e2", "setTokenLockup(address)": "69567f84", "setTradingFeeRate(uint256)": "1091f67c", "setUserEligibilityCheck(bool)": "bd6010d6", "tokenInfos(address)": "ba46ae72", "userEligible(address)": "268d3a2f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"buyer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmountOut\",\"type\":\"uint256\"}],\"name\":\"Buy\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"Complete\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pairAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"Graduate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"launcher\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"repoUrl\",\"type\":\"string\"}],\"name\":\"Launch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"seller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"bnbAmountOut\",\"type\":\"uint256\"}],\"name\":\"Sell\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"dexRouter\",\"type\":\"address\"}],\"name\":\"SetDexRouter\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeTo\",\"type\":\"address\"}],\"name\":\"SetFeeTo\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"graduateFee\",\"type\":\"uint256\"}],\"name\":\"SetGraduateFee\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"SetOperator\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardAdmin\",\"type\":\"address\"}],\"name\":\"SetRewardAdmin\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenLockup\",\"type\":\"address\"}],\"name\":\"SetTokenLockup\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tradingFeeRate\",\"type\":\"uint256\"}],\"name\":\"SetTradingFeeRate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"SetUserEligibilityCheck\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldSigner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newSigner\",\"type\":\"address\"}],\"name\":\"SignerUpdated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"}],\"name\":\"buy\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"getInBnbAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"getInBnbAmountNotYetLaunched\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"getOutBnbAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"getOutBnbAmountAfterFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"bnbAmount\",\"type\":\"uint256\"}],\"name\":\"getOutTokenAmount\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"ethAmount\",\"type\":\"uint256\"}],\"name\":\"getOutTokenAmountAfterFee\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"graduate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"repoUrl\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"logo\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"launch\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"progress\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOutMin\",\"type\":\"uint256\"}],\"name\":\"sell\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"setFeeTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newGraduateFee\",\"type\":\"uint256\"}],\"name\":\"setGraduateFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newRewardAdmin\",\"type\":\"address\"}],\"name\":\"setRewardAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newTokenLockup\",\"type\":\"address\"}],\"name\":\"setTokenLockup\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newTradingFeeRate\",\"type\":\"uint256\"}],\"name\":\"setTradingFeeRate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"setUserEligibilityCheck\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"name\":\"tokenInfos\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"virtualX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"K\",\"type\":\"uint256\"},{\"internalType\":\"enum IMeme.TokenState\",\"name\":\"state\",\"type\":\"uint8\"}],\"internalType\":\"struct IMeme.TokenInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"userEligible\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IMemeV1.sol\":\"IMeme\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":20000},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\"],\"viaIR\":true},\"sources\":{\"src/interfaces/IMemeV1.sol\":{\"keccak256\":\"0x3699b42a52bccc84d14a986009a99146d37b48bbd037d6e8e443af2a0f4ef25c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e698f893ffbe47b69a2f22f5006d3381a8573c3229c64edccefe614d463ac0dd\",\"dweb:/ipfs/QmXwBw54i3xx1wMUsBFjBpufZiuKVm14YE6Na82171R6BH\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "buyer", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "bnbAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "Buy", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}], "type": "event", "name": "Complete", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "Graduate", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "launcher", "type": "address", "indexed": true}, {"internalType": "string", "name": "name", "type": "string", "indexed": false}, {"internalType": "string", "name": "symbol", "type": "string", "indexed": false}, {"internalType": "string", "name": "repoUrl", "type": "string", "indexed": false}], "type": "event", "name": "Launch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address", "indexed": true}, {"internalType": "address", "name": "seller", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "bnbAmountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON>ll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "dex<PERSON><PERSON><PERSON>", "type": "address", "indexed": true}], "type": "event", "name": "SetDexRouter", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeTo", "type": "address", "indexed": true}], "type": "event", "name": "SetFeeTo", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "graduateFee", "type": "uint256", "indexed": false}], "type": "event", "name": "SetGraduateFee", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address", "indexed": true}], "type": "event", "name": "SetOperator", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardAdmin", "type": "address", "indexed": true}], "type": "event", "name": "SetRewardAdmin", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenLockup", "type": "address", "indexed": true}], "type": "event", "name": "SetTokenLockup", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "tradingFeeRate", "type": "uint256", "indexed": false}], "type": "event", "name": "SetTradingFeeRate", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool", "indexed": false}], "type": "event", "name": "SetUserEligibilityCheck", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": true}], "type": "event", "name": "SignerUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "buy"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getInBnbAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "getInBnbAmountNotYetLaunched", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutBnbAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutBnbAmountAfterFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutTokenAmount", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "ethAmount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getOutTokenAmountAfterFee", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "graduate"}, {"inputs": [{"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "repoUrl", "type": "string"}, {"internalType": "string", "name": "logo", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "launch"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "progress", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "sell"}, {"inputs": [{"internalType": "address", "name": "newFeeTo", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeTo"}, {"inputs": [{"internalType": "uint256", "name": "newGraduateFee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setGraduateFee"}, {"inputs": [{"internalType": "address", "name": "newOperator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "address", "name": "newRewardAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardAdmin"}, {"inputs": [{"internalType": "address", "name": "newTokenLockup", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setTokenLockup"}, {"inputs": [{"internalType": "uint256", "name": "newTradingFeeRate", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setTradingFeeRate"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setUserEligibilityCheck"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "view", "type": "function", "name": "tokenInfos", "outputs": [{"internalType": "struct IMeme.TokenInfo", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "virtualX", "type": "uint256"}, {"internalType": "uint256", "name": "virtualY", "type": "uint256"}, {"internalType": "uint256", "name": "K", "type": "uint256"}, {"internalType": "enum IMeme.TokenState", "name": "state", "type": "uint8"}]}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "userEligible", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/"], "optimizer": {"enabled": true, "runs": 20000}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/IMemeV1.sol": "IMeme"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"src/interfaces/IMemeV1.sol": {"keccak256": "0x3699b42a52bccc84d14a986009a99146d37b48bbd037d6e8e443af2a0f4ef25c", "urls": ["bzz-raw://e698f893ffbe47b69a2f22f5006d3381a8573c3229c64edccefe614d463ac0dd", "dweb:/ipfs/QmXwBw54i3xx1wMUsBFjBpufZiuKVm14YE6Na82171R6BH"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [], "types": {}}, "ast": {"absolutePath": "src/interfaces/IMemeV1.sol", "id": 57770, "exportedSymbols": {"IMeme": [57769]}, "nodeType": "SourceUnit", "src": "32:3166:74", "nodes": [{"id": 57514, "nodeType": "PragmaDirective", "src": "32:24:74", "nodes": [], "literals": ["solidity", "^", "0.8", ".20"]}, {"id": 57769, "nodeType": "ContractDefinition", "src": "58:3139:74", "nodes": [{"id": 57519, "nodeType": "EnumDefinition", "src": "80:94:74", "nodes": [], "canonicalName": "IMeme.TokenState", "members": [{"id": 57515, "name": "InActive", "nameLocation": "106:8:74", "nodeType": "EnumValue", "src": "106:8:74"}, {"id": 57516, "name": "Active", "nameLocation": "124:6:74", "nodeType": "EnumValue", "src": "124:6:74"}, {"id": 57517, "name": "Completed", "nameLocation": "140:9:74", "nodeType": "EnumValue", "src": "140:9:74"}, {"id": 57518, "name": "Graduated", "nameLocation": "159:9:74", "nodeType": "EnumValue", "src": "159:9:74"}], "name": "TokenState", "nameLocation": "85:10:74"}, {"id": 57529, "nodeType": "StructDefinition", "src": "204:121:74", "nodes": [], "canonicalName": "IMeme.TokenInfo", "members": [{"constant": false, "id": 57521, "mutability": "mutable", "name": "virtualX", "nameLocation": "239:8:74", "nodeType": "VariableDeclaration", "scope": 57529, "src": "231:16:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57520, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "231:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57523, "mutability": "mutable", "name": "virtualY", "nameLocation": "265:8:74", "nodeType": "VariableDeclaration", "scope": 57529, "src": "257:16:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57522, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "257:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57525, "mutability": "mutable", "name": "K", "nameLocation": "291:1:74", "nodeType": "VariableDeclaration", "scope": 57529, "src": "283:9:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57524, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "283:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57528, "mutability": "mutable", "name": "state", "nameLocation": "313:5:74", "nodeType": "VariableDeclaration", "scope": 57529, "src": "302:16:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_TokenState_$57519", "typeString": "enum IMeme.TokenState"}, "typeName": {"id": 57527, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 57526, "name": "TokenState", "nameLocations": ["302:10:74"], "nodeType": "IdentifierPath", "referencedDeclaration": 57519, "src": "302:10:74"}, "referencedDeclaration": 57519, "src": "302:10:74", "typeDescriptions": {"typeIdentifier": "t_enum$_TokenState_$57519", "typeString": "enum IMeme.TokenState"}}, "visibility": "internal"}], "name": "TokenInfo", "nameLocation": "211:9:74", "scope": 57769, "visibility": "public"}, {"id": 57541, "nodeType": "EventDefinition", "src": "331:113:74", "nodes": [], "anonymous": false, "eventSelector": "39a8d2592053becd46f6b24ac2b68a5261b4fb9726542cc377cddfa6e1dbc987", "name": "Launch", "nameLocation": "337:6:74", "parameters": {"id": 57540, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57531, "indexed": true, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "360:12:74", "nodeType": "VariableDeclaration", "scope": 57541, "src": "344:28:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57530, "name": "address", "nodeType": "ElementaryTypeName", "src": "344:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57533, "indexed": true, "mutability": "mutable", "name": "launcher", "nameLocation": "390:8:74", "nodeType": "VariableDeclaration", "scope": 57541, "src": "374:24:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57532, "name": "address", "nodeType": "ElementaryTypeName", "src": "374:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57535, "indexed": false, "mutability": "mutable", "name": "name", "nameLocation": "407:4:74", "nodeType": "VariableDeclaration", "scope": 57541, "src": "400:11:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57534, "name": "string", "nodeType": "ElementaryTypeName", "src": "400:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57537, "indexed": false, "mutability": "mutable", "name": "symbol", "nameLocation": "420:6:74", "nodeType": "VariableDeclaration", "scope": 57541, "src": "413:13:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57536, "name": "string", "nodeType": "ElementaryTypeName", "src": "413:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57539, "indexed": false, "mutability": "mutable", "name": "repoUrl", "nameLocation": "435:7:74", "nodeType": "VariableDeclaration", "scope": 57541, "src": "428:14:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57538, "name": "string", "nodeType": "ElementaryTypeName", "src": "428:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "343:100:74"}}, {"id": 57551, "nodeType": "EventDefinition", "src": "449:108:74", "nodes": [], "anonymous": false, "eventSelector": "89f5adc174562e07c9c9b1cae7109bbecb21cf9d1b2847e550042b8653c54a0e", "name": "Buy", "nameLocation": "455:3:74", "parameters": {"id": 57550, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57543, "indexed": true, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "475:12:74", "nodeType": "VariableDeclaration", "scope": 57551, "src": "459:28:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57542, "name": "address", "nodeType": "ElementaryTypeName", "src": "459:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57545, "indexed": true, "mutability": "mutable", "name": "buyer", "nameLocation": "505:5:74", "nodeType": "VariableDeclaration", "scope": 57551, "src": "489:21:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57544, "name": "address", "nodeType": "ElementaryTypeName", "src": "489:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57547, "indexed": false, "mutability": "mutable", "name": "bnbAmountIn", "nameLocation": "520:11:74", "nodeType": "VariableDeclaration", "scope": 57551, "src": "512:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57546, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "512:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57549, "indexed": false, "mutability": "mutable", "name": "tokenAmountOut", "nameLocation": "541:14:74", "nodeType": "VariableDeclaration", "scope": 57551, "src": "533:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57548, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "533:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "458:98:74"}}, {"id": 57561, "nodeType": "EventDefinition", "src": "562:110:74", "nodes": [], "anonymous": false, "eventSelector": "a082022e93cfcd9f1da5f9236718053910f7e840da080c789c7845698dc032ff", "name": "<PERSON>ll", "nameLocation": "568:4:74", "parameters": {"id": 57560, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57553, "indexed": true, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "589:12:74", "nodeType": "VariableDeclaration", "scope": 57561, "src": "573:28:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57552, "name": "address", "nodeType": "ElementaryTypeName", "src": "573:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57555, "indexed": true, "mutability": "mutable", "name": "seller", "nameLocation": "619:6:74", "nodeType": "VariableDeclaration", "scope": 57561, "src": "603:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57554, "name": "address", "nodeType": "ElementaryTypeName", "src": "603:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57557, "indexed": false, "mutability": "mutable", "name": "tokenAmountIn", "nameLocation": "635:13:74", "nodeType": "VariableDeclaration", "scope": 57561, "src": "627:21:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57556, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "627:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57559, "indexed": false, "mutability": "mutable", "name": "bnbAmountOut", "nameLocation": "658:12:74", "nodeType": "VariableDeclaration", "scope": 57561, "src": "650:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57558, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "650:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "572:99:74"}}, {"id": 57565, "nodeType": "EventDefinition", "src": "677:45:74", "nodes": [], "anonymous": false, "eventSelector": "e7e5960096a83da5467ea9b5b6f4382af1dbe347f9a7ceb4004a97fb05ed3571", "name": "Complete", "nameLocation": "683:8:74", "parameters": {"id": 57564, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57563, "indexed": true, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "708:12:74", "nodeType": "VariableDeclaration", "scope": 57565, "src": "692:28:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57562, "name": "address", "nodeType": "ElementaryTypeName", "src": "692:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "691:30:74"}}, {"id": 57575, "nodeType": "EventDefinition", "src": "727:114:74", "nodes": [], "anonymous": false, "eventSelector": "e056319f85c07bd23043dd62d7844b4e49b02561f1dc2de39b855fe83a5cc142", "name": "Graduate", "nameLocation": "733:8:74", "parameters": {"id": 57574, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57567, "indexed": true, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "758:11:74", "nodeType": "VariableDeclaration", "scope": 57575, "src": "742:27:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57566, "name": "address", "nodeType": "ElementaryTypeName", "src": "742:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57569, "indexed": true, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "787:12:74", "nodeType": "VariableDeclaration", "scope": 57575, "src": "771:28:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57568, "name": "address", "nodeType": "ElementaryTypeName", "src": "771:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57571, "indexed": false, "mutability": "mutable", "name": "bnbAmount", "nameLocation": "809:9:74", "nodeType": "VariableDeclaration", "scope": 57575, "src": "801:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57570, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "801:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57573, "indexed": false, "mutability": "mutable", "name": "tokenAmount", "nameLocation": "828:11:74", "nodeType": "VariableDeclaration", "scope": 57575, "src": "820:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57572, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "820:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "741:99:74"}}, {"id": 57579, "nodeType": "EventDefinition", "src": "846:42:74", "nodes": [], "anonymous": false, "eventSelector": "4e2b40e264d1c8353de58f6f09a8bc45ff8b68bf2ad1efef0aadab82f6e2842a", "name": "SetGraduateFee", "nameLocation": "852:14:74", "parameters": {"id": 57578, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57577, "indexed": false, "mutability": "mutable", "name": "graduateFee", "nameLocation": "875:11:74", "nodeType": "VariableDeclaration", "scope": 57579, "src": "867:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57576, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "867:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "866:21:74"}}, {"id": 57583, "nodeType": "EventDefinition", "src": "893:48:74", "nodes": [], "anonymous": false, "eventSelector": "c6c534f9881d1fd6bf41a666443461d992d15392e88c21ab297fe5eeb4a235b5", "name": "SetTradingFeeRate", "nameLocation": "899:17:74", "parameters": {"id": 57582, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57581, "indexed": false, "mutability": "mutable", "name": "tradingFeeRate", "nameLocation": "925:14:74", "nodeType": "VariableDeclaration", "scope": 57583, "src": "917:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57580, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "917:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "916:24:74"}}, {"id": 57587, "nodeType": "EventDefinition", "src": "946:38:74", "nodes": [], "anonymous": false, "eventSelector": "f6b59ffc88fbb27f33470b919e00b41139ee340eb349521f0cbbc1504ce29c3e", "name": "SetFeeTo", "nameLocation": "952:8:74", "parameters": {"id": 57586, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57585, "indexed": true, "mutability": "mutable", "name": "feeTo", "nameLocation": "977:5:74", "nodeType": "VariableDeclaration", "scope": 57587, "src": "961:21:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57584, "name": "address", "nodeType": "ElementaryTypeName", "src": "961:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "960:23:74"}}, {"id": 57591, "nodeType": "EventDefinition", "src": "989:44:74", "nodes": [], "anonymous": false, "eventSelector": "dbebfba65bd6398fb722063efc10c99f624f9cd8ba657201056af918a676d5ee", "name": "SetOperator", "nameLocation": "995:11:74", "parameters": {"id": 57590, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57589, "indexed": true, "mutability": "mutable", "name": "operator", "nameLocation": "1023:8:74", "nodeType": "VariableDeclaration", "scope": 57591, "src": "1007:24:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57588, "name": "address", "nodeType": "ElementaryTypeName", "src": "1007:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1006:26:74"}}, {"id": 57597, "nodeType": "EventDefinition", "src": "1038:74:74", "nodes": [], "anonymous": false, "eventSelector": "2d025324f0a785e8c12d0a0d91a9caa49df4ef20ff87e0df7213a1d4f3157beb", "name": "SignerUpdated", "nameLocation": "1044:13:74", "parameters": {"id": 57596, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57593, "indexed": true, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1074:9:74", "nodeType": "VariableDeclaration", "scope": 57597, "src": "1058:25:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57592, "name": "address", "nodeType": "ElementaryTypeName", "src": "1058:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57595, "indexed": true, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1101:9:74", "nodeType": "VariableDeclaration", "scope": 57597, "src": "1085:25:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57594, "name": "address", "nodeType": "ElementaryTypeName", "src": "1085:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1057:54:74"}}, {"id": 57601, "nodeType": "EventDefinition", "src": "1117:50:74", "nodes": [], "anonymous": false, "eventSelector": "5c54bff16f345578d5cf7e26e6774bb80b0cc178c5fe60e2fa4f4ff64adb1df5", "name": "SetRewardAdmin", "nameLocation": "1123:14:74", "parameters": {"id": 57600, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57599, "indexed": true, "mutability": "mutable", "name": "rewardAdmin", "nameLocation": "1154:11:74", "nodeType": "VariableDeclaration", "scope": 57601, "src": "1138:27:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57598, "name": "address", "nodeType": "ElementaryTypeName", "src": "1138:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1137:29:74"}}, {"id": 57605, "nodeType": "EventDefinition", "src": "1172:50:74", "nodes": [], "anonymous": false, "eventSelector": "6734cb46735cf306192a2f3ad3c17b0bf9f7bd108ffb437b84d5c72555b4d593", "name": "SetTokenLockup", "nameLocation": "1178:14:74", "parameters": {"id": 57604, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57603, "indexed": true, "mutability": "mutable", "name": "tokenLockup", "nameLocation": "1209:11:74", "nodeType": "VariableDeclaration", "scope": 57605, "src": "1193:27:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57602, "name": "address", "nodeType": "ElementaryTypeName", "src": "1193:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1192:29:74"}}, {"id": 57609, "nodeType": "EventDefinition", "src": "1227:44:74", "nodes": [], "anonymous": false, "eventSelector": "28dc0a2db29df837879cb24130bef67726570a58be99905b589a539bcc34969d", "name": "SetUserEligibilityCheck", "nameLocation": "1233:23:74", "parameters": {"id": 57608, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57607, "indexed": false, "mutability": "mutable", "name": "enabled", "nameLocation": "1262:7:74", "nodeType": "VariableDeclaration", "scope": 57609, "src": "1257:12:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57606, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1257:4:74", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1256:14:74"}}, {"id": 57613, "nodeType": "EventDefinition", "src": "1276:46:74", "nodes": [], "anonymous": false, "eventSelector": "58933a65d01062d01928322793a5bf43a96c1e68e5397d5bfd2eca44456502b8", "name": "SetDexRouter", "nameLocation": "1282:12:74", "parameters": {"id": 57612, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57611, "indexed": true, "mutability": "mutable", "name": "dex<PERSON><PERSON><PERSON>", "nameLocation": "1311:9:74", "nodeType": "VariableDeclaration", "scope": 57613, "src": "1295:25:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57610, "name": "address", "nodeType": "ElementaryTypeName", "src": "1295:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1294:27:74"}}, {"id": 57626, "nodeType": "FunctionDefinition", "src": "1328:139:74", "nodes": [], "functionSelector": "58d7baeb", "implemented": false, "kind": "function", "modifiers": [], "name": "getOutTokenAmount", "nameLocation": "1337:17:74", "parameters": {"id": 57618, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57615, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1363:12:74", "nodeType": "VariableDeclaration", "scope": 57626, "src": "1355:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57614, "name": "address", "nodeType": "ElementaryTypeName", "src": "1355:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57617, "mutability": "mutable", "name": "bnbAmount", "nameLocation": "1385:9:74", "nodeType": "VariableDeclaration", "scope": 57626, "src": "1377:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57616, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1377:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1354:41:74"}, "returnParameters": {"id": 57625, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57620, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57626, "src": "1443:4:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57619, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1443:4:74", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 57622, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57626, "src": "1449:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57621, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1449:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57624, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57626, "src": "1458:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57623, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1458:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1442:24:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57635, "nodeType": "FunctionDefinition", "src": "1473:97:74", "nodes": [], "functionSelector": "5a328fda", "implemented": false, "kind": "function", "modifiers": [], "name": "getInBnbAmount", "nameLocation": "1482:14:74", "parameters": {"id": 57631, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57628, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1505:12:74", "nodeType": "VariableDeclaration", "scope": 57635, "src": "1497:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57627, "name": "address", "nodeType": "ElementaryTypeName", "src": "1497:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57630, "mutability": "mutable", "name": "amountOut", "nameLocation": "1527:9:74", "nodeType": "VariableDeclaration", "scope": 57635, "src": "1519:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57629, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1519:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1496:41:74"}, "returnParameters": {"id": 57634, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57633, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57635, "src": "1561:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57632, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1561:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1560:9:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57642, "nodeType": "FunctionDefinition", "src": "1576:89:74", "nodes": [], "functionSelector": "772e3bbb", "implemented": false, "kind": "function", "modifiers": [], "name": "getInBnbAmountNotYetLaunched", "nameLocation": "1585:28:74", "parameters": {"id": 57638, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57637, "mutability": "mutable", "name": "amountOut", "nameLocation": "1622:9:74", "nodeType": "VariableDeclaration", "scope": 57642, "src": "1614:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57636, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1614:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1613:19:74"}, "returnParameters": {"id": 57641, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57640, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57642, "src": "1656:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57639, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1656:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1655:9:74"}, "scope": 57769, "stateMutability": "pure", "virtual": false, "visibility": "external"}, {"id": 57651, "nodeType": "FunctionDefinition", "src": "1671:100:74", "nodes": [], "functionSelector": "d5e42595", "implemented": false, "kind": "function", "modifiers": [], "name": "getOutBnbAmount", "nameLocation": "1680:15:74", "parameters": {"id": 57647, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57644, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1704:12:74", "nodeType": "VariableDeclaration", "scope": 57651, "src": "1696:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57643, "name": "address", "nodeType": "ElementaryTypeName", "src": "1696:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57646, "mutability": "mutable", "name": "tokenAmount", "nameLocation": "1726:11:74", "nodeType": "VariableDeclaration", "scope": 57651, "src": "1718:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57645, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1718:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1695:43:74"}, "returnParameters": {"id": 57650, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57649, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57651, "src": "1762:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57648, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1762:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1761:9:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57664, "nodeType": "FunctionDefinition", "src": "1777:147:74", "nodes": [], "functionSelector": "5212f9d9", "implemented": false, "kind": "function", "modifiers": [], "name": "getOutTokenAmountAfterFee", "nameLocation": "1786:25:74", "parameters": {"id": 57656, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57653, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1820:12:74", "nodeType": "VariableDeclaration", "scope": 57664, "src": "1812:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57652, "name": "address", "nodeType": "ElementaryTypeName", "src": "1812:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57655, "mutability": "mutable", "name": "ethAmount", "nameLocation": "1842:9:74", "nodeType": "VariableDeclaration", "scope": 57664, "src": "1834:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57654, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1834:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1811:41:74"}, "returnParameters": {"id": 57663, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57658, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57664, "src": "1900:4:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57657, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1900:4:74", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 57660, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57664, "src": "1906:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57659, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1906:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57662, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57664, "src": "1915:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57661, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1915:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1899:24:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57673, "nodeType": "FunctionDefinition", "src": "1930:108:74", "nodes": [], "functionSelector": "fd3ad650", "implemented": false, "kind": "function", "modifiers": [], "name": "getOutBnbAmountAfterFee", "nameLocation": "1939:23:74", "parameters": {"id": 57669, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57666, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "1971:12:74", "nodeType": "VariableDeclaration", "scope": 57673, "src": "1963:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57665, "name": "address", "nodeType": "ElementaryTypeName", "src": "1963:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57668, "mutability": "mutable", "name": "tokenAmount", "nameLocation": "1993:11:74", "nodeType": "VariableDeclaration", "scope": 57673, "src": "1985:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57667, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1985:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1962:43:74"}, "returnParameters": {"id": 57672, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57671, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57673, "src": "2029:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57670, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2029:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2028:9:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57680, "nodeType": "FunctionDefinition", "src": "2044:72:74", "nodes": [], "functionSelector": "da8589a2", "implemented": false, "kind": "function", "modifiers": [], "name": "progress", "nameLocation": "2053:8:74", "parameters": {"id": 57676, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57675, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "2070:12:74", "nodeType": "VariableDeclaration", "scope": 57680, "src": "2062:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57674, "name": "address", "nodeType": "ElementaryTypeName", "src": "2062:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2061:22:74"}, "returnParameters": {"id": 57679, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57678, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57680, "src": "2107:7:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57677, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2107:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2106:9:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57687, "nodeType": "FunctionDefinition", "src": "2122:65:74", "nodes": [], "functionSelector": "268d3a2f", "implemented": false, "kind": "function", "modifiers": [], "name": "userEligible", "nameLocation": "2131:12:74", "parameters": {"id": 57683, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57682, "mutability": "mutable", "name": "user", "nameLocation": "2152:4:74", "nodeType": "VariableDeclaration", "scope": 57687, "src": "2144:12:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57681, "name": "address", "nodeType": "ElementaryTypeName", "src": "2144:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2143:14:74"}, "returnParameters": {"id": 57686, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57685, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57687, "src": "2181:4:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57684, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2181:4:74", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2180:6:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57695, "nodeType": "FunctionDefinition", "src": "2193:83:74", "nodes": [], "functionSelector": "ba46ae72", "implemented": false, "kind": "function", "modifiers": [], "name": "tokenInfos", "nameLocation": "2202:10:74", "parameters": {"id": 57690, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57689, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "2221:12:74", "nodeType": "VariableDeclaration", "scope": 57695, "src": "2213:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57688, "name": "address", "nodeType": "ElementaryTypeName", "src": "2213:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2212:22:74"}, "returnParameters": {"id": 57694, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57693, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 57695, "src": "2258:16:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_TokenInfo_$57529_memory_ptr", "typeString": "struct IMeme.TokenInfo"}, "typeName": {"id": 57692, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 57691, "name": "TokenInfo", "nameLocations": ["2258:9:74"], "nodeType": "IdentifierPath", "referencedDeclaration": 57529, "src": "2258:9:74"}, "referencedDeclaration": 57529, "src": "2258:9:74", "typeDescriptions": {"typeIdentifier": "t_struct$_TokenInfo_$57529_storage_ptr", "typeString": "struct IMeme.TokenInfo"}}, "visibility": "internal"}], "src": "2257:18:74"}, "scope": 57769, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 57712, "nodeType": "FunctionDefinition", "src": "2296:248:74", "nodes": [], "functionSelector": "565d06ba", "implemented": false, "kind": "function", "modifiers": [], "name": "launch", "nameLocation": "2305:6:74", "parameters": {"id": 57710, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57697, "mutability": "mutable", "name": "creator", "nameLocation": "2329:7:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2321:15:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57696, "name": "address", "nodeType": "ElementaryTypeName", "src": "2321:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57699, "mutability": "mutable", "name": "name", "nameLocation": "2360:4:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2346:18:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57698, "name": "string", "nodeType": "ElementaryTypeName", "src": "2346:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57701, "mutability": "mutable", "name": "symbol", "nameLocation": "2388:6:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2374:20:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57700, "name": "string", "nodeType": "ElementaryTypeName", "src": "2374:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57703, "mutability": "mutable", "name": "repoUrl", "nameLocation": "2418:7:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2404:21:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57702, "name": "string", "nodeType": "ElementaryTypeName", "src": "2404:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57705, "mutability": "mutable", "name": "logo", "nameLocation": "2449:4:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2435:18:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57704, "name": "string", "nodeType": "ElementaryTypeName", "src": "2435:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57707, "mutability": "mutable", "name": "description", "nameLocation": "2477:11:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2463:25:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 57706, "name": "string", "nodeType": "ElementaryTypeName", "src": "2463:6:74", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 57709, "mutability": "mutable", "name": "signature", "nameLocation": "2511:9:74", "nodeType": "VariableDeclaration", "scope": 57712, "src": "2498:22:74", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 57708, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2498:5:74", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "2311:215:74"}, "returnParameters": {"id": 57711, "nodeType": "ParameterList", "parameters": [], "src": "2543:0:74"}, "scope": 57769, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 57719, "nodeType": "FunctionDefinition", "src": "2550:71:74", "nodes": [], "functionSelector": "cce7ec13", "implemented": false, "kind": "function", "modifiers": [], "name": "buy", "nameLocation": "2559:3:74", "parameters": {"id": 57717, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57714, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "2571:12:74", "nodeType": "VariableDeclaration", "scope": 57719, "src": "2563:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57713, "name": "address", "nodeType": "ElementaryTypeName", "src": "2563:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57716, "mutability": "mutable", "name": "bnbAmount", "nameLocation": "2593:9:74", "nodeType": "VariableDeclaration", "scope": 57719, "src": "2585:17:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57715, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2585:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2562:41:74"}, "returnParameters": {"id": 57718, "nodeType": "ParameterList", "parameters": [], "src": "2620:0:74"}, "scope": 57769, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 57728, "nodeType": "FunctionDefinition", "src": "2627:85:74", "nodes": [], "functionSelector": "6a272462", "implemented": false, "kind": "function", "modifiers": [], "name": "sell", "nameLocation": "2636:4:74", "parameters": {"id": 57726, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57721, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "2649:12:74", "nodeType": "VariableDeclaration", "scope": 57728, "src": "2641:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57720, "name": "address", "nodeType": "ElementaryTypeName", "src": "2641:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 57723, "mutability": "mutable", "name": "amountIn", "nameLocation": "2671:8:74", "nodeType": "VariableDeclaration", "scope": 57728, "src": "2663:16:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57722, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2663:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 57725, "mutability": "mutable", "name": "amountOutMin", "nameLocation": "2689:12:74", "nodeType": "VariableDeclaration", "scope": 57728, "src": "2681:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57724, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2681:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2640:62:74"}, "returnParameters": {"id": 57727, "nodeType": "ParameterList", "parameters": [], "src": "2711:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57733, "nodeType": "FunctionDefinition", "src": "2718:49:74", "nodes": [], "functionSelector": "ff6d8d05", "implemented": false, "kind": "function", "modifiers": [], "name": "graduate", "nameLocation": "2727:8:74", "parameters": {"id": 57731, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57730, "mutability": "mutable", "name": "tokenAddress", "nameLocation": "2744:12:74", "nodeType": "VariableDeclaration", "scope": 57733, "src": "2736:20:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57729, "name": "address", "nodeType": "ElementaryTypeName", "src": "2736:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2735:22:74"}, "returnParameters": {"id": 57732, "nodeType": "ParameterList", "parameters": [], "src": "2766:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57738, "nodeType": "FunctionDefinition", "src": "2773:57:74", "nodes": [], "functionSelector": "bc8b30da", "implemented": false, "kind": "function", "modifiers": [], "name": "setGraduateFee", "nameLocation": "2782:14:74", "parameters": {"id": 57736, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57735, "mutability": "mutable", "name": "newGraduateFee", "nameLocation": "2805:14:74", "nodeType": "VariableDeclaration", "scope": 57738, "src": "2797:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57734, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2797:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2796:24:74"}, "returnParameters": {"id": 57737, "nodeType": "ParameterList", "parameters": [], "src": "2829:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57743, "nodeType": "FunctionDefinition", "src": "2836:63:74", "nodes": [], "functionSelector": "1091f67c", "implemented": false, "kind": "function", "modifiers": [], "name": "setTradingFeeRate", "nameLocation": "2845:17:74", "parameters": {"id": 57741, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57740, "mutability": "mutable", "name": "newTradingFeeRate", "nameLocation": "2871:17:74", "nodeType": "VariableDeclaration", "scope": 57743, "src": "2863:25:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 57739, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2863:7:74", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2862:27:74"}, "returnParameters": {"id": 57742, "nodeType": "ParameterList", "parameters": [], "src": "2898:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57748, "nodeType": "FunctionDefinition", "src": "2905:45:74", "nodes": [], "functionSelector": "f46901ed", "implemented": false, "kind": "function", "modifiers": [], "name": "setFeeTo", "nameLocation": "2914:8:74", "parameters": {"id": 57746, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57745, "mutability": "mutable", "name": "newFeeTo", "nameLocation": "2931:8:74", "nodeType": "VariableDeclaration", "scope": 57748, "src": "2923:16:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57744, "name": "address", "nodeType": "ElementaryTypeName", "src": "2923:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2922:18:74"}, "returnParameters": {"id": 57747, "nodeType": "ParameterList", "parameters": [], "src": "2949:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57753, "nodeType": "FunctionDefinition", "src": "2956:51:74", "nodes": [], "functionSelector": "b3ab15fb", "implemented": false, "kind": "function", "modifiers": [], "name": "setOperator", "nameLocation": "2965:11:74", "parameters": {"id": 57751, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57750, "mutability": "mutable", "name": "newOperator", "nameLocation": "2985:11:74", "nodeType": "VariableDeclaration", "scope": 57753, "src": "2977:19:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57749, "name": "address", "nodeType": "ElementaryTypeName", "src": "2977:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2976:21:74"}, "returnParameters": {"id": 57752, "nodeType": "ParameterList", "parameters": [], "src": "3006:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57758, "nodeType": "FunctionDefinition", "src": "3013:57:74", "nodes": [], "functionSelector": "69567f84", "implemented": false, "kind": "function", "modifiers": [], "name": "setTokenLockup", "nameLocation": "3022:14:74", "parameters": {"id": 57756, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57755, "mutability": "mutable", "name": "newTokenLockup", "nameLocation": "3045:14:74", "nodeType": "VariableDeclaration", "scope": 57758, "src": "3037:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57754, "name": "address", "nodeType": "ElementaryTypeName", "src": "3037:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3036:24:74"}, "returnParameters": {"id": 57757, "nodeType": "ParameterList", "parameters": [], "src": "3069:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57763, "nodeType": "FunctionDefinition", "src": "3076:57:74", "nodes": [], "functionSelector": "a1b7e2e2", "implemented": false, "kind": "function", "modifiers": [], "name": "setRewardAdmin", "nameLocation": "3085:14:74", "parameters": {"id": 57761, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57760, "mutability": "mutable", "name": "newRewardAdmin", "nameLocation": "3108:14:74", "nodeType": "VariableDeclaration", "scope": 57763, "src": "3100:22:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 57759, "name": "address", "nodeType": "ElementaryTypeName", "src": "3100:7:74", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3099:24:74"}, "returnParameters": {"id": 57762, "nodeType": "ParameterList", "parameters": [], "src": "3132:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 57768, "nodeType": "FunctionDefinition", "src": "3139:56:74", "nodes": [], "functionSelector": "bd6010d6", "implemented": false, "kind": "function", "modifiers": [], "name": "setUserEligibilityCheck", "nameLocation": "3148:23:74", "parameters": {"id": 57766, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 57765, "mutability": "mutable", "name": "enabled", "nameLocation": "3177:7:74", "nodeType": "VariableDeclaration", "scope": 57768, "src": "3172:12:74", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 57764, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3172:4:74", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3171:14:74"}, "returnParameters": {"id": 57767, "nodeType": "ParameterList", "parameters": [], "src": "3194:0:74"}, "scope": 57769, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IMeme", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [57769], "name": "IMeme", "nameLocation": "68:5:74", "scope": 57770, "usedErrors": [], "usedEvents": [57541, 57551, 57561, 57565, 57575, 57579, 57583, 57587, 57591, 57597, 57601, 57605, 57609, 57613]}], "license": "MIT"}, "id": 74}