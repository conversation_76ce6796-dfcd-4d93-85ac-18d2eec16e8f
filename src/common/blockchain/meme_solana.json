{"version": "0.1.0", "name": "meme_solana", "instructions": [{"name": "initialize", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "global", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "InitGlobalParams"}}]}, {"name": "create", "accounts": [{"name": "creator", "isMut": true, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "meme", "isMut": true, "isSigner": false}, {"name": "bondingCurve", "isMut": true, "isSigner": false}, {"name": "bondingCurveVault", "isMut": true, "isSigner": false}, {"name": "metadataAccount", "isMut": true, "isSigner": false}, {"name": "bondingCurveMemeTokenAccount", "isMut": true, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "tokenMetadataProgram", "isMut": false, "isSigner": false}, {"name": "associatedTokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "CreateParams"}}]}, {"name": "buy", "accounts": [{"name": "buyer", "isMut": true, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "meme", "isMut": false, "isSigner": false}, {"name": "bondingCurve", "isMut": true, "isSigner": false}, {"name": "bondingCurveTokenAccount", "isMut": true, "isSigner": false}, {"name": "buyerTokenAccount", "isMut": true, "isSigner": false}, {"name": "bondingCurveVault", "isMut": true, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "associatedTokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "BuyParams"}}]}, {"name": "sell", "accounts": [{"name": "seller", "isMut": true, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "meme", "isMut": false, "isSigner": false}, {"name": "bondingCurve", "isMut": true, "isSigner": false}, {"name": "bondingCurveVault", "isMut": true, "isSigner": false}, {"name": "bondingCurveTokenAccount", "isMut": true, "isSigner": false}, {"name": "sellerTokenAccount", "isMut": true, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "SellParams"}}]}], "accounts": [{"name": "BondingCurve", "type": {"kind": "struct", "fields": [{"name": "bump", "type": "u8"}, {"name": "creator", "type": "public<PERSON>ey"}, {"name": "virtualSolReserve", "type": "u64"}, {"name": "virtualMemeReserve", "type": "u64"}, {"name": "realSolReserve", "type": "u64"}, {"name": "realMemeReserve", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "status", "type": {"defined": "Status"}}, {"name": "padding", "type": {"array": ["u8", 100]}}]}}, {"name": "Global", "type": {"kind": "struct", "fields": [{"name": "version", "type": "u8"}, {"name": "bump", "type": "u8"}, {"name": "admin", "type": "public<PERSON>ey"}, {"name": "feeRecipient", "type": "public<PERSON>ey"}, {"name": "dataSigner", "type": "public<PERSON>ey"}, {"name": "operator", "type": "public<PERSON>ey"}, {"name": "tradeFeeBasisPoints", "type": "u16"}, {"name": "pumpFee", "type": "u64"}, {"name": "padding", "type": {"array": ["u8", 100]}}]}}], "types": [{"name": "BuyParams", "type": {"kind": "struct", "fields": [{"name": "amountIn", "type": "u64"}, {"name": "minimumAmountOut", "type": "u64"}]}}, {"name": "CreateParams", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "ticker", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "repo<PERSON><PERSON>", "type": "string"}, {"name": "signature", "type": "string"}]}}, {"name": "InitGlobalParams", "type": {"kind": "struct", "fields": [{"name": "feeRecipient", "type": "public<PERSON>ey"}, {"name": "dataSigner", "type": "public<PERSON>ey"}, {"name": "tradeFeeBasisPoints", "type": {"option": "u16"}}]}}, {"name": "SellParams", "type": {"kind": "struct", "fields": [{"name": "amountIn", "type": "u64"}, {"name": "minimumAmountOut", "type": "u64"}]}}, {"name": "Status", "type": {"kind": "enum", "variants": [{"name": "Launched"}, {"name": "Completed"}, {"name": "Graduated"}]}}], "events": [{"name": "GlobalInitialized", "fields": [{"name": "version", "type": "u8", "index": false}, {"name": "feeRecipient", "type": "public<PERSON>ey", "index": false}, {"name": "dataSigner", "type": "public<PERSON>ey", "index": false}, {"name": "operator", "type": "public<PERSON>ey", "index": false}, {"name": "tradeFeeBasisPoints", "type": "u16", "index": false}]}, {"name": "CreateEvent", "fields": [{"name": "creator", "type": "public<PERSON>ey", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}, {"name": "createdAt", "type": "u64", "index": false}, {"name": "name", "type": "string", "index": false}, {"name": "ticker", "type": "string", "index": false}, {"name": "uri", "type": "string", "index": false}, {"name": "repo<PERSON><PERSON>", "type": "string", "index": false}]}, {"name": "TradeEvent", "fields": [{"name": "bondingCurve", "type": "public<PERSON>ey", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}, {"name": "solAmount", "type": "u64", "index": false}, {"name": "memeAmount", "type": "u64", "index": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool", "index": false}, {"name": "user", "type": "public<PERSON>ey", "index": false}, {"name": "timestamp", "type": "i64", "index": false}, {"name": "status", "type": {"defined": "Status"}, "index": false}, {"name": "virtualSolReserve", "type": "u64", "index": false}, {"name": "virtualMemeReserve", "type": "u64", "index": false}, {"name": "realSolReserve", "type": "u64", "index": false}, {"name": "realMemeReserve", "type": "u64", "index": false}]}], "errors": [{"code": 6000, "name": "InvalidFeeRecipient", "msg": "Invalid fee recipient"}, {"code": 6001, "name": "NotLaunched", "msg": "Not launched"}, {"code": 6002, "name": "LessThanMinimumSolBuyAmount", "msg": "Less than minimum sol buy amount"}, {"code": 6003, "name": "InsufficientBalance", "msg": "Insufficient balance"}, {"code": 6004, "name": "InsufficientMinimumAmountOut", "msg": "Insufficient minimum amount out"}, {"code": 6005, "name": "MoreThanMinimumAmountIn", "msg": "More than minimum amount in"}, {"code": 6006, "name": "MemeAtaOwnerError", "msg": "Meme ata owner error"}, {"code": 6007, "name": "MemeAtaMintError", "msg": "Meme ata mint error"}], "metadata": {"address": "8fmUNSYGkYxHXyuSpmvJHYVqLM5YStenHn9joi1a4P95"}}