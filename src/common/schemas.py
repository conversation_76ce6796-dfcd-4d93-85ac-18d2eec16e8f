from typing import Generic, List

from fastapi import Query
from pydantic import BaseModel

from src.common.dto import T


class Page(BaseModel, Generic[T]):
    items: List[T]
    page: int
    page_size: int
    total_items: int
    total_pages: int


class PaginationParams(BaseModel):
    page: int = Query(1, ge=1, description="Page number")
    page_size: int = Query(50, ge=1, le=100, description="Page size")
