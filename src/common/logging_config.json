{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s [%(process)d] [%(levelname)s] %(name)s: %(message)s", "datefmt": "[%Y-%m-%d %H:%M:%S %z]"}, "gunicorn": {"format": "%(asctime)s [%(process)d] [%(levelname)s] gunicorn: %(message)s", "datefmt": "[%Y-%m-%d %H:%M:%S %z]"}, "access": {"format": "%(asctime)s [%(process)d] [INFO] %(name)s: %(message)s", "datefmt": "[%Y-%m-%d %H:%M:%S %z]"}}, "handlers": {"console": {"class": "logging.StreamHandler", "formatter": "default", "stream": "ext://sys.stdout"}, "gunicorn_console": {"class": "logging.StreamHandler", "formatter": "gunicorn", "stream": "ext://sys.stdout"}, "access_console": {"class": "logging.StreamHandler", "formatter": "access", "stream": "ext://sys.stdout"}}, "loggers": {"gunicorn.error": {"handlers": ["gunicorn_console"], "level": "INFO", "propagate": false}, "gunicorn.access": {"handlers": ["access_console"], "level": "INFO", "propagate": false}, "uvicorn": {"handlers": ["console"], "level": "INFO", "propagate": false}, "uvicorn.error": {"handlers": ["console"], "level": "INFO", "propagate": false}, "uvicorn.access": {"handlers": ["access_console"], "level": "INFO", "propagate": false}}, "root": {"level": "INFO", "handlers": ["console"]}}