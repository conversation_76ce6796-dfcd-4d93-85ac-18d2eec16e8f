# Gunicorn production configuration
# This file provides centralized gunicorn configuration for all services
# Supports dynamic worker allocation based on service type

import psutil
import os

from src.common.settings import settings

# ==================== Service Classification Configuration ====================

# Core services - High traffic, need multiple workers
CORE_SERVICES = ["memecoin", "feed", "posts"]

# Medium services - Moderate traffic, 2 workers sufficient  
MEDIUM_SERVICES = ["auth", "search", "authors", "agora"]

# Lightweight services - Low traffic, single worker or use uvicorn directly
LIGHTWEIGHT_SERVICES = ["im", "admin", "notifications", "media_core", "feedback",
                        "reports", "tags", "mini_apps", "ai", "suggester"]

# Worker configuration per service type
WORKERS_CONFIG = {
    "core": settings.WORKERS_COUNT_CORE,           # 3 workers for core services
    "medium": settings.WORKERS_COUNT_MEDIUM,         # 2 workers for medium services
    "lightweight": settings.WORKERS_COUNT_LIGHTWEIGHT     # 1 worker for lightweight services
}

# Auto-detect service name from current working directory or environment
def get_service_name():
    """Auto-detect current service name"""
    # First try environment variable
    env_service = os.environ.get("SERVICE_NAME", "").strip()
    if env_service:
        return env_service
    
    # Try to detect from module path of the running app
    import sys
    for module_path in sys.argv:
        if ".main:app" in module_path:
            # Extract service name from module path like "memecoin.main:app"
            service_name = module_path.split(".main:app")[0]
            if service_name in CORE_SERVICES + MEDIUM_SERVICES + LIGHTWEIGHT_SERVICES:
                return service_name
    
    # Try to detect from current working directory
    cwd = os.getcwd()
    if "/src/" in cwd:
        # Extract service name from path like /src/memecoin or /app/src/memecoin
        parts = cwd.split("/src/")
        if len(parts) > 1:
            service_path = parts[1].split("/")[0]
            if service_path:
                return service_path
    
    # Fallback: try common patterns
    for service in CORE_SERVICES + MEDIUM_SERVICES + LIGHTWEIGHT_SERVICES:
        if service in cwd:
            return service
    
    return "unknown"

SERVICE_NAME = get_service_name()

# Determine worker count based on service classification
def get_worker_count():
    """Dynamically determine worker count based on service type"""
    if SERVICE_NAME in CORE_SERVICES:
        return WORKERS_CONFIG["core"]
    elif SERVICE_NAME in MEDIUM_SERVICES:
        return WORKERS_CONFIG["medium"]
    elif SERVICE_NAME in LIGHTWEIGHT_SERVICES:
        return WORKERS_CONFIG["lightweight"]
    else:
        # Default to 2 workers for unclassified services
        return 2

# ==================== Server Configuration ====================

# Server socket
bind = "0.0.0.0:8000"
backlog = 65535

# Worker processes - Dynamic based on service type
workers = get_worker_count()
worker_class = "uvicorn.workers.UvicornWorker"

# Connection settings - Optimized for different service types
def get_worker_connections():
    """Get worker connections based on service type"""
    if SERVICE_NAME in CORE_SERVICES:
        return 2000      # High concurrency for core services
    elif SERVICE_NAME in MEDIUM_SERVICES:
        return 1000      # Medium concurrency
    else:
        return 500       # Lower concurrency for lightweight services

worker_connections = get_worker_connections()
max_requests = 1000
max_requests_jitter = 100
preload_app = True

# Worker lifecycle
timeout = 30
keepalive = 2
graceful_timeout = 30

# Memory and file handling
worker_tmp_dir = "/dev/shm"
tmp_upload_dir = None

# Logging
loglevel = "info"
accesslog = "-"
errorlog = "-"
# logconfig = "/src/common/logging_config.json"  # Disabled - JSON format not compatible with gunicorn's INI expectation
capture_output = True
enable_stdio_inheritance = True

# Process naming
proc_name = "gunicorn"

# SSL (if needed in the future)
# keyfile = None
# certfile = None

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Performance tuning
sendfile = True
reuse_port = True

# Restart workers gracefully
max_requests_jitter = 100

# ==================== Service-specific Performance Tuning ====================

def get_timeout_settings():
    """Get timeout settings based on service type"""
    if SERVICE_NAME in CORE_SERVICES:
        # Core services may have longer running operations (WebSocket, etc.)
        return {
            "timeout": 60,           # Longer timeout for complex operations
            "keepalive": 10,         # Higher keepalive for persistent connections
            "graceful_timeout": 45   # More time for graceful shutdown
        }
    elif SERVICE_NAME in MEDIUM_SERVICES:
        return {
            "timeout": 45,
            "keepalive": 5,
            "graceful_timeout": 30
        }
    else:
        # Lightweight services - shorter timeouts
        return {
            "timeout": 30,
            "keepalive": 2,
            "graceful_timeout": 20
        }

# Apply timeout settings
_timeout_settings = get_timeout_settings()
timeout = _timeout_settings["timeout"]
keepalive = _timeout_settings["keepalive"]  
graceful_timeout = _timeout_settings["graceful_timeout"]

# ==================== Lifecycle Hooks ====================

def on_starting(server):
    """Called just before the master process is initialized."""
    service_type = "unknown"
    if SERVICE_NAME in CORE_SERVICES:
        service_type = "CORE"
    elif SERVICE_NAME in MEDIUM_SERVICES:
        service_type = "MEDIUM"
    elif SERVICE_NAME in LIGHTWEIGHT_SERVICES:
        service_type = "LIGHTWEIGHT"
    
    server.log.info(f"Gunicorn server starting - Service: {SERVICE_NAME} ({service_type})")
    server.log.info(f"Configuration - Workers: {workers}, Connections: {worker_connections}")

def on_reload(server):
    """Called to recycle workers during a reload via SIGHUP."""
    server.log.info(f"Gunicorn server reloading - Service: {SERVICE_NAME}")

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info(f"Worker {worker.pid} ({SERVICE_NAME}) received INT or QUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.info(f"Worker {worker.pid} ({SERVICE_NAME}) is being forked")

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info(f"Worker {worker.pid} ({SERVICE_NAME}) has been forked")

def post_worker_init(worker):
    """Called just after a worker has initialized the application."""
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    worker.log.info(f"Worker {worker.pid} ({SERVICE_NAME}) initialized - Memory: {memory_mb:.1f}MB")

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.info(f"Worker {worker.pid} ({SERVICE_NAME}) received ABORT signal")

def pre_exec(server):
    """Called just before a new master process is forked."""
    server.log.info(f"Gunicorn master ({SERVICE_NAME}) is about to exec new process")

def when_ready(server):
    """Called just after the server is started."""
    server.log.info(f"Gunicorn server ready - Service: {SERVICE_NAME}, Workers: {workers}")

def on_exit(server):
    """Called just before exiting."""
    server.log.info(f"Gunicorn server ({SERVICE_NAME}) is shutting down...")