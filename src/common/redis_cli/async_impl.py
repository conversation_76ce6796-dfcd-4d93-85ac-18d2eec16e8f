import json
import logging
from typing import Any, Dict, List, Optional, Union
from redis.asyncio import Redis, ConnectionPool

from src.common.settings import settings

logger = logging.getLogger(__name__)

_apool = ConnectionPool.from_url(settings.REDIS_URL, **settings.REDIS_POOL)

class AsyncRedisClient(Redis):
    """Async Redis client with utility methods and monitoring."""
    
    def __init__(self):
        """Initialize async Redis client with shared connection pool."""
        super().__init__(connection_pool=_apool)
    
    def get_pool_stats(self) -> dict:
        """Get connection pool statistics.
        
        Returns:
            Dict containing pool statistics
        """
        return {
            "created_connections": self.connection_pool.created_connections,
            "available_connections": len(self.connection_pool._available_connections),
            "in_use_connections": len(self.connection_pool._in_use_connections),
            "max_connections": self.connection_pool.max_connections,
        }
    
    async def get_info(self, section: str = None) -> dict:
        """Get Redis server information.
        
        Args:
            section: Optional section to retrieve (e.g., 'server', 'clients', 'memory')
            
        Returns:
            Dict containing Redis server info
        """
        try:
            if section:
                return await self.info(section)
            else:
                return await self.info()
        except Exception as e:
            logger.error(f"Redis info error: {e}")
            return {}
    
    async def get_memory_usage(self, key: str) -> Optional[int]:
        """Get memory usage of a specific key.
        
        Args:
            key: Redis key to check
            
        Returns:
            Memory usage in bytes, or None if error
        """
        try:
            return await self.memory_usage(key)
        except Exception as e:
            logger.error(f"Redis memory usage error for key {key}: {e}")
            return None
    
    # JSON Utility Methods
    async def get_json(self, key: str, default=None) -> Any:
        """Get and parse JSON value with error handling.
        
        Args:
            key: Redis key to fetch
            default: Default value to return if key doesn't exist or error occurs
            
        Returns:
            Parsed JSON value or default
        """
        try:
            value = await self.get(key)
            if value is None:
                return default
            
            # Decode bytes if needed
            if isinstance(value, bytes):
                value = value.decode('utf-8')
                
            return json.loads(value)
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"JSON decode error for key {key}: {e}")
            return default
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            return default
    
    async def set_json(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """Set value as JSON with optional TTL.
        
        Args:
            key: Redis key to set
            value: Python object to serialize as JSON
            ex: Optional expiry time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            json_value = json.dumps(value)
            return await self.set(key, json_value, ex=ex)
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            return False
    
    async def mget_json(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple JSON values efficiently.
        
        Args:
            keys: List of Redis keys to fetch
            
        Returns:
            Dict mapping keys to their parsed JSON values (None if not found)
        """
        try:
            values = await self.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        # Decode bytes if needed
                        if isinstance(value, bytes):
                            value = value.decode('utf-8')
                        result[key] = json.loads(value)
                    except json.JSONDecodeError:
                        logger.error(f"JSON decode error for key {key}")
                        result[key] = None
                else:
                    result[key] = None
            return result
        except Exception as e:
            logger.error(f"Redis mget error: {e}")
            return {key: None for key in keys}
    
    async def mset_json(self, mapping: Dict[str, Any]) -> bool:
        """Set multiple JSON values efficiently.
        
        Args:
            mapping: Dict mapping keys to values to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert all values to JSON
            json_mapping = {k: json.dumps(v) for k, v in mapping.items()}
            return await self.mset(json_mapping)
        except Exception as e:
            logger.error(f"Redis mset error: {e}")
            return False

    async def safe_delete(self, *keys: str) -> int:
        """Delete keys with error handling.

        Args:
            keys: Redis keys to delete

        Returns:
            Number of keys deleted, or 0 if error
        """
        try:
            return await self.delete(*keys)
        except Exception as e:
            logger.error(f"Redis delete error for keys {keys}: {e}")
            return 0
    
    # TTL Helper Methods
    async def set_with_ttl(self, key: str, value: Union[str, bytes], ttl: int) -> bool:
        """Set value with TTL helper.
        
        Args:
            key: Redis key to set
            value: Value to set
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            return await self.set(key, value, ex=ttl)
        except Exception as e:
            logger.error(f"Redis set with TTL error for key {key}: {e}")
            return False
    
    # Utility Methods
    async def health_check(self) -> bool:
        """Simple health check.
        
        Returns:
            True if Redis is healthy, False otherwise
        """
        try:
            return await self.ping()
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    async def exists_many(self, *keys: str) -> Dict[str, bool]:
        """Check existence of multiple keys.
        
        Args:
            keys: Redis keys to check
            
        Returns:
            Dict mapping keys to their existence status
        """
        try:
            result = {}
            for key in keys:
                result[key] = bool(await self.exists(key))
            return result
        except Exception as e:
            logger.error(f"Redis exists check error: {e}")
            return {key: False for key in keys}

    # Lightweight string helpers (compatible with decode_responses=True)
    async def hget_str(self, key: str, field: str) -> Optional[str]:
        """Safe HGET that returns str|None with soft-fail semantics.

        With decode_responses=True, returns str or None. If bytes are returned
        (mixed environments), decode to utf-8.
        """
        try:
            value = await self.hget(key, field)
            if value is None:
                return None
            if isinstance(value, (bytes, bytearray)):
                return value.decode("utf-8", errors="ignore")
            return value  # type: ignore[return-value]
        except Exception as e:
            logger.error(f"Redis hget error for key={key}, field={field}: {e}")
            return None

    async def hmget_str(self, key: str, fields: List[str]) -> Dict[str, Optional[str]]:
        """Safe HMGET returning a mapping of field->str|None with soft-fail semantics."""
        result: Dict[str, Optional[str]] = {f: None for f in fields}
        try:
            values = await self.hmget(key, fields)
            for field, value in zip(fields, values):
                if value is None:
                    result[field] = None
                elif isinstance(value, (bytes, bytearray)):
                    result[field] = value.decode("utf-8", errors="ignore")
                else:
                    result[field] = value  # type: ignore[assignment]
            return result
        except Exception as e:
            logger.error(f"Redis hmget error for key={key}, fields={fields}: {e}")
            return result

    async def get_token_info_from_hash(self, key_prefix: str, token_address: str) -> Dict[str, str]:
        """Fetch token basic info (symbol, image_url) from a Redis hash via HMGET.

        Args:
            key_prefix: Prefix for the Redis key namespace
            token_address: Token address string

        Returns:
            Dict with keys: symbol, image_url (empty string when missing)
        """
        fields = ["symbol", "image_url"]
        key = f"{key_prefix}:{token_address}"
        values = await self.hmget_str(key, fields)
        return {
            "symbol": values.get("symbol") or "",
            "image_url": values.get("image_url") or "",
        }

    async def batch_get_token_info_from_hash(
        self,
        key_prefix: str,
        token_addresses: List[str],
    ) -> Dict[str, Dict[str, str]]:
        """Batch fetch token basic info using pipeline HMGET to minimize RTT.

        Returns mapping: token_address -> {symbol, image_url}
        Missing values are empty strings.
        """
        if not token_addresses:
            return {}

        fields = ["symbol", "image_url"]
        keys = [f"{key_prefix}:{addr}" for addr in token_addresses]
        try:
            pipe = self.pipeline(transaction=False)
            for key in keys:
                pipe.hmget(key, fields)
            raw_results = await pipe.execute()

            result: Dict[str, Dict[str, str]] = {}
            for addr, raw in zip(token_addresses, raw_results):
                symbol_val: Optional[str]
                image_val: Optional[str]
                # raw is a list like [symbol, image_url]
                if isinstance(raw, (list, tuple)) and len(raw) == 2:
                    sym, img = raw[0], raw[1]
                    if isinstance(sym, (bytes, bytearray)):
                        symbol_val = sym.decode("utf-8", errors="ignore")
                    else:
                        symbol_val = sym
                    if isinstance(img, (bytes, bytearray)):
                        image_val = img.decode("utf-8", errors="ignore")
                    else:
                        image_val = img
                else:
                    symbol_val = None
                    image_val = None
                result[addr] = {
                    "symbol": symbol_val or "",
                    "image_url": image_val or "",
                }
            return result
        except Exception as e:
            logger.error(
                f"Redis pipeline HMGET error for prefix={key_prefix}, count={len(token_addresses)}: {e}"
            )
            return {addr: {"symbol": "", "image_url": ""} for addr in token_addresses}