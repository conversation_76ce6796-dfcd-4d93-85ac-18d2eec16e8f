from strenum import StrEnum
from enum import Enum, IntEnum

# Use this first
MEMECOIN_REDIS_ENDPOINT = "memecoin"

class CarnivalStatus(StrEnum):
    NEW = "new"
    STARTED = "started"
    VOTING = "voting"
    FINISHED = "finished"

class ContentZone(str, Enum):
    UNDECIDED = 'undecided'
    GREEN = 'green'
    YELLOW = 'yellow'
    RED = 'red'


class Environment(str, Enum):
    LOCAL = "LOCAL"
    STAGING = "STAGING"
    TESTING = "TESTING"
    PRODUCTION = "PRODUCTION"

    @property
    def is_debug(self):
        return self in (self.LOCAL, self.STAGING, self.TESTING)

    @property
    def is_testing(self):
        return self == self.TESTING

    @property
    def is_deployed(self) -> bool:
        return self in (self.STAGING, self.PRODUCTION)


class PostType(StrEnum):
    ARTICLE = "Article"
    QUESTION = "Question"
    ANSWER = "Answer"
    COLLECTION = "Collection"
    IMAGE = "Image"
    VIDEO = "Video"
    TEXT = "Text"
    COMMENT = "Comment"
    FILE = "File"
    MINI_APP = "MiniApp"

    @classmethod
    def all(cls):
        return list(map(lambda c: c.value, cls))


class PostStatus(StrEnum):
    POSTED = "posted"
    DRAFTED = "drafted"
    SCHEDULED = "scheduled"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class FeedReason(StrEnum):
    AUTHORS_LIKES = "authors_likes"
    AUTHOR_CREATE = "author_create"
    QUESTION_ANSWER = "question_answer"
    COLLECTIONS_POSTS = "collections_posts"


class CollectionContentType(StrEnum):
    MIXED = "Mixed"
    VIDEOS = "Video"
    IMAGES = "Image"
    TEXTS = "Text"
    FILES = "File"
    ARTICLES = "Article"
    DISCUSSIONS = "Discussion"


class Gender(StrEnum):
    MALE = "m"
    FEMALE = "f"
    UNDEFINED = "n"


class PermissionTypes(StrEnum):
    user = "user"
    admin = "admin"
    super_admin = "super_admin"


class UserStatus(StrEnum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    DELETED = "deleted"
    BANNED = "banned"


class ComplaintStatus(StrEnum):
    OPEN = "open"
    CLOSED = "closed"


class Region(StrEnum):
    ENGLISH = "en"
    CHINESE = "zh"
    RUSSIAN = "ru"
    OTHER = "oth"


class Language(StrEnum):
    ENGLISH = "en"
    CHINESE = "zh"
    RUSSIAN = "ru"
    GLOBAL = "global"
    OTHER = "oth"


class UserTransactionType(IntEnum):
    CREATE_MEME = 0
    SEND_TOKEN = 1
    RECEIVE_TOKEN = 2
    BUY_TOKEN = 3
    SELL_TOKEN = 4
    PAY_SUBSCRIBE = 5
    BUY_TOKEN_DEX = 6
    SELL_TOKEN_DEX = 7
    GIFT_BUY_TOKEN = 8
    WITHDRAW_CASH = 9
    DEPOSIT_CASH = 10
    PAY_CONTENT = 11  # Content purchase payment
    CREATE_L1_MEME = 12
    CREATE_L2_MEME = 13
    BRIDGE_L1_TO_L2 = 14
    COMPLETE_TOKEN = 15


class UserTransactionStatus(IntEnum):
    AWAITING_CONFIRMATION = 0
    CONFIRMED = 1
    FAILED = 2
    L1_CONFIRMED = 3
    AWAITING_L1_CONFIRMATION = 4

class PairStatus(IntEnum):
    NOT_READY = 0
    READY = 1
    FAILED = 2

class ReportStatus(StrEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"


class ContentPurchaseStatus(StrEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    CONFIRMED = "confirmed"
    REFUNDED = "refunded"
    FAILED = "failed"


# Network constants for cross-layer operations
class Network(StrEnum):
    LAYER1 = "layer1"