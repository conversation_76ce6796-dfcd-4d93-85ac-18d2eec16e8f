from typing import Dict, Any
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(extra="ignore")

    RECOMMENDER_TOKEN: str
    
    # Redis connection string, e.g. redis://user:pass@host:6379/0
    REDIS_URL: str
    REDIS_POOL: Dict[str, Any] = dict(
        decode_responses=True,
        max_connections=512,  # 先满足峰值 QPS * 平均并发命令深度
        socket_timeout=5.0,
        socket_connect_timeout=2.0,
        retry_on_timeout=True,
    )

    # Elasticsearch Configuration
    ELASTICSEARCH_URL: str = "http://meme-collector-01.aurora:9200"
    ELASTICSEARCH_USERNAME: str = "elastic"
    ELASTICSEARCH_PASSWORD: str = "mf_elk_passwd"
    ELASTICSEARCH_INDEX_PREFIX: str = "memefans"
    ELASTICSEARCH_TIMEOUT: int = 30
    ELASTICSEARCH_MAX_RETRIES: int = 3
    
    # Image proxy security settings
    IMAGE_PROXY_SECRET_KEY: str = "default-secret-change-in-production"

    # Worker configuration
    WORKERS_COUNT_CORE: int = 2
    WORKERS_COUNT_MEDIUM: int = 1
    WORKERS_COUNT_LIGHTWEIGHT: int = 1

settings = Settings()
