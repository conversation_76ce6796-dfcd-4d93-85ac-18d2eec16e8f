from strenum import StrEnum


class NotificationStatus(StrEnum):
    created = "created"
    delivered = "delivered"
    read = "read"


class CommitReadStatus(StrEnum):
    unread = "unread"
    read = "read"


class NotificationGeneralType(StrEnum):
    upvote = "upvote"
    answer = "answer"
    follow = "follow"
    subscribe = "subscribe"
    update = "update"
    comment = "comment"
    save = "save"
    commit = "commit"
    invitation = "invitation"
    translation = "translation"


class NotificationType(StrEnum):
    author_follow = "author_follow"
    author_new_post = "author_new_post"
    author_new_question = "author_new_question"
    question_subscribe = "question_subscribe"
    question_answer = "question_answer"
    question_answer_update = "question_answer_update"
    article_update = "article_update"
    post_upvote = "post_upvote"
    post_comment = "post_comment"
    post_commit = "post_commit"
    post_save = "post_save"
    collection_subscribe = "collection_subscribe"
    collection_update = "collection_update"
    invitation_accept = "invitation_accept"
    invitation_expiration = "invitation_expiration"
    commit_submit = "commit_submit"
    commit_approve = "commit_approve"
    commit_reject = "commit_reject"
    translation_created = "translation_created"
    translation_updated = "translation_updated"
    new_message = "new_message"
    follow_meme = "follow_meme"
