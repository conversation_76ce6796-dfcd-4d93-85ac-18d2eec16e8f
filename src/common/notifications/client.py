import logging
from typing import List

from httpx import AsyncClient

from src.common.notifications.enums import NotificationType


class NotificationsClient:
    """
    Client for notifications
    """

    def __init__(
        self,
        base_url: str = "http://notifications:8000"
    ):
        """
        Initializes `self`
        :param base_url: A base url for notifications `https://notifications:8000` by default
        """
        self._logger = logging.getLogger(__name__)
        self._base_url = base_url
        self.client = AsyncClient(
            base_url=base_url
        )

    @staticmethod
    def _build_request_json(
        recipients_ids: List[str],
        n_type: NotificationType,
        meta: dict,
    ):
        """
        Builds `request_json`
        :param recipients_ids:
        :param n_type:
        :param meta:
        :return:
        """
        return {
            "recipients_ids": recipients_ids,
            "notification_base": {
                "type": n_type,
                "meta": meta,
            }
        }

    async def notify(
        self,
        notification_type: NotificationType,
        recipients_ids: List[str],
        meta: dict
    ):
        """
        Notifies users
        :param notification_type: A type of notification
        :param recipients_ids:
        :param meta: Meta-information (post-id, type, etc..)
        :return:
        """

        json = self._build_request_json(
            recipients_ids,
            notification_type,
            meta
        )

        try:
            result = await self.client.post("/", json=json)
        except Exception as e:
            self._logger.error(
                f"Request to notification service failed with the following exception",
                exc_info=e
            )
            raise e

        self._logger.debug(f"Sent request to notifications with result being:\n{result}")
        return result
