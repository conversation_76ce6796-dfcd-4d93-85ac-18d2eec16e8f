"""
通用Celery客户端
提供统一的任务提交和状态查询功能，可被各个服务复用
优化要点：
1. 真正共享连接池：相同broker配置使用同一个Celery应用
2. 线程安全：使用双重检查锁定
3. 资源管理：控制连接池参数，支持优雅关闭
4. 安全性：日志中隐藏敏感信息
"""
import os
import logging
import threading
from functools import lru_cache
from typing import Any, Dict, Optional, List
from celery import Celery


# 线程锁，用于双重检查锁定
_cache_lock = threading.Lock()


def _safe_broker_url(broker_url: str) -> str:
    """
    安全地处理broker URL，隐藏敏感信息用于日志
    """
    try:
        # 简单的密码隐藏逻辑
        if '@' in broker_url:
            parts = broker_url.split('@')
            if len(parts) >= 2:
                # 提取协议和用户信息部分
                protocol_user = parts[0]
                if ':' in protocol_user:
                    protocol, user_part = protocol_user.split(':', 1)
                    if ':' in user_part:
                        user, _ = user_part.split(':', 1)
                        return f"{protocol}://{user}:***@{parts[1]}"
        return broker_url[:50] + "..." if len(broker_url) > 50 else broker_url
    except Exception:
        return broker_url[:50] + "..."


@lru_cache(maxsize=3)  # 控制最大实例数，避免连接数爆炸
def _create_celery_app(broker_url: str, result_backend: str) -> Celery:
    """
    创建Celery应用实例（真正共享连接池）
    
    Args:
        broker_url: 消息代理URL
        result_backend: 结果后端URL
        
    Returns:
        Celery: 共享的Celery应用实例
    """
    with _cache_lock:
        # 双重检查锁定，防止并发创建
        cache_info = _create_celery_app.cache_info()
        cache_key = (broker_url, result_backend)
        
        # 使用固定的app_name以实现真正的连接池共享
        celery_app = Celery("memefans_shared_app")
        celery_app.conf.update(
            broker_url=broker_url,
            result_backend=result_backend,
            task_serializer='json',
            accept_content=['json'],
            result_serializer='json',
            timezone='UTC',
            enable_utc=True,
            task_ignore_result=False,
            result_expires=3600,
            # 连接池控制
            broker_pool_limit=10,  # 限制连接池大小
            broker_heartbeat=30,   # 心跳检测
            broker_connection_retry_on_startup=True,
            broker_connection_retry=True,
            broker_connection_max_retries=3,
            # 任务路由配置
            task_routes={
                'src.worker.tasks.elasticsearch_sync.sync_*': {
                    'queue': 'elasticsearch_sync',
                    'routing_key': 'elasticsearch_sync',
                },
                'src.worker.tasks.elasticsearch_sync.queue_*': {
                    'queue': 'elasticsearch_queue',
                    'routing_key': 'elasticsearch_queue',
                },
                'src.worker.tasks.elasticsearch_sync.bulk_*': {
                    'queue': 'elasticsearch_bulk',
                    'routing_key': 'elasticsearch_bulk',
                },
                'src.worker.tasks.elasticsearch_sync.batch_*': {
                    'queue': 'elasticsearch_batch',
                    'routing_key': 'elasticsearch_batch',
                },
                'src.worker.tasks.search.*': {
                    'queue': 'search',
                    'routing_key': 'search',
                },
                'src.worker.tasks.memecoin.*': {
                    'queue': 'memecoin',
                    'routing_key': 'memecoin',
                },
                'src.worker.tasks.collection.*': {
                    'queue': 'collection',
                    'routing_key': 'collection',
                },
                'src.worker.tasks.reports.*': {
                    'queue': 'reports',
                    'routing_key': 'reports',
                },
                'src.worker.tasks.*': {
                    'queue': 'default',
                    'routing_key': 'default',
                },
            },
            # 队列定义 - 确保与worker配置一致
            task_queues={
                'elasticsearch_sync': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'elasticsearch_sync',
                    'queue_arguments': {'x-max-priority': 9},
                },
                'elasticsearch_queue': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'elasticsearch_queue',
                    'queue_arguments': {'x-max-priority': 9},
                },
                'elasticsearch_bulk': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'elasticsearch_bulk',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'elasticsearch_batch': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'elasticsearch_batch',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'search': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'search',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'memecoin': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'memecoin',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'collection': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'collection',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'reports': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'reports',
                    'queue_arguments': {'x-max-priority': 5},
                },
                'default': {
                    'exchange': 'memefans_tasks',
                    'exchange_type': 'direct',
                    'routing_key': 'default',
                    'queue_arguments': {'x-max-priority': 3},
                },
            },
            # 队列配置
            task_create_missing_queues=True,
            task_default_queue='default',
            task_default_exchange='memefans_tasks',
            task_default_exchange_type='direct',
            task_default_routing_key='default',
            # RabbitMQ传输选项 - 支持优先级队列
            broker_transport_options={
                'priority_steps': list(range(10)),  # 支持0-9优先级
                'queue_order_strategy': 'priority',
                'master_name': 'memefans_celery',
                'visibility_timeout': 3600,
            },
        )
        
        # 安全日志记录
        safe_url = _safe_broker_url(broker_url)
        logger = logging.getLogger(__name__)
        logger.info(f"创建共享Celery应用实例: broker={safe_url}, currsize={cache_info.currsize + 1}")
        
        return celery_app


def _normalize_config(broker_url: Optional[str], result_backend: Optional[str]) -> tuple:
    """
    标准化配置参数，确保缓存键的一致性
    """
    # 统一使用默认值，避免参数差异导致的缓存失效
    normalized_broker = broker_url or os.getenv("CELERY_BROKER_URL", "amqp://guest:<EMAIL>:5672//")
    normalized_backend = result_backend or os.getenv("CELERY_RESULT_BACKEND", "rpc://")
    return normalized_broker, normalized_backend


class CommonCeleryClient:
    """通用Celery客户端，提供任务提交和状态查询功能"""
    
    def __init__(self, 
                 client_name: str = "common_client",
                 broker_url: Optional[str] = None,
                 result_backend: Optional[str] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化Celery客户端
        
        Args:
            client_name: 客户端名称，用于标识不同的服务
            broker_url: 消息代理URL，默认从环境变量获取
            result_backend: 结果后端URL，默认从环境变量获取
            logger: 日志记录器，如果不提供则使用默认logger
        """
        self.client_name = client_name
        self.logger = logger or logging.getLogger(__name__)
        
        # 标准化配置
        self.broker_url, self.result_backend = _normalize_config(broker_url, result_backend)
        
        # 使用共享的Celery应用实例
        self.celery_app = _create_celery_app(self.broker_url, self.result_backend)
        
        self.logger.info(f"Celery客户端 '{client_name}' 初始化完成")
    
    def close(self):
        """
        优雅关闭客户端连接
        注意：这会影响所有使用相同broker配置的客户端
        """
        try:
            if hasattr(self.celery_app, 'close'):
                self.celery_app.close()
            self.logger.info(f"Celery客户端 '{self.client_name}' 已关闭")
        except Exception as e:
            self.logger.warning(f"关闭Celery客户端时出错: {e}")
    
    @staticmethod
    def clear_cache():
        """
        清除所有缓存
        ⚠️ 警告：仅限单线程测试场景使用！
        生产环境中可能造成悬挂连接
        """
        with _cache_lock:
            # 在清理前尝试优雅关闭
            cache_info = _create_celery_app.cache_info()
            if cache_info.currsize > 0:
                logging.getLogger(__name__).warning(
                    "清理缓存前检测到活跃连接，可能造成资源泄漏"
                )
            
            _create_celery_app.cache_clear()
            create_celery_client.cache_clear()
    
    @staticmethod
    def get_cache_info() -> Dict[str, Any]:
        """获取缓存信息"""
        celery_app_info = _create_celery_app.cache_info()
        client_info = create_celery_client.cache_info()
        
        return {
            "celery_apps": {
                "hits": celery_app_info.hits,
                "misses": celery_app_info.misses,
                "maxsize": celery_app_info.maxsize,
                "currsize": celery_app_info.currsize,
                "efficiency": celery_app_info.hits / (celery_app_info.hits + celery_app_info.misses) if (celery_app_info.hits + celery_app_info.misses) > 0 else 0
            },
            "client_instances": {
                "hits": client_info.hits,
                "misses": client_info.misses,
                "maxsize": client_info.maxsize,
                "currsize": client_info.currsize,
                "efficiency": client_info.hits / (client_info.hits + client_info.misses) if (client_info.hits + client_info.misses) > 0 else 0
            }
        }

    def send_task(self, 
                  task_name: str, 
                  args: Optional[List[Any]] = None, 
                  kwargs: Optional[Dict[str, Any]] = None,
                  queue: Optional[str] = None,
                  routing_key: Optional[str] = None,
                  **options):
        """
        发送任务到Celery队列
        
        Args:
            task_name: 完整的任务名称（如：src.worker.tasks.agora.process_gift_purchase_task）
            args: 位置参数列表
            kwargs: 关键字参数字典
            queue: 指定队列名称
            routing_key: 路由键
            **options: 其他Celery选项
            
        Returns:
            AsyncResult: Celery任务结果对象
        """
        try:
            # 准备任务参数，确保kwargs不为None
            task_kwargs = {
                'args': args or [],
                'kwargs': kwargs or {},  # 防止None传入
                **options
            }
            
            # 如果指定了队列或路由键
            if queue:
                task_kwargs['queue'] = queue
            if routing_key:
                task_kwargs['routing_key'] = routing_key
            
            # 发送任务
            result = self.celery_app.send_task(task_name, **task_kwargs)
            
            self.logger.info(f"任务已提交: task_name={task_name}, task_id={result.id}, queue={queue or 'default'}")
            return result
            
        except Exception as e:
            self.logger.error(f"任务提交失败: task_name={task_name}, error={str(e)}")
            raise

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 任务状态信息
        """
        try:
            result = self.celery_app.AsyncResult(task_id)
            status_info = {
                "task_id": task_id,
                "status": result.status,
                "result": result.result if result.ready() else None,
                "successful": result.successful() if result.ready() else None,
                "failed": result.failed() if result.ready() else None,
                "traceback": result.traceback if result.failed() else None
            }
            
            self.logger.debug(f"查询任务状态: task_id={task_id}, status={result.status}")
            return status_info
            
        except Exception as e:
            self.logger.error(f"获取任务状态失败: task_id={task_id}, error={str(e)}")
            return {
                "task_id": task_id,
                "status": "UNKNOWN",
                "error": str(e)
            }
    
    def revoke_task(self, task_id: str, terminate: bool = False) -> bool:
        """
        撤销任务
        
        Args:
            task_id: 任务ID
            terminate: 是否强制终止正在执行的任务
                      注意：需要worker启用 --pool=solo 或 gevent/threads 支持
            
        Returns:
            bool: 撤销是否成功
        """
        try:
            self.celery_app.control.revoke(task_id, terminate=terminate)
            self.logger.info(f"任务已撤销: task_id={task_id}, terminate={terminate}")
            return True
        except Exception as e:
            self.logger.error(f"撤销任务失败: task_id={task_id}, error={str(e)}")
            return False
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取活跃任务列表
        
        Returns:
            list: 活跃任务信息列表
            注意：如果worker不在线或celery events未开启，返回空列表
        """
        try:
            inspect = self.celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if not active_tasks:
                self.logger.debug("未检测到活跃任务（worker可能离线或events未开启）")
                return []
            
            all_tasks = []
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    task_info = {
                        "worker": worker,
                        "task_id": task.get("id"),
                        "task_name": task.get("name"),
                        "args": task.get("args"),
                        "kwargs": task.get("kwargs")
                    }
                    all_tasks.append(task_info)
            
            self.logger.debug(f"获取活跃任务: 共{len(all_tasks)}个任务")
            return all_tasks
            
        except Exception as e:
            self.logger.error(f"获取活跃任务失败: error={str(e)}")
            return []


class TaskSubmitter:
    """
    任务提交器 - 为特定服务提供便捷的任务提交方法
    """
    
    def __init__(self, celery_client: CommonCeleryClient, service_name: str):
        """
        初始化任务提交器
        
        Args:
            celery_client: Celery客户端实例
            service_name: 服务名称，用于日志标识
        """
        self.client = celery_client
        self.service_name = service_name
        self.logger = celery_client.logger
    
    def submit_task(self, task_name: str, *args, **kwargs) -> Any:
        """
        提交任务的便捷方法
        
        Args:
            task_name: 任务名称
            *args: 任务参数
            **kwargs: 任务关键字参数
            
        Returns:
            AsyncResult: 任务结果对象
        """
        self.logger.info(f"[{self.service_name}] 提交任务: {task_name}")
        # 确保kwargs不为None
        task_kwargs = dict(kwargs) if kwargs else {}
        return self.client.send_task(task_name, args=list(args), kwargs=task_kwargs)


@lru_cache(maxsize=20)
def create_celery_client(service_name: str, 
                        broker_url: str = "__default__", 
                        result_backend: str = "__default__") -> CommonCeleryClient:
    """
    工厂函数：为指定服务创建Celery客户端（使用LRU缓存避免重复实例化）
    
    Args:
        service_name: 服务名称
        broker_url: 消息代理URL，"__default__" 表示使用环境变量
        result_backend: 结果后端URL，"__default__" 表示使用环境变量
        
    Returns:
        CommonCeleryClient: Celery客户端实例（缓存的实例）
    """
    # 处理默认值占位符，确保缓存键的一致性
    actual_broker = None if broker_url == "__default__" else broker_url
    actual_backend = None if result_backend == "__default__" else result_backend
    
    # 创建客户端实例
    client_name = f"{service_name}_client"
    client = CommonCeleryClient(
        client_name=client_name,
        broker_url=actual_broker,
        result_backend=actual_backend,
        logger=logging.getLogger(__name__)
    )
    
    return client


def create_task_submitter(service_name: str) -> TaskSubmitter:
    """
    工厂函数：为指定服务创建任务提交器（使用缓存的客户端）
    
    Args:
        service_name: 服务名称
        
    Returns:
        TaskSubmitter: 任务提交器实例
    """
    client = create_celery_client(service_name)
    return TaskSubmitter(client, service_name)


def clear_all_caches():
    """
    清除所有缓存（Celery实例和客户端实例）
    ⚠️ 警告：仅限单线程测试场景使用！
    """
    CommonCeleryClient.clear_cache()
    
    # 清除ES同步客户端缓存
    try:
        from .elasticsearch.sync_client import clear_es_sync_cache
        clear_es_sync_cache()
    except ImportError:
        pass


def get_all_cache_info() -> Dict[str, Any]:
    """
    获取所有缓存信息（用于调试和监控）
    
    Returns:
        dict: 所有缓存的统计信息
    """
    cache_info = CommonCeleryClient.get_cache_info()
    
    # 添加ES同步客户端缓存信息
    try:
        from .elasticsearch.sync_client import get_es_sync_cache_info
        cache_info["es_sync_clients"] = get_es_sync_cache_info()._asdict()
    except ImportError:
        pass
    
    return cache_info 