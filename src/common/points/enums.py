from enum import Enum


class StrEnum(str, Enum):
    """
    `StrEnum` is `python3.11+` feature
    I don't want upgrade our python from 3.10 to 3.12
    Because I want to avoid potential backward compatibility issues.

    This class provides similar functionality.
    """

    def __str__(self):
        return str(self.value)


class PointStatus(StrEnum):
    PENDING = "pending"
    ISSUED = "issued"
    LOCKED = "locked"
    SPENT = "spent"


class PointsStatus(StrEnum):
    PENDING = "pending"
    ISSUED = "issued"
    LOCKED = "locked"
    SPENT = "spent"


class PointsAction(StrEnum):
    CONVERT = "convert"
    LIKE = "like"
    COMMENT = "comment"
    COMMIT = "commit"
    SAVE = "save"
    COMMIT_APPROVE = "commit_approve"
    MONTHLY_MOST_ACTIVE = "monthly_most_active"
    DAILY_ACTIVE = "daily_active"
    CARNIVAL_POST_VOTE = "carnival_post_vote"
    DAILY_COLLECTION_RATING = "daily_collection_rating"
    CONTENT_CREATE = "content_create"

    INVITATION_ACCEPT = "invitation_accept"
    INITIAL_BY_INVITATION = "initial_by_invitation"

    CARNIVAL_COLLECTION_COMMIT = "carnival_collection_commit"
    CARNIVAL_ACTIVITY = "carnival_activity"
    CARNIVAL_VOTE_TOP = "carnival_vote_top"
    CARNIVAL_VOTE_TOP_PERCENT = "carnival_vote_top_percent"
    CARNIVAL_TOP_POST_AUTHOR = "carnival_top_post_author"
    CARNIVAL_TOP_POST_COMMITER = "carnival_top_post_commiter"
    CARNIVAL_COLLECTION_AUTHOR = "carnival_collection_author"
