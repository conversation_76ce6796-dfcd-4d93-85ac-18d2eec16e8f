from decimal import Decimal
from typing import Optional

from fastapi import Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.base_service import BaseService
from src.common.points.enums import PointsAction, PointsStatus
from src.database.models import Point as Model
from src.database.session import get_session


class PointService(BaseService):
    model = Model

    async def add_points(
        self,
        author_id: str,
        amount: float,
        action: PointsAction,
        meta: Optional[dict] = None,
    ):
        status = PointsStatus.ISSUED if amount <= 0 else PointsStatus.PENDING
        amount = Decimal(amount).quantize(Decimal("1.00"))
        model: Model = Model(
            author_id=author_id,
            amount=amount,
            action=action,
            status=status,
            meta=meta if meta else {},
        )
        self.session.add(model)
        await self.session.flush()
        await self.session.commit()
        await self.session.refresh(model)
        return model

    @classmethod
    async def issue_pending(cls, session: AsyncSession):
        stmt = text(
            f"""
            UPDATE points
            SET status='{PointsStatus.ISSUED}'
            WHERE status='{PointsStatus.PENDING}' AND NOW() > created_at + INTERVAL '60 seconds'
        """
        )
        await session.execute(stmt)
        await session.commit()

    async def lock_points(self, author_id: str) -> None:
        stmt = text(
            f"""
            UPDATE points
            SET status='{PointsStatus.LOCKED}'
            WHERE status='{PointsStatus.ISSUED}' AND author_id='{author_id}'
        """
        )
        await self.session.execute(stmt)
        await self.session.commit()

    async def unlock_points(self, author_id: str) -> None:
        stmt = text(
            f"""
            UPDATE points
            SET status='{PointsStatus.ISSUED}'
            WHERE status='{PointsStatus.LOCKED}' AND author_id='{author_id}'
        """
        )
        await self.session.execute(stmt)
        await self.session.commit()

    async def spend_locked_points(self, author_id: str) -> None:
        stmt = text(
            f"""
            UPDATE points
            SET status='{PointsStatus.SPENT}'
            WHERE status='{PointsStatus.LOCKED}' AND author_id='{author_id}'
        """
        )
        await self.session.execute(stmt)
        await self.session.commit()

    async def get_amount(self, author_id: str) -> int:
        stmt = text(
            f"""
            SELECT SUM(amount)
            FROM points
            WHERE status='{PointsStatus.ISSUED}' AND author_id = '{author_id}'
        """
        )
        points: int = (await self.session.execute(stmt)).scalar() or 0
        return points


def get_points_service(session: AsyncSession = Depends(get_session)):
    # This dependency should be moved to `dependencies` package
    yield PointService(session)
