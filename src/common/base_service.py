from abc import abstractmethod

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session, Session


class BaseService:

    @property
    @abstractmethod
    def model(self):
        pass

    def __init__(self, session: AsyncSession = Depends(get_session)):
        self.session = session

    def get(self, id: str):
        return self.session.query(self.model).filter_by(id=id).first()

    def get_all(self):
        return self.session.query(self.model)

    def create(self, obj):
        model: self.model = self.model(**obj)
        self.session.add(model)
        self.session.commit()
        return model

    def update(self, obj):
        self.session.add(obj)
        self.session.commit()
        return obj

    def delete(self, obj):
        self.session.delete(obj)
        self.session.commit()
        return obj

    def delete_by_id(self, id: str):
        obj = self.get(id)
        return self.delete(obj)

    def delete_all(self):
        objs = self.get_all()
        for obj in objs:
            self.delete(obj)
        return objs

    def count(self):
        return self.session.query(self.model).count()

    def exists(self, id: int):
        return self.session.query(self.model).filter_by(id=id).scalar() is not None

    def exists_all(self, ids: list):
        return self.session.query(self.model).filter(
            self.model.id.in_(ids)
        ).count() == len(ids)

    def exists_any(self, ids: list):
        return self.session.query(self.model).filter(self.model.id.in_(ids)).count() > 0

    def exists_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs).scalar() is not None

    def exists_all_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs).count() == len(kwargs)

    def exists_any_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs).count() > 0

    def get_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs).first()

    def get_all_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs)

    def get_any_by(self, **kwargs):
        return self.session.query(self.model).filter_by(**kwargs).first()

    def get_all_by_ids(self, ids: list):
        return self.session.query(self.model).filter(self.model.id.in_(ids)).all()

    def get_any_by_ids(self, ids: list):
        return self.session.query(self.model).filter(self.model.id.in_(ids)).first()
