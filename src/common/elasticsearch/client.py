"""
Elasticsearch异步客户端
支持连接池管理和重试机制
"""
import asyncio
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import ConnectionError, TransportError

from src.common.settings import settings

logger = logging.getLogger(__name__)

# 全局ES客户端实例
_es_client: Optional[AsyncElasticsearch] = None


class ElasticsearchClient:
    """Elasticsearch客户端封装类"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.client = es_client
    
    async def index_document(self, index: str, document: Dict[str, Any], doc_id: str = None, pipeline: str = None) -> bool:
        """
        索引单个文档
        
        Args:
            index: 索引名称
            document: 文档内容
            doc_id: 文档ID（可选，如果不提供则自动生成）
            pipeline: ingest pipeline名称（可选）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 构建索引参数
            index_params = {
                "index": index,
                "document": document,
                "refresh": "wait_for"  # 等待刷新，确保立即可搜索
            }
            
            # 如果提供了doc_id，则使用它
            if doc_id:
                index_params["id"] = doc_id
                
            # 如果提供了pipeline，则使用它
            if pipeline:
                index_params["pipeline"] = pipeline
            
            response = await self.client.index(**index_params)
            
            result_id = response.get("_id", doc_id or "auto")
            logger.debug(f"文档索引成功: index={index}, id={result_id}, result={response['result']}")
            return True
        except Exception as e:
            logger.error(f"文档索引失败: index={index}, id={doc_id or 'auto'}, error={str(e)}")
            return False
    
    async def bulk_index_documents(self, index: str, documents: List[Dict[str, Any]], pipeline: str = None, refresh: str = "false") -> tuple[int, int]:
        """
        批量索引文档
        
        Args:
            index: 索引名称
            documents: 文档列表，每个文档需包含 '_id' 字段
            pipeline: ingest pipeline名称（可选）
            refresh: 刷新策略，默认"false"为不等待刷新
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        if not documents:
            return 0, 0
        
        try:
            # 构造批量操作请求
            actions = []
            for doc in documents:
                doc_copy = doc.copy()  # 避免修改原始文档
                doc_id = doc_copy.pop('_id')  # 提取文档ID
                
                # 构建index操作
                index_op = {"index": {"_index": index, "_id": doc_id}}
                if pipeline:
                    index_op["index"]["pipeline"] = pipeline
                    
                actions.extend([index_op, doc_copy])
            
            response = await self.client.bulk(
                operations=actions,
                refresh=refresh
            )
            
            # 统计成功和失败数量
            success_count = 0
            error_count = 0
            
            for item in response['items']:
                if 'index' in item:
                    if item['index']['status'] in [200, 201]:
                        success_count += 1
                    else:
                        error_count += 1
                        logger.warning(f"批量索引失败项: {item['index']}")
            
            logger.debug(f"批量索引完成: index={index}, 成功={success_count}, 失败={error_count}")
            if error_count > 0:
                logger.warning(f"ES批量索引有失败项: 成功={success_count}, 失败={error_count}")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"批量索引失败: index={index}, error={str(e)}")
            return 0, len(documents)
    
    async def delete_document(self, index: str, doc_id: str) -> bool:
        """
        删除文档
        
        Args:
            index: 索引名称
            doc_id: 文档ID
            
        Returns:
            bool: 是否成功
        """
        try:
            response = await self.client.delete(
                index=index,
                id=doc_id,
                refresh="wait_for"
            )
            logger.debug(f"文档删除成功: index={index}, id={doc_id}, result={response['result']}")
            return True
        except Exception as e:
            logger.error(f"文档删除失败: index={index}, id={doc_id}, error={str(e)}")
            return False
    
    async def search_documents(self, index: str, query: Dict[str, Any], 
                             size: int = 10, from_: int = 0, sort: List[Dict] = None) -> Dict[str, Any]:
        """
        搜索文档
        
        Args:
            index: 索引名称
            query: ES查询DSL
            size: 返回数量
            from_: 偏移量
            sort: 排序规则列表
            
        Returns:
            dict: ES响应结果
        """
        try:
            search_params = {
                "index": index,
                "query": query,
                "size": size,
                "from_": from_
            }
            
            # 如果提供了排序规则，则添加到搜索参数中
            if sort:
                search_params["sort"] = sort
            
            response = await self.client.search(**search_params)
            return response
        except Exception as e:
            logger.error(f"搜索失败: index={index}, error={str(e)}")
            return {"hits": {"hits": [], "total": {"value": 0}}}
    
    async def ping(self) -> bool:
        """
        检查ES连接状态
        
        Returns:
            bool: 是否连接正常
        """
        try:
            return await self.client.ping()
        except Exception as e:
            logger.error(f"ES ping失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.close()


async def create_elasticsearch_client() -> AsyncElasticsearch:
    """创建Elasticsearch客户端"""
    try:
        # 准备连接参数
        client_params = {
            "request_timeout": settings.ELASTICSEARCH_TIMEOUT,
            "max_retries": settings.ELASTICSEARCH_MAX_RETRIES,
            "retry_on_timeout": True,
            # 连接池配置
            "http_compress": True,
            "verify_certs": False,  # 开发环境可以禁用证书验证
        }
        
        # 如果提供了认证信息，则添加基本认证
        if settings.ELASTICSEARCH_USERNAME and settings.ELASTICSEARCH_PASSWORD:
            client_params["basic_auth"] = (settings.ELASTICSEARCH_USERNAME, settings.ELASTICSEARCH_PASSWORD)
        
        client = AsyncElasticsearch(
            [settings.ELASTICSEARCH_URL],
            **client_params
        )
        
        # 测试连接
        if await client.ping():
            logger.info(f"Elasticsearch连接成功: {settings.ELASTICSEARCH_URL}")
            return client
        else:
            raise ConnectionError("Elasticsearch连接失败")
            
    except Exception as e:
        logger.error(f"创建Elasticsearch客户端失败: {str(e)}")
        raise


async def get_elasticsearch_client() -> ElasticsearchClient:
    """获取全局ES客户端实例"""
    global _es_client
    
    # 检查客户端是否需要重新创建
    needs_new_client = _es_client is None
    if _es_client is not None:
        # 最新版本的 AsyncElasticsearch 没有 closed 属性
        # 我们通过尝试 ping 来检查连接状态
        try:
            await _es_client.ping()
        except Exception:
            # 如果 ping 失败，说明连接有问题，需要重新创建
            needs_new_client = True
    
    if needs_new_client:
        _es_client = await create_elasticsearch_client()
    
    return ElasticsearchClient(_es_client)


@asynccontextmanager
async def elasticsearch_context():
    """ES客户端上下文管理器"""
    client = await get_elasticsearch_client()
    try:
        yield client
    finally:
        # 注意：这里不要关闭全局客户端
        pass


async def close_elasticsearch_client():
    """关闭全局ES客户端"""
    global _es_client
    if _es_client:
        # 最新版本的 AsyncElasticsearch 没有 closed 属性
        # 直接调用 close()，它会处理已关闭的情况
        try:
            await _es_client.close()
        except Exception as e:
            logger.warning(f"关闭ES客户端时出现异常: {e}")
        _es_client = None
        logger.info("Elasticsearch客户端已关闭") 