"""
Elasticsearch模块 - 语言独立索引架构
"""

from .client import get_elasticsearch_client, ElasticsearchClient
from .indices import (
    UnifiedLanguageIndexManager,
    PostsLanguageIndexManager,
    AuthorsLanguageIndexManager,
    MemeCoinsLanguageIndexManager,
    SearchHistoryIndexManager
)
from .mappings import (
    create_posts_language_mapping,
    create_authors_language_mapping,
    create_memecoins_language_mapping,
    SEARCH_HISTORY_INDEX_MAPPING,
    LANGUAGE_DETECTION_PIPELINE,
    SUPPORTED_LANGUAGES,
    get_index_aliases,
    get_language_analyzer_settings
)
from .sync_manager import ElasticsearchSyncManager, AsyncElasticsearchSyncManager

__all__ = [
    # 客户端
    "get_elasticsearch_client",
    "ElasticsearchClient",
    
    # 语言特定索引管理器
    "UnifiedLanguageIndexManager",
    "PostsLanguageIndexManager", 
    "AuthorsLanguageIndexManager",
    "MemeCoinsLanguageIndexManager",
    "SearchHistoryIndexManager",
    
    # 映射和配置
    "create_posts_language_mapping",
    "create_authors_language_mapping",
    "create_memecoins_language_mapping",
    "SEARCH_HISTORY_INDEX_MAPPING",
    "LANGUAGE_DETECTION_PIPELINE",
    "SUPPORTED_LANGUAGES",
    "get_index_aliases",
    "get_language_analyzer_settings",
    
    # 同步管理器
    "ElasticsearchSyncManager",
    "AsyncElasticsearchSyncManager"
] 