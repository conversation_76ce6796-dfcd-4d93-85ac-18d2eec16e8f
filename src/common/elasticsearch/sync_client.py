"""
Elasticsearch同步客户端工具
提供统一的ES同步接口，供各个服务调用
"""

import logging
from functools import lru_cache
from typing import Optional

from src.common.celery_client import create_celery_client


class ElasticsearchSyncClient:
    """
    Elasticsearch同步客户端
    封装了通过Celery发送ES同步任务的逻辑
    """
    
    def __init__(self, service_name: str, logger: Optional[logging.Logger] = None):
        """
        初始化ES同步客户端
        
        Args:
            service_name: 服务名称，用于标识Celery客户端
            logger: 日志记录器，如果不提供则使用默认logger
        """
        self.service_name = service_name
        self.logger = logger or logging.getLogger(__name__)
        self._celery_client = None
    
    def _get_celery_client(self):
        """获取Celery客户端实例（懒加载）"""
        if self._celery_client is None:
            # 使用优化后的create_celery_client，它会自动处理配置标准化
            self._celery_client = create_celery_client(self.service_name)
        return self._celery_client
    
    def sync_entity(self, 
                   entity_type: str, 
                   entity_id: str, 
                   operation: str = "create", 
                   priority: int = 5,
                   queue: str = "elasticsearch_queue") -> bool:
        """
        同步实体到Elasticsearch
        
        Args:
            entity_type: 实体类型 ('Video', 'Image', 'Author', 'MemeCoin')
            entity_id: 实体ID
            operation: 操作类型 ('create', 'update', 'delete')
            priority: 优先级 (0-9, 数字越小优先级越高)
            queue: 队列名称
            
        Returns:
            bool: 是否成功发送任务
        """
        try:
            celery_client = self._get_celery_client()
            result = celery_client.send_task(
                "src.worker.tasks.elasticsearch_sync.queue_sync_entity",
                args=[entity_type, entity_id, operation],
                kwargs={"priority": priority},
                queue=queue,
                priority=priority
            )
            
            self.logger.info(
                f"ES同步任务已发送: {entity_type}#{entity_id}, "
                f"operation={operation}, priority={priority}, task_id={result.id}"
            )
            return True
            
        except Exception as e:
            self.logger.error(
                f"发送ES同步任务失败: {entity_type}#{entity_id}, "
                f"operation={operation}, error={e}"
            )
            return False
    
    def sync_video(self, video_id: str, operation: str = "create", priority: int = 5) -> bool:
        """同步Video到ES"""
        return self.sync_entity("Video", video_id, operation, priority)
    
    def sync_image(self, image_id: str, operation: str = "create", priority: int = 5) -> bool:
        """同步Image到ES"""
        return self.sync_entity("Image", image_id, operation, priority)
    
    def sync_author(self, author_id: str, operation: str = "create", priority: int = 5) -> bool:
        """同步Author到ES"""
        return self.sync_entity("Author", author_id, operation, priority)
    
    def sync_memecoin(self, memecoin_id: str, operation: str = "create", priority: int = 5) -> bool:
        """同步MemeCoin到ES"""
        return self.sync_entity("MemeCoin", memecoin_id, operation, priority)
    
    def delete_video(self, video_id: str, priority: int = 3) -> bool:
        """从ES删除Video"""
        return self.sync_entity("Video", video_id, "delete", priority)
    
    def delete_image(self, image_id: str, priority: int = 3) -> bool:
        """从ES删除Image"""
        return self.sync_entity("Image", image_id, "delete", priority)
    
    def delete_author(self, author_id: str, priority: int = 3) -> bool:
        """从ES删除Author"""
        return self.sync_entity("Author", author_id, "delete", priority)
    
    def delete_memecoin(self, memecoin_id: str, priority: int = 3) -> bool:
        """从ES删除MemeCoin"""
        return self.sync_entity("MemeCoin", memecoin_id, "delete", priority)


@lru_cache(maxsize=10)
def create_es_sync_client(service_name: str) -> ElasticsearchSyncClient:
    """
    创建ES同步客户端的便捷函数（使用LRU缓存避免重复实例化）
    
    Args:
        service_name: 服务名称
        
    Returns:
        ElasticsearchSyncClient: ES同步客户端实例（缓存的实例）
    """
    return ElasticsearchSyncClient(service_name, logging.getLogger(__name__))


def clear_es_sync_cache():
    """清除ES同步客户端缓存"""
    create_es_sync_client.cache_clear()


def get_es_sync_cache_info():
    """获取ES同步客户端缓存信息"""
    return create_es_sync_client.cache_info() 