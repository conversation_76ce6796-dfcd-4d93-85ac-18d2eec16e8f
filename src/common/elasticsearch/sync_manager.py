"""
Elasticsearch同步事件管理器
负责创建同步事件和触发数据同步
"""
import uuid
from typing import Optional, Dict, Any
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.models.SearchSyncEvent import SearchSyncEvent, SyncOperation, SyncStatus
from src.common.celery_client import CommonCeleryClient


class ElasticsearchSyncManager:
    """Elasticsearch同步管理器"""
    
    def __init__(self, session: Session, logger=None):
        self.session = session
        self.logger = logger
        self.celery_client = CommonCeleryClient("elasticsearch_sync", logger=logger)
    
    def create_sync_event(
        self,
        entity_type: str,
        entity_id: str,
        operation: SyncOperation,
        priority: int = 100,
        extra_data: Optional[Dict[str, Any]] = None,
        batch_id: Optional[str] = None
    ) -> SearchSyncEvent:
        """
        创建同步事件
        
        Args:
            entity_type: 实体类型（Video, Image, Author）
            entity_id: 实体ID
            operation: 同步操作
            priority: 优先级（数字越小优先级越高）
            extra_data: 额外数据
            batch_id: 批次ID
            
        Returns:
            SearchSyncEvent: 创建的同步事件
        """
        sync_event = SearchSyncEvent(
            entity_type=entity_type,
            entity_id=entity_id,
            operation=operation,
            priority=priority,
            extra_data=extra_data,
            batch_id=batch_id or str(uuid.uuid4())
        )
        
        self.session.add(sync_event)
        
        if self.logger:
            self.logger.debug(f"创建同步事件: {entity_type}#{entity_id}, operation={operation}")
        
        return sync_event
    
    def trigger_immediate_sync(
        self,
        entity_type: str,
        entity_id: str,
        operation: SyncOperation
    ) -> bool:
        """
        触发立即同步（强一致性场景）
        
        Args:
            entity_type: 实体类型
            entity_id: 实体ID
            operation: 同步操作
            
        Returns:
            bool: 是否成功提交任务
        """
        try:
            # 提交实时同步任务
            task_result = self.celery_client.send_task(
                task_name="src.worker.tasks.elasticsearch_sync.sync_single_entity_task",
                args=[entity_type, entity_id, operation.value]
            )
            
            if self.logger:
                self.logger.info(f"提交实时同步任务: {entity_type}#{entity_id}, task_id={task_result.id}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"提交实时同步任务失败: {entity_type}#{entity_id}, error={str(e)}")
            return False
    
    def sync_post_created(self, post_id: str, post_type: str, immediate: bool = False) -> bool:
        """
        处理post创建事件
        
        Args:
            post_id: Post ID
            post_type: Post类型
            immediate: 是否立即同步
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                # 立即同步
                return self.trigger_immediate_sync(post_type, post_id, SyncOperation.CREATE)
            else:
                # 创建同步事件
                self.create_sync_event(
                    entity_type=post_type,
                    entity_id=post_id,
                    operation=SyncOperation.CREATE,
                    priority=50  # 新建内容优先级较高
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理post创建事件失败: {post_id}, error={str(e)}")
            return False
    
    def sync_post_updated(self, post_id: str, post_type: str, immediate: bool = False) -> bool:
        """
        处理post更新事件
        
        Args:
            post_id: Post ID
            post_type: Post类型
            immediate: 是否立即同步
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync(post_type, post_id, SyncOperation.UPDATE)
            else:
                self.create_sync_event(
                    entity_type=post_type,
                    entity_id=post_id,
                    operation=SyncOperation.UPDATE,
                    priority=75  # 更新优先级中等
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理post更新事件失败: {post_id}, error={str(e)}")
            return False
    
    def sync_post_deleted(self, post_id: str, post_type: str, immediate: bool = True) -> bool:
        """
        处理post删除事件
        
        Args:
            post_id: Post ID
            post_type: Post类型
            immediate: 是否立即同步（删除通常需要立即同步）
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync(post_type, post_id, SyncOperation.DELETE)
            else:
                self.create_sync_event(
                    entity_type=post_type,
                    entity_id=post_id,
                    operation=SyncOperation.DELETE,
                    priority=25  # 删除优先级最高
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理post删除事件失败: {post_id}, error={str(e)}")
            return False
    
    def sync_author_updated(self, author_id: str, immediate: bool = False) -> bool:
        """
        处理作者更新事件
        
        Args:
            author_id: 作者ID
            immediate: 是否立即同步
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync("Author", author_id, SyncOperation.UPDATE)
            else:
                self.create_sync_event(
                    entity_type="Author",
                    entity_id=author_id,
                    operation=SyncOperation.UPDATE,
                    priority=90  # 作者更新优先级较低
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理作者更新事件失败: {author_id}, error={str(e)}")
            return False
    
    def sync_memecoin_created(self, memecoin_id: str, immediate: bool = False) -> bool:
        """
        处理代币创建事件
        
        Args:
            memecoin_id: 代币ID
            immediate: 是否立即同步
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.CREATE)
            else:
                self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.CREATE,
                    priority=60  # 代币创建优先级中等
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币创建事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    def sync_memecoin_updated(self, memecoin_id: str, immediate: bool = False) -> bool:
        """
        处理代币更新事件
        
        Args:
            memecoin_id: 代币ID
            immediate: 是否立即同步
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.UPDATE)
            else:
                self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.UPDATE,
                    priority=80  # 代币更新优先级较低
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币更新事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    def sync_memecoin_deleted(self, memecoin_id: str, immediate: bool = True) -> bool:
        """
        处理代币删除事件
        
        Args:
            memecoin_id: 代币ID
            immediate: 是否立即同步（删除通常需要立即同步）
            
        Returns:
            bool: 是否成功
        """
        try:
            if immediate:
                return self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.DELETE)
            else:
                self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.DELETE,
                    priority=30  # 删除优先级较高
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币删除事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    def bulk_sync_posts(self, post_ids: list, post_type: str, operation: SyncOperation) -> bool:
        """
        批量同步posts
        
        Args:
            post_ids: Post ID列表
            post_type: Post类型
            operation: 同步操作
            
        Returns:
            bool: 是否成功
        """
        try:
            batch_id = str(uuid.uuid4())
            
            for post_id in post_ids:
                self.create_sync_event(
                    entity_type=post_type,
                    entity_id=post_id,
                    operation=operation,
                    priority=200,  # 批量操作优先级较低
                    batch_id=batch_id
                )
            
            if self.logger:
                self.logger.info(f"创建批量同步事件: {len(post_ids)}个{post_type}, batch_id={batch_id}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"批量同步失败: {len(post_ids)}个{post_type}, error={str(e)}")
            return False
    
    def commit_events(self):
        """提交同步事件到数据库"""
        try:
            self.session.commit()
            if self.logger:
                self.logger.debug("同步事件已提交到数据库")
        except Exception as e:
            self.session.rollback()
            if self.logger:
                self.logger.error(f"提交同步事件失败: {str(e)}")
            raise


# 异步版本的同步管理器
class AsyncElasticsearchSyncManager:
    """异步Elasticsearch同步管理器"""
    
    def __init__(self, session: AsyncSession, logger=None):
        self.session = session
        self.logger = logger
        self.celery_client = CommonCeleryClient("elasticsearch_sync", logger=logger)
    
    async def create_sync_event(
        self,
        entity_type: str,
        entity_id: str,
        operation: SyncOperation,
        priority: int = 100,
        extra_data: Optional[Dict[str, Any]] = None,
        batch_id: Optional[str] = None
    ) -> SearchSyncEvent:
        """异步创建同步事件"""
        sync_event = SearchSyncEvent(
            entity_type=entity_type,
            entity_id=entity_id,
            operation=operation,
            priority=priority,
            extra_data=extra_data,
            batch_id=batch_id or str(uuid.uuid4())
        )
        
        self.session.add(sync_event)
        
        if self.logger:
            self.logger.debug(f"创建同步事件: {entity_type}#{entity_id}, operation={operation}")
        
        return sync_event
    
    async def trigger_immediate_sync(
        self,
        entity_type: str,
        entity_id: str,
        operation: SyncOperation
    ) -> bool:
        """异步触发立即同步"""
        try:
            task_result = self.celery_client.send_task(
                task_name="src.worker.tasks.elasticsearch_sync.sync_single_entity_task",
                args=[entity_type, entity_id, operation.value]
            )
            
            if self.logger:
                self.logger.info(f"提交实时同步任务: {entity_type}#{entity_id}, task_id={task_result.id}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"提交实时同步任务失败: {entity_type}#{entity_id}, error={str(e)}")
            return False
    
    async def sync_post_created(self, post_id: str, post_type: str, immediate: bool = False) -> bool:
        """异步处理post创建事件"""
        try:
            if immediate:
                return await self.trigger_immediate_sync(post_type, post_id, SyncOperation.CREATE)
            else:
                await self.create_sync_event(
                    entity_type=post_type,
                    entity_id=post_id,
                    operation=SyncOperation.CREATE,
                    priority=50
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理post创建事件失败: {post_id}, error={str(e)}")
            return False
    
    async def sync_author_updated(self, author_id: str, immediate: bool = False) -> bool:
        """异步处理作者更新事件"""
        try:
            if immediate:
                return await self.trigger_immediate_sync("Author", author_id, SyncOperation.UPDATE)
            else:
                await self.create_sync_event(
                    entity_type="Author",
                    entity_id=author_id,
                    operation=SyncOperation.UPDATE,
                    priority=90
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理作者更新事件失败: {author_id}, error={str(e)}")
            return False
    
    async def sync_memecoin_created(self, memecoin_id: str, immediate: bool = False) -> bool:
        """异步处理代币创建事件"""
        try:
            if immediate:
                return await self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.CREATE)
            else:
                await self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.CREATE,
                    priority=60
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币创建事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    async def sync_memecoin_updated(self, memecoin_id: str, immediate: bool = False) -> bool:
        """异步处理代币更新事件"""
        try:
            if immediate:
                return await self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.UPDATE)
            else:
                await self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.UPDATE,
                    priority=80
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币更新事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    async def sync_memecoin_deleted(self, memecoin_id: str, immediate: bool = True) -> bool:
        """异步处理代币删除事件"""
        try:
            if immediate:
                return await self.trigger_immediate_sync("MemeCoin", memecoin_id, SyncOperation.DELETE)
            else:
                await self.create_sync_event(
                    entity_type="MemeCoin",
                    entity_id=memecoin_id,
                    operation=SyncOperation.DELETE,
                    priority=30
                )
                return True
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"处理代币删除事件失败: {memecoin_id}, error={str(e)}")
            return False
    
    async def commit_events(self):
        """异步提交同步事件到数据库"""
        try:
            await self.session.commit()
            if self.logger:
                self.logger.debug("同步事件已提交到数据库")
        except Exception as e:
            await self.session.rollback()
            if self.logger:
                self.logger.error(f"提交同步事件失败: {str(e)}")
            raise 