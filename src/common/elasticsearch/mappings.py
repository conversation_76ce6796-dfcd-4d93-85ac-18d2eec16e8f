"""
Elasticsearch索引映射定义 - 语言独立索引架构
每种语言使用独立索引：{prefix}_posts_zh, {prefix}_posts_en, {prefix}_posts_th, {prefix}_posts_id 等
通过 ingest pipeline 自动语言识别和路由
"""

from src.common.settings import settings

# 基础分析器配置 - 每种语言的专用分析器
def get_language_analyzer_settings(language_code: str):
    """获取特定语言的分析器配置 - 仅支持中英俄三种语言"""
    analyzer_configs = {
        "zh": {
            "analysis": {
                "analyzer": {
                    "default": {
                        "type": "ik_max_word"
                    },
                    "search_analyzer": {
                        "type": "ik_smart"
                    },
                    "tag_search_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "stop"]
                    }
                }
            }
        },
        "en": {
            "analysis": {
                "analyzer": {
                    "default": {
                        "type": "standard",
                        "stopwords": "_english_"
                    },
                    "tag_search_analyzer": {
                        "type": "custom", 
                        "tokenizer": "standard",
                        "filter": ["lowercase", "stop", "porter_stem"]
                    }
                }
            }
        },
        "ru": {
            "analysis": {
                "analyzer": {
                    "default": {
                        "type": "russian"
                    },
                    "tag_search_analyzer": {
                        "type": "custom",
                        "tokenizer": "standard", 
                        "filter": ["lowercase", "stop"]
                    }
                }
            }
        }
    }
    
    # 默认配置（用于其他语言）
    default_config = {
        "analysis": {
            "analyzer": {
                "default": {
                    "type": "standard",
                    "stopwords": "_none_"
                },
                "tag_search_analyzer": {
                    "type": "custom",
                    "tokenizer": "standard",
                    "filter": ["lowercase"]
                }
            }
        }
    }
    
    return analyzer_configs.get(language_code, default_config)

# Posts索引模板 - 语言特定
def create_posts_language_mapping(language_code: str):
    """创建特定语言的Posts索引映射"""
    return {
        "settings": {
            "number_of_shards": 2,
            "number_of_replicas": 1,
            "refresh_interval": "5s",
            **get_language_analyzer_settings(language_code)
        },
        "mappings": {
            "properties": {
                # 基础标识字段
                "id": {
                    "type": "keyword"
                },
                "type": {
                    "type": "keyword"  # Video, Image
                },
                "status": {
                    "type": "keyword"
                },
                "language": {
                    "type": "keyword"  # 检测到的语言
                },
                
                # 搜索文本字段 - 使用语言特定分析器
                "description": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 512
                        }
                    }
                },
                
                # 媒体文件字段（Video和Image子类型）
                "cover": {
                    "type": "keyword",
                    "index": False  # 只存储，不索引
                },
                "url": {
                    "type": "keyword", 
                    "index": False  # 只存储，不索引（videos专用）
                },
                "height": {
                    "type": "integer",
                    "index": False  # 只存储，不索引
                },
                "width": {
                    "type": "integer",
                    "index": False  # 只存储，不索引
                },
                "images_data": {
                    "type": "flattened",
                    "index": False  # 只存储，不索引（images专用）
                },
                
                # 标签（优化的多字段映射）
                "tags": {
                    "type": "text",
                    "analyzer": "tag_search_analyzer",  # 使用专门的标签搜索分析器
                    "fields": {
                        "keyword": {
                            "type": "keyword"  # 精确匹配
                        },
                        "search": {
                            "type": "text",
                            "analyzer": "tag_search_analyzer"  # 模糊搜索
                        }
                    }
                },
                
                # 作者信息
                "author": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "keyword"
                        },
                        "name": {
                            "type": "text",
                            "fields": {
                                "keyword": {
                                    "type": "keyword",
                                    "ignore_above": 256
                                },
                                "suggest": {
                                    "type": "completion"
                                }
                            }
                        },
                        "username": {
                            "type": "keyword",
                            "fields": {
                                "text": {
                                    "type": "text"
                                }
                            }
                        },
                        "avatar": {
                            "type": "keyword",
                            "index": False
                        }
                    }
                },
                
                # 时间和地理字段
                "created_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                },
                "updated_at": {
                    "type": "date", 
                    "format": "strict_date_optional_time"
                },
                "region": {
                    "type": "keyword"
                },
                
                # 索引元数据
                "indexed_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                }
            }
        }
    }

# Authors索引模板 - 语言特定
def create_authors_language_mapping(language_code: str):
    """创建特定语言的Authors索引映射"""
    return {
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 1,
            "refresh_interval": "30s",
            **get_language_analyzer_settings(language_code)
        },
        "mappings": {
            "properties": {
                "id": {
                    "type": "keyword"
                },
                "status": {
                    "type": "keyword"
                },
                "language": {
                    "type": "keyword"
                },
                
                # 作者信息字段
                "name": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        },
                        "suggest": {
                            "type": "completion"
                        }
                    }
                },
                "username": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text"
                        },
                        "suggest": {
                            "type": "completion"
                        }
                    }
                },
                "description": {
                    "type": "text"
                },
                
                # 头像字段
                "avatar": {
                    "type": "keyword",
                    "index": False
                },
                
                # 个人信息字段
                "country": {
                    "type": "keyword"
                },
                "phone_number": {
                    "type": "keyword",
                    "index": False  # 隐私字段，不索引
                },
                "email": {
                    "type": "keyword",
                    "index": False  # 隐私字段，不索引
                },
                
                # 时间字段
                "created_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                },
                "updated_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                },
                "region": {
                    "type": "keyword"
                },
                "indexed_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                }
            }
        }
    }

# MemeCoins索引模板 - 语言特定  
def create_memecoins_language_mapping(language_code: str):
    """创建特定语言的MemeCoins索引映射"""
    return {
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 1,
            "refresh_interval": "30s",
            **get_language_analyzer_settings(language_code)
        },
        "mappings": {
            "properties": {
                "id": {
                    "type": "keyword"
                },
                "status": {
                    "type": "keyword"
                },
                "language": {
                    "type": "keyword"
                },
                
                # 代币信息字段
                "name": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        },
                        "suggest": {
                            "type": "completion"
                        }
                    }
                },
                "symbol": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text"
                        },
                        "suggest": {
                            "type": "completion"
                        }
                    }
                },
                "description": {
                    "type": "text"
                },
                "chain": {
                    "type": "keyword"  # 支持精确搜索
                },
                "dex": {
                    "type": "keyword"  # 支持精确搜索
                },
                "image_url": {
                    "type": "keyword",
                    "index": False  # 只存储，不索引
                },
                "address": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text"
                        }
                    }
                },
                "creator": {
                    "type": "keyword"
                },
                
                # 时间字段
                "created_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                },
                "updated_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                },
                "indexed_at": {
                    "type": "date",
                    "format": "strict_date_optional_time"
                }
            }
        }
    }

# 搜索历史索引 - 不分语言，统一存储
SEARCH_HISTORY_INDEX_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 1,
        "refresh_interval": "30s"
    },
    "mappings": {
        "properties": {
            "query": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 512
                    }
                }
            },
            "search_type": {
                "type": "keyword"
            },
            "language": {
                "type": "keyword"
            },
            "user_id": {
                "type": "keyword"
            },
            "results_count": {
                "type": "integer"
            },
            "ip_address": {
                "type": "ip"
            },
            "user_agent": {
                "type": "keyword",
                "index": False
            },
            "created_at": {
                "type": "date",
                "format": "strict_date_optional_time"
            }
        }
    }
}

# Ingest Pipeline 配置 - 语言识别和路由
LANGUAGE_DETECTION_PIPELINE = {
    "description": "Language detection and routing pipeline",
    "processors": [
        {
            "inference": {
                "model_id": "lang_ident_model_1",
                "target_field": "language_detection",
                "field_map": {
                    "title": "text",
                    "description": "text",
                    "text": "text"
                }
            }
        },
        {
            "script": {
                "source": """
                    String detected = ctx.language_detection.predicted_value;
                    String lang = 'other';
                    
                    // 映射检测到的语言到我们支持的语言（仅支持中英俄三种语言）
                    if (detected == 'zh' || detected == 'zh-cn' || detected == 'zh-tw') {
                        lang = 'zh';
                    } else if (detected == 'en') {
                        lang = 'en';
                    } else if (detected == 'ru') {
                        lang = 'ru';
                    }
                    // 其他所有语言都归类为 'other'
                    
                    ctx.language = lang;
                    
                    // 简单替换：memefans_posts_temp -> memefans_posts_zh
                    ctx._index = ctx._index.replace('_temp', '_' + lang);
                """
            }
        },
        {
            "remove": {
                "field": "language_detection"
            }
        }
    ]
}

# 支持的语言列表
SUPPORTED_LANGUAGES = [
    'other',  # 其他语言（作为兜底）
    'zh',  # 中文
    'en',  # 英文  
    'ru',  # 俄语
]

def get_prefixed_index_name(index_type: str, language: str = None) -> str:
    """
    获取带前缀的索引名称
    
    Args:
        index_type: 索引类型 (posts, authors, memecoins, search_history)
        language: 语言代码 (可选，search_history不需要)
        
    Returns:
        str: 完整的索引名称
    """
    prefix = settings.ELASTICSEARCH_INDEX_PREFIX
    if language:
        return f"{prefix}_{index_type}_{language}"
    else:
        return f"{prefix}_{index_type}"

# 索引别名配置
def get_index_aliases(index_type: str):
    """获取索引别名配置"""
    prefix = settings.ELASTICSEARCH_INDEX_PREFIX
    aliases = {}
    
    # 为不同类型设置不同的过滤器
    if index_type == "memecoins":
        # memecoins暂时不使用状态过滤器，避免解析问题
        # 当前索引别名（指向所有语言）
        aliases[f"{prefix}_{index_type}_current"] = {}
        
        # 按语言的别名
        for lang in SUPPORTED_LANGUAGES:
            aliases[f"{prefix}_{index_type}_{lang}_current"] = {
                "filter": {
                    "term": {
                        "language": lang
                    }
                }
            }
    else:
        # posts和authors使用posted状态过滤
        status_value = "posted"
        
        # 当前索引别名（指向所有语言）
        aliases[f"{prefix}_{index_type}_current"] = {
            "filter": {
                "term": {
                    "status": status_value
                }
            }
        }
        
        # 按语言的别名
        for lang in SUPPORTED_LANGUAGES:
            aliases[f"{prefix}_{index_type}_{lang}_current"] = {
                "filter": {
                    "bool": {
                        "must": [
                            {"term": {"status": status_value}},
                            {"term": {"language": lang}}
                        ]
                    }
                }
            }
    
    return aliases 