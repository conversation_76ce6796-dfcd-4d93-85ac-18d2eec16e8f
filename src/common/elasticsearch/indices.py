"""
Elasticsearch索引管理器 - 语言独立索引架构
每种语言使用独立索引，通过 ingest pipeline 自动路由
"""

import asyncio
from typing import Dict, List, Optional, Set
from datetime import datetime
from elasticsearch import AsyncElasticsearch
from src.common.elasticsearch.mappings import (
    create_posts_language_mapping,
    create_authors_language_mapping, 
    create_memecoins_language_mapping,
    SEARCH_HISTORY_INDEX_MAPPING,
    LANGUAGE_DETECTION_PIPELINE,
    SUPPORTED_LANGUAGES,
    get_index_aliases,
    get_prefixed_index_name
)
import logging

logger = logging.getLogger(__name__)

class BaseLanguageIndexManager:
    """语言特定索引管理器基类"""
    
    def __init__(self, es_client: AsyncElasticsearch, index_type: str):
        self.es_client = es_client
        self.index_type = index_type  # posts, authors, memecoins
        self.pipeline_name = f"{index_type}_language_detection"
        
    def _get_index_name(self, language: str) -> str:
        """获取特定语言的索引名称"""
        return get_prefixed_index_name(self.index_type, language)
    
    def _get_index_pattern(self) -> str:
        """获取索引模式"""
        return get_prefixed_index_name(self.index_type, "*")
    
    def _get_current_alias(self) -> str:
        """获取当前索引别名"""
        from src.common.settings import settings
        return f"{settings.ELASTICSEARCH_INDEX_PREFIX}_{self.index_type}_current"
    
    def _get_language_alias(self, language: str) -> str:
        """获取特定语言的索引别名"""
        from src.common.settings import settings
        return f"{settings.ELASTICSEARCH_INDEX_PREFIX}_{self.index_type}_{language}_current"
    
    async def create_pipeline(self) -> bool:
        """创建语言检测 ingest pipeline"""
        try:
            pipeline_config = LANGUAGE_DETECTION_PIPELINE.copy()
            pipeline_config["description"] = f"Language detection pipeline for {self.index_type}"
            
            await self.es_client.ingest.put_pipeline(
                id=self.pipeline_name,
                body=pipeline_config
            )
            logger.info(f"创建 ingest pipeline: {self.pipeline_name}")
            return True
        except Exception as e:
            logger.error(f"创建 ingest pipeline 失败: {e}")
            return False
    
    async def delete_pipeline(self) -> bool:
        """删除 ingest pipeline"""
        try:
            await self.es_client.ingest.delete_pipeline(id=self.pipeline_name)
            logger.info(f"删除 ingest pipeline: {self.pipeline_name}")
            return True
        except Exception as e:
            logger.warning(f"删除 ingest pipeline 失败: {e}")
            return False
    
    async def pipeline_exists(self) -> bool:
        """检查 pipeline 是否存在"""
        try:
            await self.es_client.ingest.get_pipeline(id=self.pipeline_name)
            return True
        except:
            return False
    
    async def create_language_index(self, language: str, mapping_func) -> bool:
        """创建特定语言的索引"""
        index_name = self._get_index_name(language)
        
        try:
            # 检查索引是否已存在
            if await self.es_client.indices.exists(index=index_name):
                logger.info(f"索引已存在: {index_name}")
                return True
            
            # 创建索引
            mapping = mapping_func(language)
            await self.es_client.indices.create(
                index=index_name,
                body=mapping
            )
            
            logger.info(f"创建语言索引: {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建语言索引失败 {index_name}: {e}")
            return False
    
    async def create_all_language_indices(self, mapping_func) -> Dict[str, bool]:
        """创建所有支持语言的索引"""
        results = {}
        
        # 并发创建所有语言索引
        tasks = []
        for language in SUPPORTED_LANGUAGES:
            task = self.create_language_index(language, mapping_func)
            tasks.append((language, task))
        
        for language, task in tasks:
            try:
                result = await task
                results[language] = result
            except Exception as e:
                logger.error(f"创建语言索引失败 {language}: {e}")
                results[language] = False
        
        return results
    
    async def update_aliases(self) -> bool:
        """更新索引别名"""
        try:
            aliases = get_index_aliases(self.index_type)
            alias_actions = []
            
            # 为每个语言索引添加相应别名
            for language in SUPPORTED_LANGUAGES:
                index_name = self._get_index_name(language)
                
                # 检查索引是否存在
                if await self.es_client.indices.exists(index=index_name):
                    # 添加到通用别名
                    current_alias = self._get_current_alias()
                    alias_config = {"index": index_name, "alias": current_alias}
                    
                    # 只有当别名配置包含filter时才添加
                    if current_alias in aliases and "filter" in aliases[current_alias]:
                        alias_config["filter"] = aliases[current_alias]["filter"]
                    
                    alias_actions.append({"add": alias_config})
                    
                    # 添加到语言特定别名
                    lang_alias = self._get_language_alias(language)
                    if lang_alias in aliases:
                        lang_alias_config = {"index": index_name, "alias": lang_alias}
                        
                        # 只有当别名配置包含filter时才添加
                        if "filter" in aliases[lang_alias]:
                            lang_alias_config["filter"] = aliases[lang_alias]["filter"]
                        
                        alias_actions.append({"add": lang_alias_config})
            
            if alias_actions:
                await self.es_client.indices.update_aliases(
                    body={"actions": alias_actions}
                )
                logger.info(f"更新索引别名: {self.index_type}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新索引别名失败: {e}")
            return False
    
    async def delete_all_indices(self) -> bool:
        """删除所有语言索引"""
        try:
            pattern = self._get_index_pattern()
            await self.es_client.indices.delete(index=pattern, ignore=[404])
            logger.info(f"删除索引模式: {pattern}")
            return True
        except Exception as e:
            logger.error(f"删除索引失败: {e}")
            return False
    
    async def get_indices_stats(self) -> Dict[str, Dict]:
        """获取所有语言索引的统计信息"""
        try:
            pattern = self._get_index_pattern()
            stats = await self.es_client.indices.stats(index=pattern)
            
            result = {}
            for index_name, index_stats in stats.get("indices", {}).items():
                language = index_name.split("_")[-1]
                result[language] = {
                    "docs_count": index_stats.get("total", {}).get("docs", {}).get("count", 0),
                    "store_size": index_stats.get("total", {}).get("store", {}).get("size_in_bytes", 0),
                    "health": "green"  # 简化处理
                }
            
            return result
            
        except Exception as e:
            logger.error(f"获取索引统计失败: {e}")
            return {}
    
    async def get_language_index(self, language: str) -> str:
        """获取特定语言的索引名称"""
        return self._get_index_name(language)


class PostsLanguageIndexManager(BaseLanguageIndexManager):
    """Posts 语言索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        super().__init__(es_client, "posts")
    
    async def initialize(self, force_recreate: bool = False) -> bool:
        """初始化所有 Posts 语言索引"""
        try:
            # 如果强制重建，先删除现有索引
            if force_recreate:
                await self.delete_all_indices()
                await self.delete_pipeline()
            
            # 创建 ingest pipeline
            await self.create_pipeline()
            
            # 创建所有语言索引
            results = await self.create_all_language_indices(create_posts_language_mapping)
            
            # 更新别名
            await self.update_aliases()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            logger.info(f"Posts索引初始化完成: {success_count}/{total_count}")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"Posts索引初始化失败: {e}")
            return False
    
    async def get_current_index(self) -> str:
        return self._get_current_alias()


class AuthorsLanguageIndexManager(BaseLanguageIndexManager):
    """Authors 语言索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        super().__init__(es_client, "authors")
    
    async def initialize(self, force_recreate: bool = False) -> bool:
        """初始化所有 Authors 语言索引"""
        try:
            if force_recreate:
                await self.delete_all_indices()
                await self.delete_pipeline()
            
            await self.create_pipeline()
            results = await self.create_all_language_indices(create_authors_language_mapping)
            await self.update_aliases()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            logger.info(f"Authors索引初始化完成: {success_count}/{total_count}")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"Authors索引初始化失败: {e}")
            return False
    
    async def get_current_index(self) -> str:
        return self._get_current_alias()


class MemeCoinsLanguageIndexManager(BaseLanguageIndexManager):
    """MemeCoins 语言索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        super().__init__(es_client, "memecoins")
    
    async def initialize(self, force_recreate: bool = False) -> bool:
        """初始化所有 MemeCoins 语言索引"""
        try:
            if force_recreate:
                await self.delete_all_indices()
                await self.delete_pipeline()
            
            await self.create_pipeline()
            results = await self.create_all_language_indices(create_memecoins_language_mapping)
            await self.update_aliases()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            logger.info(f"MemeCoins索引初始化完成: {success_count}/{total_count}")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"MemeCoins索引初始化失败: {e}")
            return False
    
    async def get_current_index(self) -> str:
        return self._get_current_alias()


class SearchHistoryIndexManager:
    """搜索历史索引管理器（不分语言）"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        from src.common.settings import settings
        self.index_prefix = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_search_history"
    
    def _get_current_index(self) -> str:
        """获取当前搜索历史索引"""
        return f"{self.index_prefix}_{datetime.now().strftime('%Y_%m')}"
    
    def get_search_pattern(self) -> str:
        """获取搜索历史索引模式"""
        return f"{self.index_prefix}_*"
    
    async def ensure_current_index_exists(self) -> str:
        """确保当前搜索历史索引存在"""
        current_index = self._get_current_index()
        
        try:
            if not await self.es_client.indices.exists(index=current_index):
                await self.es_client.indices.create(
                    index=current_index,
                    body=SEARCH_HISTORY_INDEX_MAPPING
                )
                logger.info(f"创建搜索历史索引: {current_index}")
            
            return current_index
            
        except Exception as e:
            logger.error(f"创建搜索历史索引失败: {e}")
            return current_index
    
    async def initialize(self, force_recreate: bool = False) -> bool:
        """初始化搜索历史索引"""
        try:
            if force_recreate:
                pattern = self.get_search_pattern()
                await self.es_client.indices.delete(index=pattern, ignore=[404])
            
            await self.ensure_current_index_exists()
            logger.info("搜索历史索引初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"搜索历史索引初始化失败: {e}")
            return False


class UnifiedLanguageIndexManager:
    """统一语言索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        self.posts_manager = PostsLanguageIndexManager(es_client)
        self.authors_manager = AuthorsLanguageIndexManager(es_client)
        self.memecoins_manager = MemeCoinsLanguageIndexManager(es_client)
        self.search_history_manager = SearchHistoryIndexManager(es_client)
    
    async def initialize_all_indices(self, force_recreate: bool = False) -> Dict[str, bool]:
        """并发初始化所有索引"""
        tasks = {
            "posts": self.posts_manager.initialize(force_recreate),
            "authors": self.authors_manager.initialize(force_recreate), 
            "memecoins": self.memecoins_manager.initialize(force_recreate),
            "search_history": self.search_history_manager.initialize(force_recreate)
        }
        
        results = {}
        for name, task in tasks.items():
            try:
                results[name] = await task
            except Exception as e:
                logger.error(f"初始化 {name} 索引失败: {e}")
                results[name] = False
        
        return results
    
    async def get_health_status(self) -> Dict[str, Dict]:
        """获取所有索引的健康状态"""
        try:
            health_status = {}
            
            # 获取各类型索引统计
            health_status["posts"] = await self.posts_manager.get_indices_stats()
            health_status["authors"] = await self.authors_manager.get_indices_stats()
            health_status["memecoins"] = await self.memecoins_manager.get_indices_stats()
            
            # 搜索历史统计
            try:
                pattern = self.search_history_manager.get_search_pattern()
                stats = await self.es_client.indices.stats(index=pattern)
                search_stats = {}
                for index_name, index_stats in stats.get("indices", {}).items():
                    search_stats[index_name] = {
                        "docs_count": index_stats.get("total", {}).get("docs", {}).get("count", 0),
                        "store_size": index_stats.get("total", {}).get("store", {}).get("size_in_bytes", 0),
                        "health": "green"
                    }
                health_status["search_history"] = search_stats
            except:
                health_status["search_history"] = {}
            
            return health_status
            
        except Exception as e:
            logger.error(f"获取健康状态失败: {e}")
            return {}
    
    async def delete_all_indices(self) -> bool:
        """删除所有索引"""
        try:
            tasks = [
                self.posts_manager.delete_all_indices(),
                self.authors_manager.delete_all_indices(),
                self.memecoins_manager.delete_all_indices()
            ]
            
            # 删除搜索历史索引
            pattern = self.search_history_manager.get_search_pattern()
            tasks.append(
                self.es_client.indices.delete(index=pattern, ignore=[404])
            )
            
            await asyncio.gather(*tasks)
            logger.info("删除所有索引完成")
            return True
            
        except Exception as e:
            logger.error(f"删除所有索引失败: {e}")
            return False


 