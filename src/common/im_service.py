import os
import j<PERSON>
from typing import List, Optional
from uuid import uuid4

import asyncio
from httpx import AsyncClient, Timeout, ReadTimeout, ConnectTimeout
from pydantic import BaseModel
from redis.asyncio import Redis
from sqlalchemy import text, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.settings import settings
from src.database.models.TociIm import TociIm
from src.common.redis_cli import RedisCli


class RegisterResult(BaseModel):
    toci_id: str
    im_id: str
    im_login: str
    im_password: str


class LoginResult(BaseModel):
    im_token: str
    chat_token: str
    id: str


class ImService:
    def __init__(self, session: AsyncSession, redis_client: Optional[Redis] = None):
        # Should we move `AsyncClient` build to separated dependency?

        self.chat_client = AsyncClient(
            base_url=os.environ["IM_URL"]
        )
        self.server_client = AsyncClient(
            base_url=os.environ["IM_SERVER_URL"]
        )
        self.session = session
        if redis_client is not None:
            self.redis = redis_client
        else:
            self.redis = RedisCli.async_()
        self.admin_secret = os.environ.get("IM_ADMIN_SECRET")
        self.admin_user_id = os.environ.get("IM_ADMIN_USER_ID")

    async def register(self, toci_id: str, username: str = None, avatar_url: str = "") -> RegisterResult:
        login = await self.generate_login()
        password = await self.generate_password()
        
        nickname = username if username else login

        response = await self.chat_client.post(
            url="/account/register",
            headers={
                "operationID": str(uuid4())
            },
            json={
                "autoLogin": True,
                "platform": 1,
                "user": {
                    "areaCode": "+86",
                    "faceURL": avatar_url,
                    "nickname": nickname,
                    "password": password,
                    "phoneNumber": login
                },
                "verifyCode": "666666"
            }
        )

        data = response.json()['data']

        result = RegisterResult(
            toci_id=toci_id,
            im_id=data['userID'],
            im_login=login,
            im_password=password
        )

        # /account/register无法带上ex信息，所以需要单独更新
        await self.update_user_info(
            im_id=data['userID'],
            ex={"user_id": toci_id}
        )

        return result

    async def generate_login(self) -> str:
        return str((await self.session.execute(text("SELECT nextval('short_id_seq') AS next_login"))).scalar())

    @staticmethod
    async def generate_password() -> str:
        return "password_123"

    async def get_by_im_batch(self, im_ids: str):
        stmt = select(TociIm).where(TociIm.im_id.in_(im_ids.split(",")[:50])).limit(50)
        results = await self.session.execute(stmt)

        users: List[TociIm] = results.scalars().all()  # type: ignore

        ids_dict = {}
        for u in users:
            ids_dict[u.im_id] = u.toci_id

        return ids_dict

    async def login(self, login: str, password: str) -> LoginResult:
        response = await self.chat_client.post(
            url="/account/login",
            headers={
                "operationID": str(uuid4())
            },
            json={
                "platform": 1,
                "areaCode": "+86",
                "password": password,
                "phoneNumber": login
            }
        )

        data = response.json()['data']

        return LoginResult(
            im_token=data['imToken'],
            chat_token=data['chatToken'],
            id=data['userID']
        )

    async def update_user_info(self, im_id: str, nickname: str = None, face_url: str = None, ex: dict = None) -> bool:
        """
        更新用户信息（头像、名称、扩展字段）
        
        Args:
            im_id: IM系统的用户ID
            nickname: 用户名称（可选）
            face_url: 用户头像URL（可选）
            ex: 扩展字段字典，将被序列化为JSON字符串（可选）
            
        Returns:
            bool: 更新是否成功
        """
        # 先获取管理员令牌
        admin_token = await self.get_admin_token()
        
        # 构建请求数据，只包含非None的字段
        user_info = {"userID": im_id}
        
        if nickname is not None:
            user_info["nickname"] = nickname
        if face_url is not None:
            user_info["faceURL"] = face_url
        if ex is not None:
            user_info["ex"] = json.dumps(ex, ensure_ascii=False)

        backoffs = [0.3, 0.8]
        for attempt in range(len(backoffs) + 1):
            try:
                response = await self.server_client.post(
                    url="/user/update_user_info_ex",
                    headers={
                        "operationID": str(uuid4()),
                        "token": admin_token
                    },
                    json={
                        "userInfo": user_info
                    },
                    timeout=Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0)
                )

                response_data = response.json()
                return response_data.get('errCode', -1) == 0
            except (ReadTimeout, ConnectTimeout):
                if attempt < len(backoffs):
                    await asyncio.sleep(backoffs[attempt])
                    continue
                return False

    async def get_admin_token(self) -> str:
        """
        获取管理员令牌（带Redis缓存）
        
        Args:
            secret: OpenIM secret，默认为 openIM123
            user_id: APP管理员ID，默认为 imAdmin
            
        Returns:
            AdminTokenResult: 包含令牌和过期时间的结果
        """
        cache_key = f"openim:admin_token:{self.admin_user_id}"
        
        cached_token = await self.redis.get(cache_key)
        if cached_token:
            return cached_token
        
        # 缓存中没有，请求新的令牌
        response = await self.server_client.post(
            url="/auth/get_admin_token",
            headers={
                "operationID": str(uuid4())
            },
            json={
                "secret": self.admin_secret,
                "userID": self.admin_user_id
            }
        )

        data = response.json()['data']

        token = data['token']
        expire_time_seconds = int(data['expireTimeSeconds'])

        cache_ttl = max(expire_time_seconds - 600, 0)
        await self.redis.setex(cache_key, cache_ttl, token)
        
        return token

    async def sync_author_to_im(self, author) -> bool:
        """
        同步作者信息到 IM 系统
        
        Args:
            author: Author 模型实例
            
        Returns:
            bool: 同步是否成功
        """
        try:
            # 获取作者的 IM ID
            stmt = select(TociIm).where(TociIm.toci_id == author.id)
            result = await self.session.execute(stmt)
            toci_im = result.scalar_one_or_none()
            
            if not toci_im:
                # 如果用户没有 IM 账号，跳过同步
                return True
            
            # 准备同步的数据
            nickname = author.name if author.name else None
            face_url = author.avatar if author.avatar else None
            ex = {"user_id": author.id, "username": author.username}
            
            # 调用 update_user_info 方法同步到 IM 系统
            success = await self.update_user_info(
                im_id=toci_im.im_id,
                nickname=nickname,
                face_url=face_url,
                ex=ex
            )
            
            return success
            
        except Exception as e:
            # 记录错误但不抛出异常，避免影响主流程
            import logging
            logging.error(f"Failed to sync author {author.id} to IM: {str(e)}")
            return False

    async def aclose(self) -> None:
        try:
            if hasattr(self, "chat_client") and self.chat_client is not None:
                await self.chat_client.aclose()
        except Exception:
            pass
        try:
            if hasattr(self, "server_client") and self.server_client is not None:
                await self.server_client.aclose()
        except Exception:
            pass
        try:
            if hasattr(self, "redis") and self.redis is not None:
                # Try different close variants defensively
                try:
                    await self.redis.close()
                except TypeError:
                    try:
                        self.redis.close()
                    except Exception:
                        pass
                except Exception:
                    pass
                try:
                    pool = getattr(self.redis, "connection_pool", None)
                    if pool and hasattr(pool, "disconnect"):
                        res = pool.disconnect()
                        if asyncio.iscoroutine(res):
                            await res
                except Exception:
                    pass
        except Exception:
            pass
