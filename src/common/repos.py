from typing import Callable, Any

from math import ceil
from sqlalchemy import Select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import noload

from src.common.dto import Page, T


class BaseRepository:
    """
    Base repository class.

    It has initialized `AsyncSession` object & ready to use `Pagination`
    """

    def __init__(
        self,
        session: AsyncSession,
    ):
        """
        Initializes `self`
        :param session: An `AsyncSession` object
        """
        self._session = session

    async def _paginate(
        self,
        query: Select,
        page: int,
        page_size: int,
        mapper: Callable[[Any], T],
        *args_to_mapper,
    ) -> Page[T]:

        total = await self._session.scalar(
            query.order_by(None)
            .options(noload("*"))
            .with_only_columns(func.count(), maintain_column_froms=True)
        )
        q = query.offset((page - 1) * page_size).limit(page_size)
        items = [
            mapper(item, *args_to_mapper)
            for item in (await self._session.execute(q)).unique().scalars().all()
        ]
        total_pages = ceil(total / page_size)

        return Page(
            items=items,
            total_pages=total_pages,
            page=page,
            page_size=page_size,
            total_items=total,
        )
