from fastapi import Depends, HTTPException, status
from sqlalchemy import delete, select, and_, func, insert, or_
from sqlalchemy.orm import Session
from datetime import timedelta, datetime

from src.database.models.LimitedVisit import LimitedVisit
from src.database.models.User import User
from src.database.models.Permission import Permission
from src.auth import current_user
from src.database.session import AsyncSession, get_session

from .constants import PermissionTypes
from .im_service import ImService


class RateLimiter:
    def __init__(self, amount: int, time_range: timedelta, endpoint: str):
        self.amount = amount
        self.time_range = time_range
        self.endpoint = endpoint

    async def __call__(
        self,
        user: User = Depends(current_user),
        session: AsyncSession = Depends(get_session),
    ):
        stmt = (
            select(func.count())
            .select_from(LimitedVisit)
            .where(
                and_(
                    LimitedVisit.expires_at > datetime.now(),
                    LimitedVisit.endpoint == self.endpoint,
                    LimitedVisit.user_id == user.id,
                )
            )
        )
        visit_amount = await session.scalar(stmt)
        if visit_amount >= self.amount:
            raise HTTPException(
                status.HTTP_429_TOO_MANY_REQUESTS, "Rate limit exceeded"
            )
        stmt = insert(LimitedVisit).values(
            endpoint=self.endpoint,
            expires_at=datetime.now() + self.time_range,
            user_id=user.id,
        )
        await session.execute(stmt)
        await session.commit()
        return user

    @classmethod
    async def delete_expired(cls, session: AsyncSession):
        statement = delete(LimitedVisit).where(LimitedVisit.expires_at < datetime.now())
        await session.execute(statement)
        await session.commit()


async def admin(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
) -> User:
    stmt = select(Permission).where(
        Permission.author_id == user.id,
        or_(
            Permission.name == PermissionTypes.admin,
            Permission.name == PermissionTypes.super_admin,
        ),
    )
    permission = (await session.execute(stmt)).first()
    if not permission:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)
    return user


async def super_admin(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
) -> User:
    stmt = select(Permission).where(
        Permission.author_id == user.id,
        Permission.name == PermissionTypes.super_admin
    )
    permission = (await session.execute(stmt)).first()
    if not permission:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)
    return user


async def is_current_user_admin(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
) -> bool:
    """
    Returns `True` if current user is administrator of platform.
    :param user:
    :param session:
    :return:
    """
    stmt = select(Permission).where(
        Permission.author_id == user.id,
        or_(
            Permission.name == PermissionTypes.admin,
            Permission.name == PermissionTypes.super_admin,
        ),
    )
    permission = (await session.execute(stmt)).first()

    if not permission:
        return False

    return True


def get_im_service(
    session: AsyncSession = Depends(get_session),
):
    """Initializes `ImService` object"""
    return ImService(
        session,
    )
