import logging
import random
from typing import Any, Literal

from httpx import AsyncClient

from fastapi import HTT<PERSON>Ex<PERSON>, status
from fastapi_pagination import Params

from src.database.models import Author
from src.database.schemas.Author import RecommenderAuthor
from src.common.constants import Language
from src.database.constants import PostType, Region

from src.common.recommender.schemas import Interaction, CommitSchema, ClickSchema
from src.common.settings import settings
from src.database.models import Image, Video

logger = logging.getLogger("Recommender-client")


class RecommenderClient:
    def __init__(self, recommender_token: str):
        self.client = AsyncClient(
            headers={"X-Token": recommender_token},
            base_url="http://toci-dev-01.aurora:9200/",
            trust_env=False
        )

    @staticmethod
    async def get_post_from_model(post: Any):
        schema = {
            "id": post.id,
            "type": post.type,
            "author_id": post.author_id,
            "status": post.status,
            "region": post.region,
            "language": post.language,
            "description": getattr(post, 'description', None),
            "created_at": post.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "tags": post.tags
        }
        return schema

    async def get_user_recommendations(
            self,
            user_id: str,
            params: Params,
            post_type: list[PostType] | None,
            region: Region | None,
            languages: list[Language] | None = None,
            random_factor: float | None = None,
    ) -> dict:
        await self.update_all_cache() # TODO: remove this
        type_str = ""
        for pt in post_type:
            type_str += f"&post_type={pt}"
        region_str = f"&region={region}" if region else ''
        languages_str = ''.join([f"&languages={language}" for language in languages]) if languages else ''
        random_factor_str = f"&random_factor={random_factor}"
        size_str = f"&size={params.size}"
        page_str = f"&page={params.page}"
        if user_id:
            url = f"recall?user_id={user_id}{size_str}{page_str}{type_str}{region_str}{languages_str}{random_factor_str}"
        else:
            url = f"recall?{size_str}{page_str}{type_str}{region_str}{languages_str}{random_factor_str}"

        response: dict = await self.get(url)
        logger.debug(response)
        return response

    async def get_similar_posts(
            self,
            post_id: str,
            params: Params,
            post_type: PostType | None,
            region: Region | None,
            languages: list[Language] | None = None,
    ) -> list[str]:
        type_str = f"&post_type={post_type}" if post_type else ''
        region_str = f"&region={region}" if region else ''
        languages_str = ''.join([f"&languages={language}" for language in languages]) if languages else ''
        url = f"posts/{post_id}?size={params.size}&page={params.page}{type_str}{region_str}{languages_str}"
        response: list[str] = await self.get(url)
        logger.debug(response)
        return response

    async def get_cache_status(self) -> dict:
        response: dict = await self.get(url="cache/status")
        logger.debug(response)
        return response

    async def update_hot_post_cache(self, post_type: PostType | None = None, language: Language | None = None) -> None:
        data = {    
            "post_type": post_type,
            "language": language,
            "force_refresh": True,
        }
        await self.post(url="/cache/update/hot-posts", data=data)
    
    async def update_new_content_cache(self) -> None:
        data = {    
            "force_refresh": True,
        }
        await self.post(url="/cache/update/new-content", data=data)

    async def update_all_cache(self) -> None:
        data = {    
            "force_refresh": True,
        }
        await self.post(url="/cache/update/all", data=data)
    
    async def clear_cache(self, cache_type: Literal["hot_posts", "new_content", "token_recall", "all"] = "all", post_type: PostType | None = None, language: Language | None = None) -> None:
        url = f"cache/clear?cache_type={cache_type}"
        if post_type:
            url += f"&post_type={post_type}"
        if language:
            url += f"&language={language}"
        await self.delete(url=url)
    
    async def sync_update_all_cache(self) -> None:
        await self.post(url="/cache/update/all/sync", data={})

    async def get_post_by_tag(self, tag: str, count: int = 200) -> list[str]:
        response: dict = await self.get(url=f"tag-recall/posts/{tag}?limit={count}")
        logger.debug(response)
        return response["post_ids"]
    
    async def get_tag_recall_stats(self) -> dict:
        response: dict = await self.get(url=f"tag-recall/stats")
        logger.debug(response)
        return response
    
    async def compute_tag_recall(self, sync: bool = False):
        url = f"tag-recall/compute"
        if sync:
            url += "/sync"
        await self.post(url=url, data={})

    async def add_post(self, post: Any) -> None:
        data = await self.get_post_from_model(post)
        await self.post(url=f"posts", data=data)
    
    async def update_post(self, post: Any) -> None:
        data = await self.get_post_from_model(post)
        await self.put(url=f"posts", data=data)

    async def add_user(self, user: Author) -> None:
        await self.post(url=f"users", data=self.dict_from_author_model(user))

    async def update_user(self, user_data: dict) -> None:
        await self.put(url=f"users", data=user_data)

    async def delete_user(self, user_id: str) -> None:
        await self.delete(url=f"users/{user_id}")

    async def delete_post(self, post_id: str) -> None:
        await self.delete(url=f"posts/{post_id}")

    async def add_impression(self, impression: Interaction) -> None:
        await self.post(url="metrics/passing_read", data=self.dict_from_interaction(impression))

    async def add_view(self, view: Interaction) -> None:
        await self.post(url="metrics/intentional_read", data=self.dict_from_interaction(view))

    async def add_share(self, share: Interaction) -> None:
        await self.post(url="metrics/share", data=self.dict_from_interaction(share))

    async def add_commit(self, commit: CommitSchema) -> None:
        await self.post(url="metrics/commit", data=self.dict_from_interaction(commit))

    async def add_click(self, click: ClickSchema) -> None:
        await self.post(url="metrics/click", data=self.dict_from_interaction(click))

    @staticmethod
    def dict_from_interaction(interaction: Interaction) -> dict:
        res = interaction.dict()
        res["timestamp"] = interaction.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        return res

    @staticmethod
    def dict_from_author_model(author: Author):
        return RecommenderAuthor.from_orm(author).dict()

    async def get(self, url: str) -> list | dict:
        try:
            response = await self.client.get(url=url)
            if response.status_code >= 300:
                logger.error(f"Request to recommender failed with status code {response.status_code}\n"
                             f"Response details:\n{response}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Recommender request failed"
                )
            return response.json()
        except Exception as e:
            logger.error("Recommender request failed")
            logger.error(e)
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Recommender request failed")

    async def post(self, url: str, data: dict | None = None) -> list | dict | None:
        try:
            response = await self.client.post(url=url, json=data)
            if not (200 <= response.status_code < 300):
                logger.error(f"Request to recommender failed with status code {response.status_code}\n"
                             f"Response details:\n{response}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Recommender request failed"
                )
            return response.json()
        except Exception as e:
            logger.error("Recommender request failed")
            logger.error(e)

    async def put(self, url: str, data: dict | None = None) -> list | dict | None:
        try:
            response = await self.client.put(url=url, json=data)
            if response.status_code >= 300:
                logger.error(f"Request to recommender failed with status code {response.status_code}\n"
                             f"Response details:\n{response}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Recommender request failed"
                )
            return response.json()
        except Exception as e:
            logger.error("Recommender request failed")
            logger.error(e)

    async def delete(self, url: str) -> list | dict:
        try:
            response = await self.client.delete(url=url)
            if response.status_code >= 300:
                logger.error(f"Request to recommender failed with status code {response.status_code}\n"
                             f"Response details:\n{response}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Recommender request failed"
                )
            return response.json()
        except Exception as e:
            logger.error("Recommender request failed")
            logger.error(e)

    async def add_like(self, user_id: str, post_id: str) -> None:
        await self.post(url=f"users/{user_id}/like/{post_id}")

    async def remove_like(self, user_id: str, post_id: str) -> None:
        await self.delete(url=f"users/{user_id}/like/{post_id}")

async def get_recommender_client() -> RecommenderClient | None:
    """
    Factory function to create a RecommenderClient instance.
    This is useful for dependency injection in FastAPI.
    Returns None if RECOMMENDER_TOKEN is not configured.
    """
    if not hasattr(settings, 'RECOMMENDER_TOKEN') or not settings.RECOMMENDER_TOKEN:
        logger.warning("RECOMMENDER_TOKEN not configured, recommender client will be disabled")
        return None
    return RecommenderClient(recommender_token=settings.RECOMMENDER_TOKEN)