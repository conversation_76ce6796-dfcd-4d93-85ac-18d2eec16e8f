from typing import Any
from datetime import datetime

from pydantic import BaseModel, Field
from .enums import ClickSource


class Interaction(BaseModel):
    user_id: str
    post_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class CommitSchema(Interaction):
    collection_id: str


class ClickSchema(Interaction):
    source: ClickSource
    current_recommendations: list[str] | None = None
