"""
Holdview Cache Management - Redis caching for holdview operations

This module provides both internal cache management for HoldviewService
and direct cache invalidation functions that services can call directly.
"""

import json
import logging
from typing import Optional, Dict, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.redis_cli.async_impl import AsyncRedisClient

logger = logging.getLogger(__name__)

# Cache key prefixes
POST_TOKEN_PREFIX = "holdview:post_token:"
USER_HOLDINGS_PREFIX = "holdview:user_holdings:"
POST_TOKEN_BATCH_PREFIX = "holdview:post_batch:"

# Cache TTL (Time To Live) in seconds
POST_TOKEN_TTL = 3600  # 1 hour
USER_HOLDINGS_TTL = 1800  # 30 minutes
BATCH_TTL = 1800  # 30 minutes


class HoldviewCache:
    """
    Internal cache management class for HoldviewService.
    
    This class handles caching for:
    - Post token addresses (L1 cache)
    - User token holdings (L3 cache) 
    - Batch operations
    """
    
    def __init__(self, redis_client: Optional[AsyncRedisClient], session: Optional[AsyncSession]):
        self.redis_client = redis_client
        self.session = session
    
    async def get_post_token(self, post_id: str) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        Get cached post token address.
        
        Args:
            post_id: Post ID to look up
            
        Returns:
            Tuple[bool, Optional[str]]: (cache_hit, token_address)
        """
        if not self.redis_client:
            return False, None
        
        try:
            cache_key = f"{POST_TOKEN_PREFIX}{post_id}"
            cached_value = await self.redis_client.get(cache_key)
            
            if cached_value is not None:
                # Handle both None values and actual token addresses
                token_address = None if cached_value == "None" else cached_value
                logger.debug(f"Cache hit for post token {post_id}: {token_address}")
                return True, token_address
            
            return False, None
            
        except Exception as e:
            logger.error(f"Error getting cached post token for {post_id}: {str(e)}")
            return False, None
    
    async def set_post_token(self, post_id: str, token_address: Optional[str]) -> None:
        """
        Cache post token address.
        
        Args:
            post_id: Post ID
            token_address: Token address (None if no token)
        """
        if not self.redis_client:
            return
        
        try:
            cache_key = f"{POST_TOKEN_PREFIX}{post_id}"
            # Store "None" string for None values to distinguish from cache misses
            cache_value = "None" if token_address is None else token_address
            await self.redis_client.set(cache_key, cache_value, ex=POST_TOKEN_TTL)
            
            logger.debug(f"Cached post token {post_id}: {token_address}")
            
        except Exception as e:
            logger.error(f"Error caching post token for {post_id}: {str(e)}")
    
    async def get_user_holdings(self, user_id: str) -> Optional[Dict[str, int]]:
        """
        Get cached user token holdings.
        
        Args:
            user_id: User ID to look up
            
        Returns:
            Optional[Dict[str, int]]: Token holdings or None if cache miss
        """
        if not self.redis_client:
            return None
        
        try:
            cache_key = f"{USER_HOLDINGS_PREFIX}{user_id}"
            cached_value = await self.redis_client.get(cache_key)
            
            if cached_value is not None:
                holdings = json.loads(cached_value)
                logger.debug(f"Cache hit for user holdings {user_id}: {len(holdings)} tokens")
                return holdings
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached user holdings for {user_id}: {str(e)}")
            return None
    
    async def set_user_holdings(self, user_id: str, holdings: Dict[str, int]) -> None:
        """
        Cache user token holdings.
        
        Args:
            user_id: User ID
            holdings: Dictionary of token_address -> amount
        """
        if not self.redis_client:
            return
        
        try:
            cache_key = f"{USER_HOLDINGS_PREFIX}{user_id}"
            cache_value = json.dumps(holdings)
            await self.redis_client.set(cache_key, cache_value, ex=USER_HOLDINGS_TTL)
            
            logger.debug(f"Cached user holdings {user_id}: {len(holdings)} tokens")
            
        except Exception as e:
            logger.error(f"Error caching user holdings for {user_id}: {str(e)}")
    
    async def batch_get_post_tokens(self, post_ids: List[str]) -> Dict[str, Optional[str]]:
        """
        Get cached token addresses for multiple posts.
        
        Args:
            post_ids: List of post IDs to look up
            
        Returns:
            Dict[str, Optional[str]]: Dictionary of post_id -> token_address for cache hits only
        """
        if not self.redis_client or not post_ids:
            return {}
        
        try:
            cache_keys = [f"{POST_TOKEN_PREFIX}{post_id}" for post_id in post_ids]
            cached_values = await self.redis_client.mget(cache_keys)
            
            results = {}
            for post_id, cached_value in zip(post_ids, cached_values):
                if cached_value is not None:
                    # Handle both None values and actual token addresses
                    token_address = None if cached_value == "None" else cached_value
                    results[post_id] = token_address
            
            logger.debug(f"Batch cache hit for {len(results)}/{len(post_ids)} post tokens")
            return results
            
        except Exception as e:
            logger.error(f"Error getting cached post tokens (batch): {str(e)}")
            return {}
    
    async def batch_set_post_tokens(self, post_token_map: Dict[str, Optional[str]]) -> None:
        """
        Cache token addresses for multiple posts.
        
        Args:
            post_token_map: Dictionary of post_id -> token_address
        """
        if not self.redis_client or not post_token_map:
            return
        
        try:
            # Use pipeline for efficiency
            pipe = self.redis_client.pipeline()
            
            for post_id, token_address in post_token_map.items():
                cache_key = f"{POST_TOKEN_PREFIX}{post_id}"
                # Store "None" string for None values to distinguish from cache misses
                cache_value = "None" if token_address is None else token_address
                pipe.set(cache_key, cache_value, ex=POST_TOKEN_TTL)
            
            await pipe.execute()
            
            logger.debug(f"Batch cached {len(post_token_map)} post tokens")
            
        except Exception as e:
            logger.error(f"Error caching post tokens (batch): {str(e)}")


# Direct cache invalidation functions for external services
async def invalidate_post_cache(post_id: str, redis_client: Optional[AsyncRedisClient] = None) -> None:
    """
    Direct function to invalidate post token cache.
    Call this when a post's collection assignment changes.
    
    Args:
        post_id: Post ID to invalidate
        redis_client: Redis client (will be auto-injected if None)
    """
    try:
        client = redis_client or AsyncRedisClient()
        cache_key = f"{POST_TOKEN_PREFIX}{post_id}"
        await client.delete(cache_key)
        
        logger.info(f"Invalidated post cache for {post_id}")
        
    except Exception as e:
        logger.error(f"Error invalidating post cache for {post_id}: {str(e)}")


async def invalidate_user_balance_cache(user_id: str, token_address: Optional[str] = None, redis_client: Optional[AsyncRedisClient] = None) -> None:
    """
    Direct function to invalidate user balance cache.
    Call this when a user's token balance changes.
    
    Args:
        user_id: User ID to invalidate
        token_address: Specific token address (unused for now, but available for future optimization)
        redis_client: Redis client (will be auto-injected if None)
    """

    try:
        client = redis_client or AsyncRedisClient()
        cache_key = f"{USER_HOLDINGS_PREFIX}{user_id}"
        await client.delete(cache_key)
        
        logger.info(f"Invalidated user balance cache for {user_id}")
        
    except Exception as e:
        logger.error(f"Error invalidating user balance cache for {user_id}: {str(e)}")


async def invalidate_post_batch_cache(post_ids: List[str], redis_client: Optional[AsyncRedisClient] = None) -> None:
    """
    Direct function to invalidate multiple post caches.
    Useful for batch operations or when multiple posts are affected.
    
    Args:
        post_ids: List of post IDs to invalidate
        redis_client: Redis client (will be auto-injected if None)
    """
    if not post_ids:
        return
    
    try:
        client = redis_client or AsyncRedisClient()
        cache_keys = [f"{POST_TOKEN_PREFIX}{post_id}" for post_id in post_ids]
        
        if cache_keys:
            await client.delete(*cache_keys)
        
        logger.info(f"Invalidated post cache for {len(post_ids)} posts")
        
    except Exception as e:
        logger.error(f"Error invalidating post batch cache: {str(e)}")