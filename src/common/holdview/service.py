"""
HoldviewService - Core service for USD-based paid content access control

This service provides both single and batch operations for checking access to paid content.
Access is granted based on content purchase history rather than token holdings.
It includes Redis caching integration and efficient database queries.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Optional, List, Dict
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.constants import MEMECOIN_REDIS_ENDPOINT, ContentPurchaseStatus
from src.database.models import Post, User, Pair
from src.database.models.SavedPosts import SavedPost
from src.database.models.TokenHolder import TokenHolder
from src.database.models.ContentPurchase import ContentPurchase
from src.common.holdview.cache import HoldviewCache
from src.common.image_utils import ImageTransformUtils
from fastapi import HTTPException, status
from src.common.redis_cli.async_impl import AsyncRedisClient

logger = logging.getLogger(__name__)


class HoldviewService:
    """
    Centralized service for USD-based paid content access control.
    
    Features:
    - Single post access checking
    - Batch operations for feed scenarios
    - Redis caching integration
    - Purchase-based verification using ContentPurchase records
    """
    
    def __init__(self, session: AsyncSession, redis_client: Optional[AsyncRedisClient] = None):
        self.session = session
        self.cache = HoldviewCache(redis_client, session) if redis_client else None
    
    async def check_access(self, post_id: str, user_id: Optional[str] = None) -> bool:
        """
        Check if user has access to a single post based on purchase requirements.
        
        Args:
            post_id: ID of the post to check
            user_id: ID of the user (None for anonymous users)
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            # Get post details
            stmt = select(Post).where(Post.id == post_id)
            result = await self.session.execute(stmt)
            post = result.scalar_one_or_none()
            
            if not post:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")
            
            # If no holdview requirement, allow access
            if self.get_holdview_amount(post) == 0:
                return True
            
            # If no user (anonymous), deny access to gated content
            if not user_id:
                return False
            
            # Check if user has purchased this content
            return await self._has_purchased_content(str(user_id), post_id)
            
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error checking access for post {post_id}: {str(e)}")
            # In case of error, deny access to be safe
            return False
    
    async def _has_purchased_content(self, user_id: str, post_id: str) -> bool:
        """Check if user has purchased this content"""
        try:
            # 1) Try Redis first
            try:
                redis_client: Optional[AsyncRedisClient] = getattr(self.cache, 'redis_client', None) if self.cache else None
                if redis_client:
                    key = f"purchase:by_user_post:{user_id}:{post_id}"
                    cached = await redis_client.get_json(key)
                    if isinstance(cached, dict):
                        status_val = (cached.get("status") or "").lower()
                        if status_val in {
                            ContentPurchaseStatus.CONFIRMED.value,
                            ContentPurchaseStatus.PROCESSING.value,
                            ContentPurchaseStatus.PENDING.value,
                        }:
                            return True
            except Exception:
                # Soft-fail to DB
                pass

            # 2) Fallback to DB
            stmt = select(ContentPurchase).where(
                ContentPurchase.user_id == user_id,
                ContentPurchase.post_id == post_id,
                # TODO: 暂时PROCESSING中的也可以观看
                ContentPurchase.status.in_([ContentPurchaseStatus.CONFIRMED, ContentPurchaseStatus.PROCESSING, ContentPurchaseStatus.PENDING])
            )
            result = await self.session.execute(stmt)
            purchase = result.scalar_one_or_none()
            return purchase is not None
        except Exception as e:
            logger.error(f"Error checking content purchase for user {user_id}, post {post_id}: {str(e)}")
            return False
    
    async def _get_user_purchases_batch(self, user_id: str, post_ids: List[str]) -> Dict[str, bool]:
        """Get user's purchases for multiple posts"""
        try:
            # 1) Try Redis first for all posts
            purchased_map: Dict[str, bool] = {pid: False for pid in post_ids}
            remaining_ids: List[str] = list(post_ids)

            try:
                redis_client: Optional[AsyncRedisClient] = getattr(self.cache, 'redis_client', None) if self.cache else None
                if redis_client and post_ids:
                    keys = [f"purchase:by_user_post:{user_id}:{pid}" for pid in post_ids]
                    raw = await redis_client.mget_json(keys)
                    for pid, key in zip(post_ids, keys):
                        data = raw.get(key)
                        if isinstance(data, dict):
                            status_val = (data.get("status") or "").lower()
                            if status_val in {
                                ContentPurchaseStatus.CONFIRMED.value,
                                ContentPurchaseStatus.PROCESSING.value,
                                ContentPurchaseStatus.PENDING.value,
                            }:
                                purchased_map[pid] = True
                    remaining_ids = [pid for pid in post_ids if not purchased_map[pid]]
            except Exception:
                # Soft-fail to DB for all
                remaining_ids = list(post_ids)

            if not remaining_ids:
                return purchased_map

            # 2) Fallback to DB for remaining
            stmt = select(ContentPurchase.post_id).where(
                ContentPurchase.user_id == user_id,
                ContentPurchase.post_id.in_(remaining_ids),
                ContentPurchase.status.in_([ContentPurchaseStatus.CONFIRMED, ContentPurchaseStatus.PROCESSING, ContentPurchaseStatus.PENDING])
            )
            result = await self.session.execute(stmt)
            purchased_post_ids = {row[0] for row in result.fetchall()}
            for pid in remaining_ids:
                purchased_map[pid] = pid in purchased_post_ids
            return purchased_map
        except Exception as e:
            logger.error(f"Error checking purchases for user {user_id}: {str(e)}")
            return {post_id: False for post_id in post_ids}
    
    def _apply_content_protection_blur(self, post) -> None:
        """
        Apply Cloudflare Image blur protection for locked posts.
        
        Args:
            post: Post object to apply blur protection to
        """
        try:
            # Apply blur to cover image using Cloudflare Image URL parameters
            if hasattr(post, 'cover') and post.cover:
                post.cover = ImageTransformUtils.apply_content_protection_blur(post.cover)
            
            # Hide other sensitive content for locked posts
            if hasattr(post, 'images_data'):
                post.images_data = []
                
            if hasattr(post, 'url') and post.type in ['Video', 'Image']:
                post.url = None
                
            logger.debug(f"Applied content protection blur to post {post.id}")
            
        except Exception as e:
            logger.warning(f"Failed to apply content protection to post {getattr(post, 'id', 'unknown')}: {str(e)}")
            # On error, fall back to hiding all content completely
            if hasattr(post, 'images_data'):
                post.images_data = []
            if hasattr(post, 'cover'):
                post.cover = None
            if hasattr(post, 'url') and post.type in ['Video', 'Image']:
                post.url = None

    async def get_post_binding_token_info(self, post_id: str) -> Optional[Dict[str, str]]:
        """
        Return binding token info for a post for UI display when content is locked.
        Prefers the post's own binding_token; falls back to collection-based token.
        Returns dict: { token_address, token_symbol, image_url } or None if unavailable.
        """
        try:
            token_address = await self._get_post_token_address(post_id)
            if not token_address:
                return None

            # 1) Try Redis cache (memecoin token cache) with optimized HMGET
            token_symbol = ""
            image_url = ""
            try:
                redis_client: Optional[AsyncRedisClient] = getattr(self.cache, 'redis_client', None) if self.cache else None
                if redis_client:
                    info = await redis_client.get_token_info_from_hash(MEMECOIN_REDIS_ENDPOINT, token_address)
                    token_symbol = info.get("symbol", "")
                    image_url = info.get("image_url", "")
            except Exception:
                # Soft-fail and fallback to DB
                pass

            # 2) Fallback to DB if Redis misses
            if not token_symbol or not image_url:
                stmt = (
                    select(Pair.base_symbol, Pair.base_image_url)
                    .where(Pair.base == token_address)
                    .limit(1)
                )
                result = await self.session.execute(stmt)
                row = result.first()
                if row:
                    token_symbol = token_symbol or (row[0] or "")
                    image_url = image_url or (row[1] or "")

            return {
                "token_address": token_address,
                "token_symbol": token_symbol or "",
                "image_url": image_url or "",
            }
        except Exception:
            return None
    
    async def batch_check_access(self, post_ids: List[str], user_id: Optional[str] = None) -> Dict[str, bool]:
        """
        Batch check access for multiple posts.
        
        Args:
            post_ids: List of post IDs to check
            user_id: ID of the user (None for anonymous users)
            
        Returns:
            Dict[str, bool]: Dictionary mapping post_id to access permission
        """
        if not post_ids:
            return {}
        
        try:
            # Get posts in batch
            stmt = select(Post).where(Post.id.in_(post_ids))
            result = await self.session.execute(stmt)
            posts = result.scalars().all()
            
            # Convert to list for batch processing
            post_list = list(posts)
            access_results = await self._can_user_access_posts_batch(user_id, post_list)
            
            return access_results
            
        except Exception as e:
            logger.error(f"Error in batch access check for posts {post_ids}: {str(e)}")
            # In case of error, deny access to all posts
            return {post_id: False for post_id in post_ids}
    
    async def filter_accessible_posts(self, posts: List[Dict], user_id: Optional[str] = None) -> List[Dict]:
        """
        Filter posts list to only accessible ones.
        
        Args:
            posts: List of post dictionaries (with 'id', 'holdview_amount' keys)
            user_id: ID of the user (None for anonymous users)
            
        Returns:
            List[Dict]: Filtered list of accessible posts
        """
        if not posts:
            return []
        
        try:
            # Extract post IDs for batch checking
            post_ids = [post.get('id') for post in posts if post.get('id')]
            
            # Get access results
            access_results = await self.batch_check_access(post_ids, user_id)
            
            # Filter posts based on access
            accessible_posts = []
            for post in posts:
                post_id = post.get('id')
                if post_id and access_results.get(post_id, False):
                    accessible_posts.append(post)
            
            logger.debug(f"Filtered {len(posts)} posts to {len(accessible_posts)} accessible for user {user_id or 'anonymous'}")
            return accessible_posts
            
        except Exception as e:
            logger.error(f"Error filtering accessible posts: {str(e)}")
            # On error, return only public posts (holdview_amount = 0)
            return [post for post in posts if post.get('holdview_amount', 0) == 0]
    
    async def _get_user_held_tokens(self, user_id: str) -> Dict[str, int]:
        """
        Get all tokens held by a user from the token_holder table.
        Uses cache for improved performance.
        
        Args:
            user_id: User ID to query token holdings for
            
        Returns:
            Dict[str, int]: Dictionary mapping token_address to amount held
        """
        if not self.session:
            logger.warning(f"No database session available for user {user_id}")
            return {}
        
        # Try cache first
        if self.cache:
            cached_holdings = await self.cache.get_user_holdings(user_id)
            if cached_holdings is not None:
                return cached_holdings
            
        try:
            # Query token_holder table for user's token holdings
            stmt = select(TokenHolder.token_address, TokenHolder.amount).where(
                TokenHolder.user_id == user_id
            )
            result = await self.session.execute(stmt)
            
            # Convert to dictionary mapping token_address -> amount
            token_holdings = {}
            for token_address, amount in result:
                # Convert Numeric to int for comparison
                token_holdings[token_address] = int(amount / Decimal(1e18)) if amount else 0
                
            # Cache the result
            if self.cache:
                await self.cache.set_user_holdings(user_id, token_holdings)
                
            logger.debug(f"Found {len(token_holdings)} token holdings for user {user_id}")
            return token_holdings
            
        except Exception as e:
            logger.error(f"Error querying token holdings for user {user_id}: {str(e)}")
            return {}
    
    async def _get_post_token_address(self, post_id: str) -> Optional[str]:
        """
        Get token address for a single post using indexed query.
        Uses cache for improved performance.
        
        Args:
            post_id: Post ID
            
        Returns:
            Optional[str]: Token address or None if no associated token
        """
        if not self.session:
            return None
        
        # Try cache first
        if self.cache:
            cache_hit, cached_token = await self.cache.get_post_token(post_id)
            if cache_hit:
                return cached_token
            
        try:
            # 1) Prefer the binding_token on the Post itself
            post_stmt = select(Post.binding_token).where(Post.id == post_id)
            post_result = await self.session.execute(post_stmt)
            token_address = post_result.scalar_one_or_none()
            
            # 2) Fallback to joining SavedPost -> Pair to infer token
            if not token_address:
                stmt = (
                    select(Pair.base)
                    .select_from(SavedPost)
                    .join(Pair, SavedPost.collection_id == Pair.collection_id)
                    .where(SavedPost.post_id == post_id)
                    .limit(1)
                )
                result = await self.session.execute(stmt)
                token_address = result.scalar_one_or_none()
            
            # Cache the result
            if self.cache:
                await self.cache.set_post_token(post_id, token_address)
            
            logger.debug(f"Found token address {token_address} for post {post_id}")
            return token_address
            
        except Exception as e:
            logger.error(f"Error getting post token address for {post_id}: {str(e)}")
            return None
    
    async def _get_post_token_addresses_batch(self, post_ids: List[str]) -> Dict[str, Optional[str]]:
        """
        Get token addresses for multiple posts using batch query.
        Uses cache for improved performance.
        
        Args:
            post_ids: List of post IDs to look up
            
        Returns:
            Dict[str, Optional[str]]: Dictionary mapping post_id to token_address
        """
        if not self.session or not post_ids:
            return {post_id: None for post_id in post_ids}
            
        # Try cache first
        cache_results = {}
        uncached_post_ids = []
        
        if self.cache:
            cache_results = await self.cache.batch_get_post_tokens(post_ids)
            # Identify cache misses
            uncached_post_ids = [post_id for post_id in post_ids if post_id not in cache_results]
        else:
            uncached_post_ids = post_ids
            
        # If all posts are cached, return cache results
        if not uncached_post_ids:
            return {post_id: cache_results.get(post_id) for post_id in post_ids}
            
        try:
            # Initialize result map
            post_token_map = {post_id: cache_results.get(post_id) for post_id in post_ids}
            
            # Query database for uncached posts only
            if uncached_post_ids:
                # 1) Prefer binding_token directly from posts table
                post_stmt = select(Post.id, Post.binding_token).where(Post.id.in_(uncached_post_ids))
                post_result = await self.session.execute(post_stmt)
                direct_tokens = {pid: token for pid, token in post_result if token}
                post_token_map.update(direct_tokens)

                # Determine which still need fallback
                remaining_ids = [pid for pid in uncached_post_ids if pid not in direct_tokens]

                if remaining_ids:
                    # 2) Fallback: infer via SavedPost -> Pair
                    stmt = (
                        select(SavedPost.post_id, Pair.base.label('token_address'))
                        .select_from(SavedPost)
                        .join(Pair, SavedPost.collection_id == Pair.collection_id)
                        .where(SavedPost.post_id.in_(remaining_ids))
                        .distinct()
                    )
                    result = await self.session.execute(stmt)
                    db_results = {}
                    for post_id, token_address in result:
                        db_results[post_id] = token_address
                        post_token_map[post_id] = token_address
                    for post_id in remaining_ids:
                        if post_id not in db_results:
                            post_token_map[post_id] = None
                
                # Cache the database results
                if self.cache:
                    cache_updates = {post_id: post_token_map[post_id] for post_id in uncached_post_ids}
                    await self.cache.batch_set_post_tokens(cache_updates)
                    
            logger.debug(f"Found token addresses for {len([v for v in post_token_map.values() if v])} posts out of {len(post_ids)} (cached: {len(post_ids) - len(uncached_post_ids)}, db: {len(uncached_post_ids)})")
            return post_token_map
            
        except Exception as e:
            logger.error(f"Error getting post token addresses (batch): {str(e)}")
            return {post_id: None for post_id in post_ids}
    
    async def _can_user_access_posts_batch(self, user_id: Optional[str], posts: List[Post]) -> Dict[str, bool]:
        """
        Internal batch check for post access using Post objects.
        
        Args:
            user_id: User ID (None for anonymous users)
            posts: List of Post objects to check access for
            
        Returns:
            Dict[str, bool]: Dictionary mapping post.id to access permission
        """
        if not posts:
            return {}
            
        # Initialize result dictionary
        access_results = {}

        # Handle anonymous users
        if user_id is None:
            for post in posts:
                # Anonymous users can only access posts with holdview_amount = 0
                holdview_amount = self.get_holdview_amount(post)
                access_results[post.id] = (holdview_amount == 0)
            return access_results
            
        try:
            # Separate posts by holdview requirement
            gated_posts = [p for p in posts if self.get_holdview_amount(p) > 0 and user_id != p.author.id]
            public_posts = [p for p in posts if self.get_holdview_amount(p) == 0 or user_id == p.author.id]
            
            # Mark all public posts as accessible
            for post in public_posts:
                access_results[post.id] = True
            
            # If no gated posts, return early
            if not gated_posts:
                return access_results
            
            # Get user's purchases for all gated posts
            gated_post_ids = [p.id for p in gated_posts]
            user_purchases = await self._get_user_purchases_batch(str(user_id), gated_post_ids)
            
            # Process each gated post
            for post in gated_posts:
                try:
                    # Check if user has purchased this content
                    has_access = user_purchases.get(post.id, False)
                    access_results[post.id] = has_access
                    
                    if not has_access:
                        logger.debug(
                            f"User {user_id} denied access to post {post.id}: "
                            f"content requires purchase (${self.get_holdview_amount(post) / 100:.2f})"
                        )
                    
                except Exception as e:
                    logger.error(f"Error checking access for post {post.id}: {str(e)}")
                    # In case of error, deny access to be safe
                    access_results[post.id] = False
                    
        except Exception as e:
            logger.error(f"Error in batch access check for user {user_id}: {str(e)}")
            # In case of error, deny access to all posts with holdview requirements
            for post in posts:
                access_results[post.id] = (self.get_holdview_amount(post) == 0)
                
        return access_results

    def get_holdview_amount(self, post) -> int:
        """
        统一解析 post 的 holdview_amount/holdview_amount_int 为整数 (cents)
        """
        if hasattr(post, "holdview_amount_int") and post.holdview_amount_int is not None:
            return post.holdview_amount_int

        if hasattr(post, "holdview_amount") and post.holdview_amount is not None:
            try:
                # 如果是字符串 "1.5"，转 float 再换算成 cents
                return int(float(post.holdview_amount) * 100)
            except ValueError:
                return 0

        return 0
    
    async def apply_holdview_locks(self, user: Optional[User], posts: List[Post]) -> List[Post]:
        """
        Apply holdview lock status to posts without filtering them out.
        Sets is_locked attribute on each post based on user's access.
        
        This method maintains compatibility with the existing feed service interface.
        
        Args:
            user: User object (None for anonymous users)
            posts: List of Post objects to check
            
        Returns:
            List[Post]: Same posts with is_locked attribute set
        """
        if not posts:
            return []
            
        try:
            # Get access results for all posts
            user_id = str(user.id) if user else None
            access_results = await self._can_user_access_posts_batch(user_id, posts)
            
            # Set is_locked attribute on each post
            for post in posts:
                # Set is_locked to True if user doesn't have access AND post has holdview requirement
                has_access = access_results.get(post.id, False)
                post.is_locked = not has_access and self.get_holdview_amount(post) > 0

                # Apply content protection to locked posts
                if post.is_locked:
                    # Apply Gaussian blur to image content instead of hiding it completely
                    self._apply_content_protection_blur(post)
                    
                    # Attach binding token info for locked posts (for UI hints)
                    try:
                        token_info = await self.get_post_binding_token_info(post.id)
                        if token_info:
                            setattr(post, 'binding_token_info', token_info)
                    except Exception:
                        # Ignore errors when enriching token info
                        pass
            
            locked_count = sum(1 for p in posts if getattr(p, 'is_locked', False))
            logger.debug(f"Applied holdview locks to {len(posts)} posts, {locked_count} locked for user {getattr(user, 'id', 'anonymous')}")
            return posts
            
        except Exception as e:
            logger.error(f"Error applying holdview locks: {str(e)}")
            # On error, return posts without lock status
            for post in posts:
                post.is_locked = False
            return posts
    
    async def close(self):
        """Close any resources (for compatibility)"""
        pass

    async def apply_holdview_lock(self, user: Optional[User], post: Post) -> Post:
        """
        Apply holdview lock for a single post with blur protection when locked.
        Convenience wrapper for single item scenarios used by detail endpoints.
        """
        posts = await self.apply_holdview_locks(user, [post])
        return posts[0] if posts else post