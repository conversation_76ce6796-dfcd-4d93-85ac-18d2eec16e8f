"""
Holdview Dependencies - Dependency injection for HoldviewService

This module provides clean dependency injection for the HoldviewService
with automatic database session and Redis client injection.
"""

from fastapi import Depends
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.session import get_session
from src.common.holdview.service import HoldviewService
from src.common.redis_cli.async_impl import AsyncRedisClient
from src.common.redis_cli import RedisCli
from src.common.settings import settings
from src.common.simple_image_proxy import SimpleImageProxy
from src.common.simple_image_proxy_dependencies import get_simple_image_proxy


def get_holdview_service(
    session: AsyncSession = Depends(get_session),
    redis_client: Optional[AsyncRedisClient] = Depends(RedisCli.async_),
) -> HoldviewService:
    """
    Get HoldviewService instance with dependency injection.
    
    Args:
        session: Database session (auto-injected)
        redis_client: Redis client for caching (auto-injected)
        simple_proxy: Simple image proxy instance (auto-injected)
        
    Returns:
        HoldviewService: Configured service instance with injected proxy
    """
    return HoldviewService(session, redis_client)