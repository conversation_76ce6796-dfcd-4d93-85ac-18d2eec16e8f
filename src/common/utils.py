import xxhash
from orjson import orjson
from datetime import datetime, timezone
from typing import Optional, Union, Dict, Any

from web3.exceptions import Web3RPCError

from .constants import Language


alphabets = {
    Language.ENGLISH: {'chars': [chr(x) for x in range(ord('a'), ord('z')+1)], 'coefficient': 1},
    Language.RUSSIAN: {'chars': [chr(x) for x in range(ord('а'), ord('я')+1)], 'coefficient': 1},
    Language.CHINESE: {'chars': set([chr(x) for x in range(0x4E00, 0x9FA5+1)]), 'coefficient': 2},
}


def detect_language(text: str) -> Language:
    filtered_characters = '.,/#-=+{}[]()\\`"\' 1234567890《》@!?'
    filtered_text = ''.join(x for x in text.lower() if x not in filtered_characters)
    stats = {Language.ENGLISH: 0, Language.CHINESE: 0, Language.RUSSIAN: 0, Language.OTHER: 0}
    for char in filtered_text:
        found = False
        for lang, chars in alphabets.items():
            if char in chars['chars']:
                stats[lang] += 1
                found = True
                break
        if not found:
            stats[Language.OTHER] += 1
    detected_language = max(stats,
                            key=lambda x: stats.get(x) * (alphabets.get(x) or {'coefficient': 1})['coefficient'])
    return detected_language


def generate_hash_key(*args) -> str:
    """
    超高速 16-hex 哈希：
      • orjson.dumps → C 层序列化
      • xxh3_64 → C 层散列
    """
    # orjson.dumps 直接得到 bytes；OPT_NON_STR_KEYS 允许 dict/int 等非字符串键
    payload = orjson.dumps(args, option=orjson.OPT_NON_STR_KEYS)
    # 64-bit 摘要 → 16 个十六进制字符
    return xxhash.xxh3_64_hexdigest(payload)


def add_utc_timezone(dt: Optional[datetime]) -> Optional[datetime]:
    """
    为 naive datetime 添加 UTC 时区信息
    
    Args:
        dt: 可能为 None 的 datetime 对象
        
    Returns:
        带有 UTC 时区信息的 datetime 对象，如果输入为 None 则返回 None
    """
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt


def ensure_naive_utc(dt: Optional[datetime]) -> Optional[datetime]:
    """
    将任意 datetime 统一为“UTC naive”。

    规则：
    - None -> None
    - 有 tzinfo -> 转为 UTC 后移除 tzinfo（naive UTC）
    - 无 tzinfo -> 按 UTC 解释，直接返回（视为 naive UTC）
    """
    if dt is None:
        return None
    if dt.tzinfo is not None:
        return dt.astimezone(timezone.utc).replace(tzinfo=None)
    return dt


def convert_datetimes_to_utc(data: Dict[str, Any], *datetime_fields: str) -> Dict[str, Any]:
    """
    批量将指定的 datetime 字段转换为带 UTC 时区信息的格式
    
    Args:
        data: 包含 datetime 字段的字典
        *datetime_fields: 需要转换的字段名
        
    Returns:
        转换后的数据字典
    """
    result = data.copy()
    for field in datetime_fields:
        if field in result and isinstance(result[field], datetime):
            result[field] = add_utc_timezone(result[field])
    return result


def format_web3_error(error: Web3RPCError) -> str:
        """
        Format Web3RPCError error messages to be more user-friendly
        
        Args:
            error: Web3RPCError exception object
            
        Returns:
            str: Formatted user-friendly error message
        """
        error_str = str(error)
        
        # Common error types and their user-friendly messages
        error_patterns = {
            "insufficient funds for gas": "Insufficient balance to pay transaction fees",
            "insufficient funds for gas * price + value": "Insufficient balance to pay transaction fees and transfer amount",
            "execution reverted": "Transaction execution failed",
            "nonce too low": "Transaction nonce too low, please try again later",
            "nonce too high": "Transaction nonce too high, please try again later",
            "gas required exceeds allowance": "Gas fee exceeds limit",
            "gas limit exceeded": "Gas limit exceeded",
            "transaction underpriced": "Transaction fee too low",
            "replacement transaction underpriced": "Replacement transaction fee too low",
            "transaction already known": "Transaction already exists",
            "invalid signature": "Invalid signature",
            "invalid nonce": "Invalid transaction nonce",
            "account not found": "Account not found",
            "contract not found": "Contract not found",
            "method not found": "Method not found",
            "invalid opcode": "Invalid opcode",
            "out of gas": "Out of gas",
            "intrinsic gas too low": "Intrinsic gas too low",
            "transaction would exceed block gas limit": "Transaction would exceed block gas limit",
            "max fee per gas less than block base fee": "Max fee per gas less than block base fee",
            "max priority fee per gas higher than max fee per gas": "Max priority fee per gas higher than max fee per gas",
            "max fee per gas higher than max total fee per gas": "Max fee per gas higher than max total fee per gas",
            "overshot": "Insufficient balance",
            "balance": "Balance",
            "tx cost": "Transaction cost",
        }
        
        # Try to match error patterns
        for pattern, friendly_message in error_patterns.items():
            if pattern.lower() in error_str.lower():
                # If it's a balance-related error, try to extract specific values
                if "balance" in error_str.lower() and "overshot" in error_str.lower():
                    try:
                        # Extract balance and transaction cost information
                        import re
                        balance_match = re.search(r'balance (\d+)', error_str)
                        cost_match = re.search(r'tx cost (\d+)', error_str)
                        
                        if balance_match and cost_match:
                            balance = int(balance_match.group(1))
                            cost = int(cost_match.group(1))
                            shortfall = cost - balance
                            
                            # Convert to more readable format
                            shortfall_eth = shortfall / 10**18
                            return f"Insufficient balance. Current balance: {balance / 10**18:.6f} ETH, Required: {cost / 10**18:.6f} ETH, Shortfall: {shortfall_eth:.6f} ETH"
                    except:
                        pass
                
                return friendly_message
        
        # If no specific pattern is matched, return generic error message
        if "code" in error_str and "message" in error_str:
            try:
                # Try to extract error code and message
                import re
                code_match = re.search(r"'code': (-?\d+)", error_str)
                message_match = re.search(r"'message': '([^']*)'", error_str)
                
                if code_match and message_match:
                    code = code_match.group(1)
                    message = message_match.group(1)
                    return f"Blockchain error (code: {code}): {message}"
            except:
                pass
        
        # Final fallback
        return f"Transaction failed: {error_str}"


def format_cents_to_usd_string(cents: Union[int, None]) -> str:
    """
    Convert cents to USD string format. 
    
    Examples:
        100 cents -> '1' (not '1.00')
        150 cents -> '1.5' 
        99 cents -> '0.99'
        0 cents -> '0'
        None -> '0'
    
    Args:
        cents: Integer value representing cents, or None
        
    Returns:
        str: USD amount as string without unnecessary trailing zeros
    """
    if cents is None:
        return "0"
    
    # Convert cents to dollars
    dollars = cents / 100
    
    # Format to remove unnecessary .00
    if dollars == int(dollars):
        return str(int(dollars))
    else:
        return f"{dollars:.2f}".rstrip('0').rstrip('.')

 