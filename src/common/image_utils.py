"""
Image utilities for Cloudflare Images transformations
"""
import re
from typing import Optional
from urllib.parse import urlparse


class ImageTransformUtils:
    """Utility class for handling Cloudflare Images transformations"""
    
    # Cloudflare Images patterns
    CLOUDFLARE_IMAGES_PATTERN = re.compile(
        r'https://images\.dev\.memefans\.ai/([^/]+)(?:\.([^.]+))?$'
    )
    
    IMAGEDELIVERY_PATTERN = re.compile(
        r'https://imagedelivery\.net/([^/]+)/([^/]+)/([^/]+)$'
    )
    
    @classmethod
    def add_blur_to_image_url(cls, original_url: str, blur_radius: int = 60) -> str:
        """
        Add Gaussian blur to an image URL using Cloudflare Images transformations.
        
        Args:
            original_url: Original image URL
            blur_radius: Blur radius (1-250, where 20 is a good default for content protection)
            
        Returns:
            str: Image URL with blur transformation applied
        """
        if not original_url or not isinstance(original_url, str):
            return original_url
        
        # Clamp blur radius to valid range
        blur_radius = max(1, min(250, blur_radius))
        
        # Handle images.dev.memefans.ai domain
        if cls.CLOUDFLARE_IMAGES_PATTERN.match(original_url):
            return cls._add_blur_to_memefans_url(original_url, blur_radius)
        
        # Handle imagedelivery.net domain
        if cls.IMAGEDELIVERY_PATTERN.match(original_url):
            return cls._add_blur_to_imagedelivery_url(original_url, blur_radius)
        
        # For other domains, try generic CDN transform approach
        return cls._add_blur_generic(original_url, blur_radius)
    
    @classmethod
    def _add_blur_to_memefans_url(cls, url: str, blur_radius: int) -> str:
        """Add blur to images.dev.memefans.ai URLs"""
        match = cls.CLOUDFLARE_IMAGES_PATTERN.match(url)
        if not match:
            return url
        
        image_id = match.group(1)
        extension = match.group(2)
        
        # Use Cloudflare Images transform syntax
        # Format: https://domain/cdn-cgi/image/blur=20/original-url
        base_domain = "https://images.dev.memefans.ai"
        return f"{base_domain}/cdn-cgi/image/blur={blur_radius}/{image_id}" + (f".{extension}" if extension else "")
    
    @classmethod  
    def _add_blur_to_imagedelivery_url(cls, url: str, blur_radius: int) -> str:
        """Add blur to imagedelivery.net URLs using variant approach"""
        match = cls.IMAGEDELIVERY_PATTERN.match(url)
        if not match:
            return url
        
        account_hash = match.group(1) 
        image_id = match.group(2)
        current_variant = match.group(3)
        
        # Create a blurred variant name by appending blur info
        # If current variant is 'public', change to 'blur20'
        # Otherwise append blur info
        if current_variant == 'public':
            blur_variant = f"blur{blur_radius}"
        else:
            blur_variant = f"{current_variant}-blur{blur_radius}"
        
        return f"https://imagedelivery.net/{account_hash}/{image_id}/{blur_variant}"
    
    @classmethod
    def _add_blur_generic(cls, url: str, blur_radius: int) -> str:
        """Generic blur transform for other domains"""
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return url
        
        # Try generic CDN transform approach
        return f"{parsed.scheme}://{parsed.netloc}/cdn-cgi/image/blur={blur_radius}{parsed.path}"
    
    @classmethod
    def is_image_url(cls, url: str) -> bool:
        """Check if URL is an image URL"""
        if not url or not isinstance(url, str):
            return False
        
        # Check for image file extensions
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'}
        parsed = urlparse(url)
        path_lower = parsed.path.lower()
        
        return any(path_lower.endswith(ext) for ext in image_extensions)
    
    @classmethod
    def apply_content_protection_blur(cls, image_url: str) -> str:
        """
        Apply content protection blur (radius=20) to an image URL.
        This is a convenience method for locked content.
        
        Args:
            image_url: Original image URL
            
        Returns:
            str: Blurred image URL suitable for content protection
        """
        return cls.add_blur_to_image_url(image_url, blur_radius=60)