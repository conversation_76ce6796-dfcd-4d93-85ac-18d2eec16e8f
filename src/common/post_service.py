from sqlalchemy.orm import with_expression, aliased
from sqlalchemy import select, exists, literal, Select, update, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status
import logging
import re
from typing import List, Optional

from src.authors.service import get_author_service, AuthorService
from src.common.recommender import RecommenderClient
from src.common.recommender.client import get_recommender_client
from src.common.celery_client import create_celery_client
from src.database.models import Author, Tag
from src.database.models.Post import Post, authors_likes
from src.database.models.SavedPosts import SavedPost
from src.database.models.Image import Image
from src.database.models.Video import Video
from src.database.constants import PostStatus, PostType
from src.database.session import get_session

logger = logging.getLogger(__name__)


class PostService:
    model = Post

    def __init__(
        self,
        session: AsyncSession,
        author_service: AuthorService,
        recommender_client: RecommenderClient | None,
    ):
        self.session = session
        self.recommender_client = recommender_client
        self.author_service = author_service

    # ---------------- Tag helpers ----------------
    @staticmethod
    def _normalize_tag_title(title: str) -> str:
        return (title or "").strip().lower()

    async def _ensure_tags_exist(self, titles: list[str]) -> None:
        """Ensure Tag rows exist for given titles (normalized to lower)."""
        if not titles:
            return
        normalized = [self._normalize_tag_title(t) for t in titles if t]
        unique = list({t for t in normalized if t})
        if not unique:
            return
        # Fetch existing
        existing_stmt = select(Tag).where(Tag.title.in_(unique))
        existing_rows = (await self.session.execute(existing_stmt)).scalars().all()
        existing_set = {t.title for t in existing_rows}
        # Create missing
        for t in unique:
            if t not in existing_set:
                self.session.add(Tag(title=t))
        await self.session.flush()

    async def _change_tags_counts(self, titles: list[str], delta: int) -> None:
        """Increase or decrease posts_count for given tags inside current transaction."""
        if not titles or delta == 0:
            return
        normalized = [self._normalize_tag_title(t) for t in titles if t]
        unique = list({t for t in normalized if t})
        if not unique:
            return
        # Ensure tags exist only when increasing
        if delta > 0:
            await self._ensure_tags_exist(unique)
        # Load tags to modify
        stmt = select(Tag).where(Tag.title.in_(unique))
        rows = (await self.session.execute(stmt)).scalars().all()
        for row in rows:
            current = row.posts_count or 0
            new_value = current + delta
            row.posts_count = new_value if new_value > 0 else 0
        await self.session.flush()

    async def get(self, entity_id: str):
        return await self.session.get(Post, entity_id)

    async def get_all_by_author(self, author_id: str, **kwargs):
        try:
            query = (
                select(self.model)
                .filter(self.model.author_id == author_id)
                .filter(self.model.status == PostStatus.POSTED)
                .filter(~self.model.type.in_(["Comment", "Question", "Answer"]))
            )

            if kwargs.get("types") is not None:
                query = query.filter(self.model.type.in_(kwargs.get("types")))

            result = await self.session.execute(query)
            posts = result.scalars().all()

            if kwargs.get("sort") == "new":
                posts.sort(key=lambda p: p.created_at, reverse=True)
            elif kwargs.get("sort") == "old":
                posts.sort(key=lambda p: p.created_at, reverse=False)

            author_posts = []
            for post in posts:
                if post.type == "Collection":
                    collection_query = select(Post).filter(Post.id.in_(post.contents))
                    collection_result = await self.session.execute(collection_query)
                    collection_posts = collection_result.scalars().all()
                    post.posts = collection_posts
                    previews = []
                    for collection_post in collection_posts:
                        if len(previews) >= 3:
                            break
                        if (
                            hasattr(collection_post, "cover")
                            and collection_post.cover is not None
                            and len(collection_post.cover) > 0
                        ):
                            previews.append(collection_post.cover)
                    post.previews = previews
                author_posts.append(post)
            return author_posts
        except Exception as e:
            await self.session.rollback()
            raise e

    def get_one(self, post_id: str, user_id: str | None = None) -> Select:
        stmt = (
            select(self.model)
            .where(self.model.id == post_id)
            .options(
                self.in_collection_expression(user_id=user_id),
                self.is_liked_expression(user_id=user_id),
            )
            .join(self.model.author)
        )
        return stmt

    def get_all(self, user_id: str | None = None) -> Select:
        stmt = (
            select(self.model)
            .filter(self.model.status == PostStatus.POSTED)
            .options(
                self.in_collection_expression(user_id=user_id),
                self.is_liked_expression(user_id=user_id),
            )
        )
        return stmt

    @staticmethod
    def get_posts_by_ids(post_ids: list[str]) -> Select:
        stmt = (
            select(Post).where(Post.id.in_(post_ids)).order_by(Post.created_at.desc())
        )
        return stmt

    def in_collection_expression(self, user_id: str | None = None, polymorphic_entity=None):
        saved_posts_table = aliased(SavedPost, name="saved_posts_table")
        
        post_id_col = polymorphic_entity.id if polymorphic_entity else Post.id
        target_attr = polymorphic_entity.is_in_collection if polymorphic_entity else self.model.is_in_collection
        
        subquery_in_collection = exists(
            select(saved_posts_table).where(
                saved_posts_table.user_id == user_id,
                saved_posts_table.post_id == post_id_col,
            )
        )
        expression = with_expression(
            target_attr,
            subquery_in_collection if user_id else literal(False),
        )
        return expression

    def is_liked_expression(self, user_id: str | None = None, polymorphic_entity=None):
        likes_table = aliased(authors_likes, name="likes_table")
        
        post_id_col = polymorphic_entity.id if polymorphic_entity else self.model.id
        target_attr = polymorphic_entity.is_liked if polymorphic_entity else self.model.is_liked
        
        subquery_liked = exists(
            select(likes_table).where(
                likes_table.c.author_id == user_id,
                likes_table.c.post_id == post_id_col,
            )
        )
        expression = with_expression(
            target_attr, subquery_liked if user_id else literal(False)
        )
        return expression

    async def is_author(self, post_id: str, author_id: str):
        post = await self.get(post_id)
        return post.author_id == author_id if post else False

    async def upvote(self, post_id: str, author_id: str):
        author: Author = await self.session.get(Author, author_id)
        post = await self.get(post_id)

        # 使用显式查询检查用户是否已经点赞，避免懒加载问题
        like_exists_stmt = select(authors_likes).where(
            authors_likes.c.post_id == post_id,
            authors_likes.c.author_id == author_id
        )
        like_result = await self.session.execute(like_exists_stmt)
        is_already_liked = like_result.first() is not None

        if is_already_liked:
            # 移除点赞
            delete_like_stmt = authors_likes.delete().where(
                authors_likes.c.post_id == post_id,
                authors_likes.c.author_id == author_id
            )
            await self.session.execute(delete_like_stmt)
            await self.recommender_client.remove_like(
                user_id=author_id, post_id=post_id
            )
            post.likes_count -= 1
            await self.author_service.likes_count_dec(post.author_id)
        else:
            # 添加点赞
            insert_like_stmt = authors_likes.insert().values(
                post_id=post_id,
                author_id=author_id
            )
            await self.session.execute(insert_like_stmt)
            await self.recommender_client.add_like(user_id=author_id, post_id=post_id)
            post.likes_count += 1
            await self.author_service.likes_count_inc(post.author_id)

        self.session.add(post)
        await self.session.commit()

    async def delete(self, post_id: str, user_id: str):
        """软删除帖子 - 只支持Image和Video类型"""
        post = await self.get(post_id)
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Post not found"
            )
        
        # 检查帖子类型
        if post.type not in [PostType.IMAGE, PostType.VIDEO]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only Image and Video posts can be deleted"
            )
        
        # 检查权限
        if not await self.is_author(post_id, user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only delete your own posts"
            )
        
        try:
            # 首先执行软删除
            stmt = update(Post).where(Post.id == post_id).values(status=PostStatus.DELETED)
            await self.session.execute(stmt)
            await self.session.commit()
            
            # 异步执行删除后的完整清理（包括collection、search、recommender）
            try:
                celery_client = create_celery_client("post_service")
                task_result = celery_client.send_task(
                    "src.worker.tasks.collection.post_deletion_cleanup",
                    args=[post_id],
                    queue="collection"
                )
                # 不等待任务完成，异步执行
                # 可以记录task_id用于后续查询状态
                logger.info(f"Post deletion cleanup task submitted for post {post_id}: {task_result.id}")
            except Exception as e:
                # 如果celery任务提交失败，记录错误但不影响删除操作
                logger.warning(f"Failed to submit post deletion cleanup task for post {post_id}: {str(e)}")
            
            return {"success": True, "message": "Post deleted successfully"}
            
        except Exception as e:
            await self.session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting post: {str(e)}"
            )

    async def update_post(self, post_id: str, user_id: str, update_data: dict):
        """更新帖子信息 - 只支持Image和Video类型，可编辑字段：description, tags_list, images（images[0]自动设为cover）"""
        # 获取帖子并进行所有验证
        post = await self.get(post_id)
        if not post:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")
        
        # 一次性检查类型和权限
        if post.type not in [PostType.IMAGE, PostType.VIDEO]:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Only Image and Video posts can be updated")
        
        if post.author_id != user_id:  # 直接检查author_id，避免额外查询
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You can only update your own posts")
        
        # 处理和过滤数据
        post_data, child_data = self._process_update_data(update_data, post.type)

        # 仅使用前端传入的 tags_list
        new_tags_list = None
        if "tags_list" in update_data:
            new_tags_list = update_data.get("tags_list") or []
            post_data["tags_list"] = new_tags_list
        if not post_data and not child_data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No valid fields to update")
        
        # 分别执行更新
        # 1. 更新 posts 表（tags_list 等字段）
        if post_data:
            stmt_post = update(Post).where(Post.id == post_id).values(**post_data)
            await self.session.execute(stmt_post)
        
        # 2. 更新子表（description, images 等字段）
        if child_data:
            model_class = Image if post.type == PostType.IMAGE else Video
            stmt_child = update(model_class).where(model_class.id == post_id).values(**child_data)
            await self.session.execute(stmt_child)
        
        # 差量维护 Tag.posts_count（仅当 tags_list 有变化时）
        if new_tags_list is not None:
            old_set = set(map(str.lower, getattr(post, "tags_list", []) or []))
            new_set = set(map(str.lower, new_tags_list or []))
            to_inc = list(new_set - old_set)
            to_dec = list(old_set - new_set)
            if to_inc:
                await self._change_tags_counts(to_inc, +1)
            if to_dec:
                await self._change_tags_counts(to_dec, -1)

        await self.session.commit()
        
        # 异步同步（不阻塞返回）
        await self._schedule_sync_task(post_id, {**post_data, **child_data})
        
        # 返回成功消息
        return {"success": True, "message": "Post updated successfully"}
    
    def _process_update_data(self, update_data: dict, post_type: str) -> tuple[dict, dict]:
        """处理和过滤更新数据，返回(post_data, child_data)"""
        allowed_fields = {"description", "tags_list", "images"}
        filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields and v is not None}
        
        # 分离不同表的字段
        post_data = {}  # 用于更新 posts 表
        child_data = {}  # 用于更新 images/videos 表
        
        # tags_list 字段属于 posts 表
        if "tags_list" in filtered_data:
            post_data["tags_list"] = filtered_data["tags_list"]
        
        # description 字段属于子表
        if "description" in filtered_data:
            child_data["description"] = filtered_data["description"]
        
        # 处理images字段特殊逻辑（根据帖子类型不同处理）
        if "images" in filtered_data:
            images_list = filtered_data["images"]
            if images_list:
                first_image = images_list[0]
                
                if post_type == PostType.VIDEO:
                    # 对于视频，验证URL格式并提取UID
                    video_info = self._validate_and_extract_video_url(first_image.get("url"))
                    child_data.update({
                        "uid": video_info["uid"],
                        "url": video_info["url"],
                        "cover": video_info["url"].replace("manifest/video.m3u8", "thumbnails/thumbnail.jpg"),  # cover 也更新为第一个图片的 URL
                        "width": first_image.get("width"),
                        "height": first_image.get("height")
                    })
                elif post_type == PostType.IMAGE:
                    # 对于图片，images[0] 更新 images 表的相关字段
                    child_data.update({
                        "images_data": images_list,
                        "cover": first_image.get("url"),
                        "width": first_image.get("width"),
                        "height": first_image.get("height")
                    })
        
        return post_data, child_data
    
    def _validate_and_extract_video_url(self, url: str) -> dict:
        """验证视频URL格式并提取UID"""
        # 验证URL格式：匹配路径中的十六进制UID并以video.m3u8结尾
        pattern = r'.*/([a-f0-9]+)/.*?video\.m3u8$'
        match = re.search(pattern, url, re.IGNORECASE)
        
        if not match:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid video URL format. URL must contain a hexadecimal UID in path and end with video.m3u8"
            )
        
        uid = match.group(1)
        return {
            "uid": uid,
            "url": url,
            "url_type": "m3u8"
        }
    
    async def _schedule_sync_task(self, post_id: str, filtered_data: dict):
        """调度同步任务"""
        try:
            celery_client = create_celery_client("post_service")
            task_result = celery_client.send_task(
                "src.worker.tasks.collection.post_update_sync",
                args=[post_id, filtered_data],
                queue="collection"
            )
            logger.info(f"Post update sync task submitted for post {post_id}: {task_result.id}")
        except Exception as e:
            logger.warning(f"Failed to submit post update sync task for post {post_id}: {str(e)}")

    # Holdview-specific methods
    def get_accessible_posts_filter(self, user_balance: int = 0) -> Select:
        """
        Get filter condition for posts accessible based on user's token balance
        
        Args:
            user_balance: User's token balance (0 for anonymous users)
            
        Returns:
            Select: Query filter for accessible posts
        """
        # Posts are accessible if:
        # 1. holdview_amount is 0 (public content), OR
        # 2. user has sufficient balance (balance >= holdview_amount)
        return or_(
            self.model.holdview_amount == 0,  # Public content
            self.model.holdview_amount <= user_balance  # User has sufficient balance
        )

    def get_feed_with_holdview_filter(
        self, 
        user_id: str | None = None, 
        user_balance: int = 0,
        post_types: Optional[List[str]] = None
    ) -> Select:
        """
        Get feed query with holdview filtering applied
        
        Args:
            user_id: User ID for personalization (likes, collections, etc.)
            user_balance: User's token balance for holdview filtering
            post_types: List of post types to include
            
        Returns:
            Select: Feed query with holdview filtering
        """
        if post_types is None:
            post_types = ["Image", "Video"]
            
        stmt = (
            select(self.model)
            .filter(self.model.status == PostStatus.POSTED)
            .filter(self.model.type.in_(post_types))
            .filter(self.get_accessible_posts_filter(user_balance))  # Apply holdview filter
            .options(
                self.in_collection_expression(user_id=user_id),
                self.is_liked_expression(user_id=user_id),
            )
            .join(self.model.author)
            .order_by(self.model.created_at.desc())
        )
        
        return stmt

    def get_author_posts_with_holdview_filter(
        self, 
        author_id: str, 
        user_id: str | None = None, 
        user_balance: int = 0,
        post_types: Optional[List[str]] = None
    ) -> Select:
        """
        Get author's posts with holdview filtering applied
        
        Args:
            author_id: Author ID whose posts to retrieve
            user_id: Viewing user ID for personalization
            user_balance: User's token balance for holdview filtering
            post_types: List of post types to include
            
        Returns:
            Select: Author posts query with holdview filtering
        """
        if post_types is None:
            post_types = ["Image", "Video"]
            
        stmt = (
            select(self.model)
            .filter(self.model.author_id == author_id)
            .filter(self.model.status == PostStatus.POSTED)
            .filter(self.model.type.in_(post_types))
            .filter(self.get_accessible_posts_filter(user_balance))  # Apply holdview filter
            .options(
                self.in_collection_expression(user_id=user_id),
                self.is_liked_expression(user_id=user_id),
            )
            .join(self.model.author)
            .order_by(self.model.created_at.desc())
        )
        
        return stmt

    async def batch_update_holdview_amounts(
        self, 
        post_ids: List[str], 
        holdview_amount: int, 
        user_id: str
    ) -> dict:
        """
        Batch update holdview amounts for multiple posts
        
        Args:
            post_ids: List of post IDs to update
            holdview_amount: New holdview amount to set
            user_id: User ID (must own all posts)
            
        Returns:
            dict: Update results with success/failure details
        """
        results = []
        updated_count = 0
        
        try:
            async with self.session.begin():
                for post_id in post_ids:
                    try:
                        # Check if post exists and user is the author
                        post = await self.session.get(Post, post_id)
                        
                        if not post:
                            results.append({
                                "id": post_id,
                                "status": "not_found",
                                "error": "Post not found"
                            })
                            continue
                        
                        if post.author_id != user_id:
                            results.append({
                                "id": post_id,
                                "status": "not_found", 
                                "error": "Post not found or not owned by user"
                            })
                            continue
                        
                        # Update holdview_amount
                        stmt = update(Post).where(Post.id == post_id).values(
                            holdview_amount=holdview_amount
                        )
                        await self.session.execute(stmt)
                        
                        results.append({
                            "id": post_id,
                            "status": "updated"
                        })
                        updated_count += 1
                        
                    except Exception as e:
                        logger.error(f"Error updating post {post_id}: {str(e)}")
                        results.append({
                            "id": post_id,
                            "status": "error",
                            "error": str(e)
                        })
            
            return {
                "updated_count": updated_count,
                "posts": results
            }
            
        except Exception as e:
            await self.session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Batch update failed: {str(e)}"
            )


def get_post_service(
    session: AsyncSession = Depends(get_session),
    author_service: AuthorService = Depends(get_author_service),
    recommender_client: RecommenderClient | None = Depends(get_recommender_client),
) -> PostService:

    return PostService(session, author_service, recommender_client)
