"""
Simple Image Proxy Dependencies - Dependency injection for image proxy service
"""

from fastapi import Depends
from src.common.simple_image_proxy import SimpleImageProxy
from src.common.settings import settings


def get_simple_image_proxy() -> SimpleImageProxy:
    """
    Get SimpleImageProxy instance with dependency injection.
    
    Returns:
        SimpleImageProxy: Configured proxy instance
    """
    # Use a simple internal key for the lightweight proxy
    simple_proxy_key = getattr(settings, 'SIMPLE_PROXY_KEY', 'internal-proxy-key')
    return SimpleImageProxy(simple_proxy_key)