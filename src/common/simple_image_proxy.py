"""
Simple internal image proxy for content protection (performance optimized)
"""
import hashlib
import time
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlencode
import httpx
from fastapi import HTTPException, status

from src.common.image_utils import ImageTransformUtils


class SimpleImageProxy:
    """Lightweight image proxy for internal services"""
    
    def __init__(self, simple_secret: str = "internal-proxy-key"):
        """
        Initialize simple proxy
        
        Args:
            simple_secret: Simple key for basic URL validation (not cryptographically secure)
        """
        self.secret = simple_secret
        self.cache_ttl = 300  # 5 minutes TTL
    
    def generate_proxy_url(self, post_id: str, original_url: str, blur: bool = True) -> str:
        """
        Generate simple proxy URL for internal use
        
        Args:
            post_id: Post ID 
            original_url: Original image URL
            blur: Whether to blur the image
            
        Returns:
            Simple proxy URL
        """
        if not original_url:
            return ""
        
        # Simple hash for basic validation (not security)
        timestamp = int(time.time())
        simple_token = hashlib.md5(f"{post_id}{timestamp}{self.secret}".encode()).hexdigest()[:8]
        
        params = {
            'p': post_id,        # post_id
            'b': '1' if blur else '0',  # blur
            't': str(timestamp), # timestamp  
            'k': simple_token,   # simple validation key
            'u': original_url    # original URL
        }
        
        return f"/api/images/proxy?" + urlencode(params)
    
    def validate_and_extract(self, params: Dict[str, str]) -> Tuple[bool, Dict[str, Any]]:
        """
        Simple validation for internal requests
        
        Args:
            params: Request parameters
            
        Returns:
            Tuple of (is_valid, extracted_data)
        """
        try:
            required = ['p', 'b', 't', 'k', 'u']
            if not all(param in params for param in required):
                return False, {'error': 'Missing parameters'}
            
            # Simple expiration check (5 minutes)
            timestamp = int(params['t'])
            if time.time() - timestamp > self.cache_ttl:
                return False, {'error': 'Expired'}
            
            # Simple token validation
            expected_token = hashlib.md5(f"{params['p']}{params['t']}{self.secret}".encode()).hexdigest()[:8]
            if params['k'] != expected_token:
                return False, {'error': 'Invalid token'}
            
            return True, {
                'post_id': params['p'],
                'blur': params['b'] == '1',
                'original_url': params['u']
            }
            
        except (ValueError, KeyError):
            return False, {'error': 'Invalid request'}
    
    async def fetch_image(self, original_url: str, apply_blur: bool = True) -> Tuple[bytes, str]:
        """
        Fetch image with optional blur (lightweight implementation)
        
        Args:
            original_url: Original image URL
            apply_blur: Whether to apply blur
            
        Returns:
            Tuple of (image_bytes, content_type)
        """
        try:
            # Simple URL construction for blur
            if apply_blur and ImageTransformUtils.is_image_url(original_url):
                fetch_url = ImageTransformUtils.add_blur_to_image_url(original_url, 15)  # Lighter blur
            else:
                fetch_url = original_url
            
            # Quick fetch with timeout
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(fetch_url)
                response.raise_for_status()
                
                content_type = response.headers.get('content-type', 'image/jpeg')
                return response.content, content_type
                
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Image fetch failed: {str(e)}"
            )
    
    async def process_image_request(self, request_params: Dict[str, str], user_has_access: bool):
        """
        Process image request directly without router
        
        Args:
            request_params: Request parameters from query string
            user_has_access: Whether user has access to original content
            
        Returns:
            Tuple of (image_bytes, content_type, cache_headers)
        """
        try:
            # Validate parameters
            is_valid, extracted = self.validate_and_extract(request_params)
            
            if not is_valid:
                raise HTTPException(status_code=400, detail="Invalid request parameters")
            
            original_url = extracted['original_url']
            should_blur = extracted['blur']
            
            # Determine what to serve based on access
            if user_has_access:
                # User has access - serve original
                image_bytes, content_type = await self.fetch_image(original_url, apply_blur=False)
                proxy_mode = "original"
            elif should_blur:
                # No access - serve blurred version  
                image_bytes, content_type = await self.fetch_image(original_url, apply_blur=True)
                proxy_mode = "blur"
            else:
                # No access and no blur requested - deny
                raise HTTPException(status_code=403, detail="Access denied")
            
            # Return with cache headers
            cache_headers = {
                "Cache-Control": "public, max-age=300",  # 5 min cache
                "X-Proxy-Mode": proxy_mode
            }
            
            return image_bytes, content_type, cache_headers
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Image processing failed: {str(e)}")