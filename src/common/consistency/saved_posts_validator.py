import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from sqlalchemy import select, delete, text, insert, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.dialects.postgresql import insert as pg_insert

from src.database.models.Collection import Collection
from src.database.models.SavedPosts import SavedPost

logger = logging.getLogger(__name__)


class ConsistencyIssueType(Enum):
    MISSING_SAVED_POST = "missing_saved_post"
    ORPHANED_SAVED_POST = "orphaned_saved_post"
    INCORRECT_COUNT = "incorrect_count"


@dataclass
class ConsistencyIssue:
    """Represents a single consistency issue found during validation"""
    issue_type: ConsistencyIssueType
    collection_id: str
    post_id: Optional[str] = None
    user_id: Optional[str] = None
    expected_count: Optional[int] = None
    actual_count: Optional[int] = None
    details: Optional[Dict[str, Any]] = None


@dataclass
class ConsistencyReport:
    """Report containing all consistency issues found"""
    timestamp: datetime
    total_collections_checked: int
    total_saved_posts_checked: int
    missing_saved_posts: List[ConsistencyIssue]
    orphaned_saved_posts: List[ConsistencyIssue]
    incorrect_counts: List[ConsistencyIssue]
    
    @property
    def total_issues(self) -> int:
        return len(self.missing_saved_posts) + len(self.orphaned_saved_posts) + len(self.incorrect_counts)
    
    @property
    def has_issues(self) -> bool:
        return self.total_issues > 0
    
    def get_summary(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp.isoformat(),
            "total_collections_checked": self.total_collections_checked,
            "total_saved_posts_checked": self.total_saved_posts_checked,
            "total_issues": self.total_issues,
            "issues_by_type": {
                "missing_saved_posts": len(self.missing_saved_posts),
                "orphaned_saved_posts": len(self.orphaned_saved_posts),
                "incorrect_counts": len(self.incorrect_counts)
            },
            "has_issues": self.has_issues
        }


class SavedPostsConsistencyService:
    """Service for checking and repairing consistency between collection.contents and saved_posts"""
    
    def __init__(self, session: AsyncSession):
        self.session = session

    async def check_consistency(self, collection_ids: Optional[List[str]] = None) -> ConsistencyReport:
        """
        Check for inconsistencies between collection.contents and saved_posts
        
        Args:
            collection_ids: Optional list of specific collection IDs to check. 
                          If None, checks all collections.
        
        Returns:
            ConsistencyReport with all found issues
        """
        logger.info(f"Starting consistency check for {len(collection_ids) if collection_ids else 'all'} collections")
        
        # Get statistics for the report
        collections_count = await self._get_collections_count(collection_ids)
        saved_posts_count = await self._get_saved_posts_count(collection_ids)
        
        # Find all types of issues
        missing_saved_posts = await self._find_missing_saved_posts(collection_ids)
        orphaned_saved_posts = await self._find_orphaned_saved_posts(collection_ids)
        incorrect_counts = await self._find_incorrect_counts(collection_ids)
        
        report = ConsistencyReport(
            timestamp=datetime.utcnow(),
            total_collections_checked=collections_count,
            total_saved_posts_checked=saved_posts_count,
            missing_saved_posts=missing_saved_posts,
            orphaned_saved_posts=orphaned_saved_posts,
            incorrect_counts=incorrect_counts
        )
        
        logger.info(f"Consistency check completed. Found {report.total_issues} issues")
        return report

    async def repair_inconsistencies(self, report: ConsistencyReport, dry_run: bool = False) -> Dict[str, int]:
        """
        Repair detected inconsistencies
        
        Args:
            report: ConsistencyReport with issues to repair
            dry_run: If True, only log what would be done without making changes
        
        Returns:
            Dictionary with counts of repaired issues by type
        """
        logger.info(f"Starting repair process (dry_run={dry_run}) for {report.total_issues} issues")
        
        repair_counts = {
            "missing_saved_posts_created": 0,
            "orphaned_saved_posts_removed": 0,
            "incorrect_counts_fixed": 0
        }
        
        if dry_run:
            logger.info("DRY RUN MODE: No changes will be made")
        
        try:
            # Start transaction for all repairs
            async with self.session.begin():
                # Repair missing saved_posts
                for issue in report.missing_saved_posts:
                    if dry_run:
                        logger.info(f"Would create SavedPost: collection={issue.collection_id}, post={issue.post_id}, user={issue.user_id}")
                    else:
                        await self._create_missing_saved_post(issue)
                    repair_counts["missing_saved_posts_created"] += 1
                
                # Remove orphaned saved_posts
                for issue in report.orphaned_saved_posts:
                    if dry_run:
                        logger.info(f"Would remove orphaned SavedPost: collection={issue.collection_id}, post={issue.post_id}, user={issue.user_id}")
                    else:
                        await self._remove_orphaned_saved_post(issue)
                    repair_counts["orphaned_saved_posts_removed"] += 1
                
                # Fix incorrect counts
                for issue in report.incorrect_counts:
                    if dry_run:
                        logger.info(f"Would fix count for collection {issue.collection_id}: {issue.actual_count} -> {issue.expected_count}")
                    else:
                        await self._fix_contents_count(issue)
                    repair_counts["incorrect_counts_fixed"] += 1
                
                if dry_run:
                    # Rollback transaction in dry run mode
                    await self.session.rollback()
                    logger.info("DRY RUN completed - no changes made")
                else:
                    logger.info("All repairs completed successfully")
                    
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error during repair process: {str(e)}")
            raise
        
        return repair_counts

    async def _get_collections_count(self, collection_ids: Optional[List[str]] = None) -> int:
        """Get count of collections to be checked"""
        if collection_ids:
            stmt = select(Collection.id).where(Collection.id.in_(collection_ids))
        else:
            stmt = select(Collection.id)
        
        result = await self.session.execute(stmt)
        return len(result.scalars().all())

    async def _get_saved_posts_count(self, collection_ids: Optional[List[str]] = None) -> int:
        """Get count of saved_posts to be checked"""
        if collection_ids:
            stmt = select(SavedPost.id).where(SavedPost.collection_id.in_(collection_ids))
        else:
            stmt = select(SavedPost.id)
        
        result = await self.session.execute(stmt)
        return len(result.scalars().all())

    async def _find_missing_saved_posts(self, collection_ids: Optional[List[str]] = None) -> List[ConsistencyIssue]:
        """Find posts in collection.contents but not in saved_posts"""
        collection_filter = ""
        if collection_ids:
            collection_ids_str = "','".join(collection_ids)
            collection_filter = f"AND c.id IN ('{collection_ids_str}')"
        
        query = text(f"""
            SELECT c.id as collection_id, c.author_id, unnest(c.contents) as post_id
            FROM collections c
            WHERE array_length(c.contents, 1) > 0
            {collection_filter}
            AND EXISTS (
                SELECT 1 FROM unnest(c.contents) as content_post_id
                WHERE NOT EXISTS (
                    SELECT 1 FROM saved_posts sp
                    WHERE sp.collection_id = c.id 
                    AND sp.post_id = content_post_id
                )
            )
        """)
        
        result = await self.session.execute(query)
        issues = []
        
        for row in result:
            issues.append(ConsistencyIssue(
                issue_type=ConsistencyIssueType.MISSING_SAVED_POST,
                collection_id=row.collection_id,
                post_id=row.post_id,
                user_id=row.author_id
            ))
        
        return issues

    async def _find_orphaned_saved_posts(self, collection_ids: Optional[List[str]] = None) -> List[ConsistencyIssue]:
        """Find SavedPost records without corresponding collection.contents entry"""
        stmt = select(SavedPost.collection_id, SavedPost.post_id, SavedPost.user_id).select_from(
            SavedPost.__table__.join(Collection)
        ).where(
            ~SavedPost.post_id.op('= ANY')(Collection.contents)
        )
        
        if collection_ids:
            stmt = stmt.where(SavedPost.collection_id.in_(collection_ids))
        
        result = await self.session.execute(stmt)
        issues = []
        
        for row in result:
            issues.append(ConsistencyIssue(
                issue_type=ConsistencyIssueType.ORPHANED_SAVED_POST,
                collection_id=row.collection_id,
                post_id=row.post_id,
                user_id=row.user_id
            ))
        
        return issues

    async def _find_incorrect_counts(self, collection_ids: Optional[List[str]] = None) -> List[ConsistencyIssue]:
        """Find collections with incorrect contents_count values"""
        query = text("""
            SELECT id, contents_count, COALESCE(array_length(contents, 1), 0) as actual_count
            FROM collections
            WHERE contents_count != COALESCE(array_length(contents, 1), 0)
        """)
        
        if collection_ids:
            collection_ids_str = "','".join(collection_ids)
            query = text(f"""
                SELECT id, contents_count, COALESCE(array_length(contents, 1), 0) as actual_count
                FROM collections
                WHERE contents_count != COALESCE(array_length(contents, 1), 0)
                AND id IN ('{collection_ids_str}')
            """)
        
        result = await self.session.execute(query)
        issues = []
        
        for row in result:
            issues.append(ConsistencyIssue(
                issue_type=ConsistencyIssueType.INCORRECT_COUNT,
                collection_id=row.id,
                expected_count=row.actual_count,
                actual_count=row.contents_count
            ))
        
        return issues

    async def _create_missing_saved_post(self, issue: ConsistencyIssue):
        """Create a missing SavedPost record"""
        stmt = pg_insert(SavedPost).values(
            user_id=issue.user_id,
            post_id=issue.post_id,
            collection_id=issue.collection_id,
            created_at=datetime.utcnow()
        ).on_conflict_do_nothing()
        
        await self.session.execute(stmt)
        logger.info(f"Created missing SavedPost: collection={issue.collection_id}, post={issue.post_id}")

    async def _remove_orphaned_saved_post(self, issue: ConsistencyIssue):
        """Remove an orphaned SavedPost record"""
        stmt = delete(SavedPost).where(
            and_(
                SavedPost.collection_id == issue.collection_id,
                SavedPost.post_id == issue.post_id,
                SavedPost.user_id == issue.user_id
            )
        )
        
        await self.session.execute(stmt)
        logger.info(f"Removed orphaned SavedPost: collection={issue.collection_id}, post={issue.post_id}")

    async def _fix_contents_count(self, issue: ConsistencyIssue):
        """Fix incorrect contents_count for a collection"""
        stmt = text("""
            UPDATE collections 
            SET contents_count = COALESCE(array_length(contents, 1), 0)
            WHERE id = :collection_id
        """).params(collection_id=issue.collection_id)
        
        await self.session.execute(stmt)
        logger.info(f"Fixed contents_count for collection {issue.collection_id}: {issue.actual_count} -> {issue.expected_count}")

    async def get_consistency_metrics(self) -> Dict[str, Any]:
        """Get high-level consistency metrics for monitoring"""
        # Total collections and saved posts
        collections_count = await self._get_collections_count()
        saved_posts_count = await self._get_saved_posts_count()
        
        # Quick consistency check
        report = await self.check_consistency()
        
        return {
            "total_collections": collections_count,
            "total_saved_posts": saved_posts_count,
            "consistency_issues": report.total_issues,
            "last_check_timestamp": report.timestamp.isoformat(),
            "issues_by_type": {
                issue_type.value: len(getattr(report, f"{issue_type.value}s", []))
                for issue_type in ConsistencyIssueType
            }
        }