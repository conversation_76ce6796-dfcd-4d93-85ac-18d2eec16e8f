from fastapi import Depends, status, HTTPException
from sqlalchemy import select

from src.database.session import AsyncSession, get_session
from src.database.models import Post
from src.common.caching.caching_client import CachingClient, CachingClientGetter
from src.common.post_service import PostService, get_post_service
from src.common.recommender.client import RecommenderClient, get_recommender_client
from src.feed.logger import logger

from src.feed.service import RecommenderService
from src.feed.cache_service import UserBlockCacheService



async def valid_post(post_id: str, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(Post).where(Post.id == post_id)
    post = (await session.execute(stmt)).first()
    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")
    return post_id


class RecommenderServiceGetter:
    def __init__(self, caching_client: CachingClient | None = None, session: AsyncSession | None = None, post_service: PostService | None = None, recommender_client: RecommenderClient | None = None):
        self.caching_client = caching_client
        self.session = session
        self.post_service = post_service
        self.recommender_client = recommender_client

    def __call__(
            self,
            caching_client: CachingClient = Depends(CachingClientGetter(logger)),
            session: AsyncSession = Depends(get_session),
            post_service: PostService = Depends(get_post_service),
            recommender_client: RecommenderClient | None = Depends(get_recommender_client)
    ) -> RecommenderService:
        # 如果未配置推荐客户端，则在依赖注入阶段快速失败，避免 None 被调用
        from fastapi import HTTPException, status
        effective_recommender = self.recommender_client or recommender_client
        if effective_recommender is None:
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Recommender client is not configured")

        return RecommenderService(
            client=self.caching_client or caching_client, 
            session=self.session or session,
            post_service=self.post_service or post_service,
            recommender_client=effective_recommender
        )


class UserBlockCacheServiceGetter:
    """用户屏蔽缓存服务依赖注入器"""
    
    def __init__(self, caching_client: CachingClient | None = None, session: AsyncSession | None = None):
        self.caching_client = caching_client
        self.session = session

    def __call__(
            self,
            caching_client: CachingClient = Depends(CachingClientGetter(logger)),
            session: AsyncSession = Depends(get_session)
    ) -> UserBlockCacheService:
        return UserBlockCacheService(
            caching_client=self.caching_client or caching_client, 
            session=self.session or session
        )


