import json
import hashlib
import asyncio
import time
from datetime import timedelta
from random import randint, random

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectin_polymorphic
from sqlalchemy import not_

from src.common.recommender import RecommenderClient
from src.common.constants import Language
from src.database.constants import Region
from src.database.models import Post, post_models
from src.database.constants import PostType
from src.common.caching.caching_client import CachingClient
from src.common.post_service import PostService
from src.common.utils import generate_hash_key
from .logger import logger
from .settings import settings


class RecommenderService:
    recommendation_endpoint = "recommendations"
    similar_endpoint = "similar"
    cache_time = timedelta(minutes=30)

    def __init__(self, client: CachingClient, session: AsyncSession, post_service: PostService, recommender_client: RecommenderClient | None = None):
        self.caching_client = client
        self.session = session
        self.post_service = post_service
        self.recommender_client = recommender_client

    async def get_recommendation_ids(
            self,
            user_id: str,
            params: Params,
            region: Region | None = None,
            post_type: list[PostType] | None = None,
            languages: list[Language] | None = None,
            blocked_author_ids: list[str] | None = None
    ) -> dict:
        start_time = time.time()
        logger.debug(f"[PERF] get_recommendation_ids started - user_id: {user_id}, post_type: {post_type}, page: {params.page}, size: {params.size}")
        
        cache_start = time.time()
        random_factor = round(random(), 3)
        key = generate_hash_key(user_id if user_id else randint(0, 10), post_type, params.page, params.size, languages, blocked_author_ids, random_factor)
        cache = await self.caching_client.get(endpoint=self.recommendation_endpoint, key=key)
        cache_duration = (time.time() - cache_start) * 1000
        
        if cache:
            total_duration = (time.time() - start_time) * 1000
            logger.debug(f"[PERF] get_recommendation_ids cache_hit - cache_time: {cache_duration:.2f}ms, total_time: {total_duration:.2f}ms, user_id: {user_id}")
            return cache
        
        logger.debug(f"[PERF] get_recommendation_ids cache_miss - cache_time: {cache_duration:.2f}ms, user_id: {user_id}")
        
        # 调用推荐服务
        recommender_start = time.time()
        response = await self.recommender_client.get_user_recommendations(
            user_id=user_id,
            params=params,
            post_type=post_type,
            region=region,
            languages=languages,
            random_factor=random_factor
        )
        recommender_duration = (time.time() - recommender_start) * 1000
        logger.debug(f"[PERF] get_recommendation_ids recommender_call - time: {recommender_duration:.2f}ms, user_id: {user_id}")
        
        # 缓存结果
        cache_set_start = time.time()
        await self.caching_client.set(
            endpoint=self.recommendation_endpoint,
            key=key,
            value=response,
            expiration_time=self.cache_time
        )
        cache_set_duration = (time.time() - cache_set_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_recommendation_ids cache_set - time: {cache_set_duration:.2f}ms, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendation_ids completed - total_time: {total_duration:.2f}ms, user_id: {user_id}")
        
        return response

    async def get_similar_post_ids(
            self,
            post_id: str,
            params: Params,
            region: Region | None = None,
            post_type: PostType | None = None,
            languages: list[Language] | None = None,
            blocked_author_ids: list[str] | None = None
    ) -> list[str]:
        start_time = time.time()
        logger.debug(f"[PERF] get_similar_post_ids started - post_id: {post_id}, post_type: {post_type}, page: {params.page}, size: {params.size}")
        
        cache_start = time.time()
        key = generate_hash_key(post_id, post_type, params.page, params.size, languages, blocked_author_ids)
        cache: list[str] | None = await self.caching_client.get(endpoint=self.similar_endpoint, key=key)
        cache_duration = (time.time() - cache_start) * 1000
        
        if cache:
            total_duration = (time.time() - start_time) * 1000
            logger.debug(f"[PERF] get_similar_post_ids cache_hit - cache_time: {cache_duration:.2f}ms, total_time: {total_duration:.2f}ms, post_id: {post_id}")
            return cache
        
        logger.debug(f"[PERF] get_similar_post_ids cache_miss - cache_time: {cache_duration:.2f}ms, post_id: {post_id}")
        
        # 调用推荐服务
        recommender_start = time.time()
        post_ids = await self.recommender_client.get_similar_posts(
            post_id=post_id,
            params=params,
            post_type=post_type,
            region=region,
            languages=languages
        )
        recommender_duration = (time.time() - recommender_start) * 1000
        logger.debug(f"[PERF] get_similar_post_ids recommender_call - time: {recommender_duration:.2f}ms, result_count: {len(post_ids)}, post_id: {post_id}")
        
        # 缓存结果
        cache_set_start = time.time()
        await self.caching_client.set(
            endpoint=self.similar_endpoint,
            key=key,
            value=post_ids,
            expiration_time=self.cache_time
        )
        cache_set_duration = (time.time() - cache_set_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_similar_post_ids cache_set - time: {cache_set_duration:.2f}ms, post_id: {post_id}")
        logger.debug(f"[PERF] get_similar_post_ids completed - total_time: {total_duration:.2f}ms, post_id: {post_id}")
        
        return post_ids

    async def get_recommendations(
            self,
            user_id: str,
            params: Params,
            region: Region | None = None,
            post_type: list[PostType] | None = None,
            languages: list[Language] | None = None,
            blocked_author_ids: list[str] | None = None
    ):
        start_time = time.time()
        logger.debug(f"[PERF] get_recommendations started - user_id: {user_id}, post_type: {post_type}, page: {params.page}, size: {params.size}")
        
        # 获取推荐ID
        id_fetch_start = time.time()
        response = await self.get_recommendation_ids(
            region=region,
            user_id=user_id,
            post_type=post_type,
            params=params,
            languages=languages
        )
        id_fetch_duration = (time.time() - id_fetch_start) * 1000
        logger.debug(f"[PERF] get_recommendations id_fetch - time: {id_fetch_duration:.2f}ms, user_id: {user_id}")
        
        # 处理响应数据
        data_process_start = time.time()
        logger.debug(f"response: {response}")
        if isinstance(response, dict):
            total = response.get("total", 0)
            items = response.get("items", [])
            # 兼容 recommender 返回 items 为 dict 列表或纯 ID 列表两种情况
            if items and isinstance(items[0], dict):
                post_ids = [item.get("post_id") for item in items if isinstance(item, dict) and item.get("post_id")]
            else:
                post_ids = items
        else:
            total = 0
            post_ids = response

        with open(f"{params.page}-{params.size}-{languages}.json", "w", encoding="utf-8") as f:
            json.dump(post_ids, f)

        if not post_ids:
            empty_page = {"items": [], "page": params.page, "size": params.size, "total": 0}
            data_process_duration = (time.time() - data_process_start) * 1000
            total_duration = (time.time() - start_time) * 1000
            logger.debug(f"[PERF] get_recommendations empty_result - data_process_time: {data_process_duration:.2f}ms, total_time: {total_duration:.2f}ms, user_id: {user_id}")
            return empty_page
        
        data_process_duration = (time.time() - data_process_start) * 1000
        logger.debug(f"[PERF] get_recommendations data_process - time: {data_process_duration:.2f}ms, post_ids_count: {len(post_ids)}, user_id: {user_id}")

        # 构建数据库查询
        query_build_start = time.time()
        stmt = self.post_service.get_posts_by_ids(post_ids).options(
            selectin_polymorphic(Post, post_models),
            self.post_service.in_collection_expression(user_id=user_id),
            self.post_service.is_liked_expression(user_id=user_id)
        )
        
        # 过滤掉被屏蔽用户的帖子 (额外保证查询时也过滤)
        if blocked_author_ids and len(blocked_author_ids) > 0:
            stmt = stmt.where(not_(Post.author_id.in_(blocked_author_ids)))
        
        query_build_duration = (time.time() - query_build_start) * 1000
        logger.debug(f"[PERF] get_recommendations query_build - time: {query_build_duration:.2f}ms, blocked_authors_count: {len(blocked_author_ids) if blocked_author_ids else 0}, user_id: {user_id}")

        # 执行数据库查询
        db_query_start = time.time()
        results = await self.session.execute(stmt)
        items = results.scalars().all()
        db_query_duration = (time.time() - db_query_start) * 1000
        
        # 构建结果
        result_build_start = time.time()
        result = {
            "items": items,
            "page": params.page,
            "size": params.size,
            "total": total
        }
        result_build_duration = (time.time() - result_build_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_recommendations db_query - time: {db_query_duration:.2f}ms, items_count: {len(items)}, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendations result_build - time: {result_build_duration:.2f}ms, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendations completed - total_time: {total_duration:.2f}ms, user_id: {user_id}")

        return result
    
    async def get_tag_recall_stats(self):
        return await self.recommender_client.get_tag_recall_stats()
    
    async def compute_tag_recall(self, sync: bool = False):
        return await self.recommender_client.compute_tag_recall(sync=sync)

    async def get_tag_recommendations(
            self,
            tag: str,
            user_id: str,
            params: Params,
            blocked_author_ids: list[str] | None = None,
    ):
        post_ids =  await self.recommender_client.get_post_by_tag(tag=tag)
        if not post_ids or len(post_ids) < params.page * params.size:
            return {"items": [], "page": params.page, "size": params.size, "total": 0}
        total = len(post_ids)
        post_ids = post_ids[params.page * params.size: (params.page + 1) * params.size]
        
        # 构建数据库查询
        stmt = self.post_service.get_posts_by_ids(post_ids).options(
            selectin_polymorphic(Post, post_models),
            self.post_service.in_collection_expression(user_id=user_id),
            self.post_service.is_liked_expression(user_id=user_id)
        )
        
        # 过滤掉被屏蔽用户的帖子 (额外保证查询时也过滤)
        if blocked_author_ids and len(blocked_author_ids) > 0:
            stmt = stmt.where(not_(Post.author_id.in_(blocked_author_ids)))
        
        # 执行数据库查询
        results = await self.session.execute(stmt)
        items = results.scalars().all()
        
        # 构建结果
        result = {
            "items": items,
            "page": params.page,
            "size": params.size,
            "total": total
        }
        
        return result
        
        

    async def get_mixed_recommendations(
            self,
            user_id: str,
            params: Params,
            image_count: int,
            video_count: int,
            region: Region | None = None,
            languages: list[Language] | None = None,
            blocked_author_ids: list[str] | None = None
    ):
        """
        并行获取图片和视频推荐，然后按交替模式排列
        """
        start_time = time.time()
        logger.debug(f"[PERF] get_mixed_recommendations started - user_id: {user_id}, image_count: {image_count}, video_count: {video_count}, page: {params.page}, size: {params.size}")
        
        # 创建参数对象
        params_create_start = time.time()
        image_params = Params(page=params.page, size=image_count)
        video_params = Params(page=params.page, size=video_count)
        params_create_duration = (time.time() - params_create_start) * 1000
        logger.debug(f"[PERF] get_mixed_recommendations params_create - time: {params_create_duration:.2f}ms, user_id: {user_id}")
        
        # 并行获取推荐ID - 这里是关键的并行优化
        parallel_start = time.time()
        image_task = self.get_recommendation_ids(
            user_id=user_id,
            params=image_params,
            region=region,
            post_type=[PostType.IMAGE],
            languages=languages,
            blocked_author_ids=blocked_author_ids
        )
        
        video_task = self.get_recommendation_ids(
            user_id=user_id,
            params=video_params,
            region=region,
            post_type=[PostType.VIDEO],
            languages=languages,
            blocked_author_ids=blocked_author_ids
        )
        
        # 并行执行推荐ID获取
        gather_start = time.time()
        image_response, video_response = await asyncio.gather(image_task, video_task)
        gather_duration = (time.time() - gather_start) * 1000
        parallel_duration = (time.time() - parallel_start) * 1000
        
        logger.debug(f"[PERF] get_mixed_recommendations parallel_fetch - gather_time: {gather_duration:.2f}ms, total_parallel_time: {parallel_duration:.2f}ms, user_id: {user_id}")
        
        # 提取ID列表
        id_extraction_start = time.time()
        image_ids = []
        video_ids = []
        total_count = 0
        
        if isinstance(image_response, dict):
            image_ids = [d["post_id"] for d in image_response.get("items", [])]
            total_count += image_response.get("total", 0)
        
        if isinstance(video_response, dict):
            video_ids = [d["post_id"] for d in video_response.get("items", [])]
            total_count += video_response.get("total", 0)
        
        id_extraction_duration = (time.time() - id_extraction_start) * 1000
        logger.debug(f"[PERF] get_mixed_recommendations id_extraction - time: {id_extraction_duration:.2f}ms, image_ids: {len(image_ids)}, video_ids: {len(video_ids)}, user_id: {user_id}")
        
        # 如果没有获取到任何ID，返回空结果
        if not image_ids and not video_ids:
            total_duration = (time.time() - start_time) * 1000
            logger.debug(f"[PERF] get_mixed_recommendations empty_result - total_time: {total_duration:.2f}ms, user_id: {user_id}")
            return {
                "items": []
            }
        
        # 合并所有ID用于单次数据库查询
        merge_start = time.time()
        all_post_ids = image_ids + video_ids
        merge_duration = (time.time() - merge_start) * 1000
        logger.debug(f"[PERF] get_mixed_recommendations id_merge - time: {merge_duration:.2f}ms, total_ids: {len(all_post_ids)}, user_id: {user_id}")
        
        # 单次数据库查询获取所有帖子
        db_query_start = time.time()
        stmt = self.post_service.get_posts_by_ids(all_post_ids).options(
            selectin_polymorphic(Post, post_models),
            self.post_service.in_collection_expression(user_id=user_id),
            self.post_service.is_liked_expression(user_id=user_id)
        )
        
        # 过滤被屏蔽用户的帖子
        if blocked_author_ids and len(blocked_author_ids) > 0:
            stmt = stmt.where(not_(Post.author_id.in_(blocked_author_ids)))
        
        results = await self.session.execute(stmt)
        all_posts = results.scalars().all()
        db_query_duration = (time.time() - db_query_start) * 1000
        logger.debug(f"[PERF] get_mixed_recommendations db_query - time: {db_query_duration:.2f}ms, posts_fetched: {len(all_posts)}, user_id: {user_id}")
        
        # 创建ID到帖子的映射，提高查找效率
        mapping_start = time.time()
        post_map = {post.id: post for post in all_posts}
        logger.debug(f"[PERF] get_mixed_recommendations post_mapping, post_map: {post_map}, user_id: {user_id}")
        
        # 分别获取图片和视频帖子，保持推荐顺序
        image_posts = [post_map[post_id] for post_id in image_ids if post_id in post_map]
        video_posts = [post_map[post_id] for post_id in video_ids if post_id in post_map]
        
        # 按时间排序
        image_posts.sort(key=lambda post: post.created_at, reverse=True)
        video_posts.sort(key=lambda post: post.created_at, reverse=True)
        mapping_duration = (time.time() - mapping_start) * 1000
        logger.debug(f"[PERF] get_mixed_recommendations post_mapping - time: {mapping_duration:.2f}ms, image_posts: {len(image_posts)}, video_posts: {len(video_posts)}, user_id: {user_id}")
        
        # 高效交替算法：3图片:2视频模式
        interleave_start = time.time()
        items = []
        image_idx = 0
        video_idx = 0
        total_size = params.size
        
        while len(items) < total_size and (image_idx < len(image_posts) or video_idx < len(video_posts)):
            # 每5个item的循环：3张图片(位置0,1,3) + 2个视频(位置2,4)
            cycle_position = len(items) % 5
            should_place_image = cycle_position in [0, 1, 3]
            
            if should_place_image:
                if image_idx < len(image_posts):
                    items.append(image_posts[image_idx])
                    image_idx += 1
                elif video_idx < len(video_posts):  # 图片用完了，用视频补充
                    items.append(video_posts[video_idx])
                    video_idx += 1
            else:  # 放视频 (位置 2,4)
                if video_idx < len(video_posts):
                    items.append(video_posts[video_idx])
                    video_idx += 1
                elif image_idx < len(image_posts):  # 视频用完了，用图片补充
                    items.append(image_posts[image_idx])
                    image_idx += 1
        
        interleave_duration = (time.time() - interleave_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_mixed_recommendations interleave - time: {interleave_duration:.2f}ms, final_items: {len(items)}, user_id: {user_id}")
        logger.debug(f"[PERF] get_mixed_recommendations completed - total_time: {total_duration:.2f}ms, user_id: {user_id}")
        
        return {
            "items": items
        }

    async def get_similar(
            self,
            post_id: str,
            params: Params,
            region: Region | None = None,
            post_type: PostType | None = None,
            user_id: str | None = None,
            languages: list[Language] | None = None,
            blocked_author_ids: list[str] | None = None
    ):
        start_time = time.time()
        logger.debug(f"[PERF] get_similar started - post_id: {post_id}, post_type: {post_type}, user_id: {user_id}, page: {params.page}, size: {params.size}")
        
        # 获取相似帖子ID
        id_fetch_start = time.time()
        post_ids = await self.get_similar_post_ids(
            post_id=post_id,
            params=params,
            post_type=post_type,
            region=region,
            languages=languages
        )
        id_fetch_duration = (time.time() - id_fetch_start) * 1000
        logger.debug(f"[PERF] get_similar id_fetch - time: {id_fetch_duration:.2f}ms, post_ids_count: {len(post_ids)}, post_id: {post_id}")
        
        # 构建数据库查询
        query_build_start = time.time()
        stmt = self.post_service.get_posts_by_ids(post_ids).options(
            selectin_polymorphic(Post, post_models),
            self.post_service.in_collection_expression(user_id=user_id),
            self.post_service.is_liked_expression(user_id=user_id)
        )
        
        # 过滤掉被屏蔽用户的帖子 (额外保证查询时也过滤)
        if blocked_author_ids and len(blocked_author_ids) > 0:
            stmt = stmt.where(not_(Post.author_id.in_(blocked_author_ids)))
        
        query_build_duration = (time.time() - query_build_start) * 1000
        logger.debug(f"[PERF] get_similar query_build - time: {query_build_duration:.2f}ms, blocked_authors_count: {len(blocked_author_ids) if blocked_author_ids else 0}, post_id: {post_id}")
        
        # 执行分页查询
        paginate_start = time.time()
        result = await paginate(
            self.session,
            stmt,
            Params(size=params.size, page=1)
        )
        paginate_duration = (time.time() - paginate_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_similar paginate - time: {paginate_duration:.2f}ms, result_items: {len(result.items)}, post_id: {post_id}")
        logger.debug(f"[PERF] get_similar completed - total_time: {total_duration:.2f}ms, post_id: {post_id}")
        
        return result
