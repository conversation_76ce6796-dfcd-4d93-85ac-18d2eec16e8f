from datetime import timedelta
from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.caching.caching_client import CachingClient
from src.database.models.Author import authors_blocks
from .logger import logger


class UserBlockCacheService:
    """用户屏蔽列表缓存服务"""
    
    CACHE_ENDPOINT = "user_blocks"
    CACHE_EXPIRY = timedelta(minutes=15)  # 15分钟缓存过期
    
    def __init__(self, caching_client: CachingClient, session: AsyncSession):
        self.caching_client = caching_client
        self.session = session
    
    async def get_blocked_author_ids(self, user_id: str) -> List[str]:
        """
        获取用户屏蔽的作者ID列表，优先从缓存获取
        
        Args:
            user_id: 用户ID
            
        Returns:
            被屏蔽的作者ID列表
        """
        cache_key = f"blocked_authors:{user_id}"
        
        # 尝试从缓存获取
        cached_result = await self.caching_client.get(
            endpoint=self.CACHE_ENDPOINT,
            key=cache_key
        )
        
        if cached_result is not None:
            logger.debug(f"Cache hit for user {user_id} blocked authors")
            return cached_result
        
        # 缓存未命中，从数据库查询
        logger.debug(f"Cache miss for user {user_id} blocked authors, querying database")
        blocked_authors_ids = await self._fetch_blocked_authors_from_db(user_id)
        
        # 存储到缓存
        await self.caching_client.set(
            endpoint=self.CACHE_ENDPOINT,
            key=cache_key,
            value=blocked_authors_ids,
            expiration_time=self.CACHE_EXPIRY
        )
        
        return blocked_authors_ids
    
    async def _fetch_blocked_authors_from_db(self, user_id: str) -> List[str]:
        """
        从数据库获取用户屏蔽的作者ID列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            被屏蔽的作者ID列表
        """
        subq_blocked_authors = (
            select(authors_blocks.c.blocked_id)
            .where(authors_blocks.c.blocker_id == user_id)
        )
        
        blocked_authors_result = await self.session.execute(subq_blocked_authors)
        blocked_authors_ids = [author_id for author_id, in blocked_authors_result]
        
        logger.debug(f"Fetched {len(blocked_authors_ids)} blocked authors for user {user_id}")
        return blocked_authors_ids
    
    async def invalidate_user_cache(self, user_id: str) -> None:
        """
        使用户屏蔽列表缓存失效
        
        Args:
            user_id: 用户ID
        """
        cache_key = f"blocked_authors:{user_id}"
        try:
            # Redis没有直接的删除方法，我们设置一个很短的过期时间
            await self.caching_client.set(
                endpoint=self.CACHE_ENDPOINT,
                key=cache_key,
                value=[],
                expiration_time=timedelta(seconds=1)
            )
            logger.debug(f"Invalidated cache for user {user_id} blocked authors")
        except Exception as e:
            logger.error(f"Failed to invalidate cache for user {user_id}: {e}")
    
    async def warm_cache(self, user_id: str) -> None:
        """
        预热用户屏蔽列表缓存
        
        Args:
            user_id: 用户ID
        """
        await self.get_blocked_author_ids(user_id)
        logger.debug(f"Warmed cache for user {user_id} blocked authors") 