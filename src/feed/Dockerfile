# Feed Service - Simplified using enhanced base
# Reduced from 76 lines to ~25 lines!

ARG PYTHON_VERSION=3.13.5
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv in builder stage
USER root
RUN pip install --no-cache-dir uv
USER appuser

# Copy service-specific requirements
COPY src/feed/requirements.txt /tmp/feed-requirements.txt
COPY src/authors/requirements.txt /tmp/authors-requirements.txt
COPY src/im/requirements.txt /tmp/im-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/feed-requirements.txt
RUN uv pip install --no-cache -r /tmp/authors-requirements.txt
RUN uv pip install --no-cache -r /tmp/im-requirements.txt

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Add service scripts to PATH
ENV PATH="$PATH:/src/feed/scripts"

# Copy service-specific code and dependencies
COPY --chown=appuser:appuser src/feed /src/feed
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database
COPY --chown=appuser:appuser src/auth /src/auth
COPY --chown=appuser:appuser src/authors /src/authors

# Set executable permissions for start script
RUN chmod +x /src/feed/scripts/start.sh
