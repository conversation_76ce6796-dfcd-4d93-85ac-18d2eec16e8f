from typing import List, Optional
from pydantic import BaseModel, Field, field_validator, computed_field

from src.common.utils import format_cents_to_usd_string
from src.database.schemas import Post, AuthorRead


class ImageDataStored(BaseModel):
    """Image data stored in database (without upload_url)"""
    url: str
    width: int
    height: int
    name: str
    content_type: str


class PostFeed(Post):
    title: str | None = None
    text: str | None = None
    description: str | None = None
    cover: str | None = None
    url: str | None = None
    url_type: str | None = None

    # Collection attrs
    content_type: str | None = None
    contents_count: int | None = None
    original_cover: str | None = None
    is_in_collection: bool | None = None
    is_liked: bool | None = None

    # Images attrs
    width: int | None = None
    height: int | None = None
    images_data: List[ImageDataStored] = Field(default=[], description="Multiple images data (without upload_url)")

    # Holdview attrs
    is_locked: bool = Field(default=False, description="Whether the post is locked due to holdview requirements")
    holdview_amount_int: Optional[int] = Field(default=0, alias="holdview_amount")

    author: AuthorRead
    
    @field_validator('images_data', mode='before')
    @classmethod
    def validate_images_data(cls, v):
        """Handle None values from database and convert to empty list"""
        if v is None:
            return []
        return v

    @computed_field  # type: ignore[misc]
    @property
    def holdview_amount(self) -> str:
        return format_cents_to_usd_string(self.holdview_amount_int)
