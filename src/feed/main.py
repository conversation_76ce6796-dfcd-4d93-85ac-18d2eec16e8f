from typing import Optional, List, Literal, Dict
import time
import math

from fastapi.openapi.utils import get_openapi
from httpx import AsyncClient

from fastapi import FastAPI, Depends, Query,status, Request
from fastapi_pagination import Page, Params, add_pagination, paginate
from fastapi_pagination.ext.sqlalchemy import paginate as sa_paginate
from fastapi_pagination.utils import disable_installed_extensions_check
from sqlalchemy import select, and_, func, text, or_, not_, literal, union_all
from sqlalchemy.orm import selectin_polymorphic, with_expression, selectinload

from src.database.session import AsyncSession, get_session
from src.database.models import Post, post_models, Collection, Answer, User
from src.database.models.Author import authors_followers, Author
from src.database.models.Post import authors_likes
from src.database.models.Collection import collections_subscriptions
from src.database.models.Question import questions_subscriptions
from src.database.models.Author import authors_blocks
from src.database.constants import FeedReason, PostStatus, PostType, Region
from src.auth import optional_user
from src.common.recommender import RecommenderClient
from src.feed.settings import settings
from src.infra.logger import get_logger
from src.common.constants import Language
from src.infra.app import create_app, install_custom_openapi

from src.feed.service import RecommenderService
from src.feed.dependencies import valid_post, RecommenderServiceGetter, UserBlockCacheServiceGetter
from src.feed.cache_service import UserBlockCacheService
from src.common.holdview import HoldviewService, get_holdview_service

from src.feed.schemas import PostFeed


logger = get_logger("feed", level="INFO", file_path="latest.log")
app = create_app(title="Feed Service", version="1.0.1", description="Feed Service API", request_logger=logger)
add_pagination(app)
disable_installed_extensions_check()
http_client = AsyncClient()

# 安全地创建 recommender_client，如果配置缺失则为 None
try:
    recommender_client = RecommenderClient(recommender_token=settings.RECOMMENDER_TOKEN) if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN else None
except Exception:
    recommender_client = None
 

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get(
    "/feed",
    response_model=Page[PostFeed]
)
async def get_feed(
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    start_time = time.time()
    user_id = getattr(user, 'id', None)
    logger.debug(f"[PERF] get_feed started - user_id: {user_id}, page: {params.page}, size: {params.size}")
    
    if user is None:
        query_start = time.time()
        stmt = select(Post).where(
            and_(
                Post.type.in_([PostType.IMAGE, PostType.VIDEO]),
                Post.status == PostStatus.POSTED,
                Post.holdview_amount == 0  # Anonymous users can only see public posts (holdview_amount = 0)
            )
        ).order_by(
            Post.created_at.desc()
        ).options(
            selectin_polymorphic(Post, post_models),
            selectinload(Post.author),
            with_expression(Post.feed_reason, None),
            with_expression(Post.is_liked, False),
            with_expression(Post.is_in_collection, False)
        )
        result = await sa_paginate(session, stmt, params)
        
        # Apply holdview lock status to anonymous user posts (all will be unlocked since holdview_amount = 0)
        posts_with_locks = await holdview_service.apply_holdview_locks(None, result.items)
        result.items = posts_with_locks
        
        query_duration = (time.time() - query_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_feed (anonymous) - query_time: {query_duration:.2f}ms, total_time: {total_duration:.2f}ms, items_count: {len(result.items)}")
        
        # 记录响应数据大小
        import json
        response_size = len(json.dumps(result.dict(), default=str))
        logger.debug(f"[PERF] get_feed response_size - size: {response_size} bytes, items_count: {len(result.items)}")
        
        return result
    else:
        # 获取当前用户屏蔽的用户ID列表
        query_start = time.time()
        subq_blocked_authors = (
            select(authors_blocks.c.blocked_id).
            where(authors_blocks.c.blocker_id == user.id).
            scalar_subquery()
        )

        subq_liked = (
            select(authors_likes.c.post_id).
            where(authors_likes.c.author_id == user.id)
        )

        subq_collections_posts = (
            select(func.unnest(Collection.contents)).
            where(Collection.author_id == user.id)
        )

        subq_subscribed_authors = (
            select(authors_followers.c.author_id).
            where(authors_followers.c.follower_id == user.id)
        )

        subquery_duration = (time.time() - query_start) * 1000
        logger.debug(f"[PERF] get_feed subqueries_build - time: {subquery_duration:.2f}ms, user_id: {user_id}")

        # Build individual statements with proper loading
        stmt_build_start = time.time()
        
        # Base query options for all statements
        base_options = [
            selectin_polymorphic(Post, post_models),
            selectinload(Post.author)
        ]
        
        # Query for posts from subscribed authors
        stmt_authors_posts = (
            select(
                Post,
                literal(FeedReason.AUTHOR_CREATE.value).label("feed_reason"),
                Post.created_at.label("sort_time")
            )
            .where(
                and_(
                    Post.type.in_([PostType.IMAGE, PostType.VIDEO]),
                    Post.status == PostStatus.POSTED,
                    Post.author_id.in_(subq_subscribed_authors),
                    not_(Post.author_id.in_(subq_blocked_authors))
                )
            )
        )

        stmt_authors_liked_posts = (
            select(
                Post,
                literal(FeedReason.AUTHORS_LIKES.value).label("feed_reason"),
                Post.created_at.label("sort_time")
            )
            .join(authors_likes, authors_likes.c.post_id == Post.id)
            .where(
                and_(
                    Post.type.in_([PostType.IMAGE, PostType.VIDEO]),
                    Post.status == PostStatus.POSTED,
                    authors_likes.c.author_id.in_(subq_subscribed_authors),
                    not_(Post.author_id.in_(subq_blocked_authors))
                )
            )
        )

        subq_subscribed_collection_ids = (
            select(collections_subscriptions.c.collection_id)
            .where(collections_subscriptions.c.author_id == user.id)
        )
        # 再获取这些合集中的帖子
        subq_subscribed_collections_posts = (
            select(func.unnest(Collection.contents))
            .where(Collection.id.in_(subq_subscribed_collection_ids))
        )

        stmt_collections_posts = (
            select(
                Post,
                literal(FeedReason.COLLECTIONS_POSTS.value).label("feed_reason"),
                Post.created_at.label("sort_time")
            )
            .where(
                and_(
                    Post.type.in_([PostType.IMAGE, PostType.VIDEO]),
                    Post.status == PostStatus.POSTED,
                    Post.id.in_(subq_subscribed_collections_posts),
                    not_(Post.author_id.in_(subq_blocked_authors))
                )
            )
        )

        union_stmt = union_all(
            stmt_authors_posts,
            stmt_authors_liked_posts,
            stmt_collections_posts
        ).subquery()
        stmt_build_duration = (time.time() - stmt_build_start) * 1000
        logger.debug(f"[PERF] get_feed statements_build - time: {stmt_build_duration:.2f}ms, user_id: {user_id}")

        final_query = (
            select(Post)
            .select_from(union_stmt)
            .join(Post, Post.id == union_stmt.c.id)  # 假设 Post 主键是 id
            .options(
                selectin_polymorphic(Post, post_models),
                selectinload(Post.author),
                with_expression(Post.feed_reason, union_stmt.c.feed_reason),
                with_expression(Post.is_liked, Post.id.in_(subq_liked)),
                with_expression(Post.is_in_collection, Post.id.in_(subq_collections_posts))
            )
            .order_by(union_stmt.c.sort_time.desc())
        )

        count_stmt = select(func.count()).select_from(
            union_all(
                stmt_authors_posts.with_only_columns(Post.id),
                stmt_authors_liked_posts.with_only_columns(Post.id),
                stmt_collections_posts.with_only_columns(Post.id),
            ).subquery()
        )

        db_paginate_start = time.time()
        try:
            page = params.page
            size = params.size

            # 获取总数
            total_result = await session.execute(select(func.count()).select_from(count_stmt.subquery()))
            total = total_result.scalar() or 0

            # 计算总页数
            pages = math.ceil(total / size) if size > 0 else 1

            # 执行分页查询
            paginated_query = final_query.limit(size).offset((page - 1) * size)
            res = await session.execute(paginated_query)
            items = res.scalars().unique().all()
            result = Page(
                items=items,
                page=page,
                size=size,
                total=total,
                pages=pages
            )
            # Apply holdview lock status to posts for logged-in users
            posts_with_locks = await holdview_service.apply_holdview_locks(user, result.items)
            result.items = posts_with_locks
            db_paginate_duration = (time.time() - db_paginate_start) * 1000
            total_duration = (time.time() - start_time) * 1000

            logger.debug(f"[PERF] get_feed (logged_in) - db_paginate_time: {db_paginate_duration:.2f}ms, "
                        f"total_time: {total_duration:.2f}ms, items_count: {len(result.items)}")

            # 记录响应大小
            import json
            response_size = len(json.dumps(result.dict(), default=str))
            logger.debug(f"[PERF] get_feed response_size - size: {response_size} bytes, items_count: {len(result.items)}")

            return result
        except Exception as e:
            logger.error(f"[ERROR] get_feed pagination failed for user {user.id}: {str(e)}")
            raise


@app.get(
    "/recommendations",
    response_model=Dict[str, List[PostFeed]]
)
async def get_recommendations(
        request: Request,
        post_type: Optional[List[PostType]] = Query(None),
        region: Region | None = None,
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        recommender_service: RecommenderService = Depends(RecommenderServiceGetter()),
        user_block_cache_service: UserBlockCacheService = Depends(UserBlockCacheServiceGetter()),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    start_time = time.time()
    user_id = getattr(user, 'id', None)
    
    # 记录请求到达时间和客户端信息
    import datetime
    request_timestamp = datetime.datetime.now().isoformat()
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")
    
    logger.debug(f"[PERF] get_recommendations request_received - timestamp: {request_timestamp}, client_ip: {client_ip}")
    logger.debug(f"[PERF] get_recommendations started - user_id: {user_id}, post_type: {post_type}, page: {params.page}, size: {params.size}")
    
    if post_type is None:
        post_type = [PostType.IMAGE, PostType.VIDEO]

    # 用户数据准备
    user_data_start = time.time()
    languages = ""
    blocked_authors_ids = []
    
    if user:
        languages = user.preferred_languages
        user_id = user.id
        # 使用缓存服务获取屏蔽用户列表，避免每次查询数据库
        cache_start = time.time()
        blocked_authors_ids = await user_block_cache_service.get_blocked_author_ids(user.id)
        cache_duration = (time.time() - cache_start) * 1000
        logger.debug(f"[PERF] get_recommendations blocked_cache_query - time: {cache_duration:.2f}ms, blocked_count: {len(blocked_authors_ids)}, user_id: {user_id}")
    
    user_data_duration = (time.time() - user_data_start) * 1000
    logger.debug(f"[PERF] get_recommendations user_data_prep - time: {user_data_duration:.2f}ms, languages: {languages}, user_id: {user_id}")

    # 如果请求包含视频类型，使用混合推荐（并行获取+交替排列）
    if PostType.VIDEO in post_type and PostType.IMAGE in post_type:
        # 计算视频和图片的数量分配
        mixed_start = time.time()
        total_size = params.size
        max_video_count = total_size // 2  # 视频数量不超过一半
        image_count = total_size - max_video_count
        
        logger.debug(f"[PERF] get_recommendations mixed_mode - image_count: {image_count}, video_count: {max_video_count}, user_id: {user_id}")
        
        # 使用服务层的并行混合推荐方法
        service_start = time.time()
        result = await recommender_service.get_mixed_recommendations(
            user_id=user_id,
            params=params,
            image_count=image_count,
            video_count=max_video_count,
            region=None,
            languages=languages,
            blocked_author_ids=blocked_authors_ids,
        )
        service_duration = (time.time() - service_start) * 1000
        logger.debug(f"[PERF] get_recommendations mixed_service_call - time: {service_duration:.2f}ms, user_id: {user_id}")
        
        # 如果没有获取到任何推荐结果，提前返回空页面
        if not result.get("items"):
            total_duration = (time.time() - start_time) * 1000
            logger.debug(f"[PERF] get_recommendations empty_result - total_time: {total_duration:.2f}ms, user_id: {user_id}")
            return {"items": []}
        
        mixed_duration = (time.time() - mixed_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_recommendations mixed_processing - time: {mixed_duration:.2f}ms, items_count: {len(result['items'])}, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendations completed (mixed) - total_time: {total_duration:.2f}ms, user_id: {user_id}")
        
        # Apply holdview lock status to posts
        holdview_start = time.time()
        posts_with_locks = await holdview_service.apply_holdview_locks(user, result["items"])
        result["items"] = posts_with_locks
        holdview_duration = (time.time() - holdview_start) * 1000
        logger.debug(f"[PERF] get_recommendations holdview_processing - time: {holdview_duration:.2f}ms, posts_count: {len(posts_with_locks)}, user_id: {user_id}")
        
        # 记录响应数据大小
        import json
        response_size = len(json.dumps(result, default=str))
        logger.debug(f"[PERF] get_recommendations response_size - size: {response_size} bytes, items_count: {len(result['items'])}, user_id: {user_id}")
        
        # 记录响应发送时间
        import datetime
        response_timestamp = datetime.datetime.now().isoformat()
        logger.debug(f"[PERF] get_recommendations response_sending - timestamp: {response_timestamp}, user_id: {user_id}")
        
        return result
    else:
        # 如果只请求单一类型或不包含视频，使用原有逻辑
        single_type_start = time.time()
        logger.debug(f"[PERF] get_recommendations single_type_mode - post_type: {post_type}, user_id: {user_id}")
        
        service_start = time.time()
        result = await recommender_service.get_recommendations(
            region=None,
            user_id=user_id,
            params=params,
            post_type=post_type,
            languages=languages,
            blocked_author_ids=blocked_authors_ids,
        )
        service_duration = (time.time() - service_start) * 1000
        single_type_duration = (time.time() - single_type_start) * 1000
        total_duration = (time.time() - start_time) * 1000
        
        logger.debug(f"[PERF] get_recommendations single_service_call - time: {service_duration:.2f}ms, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendations single_type_processing - time: {single_type_duration:.2f}ms, items_count: {len(result['items'])}, user_id: {user_id}")
        logger.debug(f"[PERF] get_recommendations completed (single) - total_time: {total_duration:.2f}ms, user_id: {user_id}")
        
        # Apply holdview lock status to posts
        holdview_start = time.time()
        posts_with_locks = await holdview_service.apply_holdview_locks(user, result["items"])
        result["items"] = posts_with_locks
        holdview_duration = (time.time() - holdview_start) * 1000
        logger.debug(f"[PERF] get_recommendations holdview_processing - time: {holdview_duration:.2f}ms, posts_count: {len(posts_with_locks)}, user_id: {user_id}")
        
        # 记录响应数据大小
        import json
        response_size = len(json.dumps(result, default=str))
        logger.debug(f"[PERF] get_recommendations response_size - size: {response_size} bytes, items_count: {len(result['items'])}, user_id: {user_id}")
        
        # 记录响应发送时间
        import datetime
        response_timestamp = datetime.datetime.now().isoformat()
        logger.debug(f"[PERF] get_recommendations response_sending - timestamp: {response_timestamp}, user_id: {user_id}")
        
        return result

@app.get(
    "/tag_recommendations",
    response_model=Page[PostFeed]
)
async def get_tag_recommendations(
        request: Request,
        tag: str,
        params: Params = Depends(),
        user: User | None = Depends(optional_user),
        recommender_service: RecommenderService = Depends(RecommenderServiceGetter()),
        user_block_cache_service: UserBlockCacheService = Depends(UserBlockCacheServiceGetter())
):
    start_time = time.time()
    user_id = getattr(user, 'id', None)
    
    # 记录请求到达时间和客户端信息
    import datetime
    request_timestamp = datetime.datetime.now().isoformat()
    client_ip = request.client.host if request.client else "unknown"
    
    blocked_authors_ids = []
    if user:
        blocked_authors_ids = await user_block_cache_service.get_blocked_author_ids(user.id)
    
    logger.debug(f"[PERF] get_recommendations request_received - timestamp: {request_timestamp}, client_ip: {client_ip}")
    logger.debug(f"[PERF] get_recommendations started - user_id: {user_id}, tag: {tag}, page: {params.page}, size: {params.size}")
    
    service_start = time.time()
    result = await recommender_service.get_tag_recommendations(
            tag=tag,
            user_id=user_id,
            params=params,
            blocked_author_ids=blocked_authors_ids,
        )
    service_duration = (time.time() - service_start) * 1000
    logger.debug(f"[PERF] get_recommendations mixed_service_call - time: {service_duration:.2f}ms, user_id: {user_id}")
        
    # 如果没有获取到任何推荐结果，提前返回空页面
    if not result.get("items"):
        total_duration = (time.time() - start_time) * 1000
        logger.debug(f"[PERF] get_recommendations empty_result - total_time: {total_duration:.2f}ms, user_id: {user_id}")
        return Page(
            items=[],
            page=params.page,
            size=params.size,
            total=0
        )
        
    total_duration = (time.time() - start_time) * 1000
    
    logger.debug(f"[PERF] get_recommendations completed (mixed) - total_time: {total_duration:.2f}ms, user_id: {user_id}")
    
    result_page = Page(
        items=result["items"],
        page=result["page"],
        size=result["size"],
        total=result["total"]
    )
    
    # 记录响应数据大小
    import json
    response_size = len(json.dumps(result, default=str))
    logger.debug(f"[PERF] get_recommendations response_size - size: {response_size} bytes, items_count: {len(result['items'])}, user_id: {user_id}")
    
    # 记录响应发送时间
    import datetime
    response_timestamp = datetime.datetime.now().isoformat()
    logger.debug(f"[PERF] get_recommendations response_sending - timestamp: {response_timestamp}, user_id: {user_id}")
    
    return result_page

@app.get(
    "/tag_recall/stats",
    status_code=status.HTTP_200_OK
)
async def get_tag_recall_stats(
        recommender_service: RecommenderService = Depends(RecommenderServiceGetter()),
):
    return await recommender_service.get_tag_recall_stats()

@app.post(
    "/tag_recall/compute",
    status_code=status.HTTP_201_CREATED
)
async def compute_tag_recall(
        sync: bool = False,
        recommender_service: RecommenderService = Depends(RecommenderServiceGetter()),
):
    return await recommender_service.compute_tag_recall(sync=sync)

@app.get(
    "/similar/{post_id}",
    response_model=Page[PostFeed]
)
async def get_similar_posts(
        post_type: Optional[PostType] = None,
        region: Region | None = None,
        post_id: str = Depends(valid_post),
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User | None = Depends(optional_user),
        recommender_service: RecommenderService = Depends(RecommenderServiceGetter()),
        user_block_cache_service: UserBlockCacheService = Depends(UserBlockCacheServiceGetter()),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    stmt = select(Post.type).where(Post.id == post_id)
    (post_db_type,) = (await session.execute(stmt)).first()
    
    if not post_type:
        post_type = post_db_type
    
    # 移除对region的条件检查
    region = None
    
    # 使用缓存服务获取当前用户屏蔽的用户列表
    blocked_authors_ids = []
    if user:
        blocked_authors_ids = await user_block_cache_service.get_blocked_author_ids(user.id)
    
    return await recommender_service.get_similar(
        post_id=post_id,
        params=params,
        region=None,  # 移除region限制
        post_type=post_type,
        user_id=getattr(user, "id", None),
        languages=getattr(user, "preferred_languages", None),
        blocked_author_ids=blocked_authors_ids,  # 传递屏蔽用户列表
    )

@app.get(
    "/cache/status",
    status_code=status.HTTP_200_OK
)
async def get_cache_status():
    return await recommender_client.get_cache_status()


@app.post(
    "/cache/update/hot-posts",
    status_code=status.HTTP_201_CREATED
)
async def update_hot_post_cache(
    post_type: PostType | None = None,
    language: Language | None = None,
):
    print(f"update_hot_post_cache - post_type: {post_type}, language: {language}")
    await recommender_client.update_hot_post_cache(post_type=post_type, language=language)

@app.post(
    "/cache/update/new-content",
    status_code=status.HTTP_201_CREATED
)
async def update_new_content_cache():
    await recommender_client.update_new_content_cache()

@app.post(
    "/cache/update/all",
    status_code=status.HTTP_201_CREATED
)
async def update_all_cache():
    await recommender_client.update_all_cache()

@app.post(
    "/cache/update/all/sync",
    status_code=status.HTTP_201_CREATED
)
async def update_all_cache_sync():
    await recommender_client.sync_update_all_cache()

@app.delete(
    "/clear_cache",
    status_code=status.HTTP_200_OK
)
async def clear_cache(
    cache_type: Literal["hot_posts", "new_content", "token_recall", "all"] = "all",
    post_type: PostType | None = None,
    language: Language | None = None,
):
    await recommender_client.clear_cache(cache_type=cache_type, post_type=post_type, language=language)

