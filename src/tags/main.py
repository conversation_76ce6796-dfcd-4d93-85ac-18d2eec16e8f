from fastapi import Fast<PERSON><PERSON>, Query, Depends, status, HTTPException, Path
from src.infra.app import create_app
from src.infra.logger import get_logger

from src.auth import current_user
from src.common.schemas import Page, PaginationParams
from src.database.models import User
from src.tags.dependencies import get_tag_service
from src.tags.dto import TagFetchParams, TagCreateDTO, TagDTO
from src.tags.exceptions import TagAlreadyExists, TagNotFound, PostNotFound
from src.tags.schemas import (
    TagSchema,
    TagCreateSchema,
    TagAddSchema,
    TagSubscriptionStatus,
)
from src.tags.services import TagService
from src.tags.exceptions import PermissionDenied

logger = get_logger("tags", level="INFO", file_path="latest.log")
app = create_app(title="Tags", version="1.0", description="Tags API", request_logger=logger)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get("/", response_model=Page[TagSchema], summary="Get all tags")
async def get_tags(
    title: str | None = Query(
        title="Title",
        description="For filtering by `title`, issues `ilike` expression",
        default=None,
    ),
    tag_service: TagService = Depends(get_tag_service),
    pagination_params: PaginationParams = Depends(),
):
    fetching_parameters = TagFetchParams(
        title=title,
        page=pagination_params.page,
        page_size=pagination_params.page_size,
    )

    return await tag_service.fetch_tags(
        fetching_parameters,
    )


@app.post(
    "/",
    response_model=TagSchema,
    summary="Create a new tag",
    status_code=status.HTTP_201_CREATED,
)
async def create_tag(
    data: TagCreateSchema,
    tag_service: TagService = Depends(get_tag_service),
):
    request = TagCreateDTO(
        title=data.title,
        description=data.description,
        cover=data.cover,
    )

    try:
        created_tag = await tag_service.create_tag(request)
    except TagAlreadyExists:
        raise HTTPException(
            detail="Tag already exists.",
            status_code=status.HTTP_400_BAD_REQUEST,
        )

    return created_tag


@app.get(
    "/{title}",
    summary="Get a single tag by it's title",
    response_model=TagSchema,
)
async def get_tag(
    title: str = Path(title="Title", description="Tag's title"),
    tag_service: TagService = Depends(get_tag_service),
):
    try:
        return await tag_service.get_tag(title)
    except TagNotFound:
        raise HTTPException(
            detail="Tag not found.",
            status_code=status.HTTP_404_NOT_FOUND,
        )


@app.put("/{title}/posts", summary="Put a post to some tag")
async def put_post_to_tag(
    data: TagAddSchema,
    title: str = Path(title="Title", description="Tag's title"),
    user: User = Depends(current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    try:
        await tag_service.put_post_to_tag(
            post_id=data.post_id,
            current_user=user,
            tag_title=title,
        )
    except PostNotFound:
        raise HTTPException(
            detail="Post not found.",
            status_code=status.HTTP_404_NOT_FOUND,
        )
    except PermissionDenied:
        raise HTTPException(
            detail="Access denied.",
            status_code=status.HTTP_403_FORBIDDEN,
        )

    return {
        "message": "Tag was successfully added to post.",
    }


@app.post(
    "/{title}/subscribe",
    summary="Subscribe to a tag",
    description="Subscribes user to `Tag`, works as switch, first request subscribes & second unsubscribes.",
    response_model=TagSubscriptionStatus,
)
async def subscribe_tag(
    tag: TagDTO = Depends(get_tag),
    user: User = Depends(current_user),
    tag_service: TagService = Depends(get_tag_service),
):
    return await tag_service.subscribe_user_to_tag(
        user,
        tag,
    )
