from dataclasses import dataclass
from datetime import datetime


@dataclass
class TagFetchParams:
    page: int
    page_size: int
    title: str


@dataclass
class TagDTO:
    title: str
    description: str
    posts_count: int
    cover: str
    created_at: datetime
    updated_at: datetime


@dataclass
class TagCreateDTO:
    title: str
    description: str | None = None
    cover: str | None = None


@dataclass
class TagSubscriptionStatusDTO:
    is_subscribed: bool
