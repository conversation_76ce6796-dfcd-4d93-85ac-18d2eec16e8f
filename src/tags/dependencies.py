from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from src.common.post_service import PostService, get_post_service
from src.tags.repos import TagRepo
from src.tags.services import TagService


def get_tag_repo(
    session: AsyncSession = Depends(get_session),
    post_service: PostService = Depends(get_post_service),
):
    return TagRepo(session, post_service)


def get_tag_service(repo: TagRepo = Depends(get_tag_repo)):
    return TagService(repo)
