from sqlalchemy import select, update
from sqlalchemy.orm import joinedload

from src.common.post_service import PostService
from src.database.models import Tag, Author, Post
from src.common.repos import BaseRepository
from src.database.models.Tag import tags_subscriptions
from src.tags.dto import TagDTO


class TagRepo(BaseRepository):
    def __init__(self, session, post_service: PostService):
        super().__init__(session)
        self.post_service = post_service

    @staticmethod
    def tag_to_dto(tag: Tag) -> TagDTO:
        return TagDTO(
            title=tag.title,
            description=tag.description,
            updated_at=tag.updated_at,
            created_at=tag.created_at,
            cover=tag.cover,
            posts_count=tag.posts_count,
        )

    async def increase_post_count(
        self,
        tag_title: str,
    ):
        await self._session.execute(
            update(Tag)
            .where(Tag.title == tag_title)
            .values(
                posts_count=Tag.posts_count + 1,
            )
        )
        await self._session.commit()

    async def decrease_post_count(
        self,
        tag_title: str,
    ):
        await self._session.execute(
            update(Tag)
            .where(Tag.title == tag_title)
            .values(
                posts_count=Tag.posts_count - 1,
            )
        )

    async def get_post_by_id(
        self,
        post_id: str,
    ) -> Post | None:
        results = await self._session.execute(
            self.post_service.get_one(
                post_id,
            )
        )

        return results.scalars().first()

    async def get_author_by_user_id(self, user_id: str) -> Author:
        """
        Ideally, this method should be in `AuthorRepo`, but...okay
        :param user_id:
        :return:
        """
        results = await self._session.execute(
            select(Author).where(Author.user_id == user_id)
        )

        return results.scalars().first()

    async def create(
        self,
        title: str,
        cover: str | None = None,
        description: str | None = None,
    ) -> TagDTO:
        model = Tag(
            title=title,
            cover=cover,
            description=description,
        )

        self._session.add(model)
        await self._session.flush()
        await self._session.refresh(model)
        await self._session.commit()

        return self.tag_to_dto(model)

    async def fetch_all(
        self,
        page: int,
        page_size: int,
        title: str,
    ):
        query = select(Tag)

        if title is not None:
            query = query.where(Tag.title.ilike(f"%{title}%"))

        return await self._paginate(
            query,
            page,
            page_size,
            self.tag_to_dto,
        )

    async def get_by_title(self, title: str) -> TagDTO | None:
        stmt = select(Tag).where(Tag.title == title.lower())

        results = await self._session.execute(stmt)

        first_one = results.scalars().first()

        if first_one is not None:
            return self.tag_to_dto(
                first_one,
            )

    async def subscribe_to_tag(self, author: Author, tag_title: str) -> bool:
        # 首先获取标签
        tag_result = await self._session.execute(
            select(Tag).where(Tag.title == tag_title)
        )
        tag = tag_result.scalars().first()
        
        if not tag:
            return False
            
        # 使用显式查询检查订阅关系，避免懒加载问题
        subscription_stmt = select(tags_subscriptions).where(
            tags_subscriptions.c.tag_id == tag.id,
            tags_subscriptions.c.author_id == author.id
        )
        subscription_result = await self._session.execute(subscription_stmt)
        is_subscribed = subscription_result.first() is not None

        if not is_subscribed:
            # 添加订阅
            await self._session.execute(
                tags_subscriptions.insert().values(
                    tag_id=tag.id,
                    author_id=author.id
                )
            )
            subscribed = True
        else:
            # 移除订阅
            await self._session.execute(
                tags_subscriptions.delete().where(
                    tags_subscriptions.c.tag_id == tag.id,
                    tags_subscriptions.c.author_id == author.id
                )
            )
            subscribed = False

        await self._session.commit()
        return subscribed

    async def put_tag_to_post(self, post_id: str, tag_title: str):
        post = await self.get_post_by_id(
            post_id,
        )

        post.tags_list.append(tag_title)

        await self._session.flush()
        await self._session.commit()
