from src.common.dto import Page
from src.database.models import User
from src.tags import exceptions
from src.tags.dto import TagFetchParams, TagCreateDTO, TagDTO, TagSubscriptionStatusDTO
from src.tags.exceptions import PermissionDenied, PostNotFound
from src.tags.repos import TagRepo


class TagService:
    def __init__(self, repo: TagRepo):
        self._repo = repo

    async def put_post_to_tag(
        self,
        post_id: str,
        current_user: User,
        tag_title: str,
    ):
        """
        Puts some tag to post
        :param post_id: A post's identifier
        :param current_user: Current user
        :param tag_title: A needed title (tag will be created automatically)
        :return: Nothing.
        """
        author = await self._repo.get_author_by_user_id(
            current_user.id,
        )

        post = await self._repo.get_post_by_id(post_id)

        if post is None:
            raise PostNotFound(f"Post with ID = {post_id} not found.")

        if tag_title.lower() in list(map(lambda tag: tag.lower(), post.tags_list)):
            return

        if post.author_id != author.id:
            raise PermissionDenied("Permission denied")

        try:
            needed_tag = await self.get_tag(tag_title)
        except exceptions.TagNotFound:
            needed_tag = await self.create_tag(
                TagCreateDTO(
                    title=tag_title,
                )
            )

        await self._repo.put_tag_to_post(
            post_id,
            tag_title,
        )

        await self._repo.increase_post_count(
            needed_tag.title,
        )

    async def fetch_tags(
        self,
        params: TagFetchParams,
    ) -> Page[TagDTO]:
        """
        Fetches all tags
        :param params: A tag fetch params
        :return:
        """

        return await self._repo.fetch_all(
            page_size=params.page_size,
            page=params.page,
            title=params.title,
        )

    async def get_tag(
        self,
        title: str,
    ) -> TagDTO:
        """
        Get tag by its title
        :param title: A title of tag
        :return: A `TagDTO` instance
        """
        tag = await self._repo.get_by_title(title)

        if tag is None:
            raise exceptions.TagNotFound()

        return tag

    async def create_tag(self, data: TagCreateDTO) -> TagDTO:
        """
        Creates a new tag
        :param data: A `TagCreateDTO` object
        :return: `TagDTO` obj
        """

        existed = await self._repo.get_by_title(data.title)

        if existed:
            raise exceptions.TagAlreadyExists()

        return await self._repo.create(
            title=data.title.lower(),
            description=data.description,
            cover=data.cover,
        )

    async def subscribe_user_to_tag(
        self, current_user: User, needed_tag: TagDTO
    ) -> TagSubscriptionStatusDTO:
        """
        Subscribes a user to tag, works as switch
        :param current_user: A current user
        :param needed_tag: A needed tag
        :return:
        """

        author = await self._repo.get_author_by_user_id(
            current_user.id,
        )

        is_subs = await self._repo.subscribe_to_tag(author, needed_tag.title)

        return TagSubscriptionStatusDTO(
            is_subs,
        )
