from datetime import datetime

from pydantic import BaseModel


class TagSchema(BaseModel):
    title: str
    description: str | None
    updated_at: datetime
    created_at: datetime
    posts_count: int


class TagCreateSchema(BaseModel):
    title: str
    description: str | None = None
    cover: str | None = None


class TagAddSchema(BaseModel):
    post_id: str


class TagSubscriptionStatus(BaseModel):
    is_subscribed: bool
