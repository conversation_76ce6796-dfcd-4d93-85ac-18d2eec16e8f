# Payments Service - Simplified using enhanced base
# Based on memecoin service Dockerfile

ARG PYTHON_VERSION=3.10.12
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv in builder stage
USER root
RUN pip install --no-cache-dir uv
USER appuser

# Copy service-specific requirements
COPY src/payments/requirements.txt /tmp/payments-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/payments-requirements.txt

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Add service scripts to PATH
ENV PATH="$PATH:/src/payments/scripts"

# Copy service-specific code
COPY --chown=appuser:appuser src/payments /src/payments

# Copy dependencies based on docker-compose.yml volumes
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database

# Set executable permissions for start script
RUN chmod +x /src/payments/scripts/start.sh