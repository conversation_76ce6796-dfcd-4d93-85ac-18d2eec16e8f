from typing import Optional, Dict, Any, Tuple
from src.common.constants import ContentPurchaseStatus, UserTransactionType
from src.common.celery_client import create_celery_client
from .schemas import PurchaseStats
from .repos import PaymentsRepository
from .logger import logger
from src.common.redis_cli.async_impl import AsyncRedisClient


class ContentPaymentService:
	def __init__(self, repo: PaymentsRepository, redis_client: Optional[AsyncRedisClient] = None):
		self.repo = repo
		self.celery_client = create_celery_client("payments")
		self._redis_ttl_seconds = 86400  # 1 day
		self.redis_client = redis_client


	async def process_content_purchase(self, user_id: str, post_id: str) -> Tuple[str, bool]:
		"""Process content purchase: create ContentPurchase (PENDING), enqueue worker, and return (purchase_id, is_retry)"""
		logger.info(f"Starting purchase processing - user_id: {user_id}, post_id: {post_id}")
		
		# Get post information
		logger.debug(f"Fetching post information - post_id: {post_id}")
		post = await self.repo.get_post_by_id(post_id)
		
		if not post:
			logger.warning(f"Post not found - post_id: {post_id}")
			raise ValueError(f"Post {post_id} not found")
		
		logger.debug(f"Post found - post_id: {post_id}, holdview_amount: {post.holdview_amount}, binding_token: {post.binding_token}")
		
		if post.holdview_amount == 0:
			logger.warning(f"Attempting to purchase free content - post_id: {post_id}")
			raise ValueError("Content is free, no payment required")
		
		# Check existing purchase status
		logger.debug(f"Checking existing purchase - user_id: {user_id}, post_id: {post_id}")
		existing_purchase = await self.repo.get_existing_purchase(user_id=user_id, post_id=post_id)
		is_retry = False
		
		if existing_purchase:
			logger.debug(f"Found existing purchase - purchase_id: {existing_purchase.id}, status: {existing_purchase.status}")
			
			if existing_purchase.status == ContentPurchaseStatus.CONFIRMED:
				logger.warning(f"Content already purchased and confirmed - user_id: {user_id}, post_id: {post_id}")
				raise ValueError("Content already purchased and confirmed")
			
			elif existing_purchase.status == ContentPurchaseStatus.PENDING or existing_purchase.status == ContentPurchaseStatus.FAILED:
				logger.info(f"Retrying {existing_purchase.status} purchase - purchase_id: {existing_purchase.id}")
				content_purchase = existing_purchase
				is_retry = True
			
			else:  # REFUNDED or other statuses
				logger.warning(f"Purchase exists with status {existing_purchase.status} - user_id: {user_id}, post_id: {post_id}")
				raise ValueError(f"Previous purchase exists with status: {existing_purchase.status}")
		else:
			# Create new ContentPurchase record (PENDING), UserTransaction will be created by worker
			logger.debug(f"Creating new ContentPurchase record - user_id: {user_id}, post_id: {post_id}, amount: {post.holdview_amount}")
			content_purchase = await self.repo.create_content_purchase(
				user_id=user_id,
				post_id=post_id,
				usd_amount_cents=post.holdview_amount,
			)
			logger.info(f"New ContentPurchase created - purchase_id: {content_purchase.id}")

		# Enqueue worker task to create UserTransaction and progress status
		try:
			logger.debug(f"Enqueuing worker task - purchase_id: {content_purchase.id}")
			self.celery_client.send_task(
				"src.worker.tasks.payments.submit_content_purchase_task",
				kwargs={
					"purchase_id": content_purchase.id,
					"user_id": user_id,
					"post_id": post_id,
					"token_address": post.binding_token,
					"usd_amount_cents": post.holdview_amount,
					"tx_type": UserTransactionType.PAY_CONTENT
				}
			)
			logger.info(f"Worker task enqueued successfully - purchase_id: {content_purchase.id}")
		except Exception as e:
			logger.error(f"Failed to enqueue worker task - purchase_id: {content_purchase.id}, error: {str(e)}")
			logger.exception("Celery task enqueue error:")
			# Soft fail: leave as PENDING; worker retry or manual repair can handle
			pass

		logger.debug(f"Committing transaction - purchase_id: {content_purchase.id}")
		await self.repo.commit()
		logger.info(f"Purchase processing completed - purchase_id: {content_purchase.id}")

		# Cache purchase status in Redis for quick holdview access
		try:
			if self.redis_client:
				status_str = str(content_purchase.status)
				# Key by user+post for quick access check
				key_user_post = f"purchase:by_user_post:{user_id}:{post_id}"
				await self.redis_client.set_json(
					key_user_post,
					{"purchase_id": content_purchase.id, "status": status_str},
					ex=self._redis_ttl_seconds,
				)
				# Key by purchase id for worker/state tracking
				key_by_id = f"purchase:status:{content_purchase.id}"
				await self.redis_client.set_json(
					key_by_id,
					{"status": status_str, "user_id": user_id, "post_id": post_id},
					ex=self._redis_ttl_seconds,
				)
		except Exception as e:
			# Soft-fail on cache errors
			logger.warning(f"Failed to write purchase status to Redis - purchase_id: {content_purchase.id}, error: {str(e)}")

		# Return the ContentPurchase id and retry flag
		return content_purchase.id, is_retry

	async def verify_content_purchase(self, user_id: str, post_id: str) -> bool:
		"""Check if user has purchased this content"""
		logger.debug(f"Verifying purchase - user_id: {user_id}, post_id: {post_id}")
		result = await self.repo.has_confirmed_purchase(user_id=user_id, post_id=post_id)
		logger.debug(f"Purchase verification result - user_id: {user_id}, post_id: {post_id}, has_purchase: {result}")
		return result

	async def get_purchase_stats(self, user_id: str) -> PurchaseStats:
		"""Get user's purchase statistics"""
		logger.debug(f"Getting purchase stats - user_id: {user_id}")
		purchases = await self.repo.get_confirmed_purchases_by_user(user_id)
		
		total_spent = sum(purchase.usd_amount_cents for purchase in purchases)
		total_count = len(purchases)
		
		logger.debug(f"Purchase stats calculated - user_id: {user_id}, count: {total_count}, total_spent_cents: {total_spent}")
		
		return PurchaseStats(
			total_spent_usd_cents=total_spent,
			total_spent_formatted=f"${total_spent / 100:.2f}",
			total_purchases=total_count
		)


