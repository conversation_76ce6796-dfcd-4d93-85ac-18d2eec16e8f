from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.infra.deps import session_dep
from .repos import PaymentsRepository
from .service import ContentPaymentService
from .purchases.service import PurchaseHistoryService
from src.common.redis_cli import RedisCli


async def get_payments_repository(
	write_session: AsyncSession = Depends(session_dep("write")),
	read_session: AsyncSession = Depends(session_dep("read")),
) -> PaymentsRepository:
	return PaymentsRepository(write_session, read_session)


async def get_content_payment_service(
	repo: PaymentsRepository = Depends(get_payments_repository),
	redis_client = Depends(RedisCli.async_),
) -> ContentPaymentService:
	return ContentPaymentService(repo, redis_client)


async def get_purchase_history_service(
	repo: PaymentsRepository = Depends(get_payments_repository),
) -> PurchaseHistoryService:
	return PurchaseHistoryService(repo)


