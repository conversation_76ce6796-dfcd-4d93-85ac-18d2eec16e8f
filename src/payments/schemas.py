from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from decimal import Decimal
from src.common.constants import ContentPurchaseStatus



class ContentPurchaseResponse(BaseModel):
	"""Response for content purchase"""
	success: bool
	purchase_id: Optional[str] = None
	status: Optional[str] = None
	message: str
	
	class Config:
		json_schema_extra = {
			"example": {
				"success": True,
				"purchase_id": "8f2f2d9a-2f64-4d5e-9a2f-1a2b3c4d5e6f",
				"status": ContentPurchaseStatus.PENDING,
				"message": "Purchase successful, content unlocked"
			}
		}


class PurchaseStats(BaseModel):
	"""User purchase statistics"""
	total_spent_usd_cents: int
	total_spent_formatted: str
	total_purchases: int
	
	class Config:
		json_schema_extra = {
			"example": {
				"total_spent_usd_cents": 1250,
				"total_spent_formatted": "$12.50",
				"total_purchases": 8
			}
		}


