from fastapi import APIRouter, Depends, HTTPException, status

from .dependencies import get_content_payment_service
from .service import ContentPaymentService
from .schemas import (
	ContentPurchaseResponse,
	PurchaseStats
)
from src.auth import current_user
from src.database.models.User import User
from .logger import logger

router = APIRouter(prefix="", tags=["payments"])


@router.post("/posts/{post_id}/purchase", response_model=ContentPurchaseResponse)
async def purchase_content(
	post_id: str,
	user: User = Depends(current_user),
	service: ContentPaymentService = Depends(get_content_payment_service),
):
	"""Purchase content access"""
	# Get user_id safely before any database operations that might fail
	user_id = user.id
	logger.info(f"Processing purchase request - user_id: {user_id}, post_id: {post_id}")
	
	try:
		purchase_id, is_retry = await service.process_content_purchase(
			user_id=user_id,
			post_id=post_id
		)
		
		logger.info(f"Purchase successful - user_id: {user_id}, post_id: {post_id}, purchase_id: {purchase_id}, is_retry: {is_retry}")
		
		message = "Purchase retried and queued for processing" if is_retry else "Purchase created and queued for processing"
		
		return ContentPurchaseResponse(
			success=True,
			purchase_id=purchase_id,
			status="pending",
			message=message
		)
	except ValueError as e:
		logger.warning(f"Purchase validation error - user_id: {user_id}, post_id: {post_id}, error: {str(e)}")
		raise HTTPException(
			status_code=status.HTTP_400_BAD_REQUEST,
			detail=str(e)
		)
	except Exception as e:
		logger.error(f"Purchase processing failed - user_id: {user_id}, post_id: {post_id}, error: {str(e)}, error_type: {type(e).__name__}")
		logger.exception("Full exception details:")
		raise HTTPException(
			status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
			detail=f"Failed to process purchase: {str(e)}"
		)


@router.get("/purchases/stats", response_model=PurchaseStats)
async def get_purchase_stats(
	user: User = Depends(current_user),
	service: ContentPaymentService = Depends(get_content_payment_service),
):
	"""Get user's purchase statistics"""
	logger.info(f"Getting purchase stats for user_id: {user.id}")
	
	try:
		stats = await service.get_purchase_stats(user.id)
		logger.info(f"Purchase stats retrieved - user_id: {user.id}, total_purchases: {stats.total_purchases}")
		return stats
	except Exception as e:
		logger.error(f"Failed to get purchase stats - user_id: {user.id}, error: {str(e)}, error_type: {type(e).__name__}")
		logger.exception("Full exception details:")
		raise HTTPException(
			status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
			detail=f"Failed to get purchase stats: {str(e)}"
		)
