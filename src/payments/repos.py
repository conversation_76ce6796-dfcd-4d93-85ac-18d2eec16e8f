from typing import List, Tuple, Optional, Dict, Iterable

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from src.database.models.Post import Post
from src.database.models.ContentPurchase import ContentPurchase
from src.database.models.UserTransaction import UserTransaction
from src.common.constants import ContentPurchaseStatus
from .logger import logger


class PaymentsRepository:
	"""Repository layer for all DB interactions in payments domain."""

	def __init__(self, write_session: AsyncSession, read_session: AsyncSession):
		self.write_session = write_session
		self.read_session = read_session

	# --- Post queries ---
	async def get_post_by_id(self, post_id: str) -> Optional[Post]:
		logger.debug(f"Fetching post from database - post_id: {post_id}")
		try:
			stmt = select(Post).where(Post.id == post_id)
			result = await self.read_session.execute(stmt)
			post = result.scalar_one_or_none()
			if post:
				logger.debug(f"Post found - post_id: {post_id}, holdview_amount: {post.holdview_amount}, binding_token: {post.binding_token}")
			else:
				logger.warning(f"Post not found in database - post_id: {post_id}")
			return post
		except Exception as e:
			logger.error(f"Database error fetching post - post_id: {post_id}, error: {str(e)}")
			logger.exception("Database exception details:")
			raise

	# --- ContentPurchase writes ---
	async def create_content_purchase(self, *, user_id: str, post_id: str, usd_amount_cents: int) -> ContentPurchase:
		logger.debug(f"Creating ContentPurchase - user_id: {user_id}, post_id: {post_id}, amount: {usd_amount_cents}")
		try:
			purchase = ContentPurchase(
				user_id=user_id,
				post_id=post_id,
				usd_amount_cents=usd_amount_cents,
				user_transaction_id=None,
				status=ContentPurchaseStatus.PENDING,
			)
			self.write_session.add(purchase)
			await self.write_session.flush()
			logger.debug(f"ContentPurchase created successfully - purchase_id: {purchase.id}")
			return purchase
		except Exception as e:
			logger.error(f"Database error creating ContentPurchase - user_id: {user_id}, post_id: {post_id}, error: {str(e)}")
			logger.exception("Database exception details:")
			await self.write_session.rollback()
			raise

	async def commit(self) -> None:
		logger.debug("Committing database transaction")
		try:
			await self.write_session.commit()
			logger.debug("Database transaction committed successfully")
		except Exception as e:
			logger.error(f"Database commit error: {str(e)}")
			logger.exception("Database commit exception details:")
			raise

	# --- ContentPurchase reads ---
	async def get_existing_purchase(self, *, user_id: str, post_id: str) -> Optional[ContentPurchase]:
		"""Get existing purchase record for user and post (any status)"""
		logger.debug(f"Getting existing purchase - user_id: {user_id}, post_id: {post_id}")
		try:
			stmt = select(ContentPurchase).where(
				ContentPurchase.user_id == user_id,
				ContentPurchase.post_id == post_id,
			)
			result = await self.read_session.execute(stmt)
			purchase = result.scalar_one_or_none()
			if purchase:
				logger.debug(f"Found existing purchase - user_id: {user_id}, post_id: {post_id}, status: {purchase.status}")
			else:
				logger.debug(f"No existing purchase found - user_id: {user_id}, post_id: {post_id}")
			return purchase
		except Exception as e:
			logger.error(f"Database error getting existing purchase - user_id: {user_id}, post_id: {post_id}, error: {str(e)}")
			logger.exception("Database exception details:")
			raise

	async def has_any_purchase(self, *, user_id: str, post_id: str) -> bool:
		"""Check if user has any purchase record (any status) for this content"""
		purchase = await self.get_existing_purchase(user_id=user_id, post_id=post_id)
		return purchase is not None

	async def has_confirmed_purchase(self, *, user_id: str, post_id: str) -> bool:
		logger.debug(f"Checking confirmed purchase - user_id: {user_id}, post_id: {post_id}")
		try:
			stmt = select(ContentPurchase).where(
				ContentPurchase.user_id == user_id,
				ContentPurchase.post_id == post_id,
				ContentPurchase.status == ContentPurchaseStatus.CONFIRMED,
			)
			result = await self.read_session.execute(stmt)
			has_purchase = result.scalar_one_or_none() is not None
			logger.debug(f"Purchase check result - user_id: {user_id}, post_id: {post_id}, has_confirmed: {has_purchase}")
			return has_purchase
		except Exception as e:
			logger.error(f"Database error checking purchase - user_id: {user_id}, post_id: {post_id}, error: {str(e)}")
			logger.exception("Database exception details:")
			raise

	async def get_confirmed_purchases_by_user(self, user_id: str) -> List[ContentPurchase]:
		stmt = select(ContentPurchase).where(
			ContentPurchase.user_id == user_id,
			ContentPurchase.status == ContentPurchaseStatus.CONFIRMED,
		)
		result = await self.read_session.execute(stmt)
		return result.scalars().all()

	# --- Purchase history & verification ---
	async def get_user_purchases_with_relations(self, *, user_id: str, page: int, page_size: int) -> List[ContentPurchase]:
		offset = (page - 1) * page_size
		stmt = (
			select(ContentPurchase)
			.where(
				ContentPurchase.user_id == user_id,
				ContentPurchase.status == ContentPurchaseStatus.CONFIRMED,
			)
			.options(
				selectinload(ContentPurchase.post),
				selectinload(ContentPurchase.user_transaction),
			)
			.order_by(ContentPurchase.purchased_at.desc())
			.offset(offset)
			.limit(page_size)
		)
		result = await self.read_session.execute(stmt)
		return result.scalars().all()

	async def get_all_confirmed_purchases_for_user(self, user_id: str) -> List[ContentPurchase]:
		stmt = select(ContentPurchase).where(
			ContentPurchase.user_id == user_id,
			ContentPurchase.status == ContentPurchaseStatus.CONFIRMED,
		)
		result = await self.read_session.execute(stmt)
		return result.scalars().all()

	async def get_purchase_with_details(self, purchase_id: str) -> Optional[Tuple[ContentPurchase, Optional[UserTransaction]]]:
		stmt = (
			select(ContentPurchase)
			.where(ContentPurchase.id == purchase_id)
			.options(selectinload(ContentPurchase.user_transaction))
		)
		result = await self.read_session.execute(stmt)
		purchase = result.scalar_one_or_none()
		if not purchase:
			return None
		return purchase, purchase.user_transaction

	async def get_purchased_post_ids_for_user(self, *, user_id: str, post_ids: Iterable[str]) -> List[str]:
		post_ids = list(post_ids)
		if not post_ids:
			return []
		stmt = (
			select(ContentPurchase.post_id)
			.where(
				ContentPurchase.user_id == user_id,
				ContentPurchase.post_id.in_(post_ids),
				ContentPurchase.status == ContentPurchaseStatus.CONFIRMED,
			)
		)
		result = await self.read_session.execute(stmt)
		return [row[0] for row in result.fetchall()]


