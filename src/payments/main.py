from fastapi import status

from src.payments.router import router as payments_router
from src.payments.purchases.router import router as purchases_router
from src.infra.app import create_app


app = create_app(title="Payments Service", version="1.0.0", description="Payments API")


app.include_router(payments_router)
app.include_router(purchases_router)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}


