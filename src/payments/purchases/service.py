from typing import List, Dict, Tuple, Optional

from src.database.models.ContentPurchase import ContentPurchase
from src.database.models.UserTransaction import UserTransaction
from src.common.constants import ContentPurchaseStatus
from .schemas import PurchaseHistoryItem, PurchaseHistoryResponse
from ..repos import PaymentsRepository


class PurchaseHistoryService:
	def __init__(self, repo: PaymentsRepository):
		self.repo = repo

	async def get_user_purchases(self, user_id: str, page: int = 1, page_size: int = 20) -> List[ContentPurchase]:
		"""Get user's content purchase history (delegated to repository)."""
		return await self.repo.get_user_purchases_with_relations(user_id=user_id, page=page, page_size=page_size)

	async def get_purchase_with_details(self, purchase_id: str) -> Tuple[ContentPurchase, UserTransaction]:
		"""Get purchase record with token transaction details"""
		result = await self.repo.get_purchase_with_details(purchase_id)
		if not result:
			raise ValueError(f"Purchase {purchase_id} not found")
		purchase, user_tx = result
		return purchase, user_tx

	async def get_purchase_history_with_details(self, user_id: str, page: int = 1, page_size: int = 20) -> PurchaseHistoryResponse:
		"""Get user's purchase history with formatted details"""
		purchases = await self.get_user_purchases(user_id, page, page_size)
		
		# Calculate totals
		total_spent_cents = sum(purchase.usd_amount_cents for purchase in purchases)
		total_count = len(purchases)
		
		# Get total count of all purchases for this user
		all_purchases = await self.repo.get_all_confirmed_purchases_for_user(user_id)
		total_spent_all = sum(purchase.usd_amount_cents for purchase in all_purchases)
		
		# Format purchase history items
		history_items = []
		for purchase in purchases:
			# Get transaction details
			transaction_details = None
			if purchase.user_transaction:
				transaction_details = {
					"user_transaction_id": purchase.user_transaction.id,
					"token_amount": str(purchase.user_transaction.base_amount),
					"token_symbol": "MEME",  # Could be derived from token registry
					"token_address": purchase.user_transaction.token_address,
					"transaction_hash": purchase.user_transaction.tx_hash
				}
			
			# Get post title if available
			post_title = None
			if purchase.post and hasattr(purchase.post, 'title'):
				post_title = purchase.post.title
			
			history_items.append(PurchaseHistoryItem(
				purchase_id=purchase.id,
				post_id=purchase.post_id,
				post_title=post_title,
				usd_amount_cents=purchase.usd_amount_cents,
				usd_amount_formatted=f"${purchase.usd_amount_cents / 100:.2f}",
				purchased_at=purchase.purchased_at,
				status=purchase.status,
				transaction_details=transaction_details
			))
		
		return PurchaseHistoryResponse(
			purchases=history_items,
			total_spent_usd=f"${total_spent_all / 100:.2f}",
			total_purchases=len(all_purchases)
		)

	async def verify_bulk_purchases(self, user_id: str, post_ids: List[str]) -> Dict[str, bool]:
		"""Verify purchases for multiple posts at once"""
		purchased_post_ids = set(await self.repo.get_purchased_post_ids_for_user(user_id=user_id, post_ids=post_ids))
		
		# Create result dict with all requested posts
		return {post_id: post_id in purchased_post_ids for post_id in post_ids}


