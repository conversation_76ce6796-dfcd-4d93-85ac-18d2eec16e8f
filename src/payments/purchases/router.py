from fastapi import APIRouter, Depends, HTTPException, status, Query
from src.auth import current_user
from src.database.models.User import User
from ..dependencies import get_purchase_history_service
from .service import PurchaseHistoryService
from .schemas import (
	PurchaseHistoryResponse,
	BulkVerifyRequest,
	BulkVerifyResponse
)
from ..logger import logger

router = APIRouter(prefix="/purchases", tags=["purchases"])


@router.get("/history", response_model=PurchaseHistoryResponse)
async def get_purchase_history(
	page: int = Query(1, ge=1, description="Page number (starting from 1)"),
	page_size: int = Query(20, ge=1, le=100, description="Items per page"),
	user: User = Depends(current_user),
    service: PurchaseHistoryService = Depends(get_purchase_history_service),
):
	"""Get user's purchase history"""
	logger.info(f"Getting purchase history - user_id: {user.id}, page: {page}, page_size: {page_size}")
	
	try:
		result = await service.get_purchase_history_with_details(user.id, page, page_size)
		logger.info(f"Purchase history retrieved - user_id: {user.id}, total_items: {len(result.items)}")
		return result
	except Exception as e:
		logger.error(f"Failed to get purchase history - user_id: {user.id}, error: {str(e)}, error_type: {type(e).__name__}")
		logger.exception("Full exception details:")
		raise HTTPException(
			status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
			detail=f"Failed to get purchase history: {str(e)}"
		)


@router.post("/verify", response_model=BulkVerifyResponse)
async def verify_bulk_purchases(
	request: BulkVerifyRequest,
	user: User = Depends(current_user),
    service: PurchaseHistoryService = Depends(get_purchase_history_service),
):
	"""Verify purchases for multiple posts at once"""
	logger.info(f"Bulk purchase verification - user_id: {user.id}, post_count: {len(request.post_ids) if request.post_ids else 0}")
	
	try:
		if not request.post_ids:
			logger.warning(f"No post IDs provided for bulk verification - user_id: {user.id}")
			raise HTTPException(
				status_code=status.HTTP_400_BAD_REQUEST,
				detail="No post IDs provided"
			)
		
		if len(request.post_ids) > 100:
			logger.warning(f"Too many post IDs for bulk verification - user_id: {user.id}, count: {len(request.post_ids)}")
			raise HTTPException(
				status_code=status.HTTP_400_BAD_REQUEST,
				detail="Too many post IDs (maximum 100)"
			)
		
		purchase_status = await service.verify_bulk_purchases(user.id, request.post_ids)
		logger.info(f"Bulk purchase verification completed - user_id: {user.id}, verified_count: {len(purchase_status)}")
		
		return BulkVerifyResponse(purchases=purchase_status)
	except ValueError as e:
		logger.warning(f"Bulk purchase verification validation error - user_id: {user.id}, error: {str(e)}")
		raise HTTPException(
			status_code=status.HTTP_400_BAD_REQUEST,
			detail=str(e)
		)
	except Exception as e:
		logger.error(f"Bulk purchase verification failed - user_id: {user.id}, error: {str(e)}, error_type: {type(e).__name__}")
		logger.exception("Full exception details:")
		raise HTTPException(
			status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
			detail=f"Failed to verify purchases: {str(e)}"
		)


