from pydantic import BaseModel
from typing import List, Dict, Optional, Any
from datetime import datetime


class PurchaseHistoryItem(BaseModel):
	"""Individual purchase history item"""
	purchase_id: str
	post_id: str
	post_title: Optional[str] = None
	usd_amount_cents: int
	usd_amount_formatted: str
	purchased_at: datetime
	status: str
	transaction_details: Optional[Dict[str, Any]] = None

	class Config:
		from_attributes = True
		json_schema_extra = {
			"example": {
				"purchase_id": "purchase-001",
				"post_id": "650e8400-e29b-41d4-a716-446655440001",
				"post_title": "Premium Tutorial",
				"usd_amount_cents": 150,
				"usd_amount_formatted": "$1.50",
				"purchased_at": "2024-01-01T12:00:00Z",
				"status": "confirmed",
				"transaction_details": {
					"user_transaction_id": 12345,
					"token_amount": "756.302521",
					"token_symbol": "MEME",
					"token_address": "0x1234...",
					"transaction_hash": "0xabcdef..."
				}
			}
		}


class PurchaseHistoryResponse(BaseModel):
	"""Purchase history response"""
	purchases: List[PurchaseHistoryItem]
	total_spent_usd: str
	total_purchases: int

	class Config:
		json_schema_extra = {
			"example": {
				"purchases": [
					{
						"purchase_id": "purchase-001",
						"post_id": "650e8400-e29b-41d4-a716-446655440001",
						"post_title": "Premium Tutorial",
						"usd_amount_cents": 150,
						"usd_amount_formatted": "$1.50",
						"purchased_at": "2024-01-01T12:00:00Z",
						"status": "confirmed"
					}
				],
				"total_spent_usd": "$12.50",
				"total_purchases": 8
			}
		}


class BulkVerifyRequest(BaseModel):
	"""Request to verify purchases for multiple posts"""
	post_ids: List[str]

	class Config:
		json_schema_extra = {
			"example": {
				"post_ids": [
					"650e8400-e29b-41d4-a716-446655440001",
					"650e8400-e29b-41d4-a716-446655440002"
				]
			}
		}


class BulkVerifyResponse(BaseModel):
	"""Response for bulk purchase verification"""
	purchases: Dict[str, bool]

	class Config:
		json_schema_extra = {
			"example": {
				"purchases": {
					"650e8400-e29b-41d4-a716-446655440001": True,
					"650e8400-e29b-41d4-a716-446655440002": False
				}
			}
		}


