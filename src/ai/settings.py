from typing import Optional

from pydantic import Extra
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")
    SUGGESTER_TOKEN: str

    REPLICATE_TOKEN: str

    BASE_URL: str

    OPENAI_KEY: str

    STABILITY_HOST: str = "https://api.stability.ai"
    STABILITY_KEY: str

    PROXY_URL: Optional[str] = None

    DALLE_DIR: str = "static/generated/dalle"
    SD_DIR: str = "static/generated/sd"
    SD3_DIR: str = "static/generated/sd3"

    SD3_MODEL: str = "sd3-large-turbo"
    SD_MODEL: str = "stable-diffusion-xl-1024-v1-0"


settings = Settings()
