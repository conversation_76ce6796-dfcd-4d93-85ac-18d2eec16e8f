import uuid
import os
import base64

from httpx import AsyncClient
from openai import Async<PERSON><PERSON>A<PERSON>
from fastapi import FastAP<PERSON>, status, Depends
from fastapi.middleware.cors import CORSMiddleware
from requests_toolbelt.multipart.encoder import MultipartEncoder

from src.infra.logger import get_logger
from src.infra.app import create_app
from src.auth import current_user
from .settings import settings
from .schemas import ImagePrompt
from .dependecies import translated_prompt

from .replicate.router import router as replicate_router

logger = get_logger("ai", level="INFO", file_path="latest.log")
app = create_app(title="AI", version="1.0", description="AI API", request_logger=logger)
app.include_router(replicate_router)

http_client = AsyncClient(proxies=settings.PROXY_URL, timeout=100)

openai_client = AsyncOpenAI(api_key=settings.OPENAI_KEY, http_client=http_client)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.post("/dalle")
async def generate_dalle(prompt: ImagePrompt = Depends(translated_prompt)):
    generate_response = await openai_client.images.generate(
        model="dall-e-3",
        prompt=prompt.prompt,
        size=prompt.size,
        quality="standard",
        n=1,
    )
    image_url = generate_response.data[0].url
    r = await http_client.get(image_url)

    os.makedirs(settings.DALLE_DIR, exist_ok=True)
    filename = settings.DALLE_DIR + f"/{uuid.uuid4()}.png"
    with open(filename, "wb") as tempfile:
        tempfile.write(r.content)
    mp = MultipartEncoder(fields={"data": (filename, open(filename, "rb"))})
    upload_response = await http_client.post(
        f"{settings.BASE_URL}/v1/upload_image",
        headers={"Content-Type": mp.content_type},
        data=mp.to_string(),
    )
    return upload_response.json()


@app.post("/sd3")
async def generate_sd3(prompt: ImagePrompt = Depends(translated_prompt)):
    response = await http_client.post(
        f"{settings.STABILITY_HOST}/v2beta/stable-image/generate/sd3",
        headers={
            "Accept": "application/json",
            "Authorization": f"Bearer {settings.STABILITY_KEY}",
        },
        files={"none": ""},
        data={"prompt": prompt.prompt, "output_format": "png", "model": settings.SD3_MODEL},
    )

    data = response.json()
    os.makedirs(settings.SD3_DIR, exist_ok=True)
    filename = settings.SD3_DIR + f"/{uuid.uuid4()}.png"
    with open(filename, "wb") as f:
        f.write(base64.b64decode(data["image"]))
    mp = MultipartEncoder(fields={"data": (filename, open(filename, "rb"))})
    upload_response = await http_client.post(
        f"{settings.BASE_URL}/v1/upload_image",
        headers={"Content-Type": mp.content_type},
        data=mp.to_string(),
    )
    return upload_response.json()


@app.post("/sd")
async def generate_sd(prompt: ImagePrompt = Depends(translated_prompt)):
    response = await http_client.post(
        f"{settings.STABILITY_HOST}/v1/generation/{settings.SD_MODEL}/text-to-image",
        headers={
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {settings.STABILITY_KEY}",
        },
        json={
            "text_prompts": [{"text": prompt.prompt}],
            "cfg_scale": 7,
            "height": int(prompt.size.split("x")[0]),
            "width": int(prompt.size.split("x")[1]),
            "samples": 1,
            "steps": 30,
        },
    )

    data = response.json()

    os.makedirs(settings.SD_DIR, exist_ok=True)
    filename = settings.SD3_DIR + f"/{uuid.uuid4()}.png"
    with open(filename, "wb") as f:
        f.write(base64.b64decode(data["artifacts"][0]["base64"]))
    mp = MultipartEncoder(fields={"data": (filename, open(filename, "rb"))})
    upload_response = await http_client.post(
        f"{settings.BASE_URL}/v1/upload_image",
        headers={"Content-Type": mp.content_type},
        data=mp.to_string()
    )
    return upload_response.json()
