import asyncio

from src.common.translation_client import TranslationClient
from src.common.utils import detect_language
from src.common.constants import Language
from src.common.exceptions import TranslationError
from .settings import settings
from .schemas import ImagePrompt
from .logger import logger


translation_client = TranslationClient(auth_token=settings.SUGGESTER_TOKEN)


async def translated_prompt(prompt: ImagePrompt) -> ImagePrompt:
    prompt_lang: Language = await asyncio.to_thread(detect_language, text=prompt.prompt)
    if prompt_lang == Language.ENGLISH:
        return prompt
    try:
        original_prompt = prompt.prompt
        prompt.prompt = await translation_client.translate_prompt(
            text=prompt.prompt, language=Language.ENGLISH
        )
        logger.debug(
            f"Original prompt -- {original_prompt}. Original prompt language -- {prompt_lang.value}. "
            f"Translated prompt -- {prompt.prompt}."
        )
    except TranslationError as e:
        logger.error(f"Translation request failed with the following error:\n{e}")
    return prompt
