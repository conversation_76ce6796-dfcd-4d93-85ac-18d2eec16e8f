from fastapi import Depends
from httpx import Client, Async<PERSON>lient

from replicate import Client
from openai import Async<PERSON>penAI

from src.ai.replicate.ext.chinese_photomaker import ChinesePhotoMaker
from src.ai.replicate.ext.flux_agent import FluxAgent
from src.ai.replicate.ext.sdxl import StableDiffusionXL
from src.ai.replicate.ext.stable_diffusion import StableDiffusion
from src.ai.replicate.service import ReplicateService
from src.ai.settings import settings


def get_replicate_client() -> Client:
    """Provides `ReplicateClient`"""
    yield Client(
        api_token=settings.REPLICATE_TOKEN,
    )


def get_replicate_service(
    client: Client = Depends(get_replicate_client),
) -> ReplicateService:
    """Provides `ReplicantService`"""
    yield ReplicateService(client, http_client=AsyncClient())


def get_open_ai_client() -> AsyncOpenAI:
    """Provides async `OpenAI` client"""

    if settings.PROXY_URL:
        http_client = AsyncClient(
            proxies=settings.PROXY_URL,
        )
    else:
        http_client = AsyncClient()

    yield AsyncOpenAI(
        http_client=http_client,
        api_key=settings.OPENAI_KEY,
    )


def get_chinese_photomaker(
    open_ai_client: AsyncOpenAI = Depends(get_open_ai_client),
    replicate: ReplicateService = Depends(get_replicate_service),
) -> ChinesePhotoMaker:
    yield ChinesePhotoMaker(
        open_ai_client=open_ai_client,
        replicate_client=replicate,
    )


def get_flux_agent(
    open_ai_client: AsyncOpenAI = Depends(get_open_ai_client),
    replicate: ReplicateService = Depends(get_replicate_service),
):
    yield FluxAgent(
        open_ai=open_ai_client,
        replicate_service=replicate,
    )


def get_sd(
    open_ai_client: AsyncOpenAI = Depends(get_open_ai_client),
    replicate: ReplicateService = Depends(get_replicate_service),
):
    yield StableDiffusion(
        open_ai=open_ai_client,
        replicate_service=replicate,
    )


def get_sdxl(
    open_ai_client: AsyncOpenAI = Depends(get_open_ai_client),
    replicate: ReplicateService = Depends(get_replicate_service),
):
    yield StableDiffusionXL(
        open_ai=open_ai_client,
        replicate_service=replicate,
    )
