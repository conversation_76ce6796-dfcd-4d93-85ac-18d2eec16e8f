from fastapi import APIRouter, Depends

from .dependencies import (
    get_replicate_service,
    get_chinese_photomaker,
    get_flux_agent,
    get_sd, get_sdxl,
)
from .ext.chinese_photomaker import ChinesePhotoMaker
from .ext.flux_agent import FluxAgent
from .ext.sdxl import StableDiffusionXL
from .ext.stable_diffusion import StableDiffusion
from .schemas import (
    PromptRequest,
    Face2ManyRequest,
    ImageRequest,
    GpfganRequest,
    PhotomakerRequest,
    FaceToStickerRequest,
    PhotomakerStyleRequest,
    CustomPhotomakerRequest,
)
from .service import ReplicateService

router = APIRouter(prefix="/replicate", tags=["Replicate"])


@router.post("/predictions/flux-schnell", summary="Flux-Schnell model")
async def create_flux_schnell_prediction(
    prompt: PromptRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(
        model="black-forest-labs/flux-schnell", **prompt.dict()
    )

    return output


@router.post("/predictions/face-to-many", summary="Face2Many")
async def create_face_to_many_prediction(
    data: Face2ManyRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(model="fofr/face-to-many", **data.dict())

    return output


@router.post("/predictions/esrgan", summary="ESR")
async def create_esr_prediction(
    data: ImageRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(model="nightmareai/real-esrgan", **data.dict())

    return output


@router.post("/predictions/rembg", summary="Remove background")
async def create_rembg_prediction(
    data: ImageRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(model="cjwbw/rembg", **data.dict())

    return output


@router.post("/predictions/gpfgan", summary="GpfGan model")
async def create_gpfgan_prediction(
    data: GpfganRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(model="tencentarc/gfpgan", **data.dict())

    return output


@router.post("/predictions/photomaker", summary="Photomaker")
async def create_photomaker_prediction(
    data: PhotomakerRequest, service: ReplicateService = Depends(get_replicate_service)
):
    output = await service.predict(model="tencentarc/photomaker", **data.dict())

    return output


@router.post("/predictions/face-to-sticker", summary="Face to sticker convertation")
async def create_face_to_sticker_prediction(
    data: FaceToStickerRequest,
    service: ReplicateService = Depends(get_replicate_service),
):
    output = await service.predict(model="fofr/face-to-sticker", **data.dict())

    return output


@router.post("/predictions/photomaker-style", summary="New photomaker")
async def create_photomaker_style_prediction(
    data: PhotomakerStyleRequest,
    service: ReplicateService = Depends(get_replicate_service),
):
    output = await service.predict(model="tencentarc/photomaker-style", **data.dict())

    return output


@router.post("/predictions/photomaker-chinese", summary="Chinese version of photomaker")
async def create_chinese_photomaker_request(
    data: CustomPhotomakerRequest,
    chinese_photomaker: ChinesePhotoMaker = Depends(get_chinese_photomaker),
):
    output = await chinese_photomaker.generate_image(
        user_input=data.prompt,
        style=data.style,
        img_url=data.input_image,
    )

    return output


@router.post("/predictions/flux-agent", summary="Chinese version of flux-schnell")
async def create_flux_agent_request(
    data: PromptRequest,
    flux_agent: FluxAgent = Depends(get_flux_agent),
):
    output = await flux_agent.generate_image(
        user_prompt=data.prompt,
    )

    return output


@router.post(
    "/predictions/stable-diffusion", summary="A stable diffusion from Stability AI"
)
async def create_sd_request(data: PromptRequest, sd: StableDiffusion = Depends(get_sd)):
    output = await sd.generate_image(
        user_prompt=data.prompt,
    )

    return output


@router.post("/predictions/sdxl", summary="A stable diffusion (XL) from Stability AI")
async def create_sdxl_request(
    data: PromptRequest, sd: StableDiffusionXL = Depends(get_sdxl)
):
    output = await sd.generate_image(
        user_prompt=data.prompt,
    )

    return output
