from typing import List
from io import By<PERSON><PERSON>

from datetime import datetime

import fastapi
from httpx import AsyncClient
from replicate import Client
from replicate.exceptions import ModelError

from src.ai.settings import settings


class ReplicateService:
    """
    Wrapper around `ReplicateService`
    """

    def __init__(self, client: Client, http_client: AsyncClient):
        self._http_client = http_client
        self._client = client

    @staticmethod
    def _build_model_ref(model):
        return f"{model.owner}/{model.name}:{model.latest_version.id}"

    async def _download_file(self, url: str) -> BytesIO:
        """Downloads file from `Replicate` & returns `BytesIO` object."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        response = await self._http_client.get(url)

        # Replicate always returns file with `out.webp`
        # To avoiding a problem with saving, we add suffix with current timestamp
        filename = f"image{timestamp}.webp"
        io = BytesIO(initial_bytes=response.content)
        io.name = filename

        return io

    async def _upload_file_to_v1(self, file: BytesIO) -> str:
        """Uploads file to `v1`"""

        data = {
            "data": (file.name, file, "image/webp"),
        }

        upload_response = await self._http_client.post(
            f"{settings.BASE_URL}/v1/upload_image",
            files=data,
        )

        if upload_response.status_code != 200:
            raise RuntimeError(
                f"V1 returned {upload_response.status_code}, details: {upload_response.text}"
            )

        return upload_response.json()["src"]  # Return an url to file

    async def predict(
        self,
        model: str,
        **data,
    ) -> List[str]:
        """Creates prediction & waits for it"""

        result = []

        model = await self._client.models.async_get(model)

        try:
            urls_list = await self._client.async_run(
                self._build_model_ref(model),
                input=data,
            )
        except ModelError as e:
            raise fastapi.HTTPException(
                status_code=422, detail=f"Error, replicate said: {e.args[0]}"
            )

        if isinstance(urls_list, str):
            # If replicate returns only one URL
            # We make a list with that url
            urls_list = [
                urls_list,
            ]

        # Because of link to replicates image lives about one hour
        # We download each file given from list & save it to our `S3` storage
        for url in urls_list:
            file = await self._download_file(url)  # Get file-like object

            # Upload
            response_from_v1 = await self._upload_file_to_v1(file)
            result.append(response_from_v1)

            # Release internal buffer of `BytesIO` object.
            file.close()

        return result

    async def create_prediction(self):
        raise NotImplementedError

    async def get_prediction(self, prediction_id: str):
        raise NotImplementedError

    async def cancel_prediction(self, prediction_id: str):
        raise NotImplementedError
