from typing import List, Literal

from pydantic import BaseModel, Field


class PromptRequest(BaseModel):
    prompt: str
    output_format: str = "png"


class ImageRequest(BaseModel):
    image: str


class Face2ManyRequest(ImageRequest):
    style: str


class ImageWithPrompt(PromptRequest, ImageRequest):
    """Mixin"""


class FluxSchnellRequest(BaseModel):
    prompt: str


class EsrganRequest(BaseModel):
    image: str
    scale: int = 2


class GpfganRequest(BaseModel):
    img: str
    scale: int = 2


class PhotomakerRequest(BaseModel):
    prompt: str
    num_steps: int = 50
    style_name: str = "Photographic (Default)"
    input_image: str
    num_outputs: int


class FaceToStickerRequest(BaseModel):
    image: str
    prompt: str
    prompt_strength: float
    instant_id_strength: float


class Prediction(BaseModel):
    id: str
    model: str
    status: str


class DetailedPrediction(Prediction):
    output: List[str] | None


class PhotomakerStyleRequest(BaseModel):
    prompt: str = Field(
        title="Prompt",
        examples=[
            "A girl img riding dragon over a whimsical castle, 3d CGI, art by <PERSON><PERSON><PERSON>, half-body, screenshot from "
            "animation"
        ],
    )
    input_image: str = Field(title="Image-url", examples=["https://example.com"])
    num_outputs: int = Field(title="Num of outputs", examples=[1], default=1)
    num_steps: int = Field(title="Num of steps", default=50)
    style_strength_ratio: int = Field(title="Strength param", default=35, examples=[35])


class CustomPhotomakerRequest(BaseModel):
    prompt: str = Field(
        title="Prompt",
        examples=[
            "A girl img riding dragon over a whimsical castle, 3d CGI, art by Pixar, half-body, screenshot from "
            "animation"
        ],
    )
    input_image: str = Field(title="Image-url", examples=["https://example.com"])
    style: Literal[
        "Ukiyo_e", "Pixar", "comic", "psychedelic", "digit", "disney", "Line_art"
    ]
