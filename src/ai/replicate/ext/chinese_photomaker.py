from openai import AsyncOpenAI
from typing import Literal

from src.ai.replicate.service import ReplicateService

style_list = [
    {
        "name": "<PERSON><PERSON><PERSON>_e",
        "cn_name": "浮世绘",
        "prompt": "A Ukiyo-e styple painting of {prompt}",
        "negative_prompt": "bad hands",
    },
    {
        "name": "Pixar",
        "cn_name": "皮克斯",
        "prompt": "A Pixar animation character of {prompt}, pixar-style, studio anime, Disney, high-quality",
        "negative_prompt": "lowres, bad anatomy, bad hands, text, bad eyes, bad arms, bad legs, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, blurry, grayscale, noisy, sloppy, messy, grainy, highly detailed, ultra textured, photo",
    },
    {
        "name": "comic",
        "cn_name": "漫画",
        "prompt": "comic book of {prompt}, graphic illustration, comic art, graphic novel art, vibrant, highly detailed",
        "negative_prompt": "photograph, deformed, glitch, noisy, realistic, stock photo",
    },
    {
        "name": "psychedelic",
        "cn_name": "迷幻",
        "prompt": "concept art of {prompt}, psychedelic style, portrait, masterpiece, digital artwork, illustrative, painterly, highly detailed",
        "negative_prompt": "photo, photorealistic, realism, ugly",
    },
    {
        "name": "digit",
        "cn_name": "数字艺术",
        "prompt": "concept art of {prompt}, digital artwork, illustrative, painterly, highly detailed",
        "negative_prompt": "photo, photorealistic, realism, ugly",
    },
    {
        "name": "disney",
        "cn_name": "迪士尼",
        "prompt": "3d CGI, art by Pixar, half-body, screenshot from animation, {prompt}",
        "negative_prompt": "realistic, photo-realistic, bad quality, bad anatomy, worst quality, low quality, lowres, extra fingers, blur, blurry, ugly, wrong proportions, watermark, image artifacts, bad eyes, bad hands, bad arms",
    },
    {
        "name": "Line_art",
        "cn_name": "简约线条",
        "prompt": "line art drawing {prompt} . professional, sleek, modern, minimalist, graphic, line art, vector graphics",
        "negative_prompt": "anime, photorealistic, 35mm film, deformed, glitch, blurry, noisy, off-center, deformed, cross-eyed, closed eyes, bad anatomy, ugly, disfigured, mutated, realism, realistic, impressionism, expressionism, oil, acrylic",
    },
]


class ChinesePhotoMaker:
    def __init__(
        self,
        open_ai_client: AsyncOpenAI,
        replicate_client: ReplicateService,
    ) -> None:
        self._replicate_client = replicate_client
        self._open_ai_client = open_ai_client
        self.styles = {
            k["name"]: (k["prompt"], k["negative_prompt"]) for k in style_list
        }

    # Based on the user's input, create an input to gpt, which is used to turn the user's input into a short English sentence that can be called later to generate an image
    def generate_instruction(self, user_input: str) -> str:
        return f"""
Your task is to extract key information from the user's input and make sentences. The key information includes:
1. Character, with the option of ["boy", "girl", "man", "woman", "old man", "old woman"]
2. Profile
3. Action
4. Background
The final output format is a coherent sentence in English:
    Character, their profile, actions performed, background
REQUIREMENTS:
1. Character can only be selected from the available options!
2. If the character is not mentioned in the user's input, choose one at random from the character options available.
3. Do not omit information from the user's input.
4. Except for the character. If the information to be extracted is not mentioned in the user's input, the corresponding output shall be empty.
5. The output should be as concise and clear as possible.
6. Answer only in English.
7. Do not output any irrelevant content!

Examples:
User input: 一个男孩在沙滩上踢足球
Output: A boy is playing soccer on the beach
User input: 一群少女骑着龙飞越城堡
Output: A girl riding dragon over a whimsical castle

Starting now, the following is the user input:

{user_input}
"""

    # According to the instruction generated by the a generate_instruction, , call gpt to generate an English sentence
    async def generate_prompt(self, user_input):
        instruction = self.generate_instruction(user_input)

        image_description = (
            (
                await self._open_ai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    temperature=0.4,
                    max_tokens=1024,
                    messages=[{"role": "user", "content": instruction}],
                )
            )
            .choices[0]
            .message.content
        )
        return image_description

    # In the prompt for generating images, need to add the keyword "img" after the focused generation object
    def add_tragger(self, image_description):
        tragger_input = ""
        for character in ["boy", "girl", "man"]:
            index = image_description.find(character)
            if index != -1:
                tragger_input = (
                    image_description[:index]
                    + character
                    + " img"
                    + image_description[index + len(character) :]
                )
                return tragger_input
        return tragger_input + " img"

    # Generate Image,
    async def generate_image(
        self,
        user_input: str,
        style: Literal[
            "Ukiyo_e", "Pixar", "comic", "psychedelic", "digit", "disney", "Line_art"
        ],
        img_url: str,
    ):
        instruction = self.generate_instruction(user_input)
        image_desc = await self.generate_prompt(instruction)
        tragger_input = self.add_tragger(image_desc)
        if tragger_input[-1] == "." or tragger_input[-1] == ",":
            tragger_input = tragger_input[:-1]
        final_prompt = self.styles[style][0].format(prompt=tragger_input)
        negative_prompt = self.styles[style][1]

        input = {
            "prompt": final_prompt,
            "num_steps": 50,
            "input_image": img_url,
            "negative_prompt": negative_prompt,
        }

        output_url = await self._replicate_client.predict(
            "tencentarc/photomaker-style", **input
        )

        return output_url
