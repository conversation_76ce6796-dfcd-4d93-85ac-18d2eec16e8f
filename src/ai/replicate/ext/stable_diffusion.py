from typing import List

from openai import AsyncOpenAI

from src.ai.replicate.service import ReplicateService


class StableDiffusion:
    def __init__(
        self, open_ai: AsyncOpenAI, replicate_service: ReplicateService
    ) -> None:
        self._replicate_service = replicate_service
        self._client = open_ai

    def painting_description(self, user_prompt: str) -> str:
        return f"""
You are an imaginative painting master. Your task is to create a visual depiction using text based on the user's input. Here are a few requirements:
 1. The description should align with the theme provided by the user.
 2. If the user's input includes specific features, do not ignore or alter them.
 3. Describe the details of the scene as clearly as possible.
 4. Specify the style of the painting (photorealistic, realistic, cartoon, etc.). If the user does not specify, choose the style based on the input provided.
 5. Do not include any additional output beyond the description of the painting.
 6. Please provide the description in English.

Here is the user's input:
{user_prompt}
"""

    def element_extraction(self, painting_prompt_desc: str) -> str:
        return f"""
You are a linguistics expert. The following is a description of a painting:

{painting_prompt_desc}

Your task is to extract the following information from this description:
  Painting style.
  The event or individual depicted in the painting.
  The subjects in the painting and their corresponding features. Features include descriptions of the subjects and the actions they are performing.
  The background (environment) and its features.

Here are some requirements for the extraction process:
  1. The event description should be as brief and clear as possible.
  2. If there are multiple subjects, extract all of them.
  3. The description of features should be as concise as possible, using phrases.
  4. The output should be in the following format:
    Style - ...
    Event - ...
    Subject 1 - Feature 1, Feature 2, ...
    Subject 2 - Feature 1, Feature 2, ...
    ...
    Background - Feature 1, Feature 2, ...
  5. The result should be in English only!
"""

    async def get_image_generation_prompt(self, user_prompt: str) -> str:
        prompt = self.painting_description(user_prompt)
        painting_description = (
            (
                await self._client.chat.completions.create(
                    model="gpt-4o-mini",
                    temperature=0.4,
                    max_tokens=1024,
                    messages=[{"role": "user", "content": prompt}],
                )
            )
            .choices[0]
            .message.content
        )

        element_extraction_prompt = self.element_extraction(painting_description)
        image_generation_prompt = (
            (
                await self._client.chat.completions.create(
                    model="gpt-4o-mini",
                    temperature=0.4,
                    max_tokens=1024,
                    messages=[{"role": "user", "content": element_extraction_prompt}],
                )
            )
            .choices[0]
            .message.content
        )
        return image_generation_prompt

    async def generate_image(self, user_prompt: str) -> List[str]:
        prompt = await self.get_image_generation_prompt(user_prompt)
        input_data = {"prompt": prompt}
        img_url = await self._replicate_service.predict(
            "stability-ai/stable-diffusion-3", **input_data
        )

        return img_url
