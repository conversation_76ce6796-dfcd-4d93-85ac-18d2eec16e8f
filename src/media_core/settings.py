from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")

    DATABASE_URL: str

    # Cloudflare
    CF_STREAM_API_TOKEN: str
    CF_ACCOUNT_ID: str
    CF_CUSTOMER_SUBDOMAIN: str

    CF_R2_ACCESS_KEY_ID: str
    CF_R2_SECRET_ACCESS_KEY: str
    CF_R2_BUCKET: str
    CF_R2_ENDPOINT: str
    CF_R2_IMAGES_MEMEFANS_ACCESS_URL: str
    CF_R2_FILES_MEMEFANS_ACCESS_URL: str


settings = Settings()
