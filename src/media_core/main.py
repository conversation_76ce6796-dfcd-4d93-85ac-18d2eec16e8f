import json
from typing import List, Optional

from fastapi import FastAPI, Depends, Request, UploadFile, HTTPException, BackgroundTasks
from fastapi.openapi.utils import get_openapi
from fastapi.params import Query, File
from sqlalchemy import update
from starlette import status

from src.auth import current_user
from src.infra.app import create_app
from src.infra.logger import get_logger
from src.database.session import get_session_context
logger = get_logger("media", level="INFO", file_path="latest.log")
from src.database.models import User, Video
from src.media_core.dependencies import get_cloudflare_service
from src.media_core.schemas import VideoUploadResponse, BatchPresignedUrlResponse
from src.media_core.services import CloudflareService
from src.posts.videos.schemas import ProcessingStatus
from src.media_core.polling_service import video_polling_service

app = create_app(title="Media Core", version="1.0", description="Media Core API", request_logger=logger)


@app.on_event("startup")
async def startup_event():
    """应用启动时启动视频状态轮询服务"""
    logger.info("启动视频状态轮询服务...")
    await video_polling_service.start()


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时停止视频状态轮询服务"""
    logger.info("停止视频状态轮询服务...")
    await video_polling_service.stop()


@app.get("/health")
async def health():
    return {"status": "ok"}


@app.post("/notifications")
async def receive_webhook(request: Request, background_tasks: BackgroundTasks, video_service: CloudflareService = Depends(get_cloudflare_service)):
    """
    Receive webhook notifications and process data asynchronously through background tasks.
    """
    payload = await request.json()
    background_tasks.add_task(process_webhook, payload, video_service)
    return {"message": "Webhook received"}


async def process_webhook(data: dict, video_service: CloudflareService):
    """
    Process webhook data in the background, including logging and database updates.
    """
    try:
        uid = data.get("uid")
        if not uid:
            logger.error("Missing uid in webhook payload")
            return

        status = data.get("status")
        if not status:
            logger.error(f"Missing status in webhook payload for uid: {uid}")
            return

        state = status.get("state")
        logger.info(f"Processing webhook payload uid: {uid}, status: {state}")
        metadata = {
            "creator": data.get("creator"),
            "size": data.get("size")
        }

        if state == "ready":
            cover_url = data.get("thumbnail")
            if cover_url:
                try:
                    cover_url = await video_service.upload_thumbnail_to_r2(data["thumbnail"])
                    logger.info(f"Successfully uploaded thumbnail to R2 for uid: {uid}, cover_url: {cover_url}")
                except Exception as thumbnail_error:
                    logger.error(f"Failed to upload thumbnail to R2 for uid: {uid}: {str(thumbnail_error)}")
                    # 如果上传失败，使用原始的 thumbnail URL
                    cover_url = data["thumbnail"]

            async with get_session_context("write") as session:
                try:
                    stmt = update(Video).where(Video.uid == uid).values(
                        url=data["playback"]["hls"],
                        processing_status=ProcessingStatus.COMPLETED,
                        cover=cover_url,
                        metainfo=json.dumps(metadata),
                        width=data["input"].get("width"),
                        height=data["input"].get("height"),
                    )
                    await session.execute(stmt)
                    await session.commit()
                    logger.info(f"Successfully updated video status for uid: {uid}")
                except Exception as db_error:
                    await session.rollback()
                    logger.error(f"Database error while updating video {uid}: {str(db_error)}")
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")


@app.get(
    "/direct_upload",
    tags=["Upload Video"],
    summary="Get upload video url",
    description="Endpoint for getting upload video url, video size should less than 200MB",
    status_code=status.HTTP_200_OK,
    response_model=VideoUploadResponse,
)
async def get_upload_url(
    title: str,
    max_duration_seconds: int = Query(default=3600),
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    url = await video_service.get_upload_video_url(user.id, title, max_duration_seconds)
    return url


@app.get(
    "/resumable_upload_url",
    tags=["Upload Video"],
    summary="Get Resumable upload video url",
    description="Endpoint for getting resumable upload video url",
    status_code=status.HTTP_200_OK,
    response_model=VideoUploadResponse,
)
async def get_resumable_upload_url(
    video_name: str,
    length: int,
    max_duration_seconds: int = Query(default=3600),
    # user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    url = await video_service.get_resumable_upload_video_url(
        "user", video_name, length, max_duration_seconds
    )
    return url


@app.get(
    "/upload_status",
    tags=["Upload Video"],
    summary="Get upload status",
    description="Endpoint for checking video upload status",
    status_code=status.HTTP_200_OK,
)
async def upload_status(
    video_id: str,
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    return await video_service.upload_progress(video_id)


@app.get(
    "/create_live_input",
    tags=["Live streaming"],
    summary="Create live streaming input",
    description="Endpoint for creating a live streaming input",
    status_code=status.HTTP_200_OK,
)
async def create_live_input(
    # user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    url = await video_service.create_live_input("")
    return url


@app.get(
    "/thumbnail",
    tags=["Thumbnails"],
    summary="Get video thumbnail",
    description="Endpoint for getting video thumbnail",
    status_code=status.HTTP_200_OK,
)
async def get_thumbnail(
    video_id: str,
    # user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    url = await video_service.get_thumbnail(video_id)
    return url


@app.get(
    "/gif_thumbnails",
    tags=["Thumbnails"],
    summary="Get video GIF thumbnail",
    description="Endpoint for getting video GIF thumbnail",
    status_code=status.HTTP_200_OK,
)
async def get_gif_thumbnail(
    video_id: str,
    # user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    url = await video_service.get_gif_thumbnail(video_id)
    return url


@app.get(
    "/video_list",
    tags=["Video Info"],
    summary="Get video list",
    description="Endpoint for getting user's video list",
    status_code=status.HTTP_200_OK,
)
async def get_video_list(
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    return await video_service.video_list(user.id)


@app.get(
    "/search_video",
    tags=["Video Info"],
    summary="Search videos",
    description="Endpoint for searching videos by name",
    status_code=status.HTTP_200_OK,
)
async def search_video(
    name: str,
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    return await video_service.search_video(user.id, name)


@app.get(
    "/video_details",
    tags=["Video Info"],
    summary="Get video details",
    description="Endpoint for getting video details by ID",
    status_code=status.HTTP_200_OK,
)
async def get_video_details(
    video_id: str,
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    return await video_service.video_details(video_id)


@app.put(
    "/upload_subtitle",
    tags=["Subtitle"],
    summary="Upload video subtitle",
    description="Endpoint for uploading a WebVTT subtitle file to a Cloudflare Stream video for a specified language.",
    status_code=status.HTTP_200_OK,
)
async def upload_subtitle(
    video_id: str,
    language: str,
    file: UploadFile = File(...),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Upload subtitle file to Cloudflare Stream:
      - video_id: Unique identifier generated by Cloudflare for the video
      - language: Subtitle language in BCP47 format
      - file: WebVTT format subtitle file to upload
    """
    try:
        content_bytes = await file.read()
        file_content = content_bytes.decode("utf-8")
    except Exception as e:
        raise HTTPException(status_code=400, detail="Unable to read or parse subtitle file") from e

    result = await video_service.upload_subtitle(video_id, language, file_content)
    if result is None:
        raise HTTPException(status_code=500, detail="Subtitle upload failed")
    return result


@app.post(
    "/autogenerate_subtitle",
    tags=["Subtitle"],
    summary="Auto-generate video subtitle",
    description="Endpoint for auto-generating subtitles for a Cloudflare Stream video in a specified language.",
    status_code=status.HTTP_200_OK,
)
async def autogenerate_subtitle(
    video_id: str,
    language: str,
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Auto-generate subtitles:
      - video_id: Unique identifier generated by Cloudflare for the video
      - language: Subtitle language in BCP47 format
    """
    result = await video_service.autogenerate_subtitle(video_id, language)
    if result is None:
        raise HTTPException(status_code=500, detail="Auto-generating subtitles failed")
    return result


@app.delete(
    "/delete_video",
    tags=["Video Info"],
    summary="Delete video",
    description="Endpoint for deleting a video",
    status_code=status.HTTP_200_OK,
)
async def delete_video(
    video_id: str,
    user: User = Depends(current_user),
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Delete video:
      - video_id: Unique identifier of the video to delete
    """
    result = await video_service.delete_video(user.id, video_id)
    if result is None:
        raise HTTPException(status_code=500, detail="Video deletion failed")
    return result


@app.post(
    "/upload_image",
    tags=["Image"],
    summary="Upload image",
    description="Endpoint for getting image upload URL",
    status_code=status.HTTP_200_OK,
)
async def upload_image(
    image_name: str,
    content_type: Optional[str] = Query(default="image/jpeg"),
    expires_seconds: Optional[int] = Query(default=2 * 60),
    image_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Get image upload URL:
      - image_name: Name of the image
    """
    result = await image_service.get_upload_image_presigned_url(image_name, content_type, expires_seconds)
    return result


@app.post(
    "/batch_upload_image",
    tags=["Image"],
    summary="Batch upload images",
    description="Endpoint for getting multiple image upload URLs in a single request",
    status_code=status.HTTP_200_OK,
    response_model=BatchPresignedUrlResponse
)
async def batch_upload_image(
    image_names: List[str] = Query(..., description="List of image names"),
    content_type: Optional[str] = Query(default="image/jpeg"),
    expires_seconds: Optional[int] = Query(default=2 * 60),
    image_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    批量获取图片上传URL:
      - image_names: 图片名称列表
      - content_type: 内容类型，默认为image/jpeg
      - expires_seconds: URL过期时间（秒），默认为120秒
    """
    result = await image_service.get_batch_upload_image_presigned_urls(image_names, content_type, expires_seconds)
    return result


@app.delete(
    "/delete_image",
    tags=["Image"],
    summary="Delete image",
    description="Endpoint for deleting an image",
    status_code=status.HTTP_200_OK,
)
async def delete_image(
    image_name: str,
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Delete image:
      - image_name: Name of the image to delete
    """
    result = await video_service.delete_image("user_id", image_name)
    return result


@app.get(
    "/check_image",
    tags=["Image"],
    summary="Check if image exists",
    description="Endpoint for checking if an image has been successfully uploaded",
    status_code=status.HTTP_200_OK,
)
async def check_image(
    image_name: str,
    video_service: CloudflareService = Depends(get_cloudflare_service),
):
    """
    Check if image exists:
      - image_name: Name of the image to check
    """
    exists = await video_service.check_image_exists(image_name)
    return {"exists": exists}


@app.post(
    "/upload_file",
    tags=["File"],
    summary="Upload file",
    description="Endpoint for getting file upload URL",
    status_code=status.HTTP_200_OK,
)
async def upload_file(
    file_name: str,
    content_type: Optional[str] = Query(default="application/octet-stream"),
    expires_seconds: Optional[int] = Query(default=2 * 60),
    image_service: CloudflareService = Depends(get_cloudflare_service),
):
    result = await image_service.get_upload_file_presigned_url(file_name, content_type, expires_seconds)
    return result


@app.post(
    "/batch_upload_file",
    tags=["File"],
    summary="Batch upload files",
    description="Endpoint for getting multiple file upload URLs in a single request",
    status_code=status.HTTP_200_OK,
    response_model=BatchPresignedUrlResponse
)
async def batch_upload_file(
    file_names: List[str] = Query(..., description="List of image names"),
    content_type: Optional[str] = Query(default="application/octet-stream"),
    expires_seconds: Optional[int] = Query(default=2 * 60),
    image_service: CloudflareService = Depends(get_cloudflare_service),
):
    result = await image_service.get_batch_upload_file_presigned_urls(file_names, content_type, expires_seconds)
    return result


@app.post(
    "/check_video_status",
    tags=["Video Info"],
    summary="Manual video status check",
    description="Manually trigger video status check for a specific video",
    status_code=status.HTTP_200_OK,
)
async def check_video_status(
    video_uid: str,
    user: User = Depends(current_user),
):
    """
    手动检查视频状态:
      - video_uid: Cloudflare视频的唯一标识符
    """
    success = await video_polling_service.check_video_status_manually(video_uid)
    return {"success": success, "message": "视频状态检查已完成" if success else "视频状态检查失败"}
