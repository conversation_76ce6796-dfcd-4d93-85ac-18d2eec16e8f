from fastapi import Depends

from src.database.session import AsyncSession, get_session
from .cloudflare_helper import CloudflareHelper

from .repos import CloudflareRepo
from .services import CloudflareService


async def get_cloudflare_repo(
    session: AsyncSession = Depends(get_session)
) -> CloudflareRepo:
    """Builds & returns `MiniApp` repository"""
    return CloudflareRepo(
        session
    )


async def get_cloudflare_helper() -> CloudflareHelper:
    """Builds & returns `MiniApp` repository"""
    return CloudflareHelper()


async def get_cloudflare_service(
    repo: CloudflareRepo = Depends(get_cloudflare_repo),
    video_helper: CloudflareHelper = Depends(get_cloudflare_helper)
) -> CloudflareService:
    """Builds & returns `AppService` object."""
    return CloudflareService(repo, video_helper)
