import base64
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union
import boto3
from botocore.config import Config

import httpx
from cloudflare import Async<PERSON>loudflare, NOT_GIVEN
from cloudflare.pagination import AsyncSinglePage
from cloudflare.types.stream import DirectUploadCreateResponse, LiveInput, Video
from fastapi import HTT<PERSON>Ex<PERSON>
from httpx import Response

from src.media_core.settings import settings


class CloudflareHelper:
    def __init__(self):
        """
        Initialize the VideoHelper with Cloudflare credentials from settings.
        """
        self.cloudflare = AsyncCloudflare(api_token=settings.CF_STREAM_API_TOKEN)
        self.account_id = settings.CF_ACCOUNT_ID
        self.stream_api_token = settings.CF_STREAM_API_TOKEN
        self.customer_subdomain = settings.CF_CUSTOMER_SUBDOMAIN

        # R2 configuration
        self.r2_access_key_id = settings.CF_R2_ACCESS_KEY_ID
        self.r2_secret_access_key = settings.CF_R2_SECRET_ACCESS_KEY
        self.r2_bucket = settings.CF_R2_BUCKET
        self.r2_endpoint = settings.CF_R2_ENDPOINT

        # Initialize S3 client for R2
        self.s3_client = boto3.client(
            's3',
            endpoint_url=self.r2_endpoint,
            aws_access_key_id=self.r2_access_key_id,
            aws_secret_access_key=self.r2_secret_access_key,
            config=Config(signature_version='s3v4'),
            region_name='auto'
        )

    async def _post_request(
        self, url: str, headers: Dict[str, str], payload: Dict[str, Any]
    ) -> Response:
        """
        Wrapper for HTTP POST requests with unified response handling.

        :param url: Request URL
        :param headers: Request headers
        :param payload: Request body JSON data
        :return: Response object
        :raises Exception: When response status code is not 200
        """
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response

    async def _get_request(self, url: str, headers: Dict[str, str]) -> Response:
        """
        Wrapper for HTTP GET requests with unified response handling.

        :param url: Request URL
        :param headers: Request headers
        :return: Response object
        :raises Exception: When response status code is not 200
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            return response

    async def create_direct_upload_url(
        self, user_id: str, name: str, max_duration_seconds: int = 3600
    ) -> DirectUploadCreateResponse:
        """
        Create a direct upload URL using Cloudflare API.

        :param user_id: ID of the user uploading the video
        :param name: Name of the video
        :param max_duration_seconds: Maximum allowed duration for the video (in seconds)
        :return: Cloudflare API response data
        :raises Exception: When upload request fails
        """
        response: DirectUploadCreateResponse = (
            await self.cloudflare.stream.direct_upload.create(
                account_id=self.account_id,
                meta={"name": name},
                # allowed_origins=["toci.com"],
                max_duration_seconds=max_duration_seconds,
                creator=user_id,
                thumbnail_timestamp_pct=0.1
            )
        )

        return response

    async def create_stream_upload_url(
        self, user_id: str, video_name: str, upload_length: int, max_duration_seconds: int = 3600
    ) -> Tuple[str, str]:
        """
        Initialize Cloudflare Stream upload:
        - Parameter upload_length: Total bytes of the upload file (provided by frontend)
        - Parameter max_duration_seconds: Maximum video duration (in seconds, provided by frontend)

        The function calculates the expiry time (current UTC time + max_duration_seconds),
        and constructs the required Upload-Metadata header in the format:

            'maxDurationSeconds <base64(max_duration_seconds)>,requiresignedurls,expiry <base64(expiry)>'

        Calls the Cloudflare Stream API (with direct_user=true parameter),
        and returns the Location header from the response to the client.

        :return: Tuple of (video_id, destination_url)
        """
        if not upload_length or max_duration_seconds is None:
            raise HTTPException(
                status_code=400,
                detail="Missing required parameters: upload_length or max_duration_seconds",
            )

        # Calculate expiry time: current UTC time + max_duration_seconds, in RFC3339 format (without microseconds)
        expiry_timestamp = (
            datetime.utcnow() + timedelta(seconds=max_duration_seconds)
        ).strftime("%Y-%m-%dT%H:%M:%SZ")

        # Base64 encode max_duration_seconds and expiry_timestamp
        name = base64.b64encode(video_name.encode()).decode()
        max_duration_encoded = base64.b64encode(
            str(max_duration_seconds).encode()
        ).decode()
        expiry_encoded = base64.b64encode(expiry_timestamp.encode()).decode()

        # Construct Upload-Metadata header, keys and values separated by spaces, key-value pairs separated by commas
        upload_metadata = f"name {name}, maxDurationSeconds {max_duration_encoded},requiresignedurls,expiry {expiry_encoded}"

        endpoint = f"https://api.cloudflare.com/client/v4/accounts/{self.account_id}/stream?direct_user=true"

        headers = {
            "Authorization": f"bearer {self.stream_api_token}",
            "Tus-Resumable": "1.0.0",
            "Upload-Length": str(upload_length),
            "Upload-Metadata": upload_metadata,
        }

        cf_response = await self._post_request(endpoint, headers, {})

        # Get Location header from Cloudflare response
        destination = cf_response.headers.get("Location", "")
        video_id = cf_response.headers.get("stream-media-id", "")

        return video_id, destination

    async def create_live_input(
        self,
        user_id: str,
        default_creator: Optional[str] = None,
        delete_recording_after_days: Optional[int] = None,
        name: Optional[str] = None,
        recording: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a live input for streaming.

        :param user_id: ID of the user creating the live input
        :param default_creator: Default creator ID (defaults to user_id if not provided)
        :param delete_recording_after_days: Days after which to delete the recording
        :param name: Name of the live input
        :param recording: Recording configuration
        :return: Live input details as a dictionary
        """
        response: LiveInput = await self.cloudflare.stream.live_inputs.create(
            account_id=self.account_id,
            default_creator=default_creator or user_id,
            delete_recording_after_days=(
                delete_recording_after_days
                if delete_recording_after_days
                else NOT_GIVEN
            ),
            meta={"name": name} if name else NOT_GIVEN,
        )
        return response.model_dump()

    async def get_thumbnail_url(self, video_id: str) -> str:
        """
        Get the thumbnail URL for a video.

        :param video_id: ID of the video
        :return: URL to the video thumbnail
        """
        return f"https://{self.customer_subdomain}/{video_id}/thumbnails/thumbnail.jpg?time=1s&height=270"

    async def get_gif_thumbnail_url(self, video_id: str) -> str:
        """
        Get the GIF thumbnail URL for a video.

        :param video_id: ID of the video
        :return: URL to the video GIF thumbnail
        """
        return f"https://{self.customer_subdomain}/{video_id}/thumbnails/thumbnail.gif?time=1s&height=200&duration=4s"

    async def get_video_details(self, video_id: str) -> Video:
        """
        Get detailed information about a video.

        :param video_id: ID of the video
        :return: Video details
        """
        response: Video = await self.cloudflare.stream.get(
            account_id=self.account_id, identifier=video_id
        )
        return response

    async def search_videos(self, user_id: str, video_name: Optional[str] = None) -> AsyncSinglePage[Video]:
        """
        Search for videos by name.

        :param user_id: ID of the user
        :param video_name: Name of the video to search for
        :return: List of matching videos
        """
        response: AsyncSinglePage[Video] = await self.cloudflare.stream.list(
            account_id=self.account_id,
            asc=True,
            # creator=user_id,
            include_counts=True,
            search=video_name if video_name else NOT_GIVEN,
        )
        return response

    async def upload_subtitle(self, video_id: str, language: str, file_content: Any) -> Any:
        """
        Upload subtitle file for a video.

        :param video_id: ID of the video
        :param language: Language code for the subtitle
        :param file_content: Content of the subtitle file
        :return: Response from the API
        """
        response = await self.cloudflare.stream.captions.language.update(
            language=language,
            account_id=self.account_id,
            identifier=video_id,
            file=file_content,
        )
        return response

    async def autogenerate_subtitle(self, video_id: str, language: str) -> Any:
        """
        Auto-generate subtitles for a video.

        :param video_id: ID of the video
        :param language: Language code for the subtitle
        :return: Response from the API
        """
        response = await self.cloudflare.stream.captions.language.create(
            language=language,
            account_id=self.account_id,
            identifier=video_id,
        )
        return response

    async def get_video(self, video_id: str) -> Video:
        """
        Get a video by ID.

        :param video_id: ID of the video
        :return: Video details
        """
        response: Video = await self.cloudflare.stream.get(
            account_id=self.account_id, identifier=video_id
        )
        return response

    async def delete_video(self, user_id: str, video_id: str) -> Any:
        """
        Delete a video.

        :param user_id: ID of the user deleting the video
        :param video_id: ID of the video to delete
        :return: Response from the API
        """
        response = await self.cloudflare.stream.delete(
            account_id=self.account_id,
            identifier=video_id
        )
        return response

    async def direct_upload_image(self, user_id: str, name: Optional[str] = None) -> Any:
        """
        Create a direct upload URL for an image.

        :param user_id: ID of the user uploading the image
        :param name: Name of the image
        :return: Response from the API
        """
        response = await self.cloudflare.images.v2.direct_uploads.create(
            account_id=self.account_id,
            metadata={"name": name, "creator": user_id},
        )
        return response

    async def delete_image(self, user_id: str, image_id: str) -> Any:
        """
        Delete an image.

        :param user_id: ID of the user deleting the image
        :param image_id: ID of the image to delete
        :return: Response from the API
        """
        response = await self.cloudflare.images.v1.delete(
            account_id=self.account_id,
            image_id=image_id
        )
        return response

    async def upload_image_to_r2(self, file_content: bytes, file_name: str, content_type: str) -> str:
        """
        Upload an image to Cloudflare R2 storage.

        :param file_content: Binary content of the image file
        :param file_name: Name of the file to store in R2
        :param content_type: MIME type of the file (e.g., 'image/jpeg', 'image/png')
        :return: URL of the uploaded image
        """
        try:
            # Upload the file to R2
            self.s3_client.put_object(
                Bucket=self.r2_bucket,
                Key=file_name,
                Body=file_content,
                ContentType=content_type
            )

            # Construct and return the public URL
            # Note: You might need to configure public access for your bucket
            image_url = f"{self.r2_endpoint}/{self.r2_bucket}/{file_name}"
            return image_url

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload image to R2: {str(e)}"
            )

    async def delete_image_from_r2(self, file_name: str) -> bool:
        """
        Delete an image from Cloudflare R2 storage.

        :param file_name: Name of the file to delete from R2
        :return: True if deletion was successful
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.r2_bucket,
                Key=file_name
            )
            return True
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete image from R2: {str(e)}"
            )

    async def get_image_url(self, file_name: str) -> str:
        """
        Get the URL for an image stored in R2.

        :param file_name: Name of the file in R2
        :return: URL of the image
        """
        return f"{self.r2_endpoint}/{self.r2_bucket}/{file_name}"

    async def generate_presigned_upload_url(
        self,
        file_name: str,
        content_type: str,
        expires_in: int = 2 * 60
    ) -> Dict[str, str]:
        """
        Generate a presigned URL for direct upload to R2.
        This URL can be used by the client to upload files directly to R2 without going through the server.

        :param file_name: Name of the file to upload
        :param content_type: MIME type of the file (e.g., 'image/jpeg', 'image/png')
        :param expires_in: URL expiration time in seconds (default: 1 hour)
        :return: Dictionary containing upload URL and file URL
        """
        try:
            # Generate presigned URL for PUT operation
            presigned_url = self.s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': self.r2_bucket,
                    'Key': file_name,
                    'ContentType': content_type
                },
                ExpiresIn=expires_in
            )

            # Generate the public URL for the file
            # file_url = f"{self.r2_endpoint}/{self.r2_bucket}/{file_name}"
            return presigned_url

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate presigned URL: {str(e)}"
            )

    async def generate_presigned_download_url(
        self,
        file_name: str,
        expires_in: int = 2 * 60
    ) -> str:
        """
        Generate a presigned URL for downloading a file from R2.

        :param file_name: Name of the file to download
        :param expires_in: URL expiration time in seconds (default: 1 hour)
        :return: Presigned download URL
        """
        try:
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.r2_bucket,
                    'Key': file_name
                },
                ExpiresIn=expires_in
            )
            return presigned_url

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate presigned download URL: {str(e)}"
            )
