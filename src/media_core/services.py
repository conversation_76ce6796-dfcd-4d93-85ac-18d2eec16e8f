import uuid
import async<PERSON>
from typing import List

import httpx
from src.media_core.repos import CloudflareRepo
from src.media_core.schemas import VideoUploadResponse
from src.media_core.cloudflare_helper import CloudflareHelper
from src.media_core.settings import settings
from src.media_core.logger import logger


class CloudflareService:

    def __init__(self, repo: CloudflareRepo, cf_helper: CloudflareHelper):
        self._repo = repo
        self._cf_helper = cf_helper
        self.r2_images_memefans_url = settings.CF_R2_IMAGES_MEMEFANS_ACCESS_URL
        self.r2_files_memefans_url = settings.CF_R2_FILES_MEMEFANS_ACCESS_URL

    async def get_upload_image_presigned_url(self, file_name: str, content_type: str = "image/jpeg", expires_in: int = 3600):
        presigned_url = await self._cf_helper.generate_presigned_upload_url(
            file_name=file_name,
            content_type=content_type,
            expires_in=expires_in
        )
        file_url = f"{self.r2_images_memefans_url}/{file_name}"

        return {
            "upload_url": presigned_url,
            "file_url": file_url
        }

    async def get_upload_file_presigned_url(self, file_name: str, content_type: str = "application/octet-stream", expires_in: int = 3600):
        presigned_url = await self._cf_helper.generate_presigned_upload_url(
            file_name=file_name,
            content_type=content_type,
            expires_in=expires_in
        )
        file_url = f"{self.r2_files_memefans_url}/{file_name}"

        return {
            "upload_url": presigned_url,
            "file_url": file_url
        }

    async def get_batch_upload_image_presigned_urls(self, file_names: List[str], content_type: str = "image/jpeg", expires_in: int = 3600):
        """
        批量生成图片上传的预签名URL

        :param file_names: 图片文件名列表
        :param content_type: 内容类型，默认为image/jpeg
        :param expires_in: URL过期时间（秒），默认为3600秒
        :return: 包含所有预签名URL的列表
        """
        urls = []
        for file_name in file_names:
            presigned_url = await self._cf_helper.generate_presigned_upload_url(
                file_name=file_name,
                content_type=content_type,
                expires_in=expires_in
            )
            file_url = f"{self.r2_images_memefans_url}/{file_name}"
            urls.append({
                "upload_url": presigned_url,
                "file_url": file_url
            })
        return {"urls": urls}

    async def get_batch_upload_file_presigned_urls(self, file_names: List[str], content_type: str = "application/octet-stream",
                                                   expires_in: int = 3600):
        """
        批量生成图片上传的预签名URL

        :param file_names: 图片文件名列表
        :param content_type: 内容类型，默认为image/jpeg
        :param expires_in: URL过期时间（秒），默认为3600秒
        :return: 包含所有预签名URL的列表
        """
        urls = []
        for file_name in file_names:
            presigned_url = await self._cf_helper.generate_presigned_upload_url(
                file_name=file_name,
                content_type=content_type,
                expires_in=expires_in
            )
            file_url = f"{self.r2_files_memefans_url}/{file_name}"
            urls.append({
                "upload_url": presigned_url,
                "file_url": file_url
            })
        return {"urls": urls}

    async def get_download_image_presigned_url(self, user_id: str, file_name: str, expires_in: int = 3600):
        response = await self._cf_helper.generate_presigned_download_url(
            file_name=file_name,
            expires_in=expires_in
        )
        return response

    async def get_upload_video_url(self, user_id: str, title: str, max_duration_seconds: int):
        response = await self._cf_helper.create_direct_upload_url(
            user_id, title, max_duration_seconds
        )
        return VideoUploadResponse(url=response.upload_url, uid=response.uid)

    async def get_resumable_upload_video_url(
        self, user_id: str, video_name: str, length: int, max_duration_seconds: int
    ):
        video_id, url = await self._cf_helper.create_stream_upload_url(
            user_id, video_name, length, max_duration_seconds
        )
        return VideoUploadResponse(url=url, uid=video_id)

    async def upload_progress(self, video_id: str):
        video_detail = await self._cf_helper.get_video(video_id)
        return video_detail.status

    async def create_live_input(
        self,
        user_id: str,
        default_creator: str = None,
        delete_recording_after_days: int = None,
        meta: dict = None,
        recording: dict = None,
    ):
        return await self._cf_helper.create_live_input(
            user_id, default_creator, delete_recording_after_days, meta, recording
        )

    async def get_thumbnail(self, video_id: str):
        return await self._cf_helper.get_thumbnail_url(video_id)

    async def get_gif_thumbnail(self, video_id: str):
        return await self._cf_helper.get_gif_thumbnail_url(video_id)

    async def search_video(self, user_id: str, video_name: str):
        return await self._cf_helper.search_videos(user_id, video_name)

    async def video_details(self, video_id: str):
        return await self._cf_helper.get_video_details(video_id)

    async def video_list(self, user_id: str):
        return await self._cf_helper.search_videos(user_id)

    async def upload_subtitle(self, video_id, language, file_content):
        return await self._cf_helper.upload_subtitle(
            video_id, language, file_content
        )

    async def autogenerate_subtitle(self, video_id, language):
        return await self._cf_helper.autogenerate_subtitle(video_id, language)

    async def delete_video(self, user_id: str, video_id: str):
        return await self._cf_helper.delete_video(user_id, video_id)

    async def get_upload_image_url(self, user_id: str):
        return await self._cf_helper.direct_upload_image(user_id)

    async def delete_image(self, user_id: str, video_id: str):
        # TODO: Check if the image belongs to the user
        return await self._cf_helper.delete_image(user_id, video_id)

    async def check_image_exists(self, file_name: str) -> bool:
        """
        检查图片是否已经存在于R2存储中

        :param file_name: 图片文件名
        :return: 如果图片存在返回True，否则返回False
        """
        try:
            # 尝试获取图片的元数据，如果图片不存在会抛出异常
            self._cf_helper.s3_client.head_object(
                Bucket=self._cf_helper.r2_bucket,
                Key=file_name
            )
            return True
        except Exception:
            return False

    async def upload_thumbnail_to_r2(self, thumbnail_url: str) -> str:
        try:
            # 下载缩略图
            timeout = httpx.Timeout(30.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(thumbnail_url)
                response.raise_for_status()
                image_content = response.content

            # 生成唯一的文件名
            file_extension = thumbnail_url.split('.')[-1] if '.' in thumbnail_url else 'jpg'
            file_name = f"video_thumbnails/{uuid.uuid4().hex}.{file_extension}"

            # 确定内容类型
            content_type = "image/jpeg"
            if file_extension.lower() in ['png']:
                content_type = "image/png"
            elif file_extension.lower() in ['gif']:
                content_type = "image/gif"
            elif file_extension.lower() in ['webp']:
                content_type = "image/webp"

            # 上传到 R2
            r2_url = await self._cf_helper.upload_image_to_r2(image_content, file_name, content_type)
            if r2_url:  # success
                return f"{self.r2_images_memefans_url}/{file_name}"
            return ""

        except Exception as e:
            logger.error(f"Failed to upload thumbnail to R2: {str(e)}")
            raise
