import asyncio
import json
from typing import List
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session_context
from src.database.models import Video
from src.media_core.logger import logger
from src.media_core.services import CloudflareService
from src.media_core.dependencies import get_cloudflare_service, get_cloudflare_repo, get_cloudflare_helper
from src.posts.videos.schemas import ProcessingStatus


class VideoPollingService:
    """视频状态轮询服务"""
    
    def __init__(self, poll_interval: int = 10):
        """
        初始化轮询服务
        
        :param poll_interval: 轮询间隔（秒），默认60秒
        """
        self.poll_interval = poll_interval
        self.batch_size = 10  # 批处理大小
        self.max_retries = 3  # 最大重试次数
        self.timeout = 10     # 超时时间（秒）
        self.is_running = False
        self._task = None
        
    async def start(self):
        """启动轮询服务"""
        if self.is_running:
            logger.warning("轮询服务已经在运行中")
            return
            
        self.is_running = True
        self._task = asyncio.create_task(self._polling_loop())
        logger.info(f"视频状态轮询服务已启动，轮询间隔: {self.poll_interval}秒")
        
    async def stop(self):
        """停止轮询服务"""
        if not self.is_running:
            logger.warning("轮询服务未在运行")
            return
            
        self.is_running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        logger.info("视频状态轮询服务已停止")
        
    async def _polling_loop(self):
        """轮询循环"""
        while self.is_running:
            try:
                await self._check_processing_videos()
                await asyncio.sleep(self.poll_interval)
            except asyncio.CancelledError:
                logger.info("轮询循环被取消")
                break
            except Exception as e:
                logger.error(f"轮询过程中发生错误: {str(e)}")
                # 出错后等待较短时间再重试
                await asyncio.sleep(min(self.poll_interval, 30))
                
    async def _check_processing_videos(self):
        """检查处理中的视频状态"""
        async with get_session_context("write") as session:
            try:
                # 查询所有处理中的视频
                stmt = select(Video).where(Video.processing_status == ProcessingStatus.PROCESSING)
                result = await session.execute(stmt)
                processing_videos = result.scalars().all()
                
                if not processing_videos:
                    logger.debug("没有找到处理中的视频")
                    return
                    
                logger.info(f"找到 {len(processing_videos)} 个处理中的视频，开始检查状态")
                
                # 创建Cloudflare服务实例
                cf_service = await self._get_cloudflare_service(session)
                
                # 批处理检查视频状态
                await self._process_videos_in_batches(session, processing_videos, cf_service)
                        
                await session.commit()
                logger.info("视频状态检查完成")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"检查视频状态时发生数据库错误: {str(e)}")
                raise
                
    async def _process_videos_in_batches(self, session: AsyncSession, videos: List[Video], cf_service: CloudflareService):
        """批处理检查视频状态"""
        for i in range(0, len(videos), self.batch_size):
            batch = videos[i:i + self.batch_size]
            logger.debug(f"处理批次 {i//self.batch_size + 1}，包含 {len(batch)} 个视频")
            
            # 并发处理批次内的视频
            tasks = []
            for video in batch:
                task = self._check_single_video_status_with_retry(session, video, cf_service)
                tasks.append(task)
            
            # 等待批次完成
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def _check_single_video_status_with_retry(self, session: AsyncSession, video: Video, cf_service: CloudflareService):
        """带重试机制的单个视频状态检查"""
        for attempt in range(self.max_retries):
            try:
                await asyncio.wait_for(
                    self._check_single_video_status(session, video, cf_service),
                    timeout=self.timeout
                )
                return  # 成功则返回
            except asyncio.TimeoutError:
                logger.warning(f"检查视频 {video.uid} 状态超时 (尝试 {attempt + 1}/{self.max_retries})")
            except Exception as e:
                logger.error(f"检查视频 {video.uid} 状态时发生错误 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                
            if attempt < self.max_retries - 1:
                # 等待后重试，指数退避
                wait_time = min(5 * (2 ** attempt), 30)
                await asyncio.sleep(wait_time)
        
        logger.error(f"检查视频 {video.uid} 状态失败，已重试 {self.max_retries} 次")
    
    async def check_video_status_manually(self, video_uid: str) -> bool:
        """手动检查单个视频状态"""
        async with get_session_context("write") as session:
            try:
                # 查询指定的视频
                stmt = select(Video).where(Video.uid == video_uid)
                result = await session.execute(stmt)
                video = result.scalar_one_or_none()
                
                if not video:
                    logger.warning(f"未找到uid为 {video_uid} 的视频")
                    return False
                    
                if video.processing_status != ProcessingStatus.PROCESSING:
                    logger.info(f"视频 {video_uid} 已不是处理中状态，当前状态: {video.processing_status}")
                    return True
                
                # 创建Cloudflare服务实例
                cf_service = await self._get_cloudflare_service(session)
                
                # 检查视频状态
                await self._check_single_video_status_with_retry(session, video, cf_service)
                await session.commit()
                
                logger.info(f"手动检查视频 {video_uid} 状态完成")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"手动检查视频 {video_uid} 状态时发生错误: {str(e)}")
                return False
                
    async def _check_single_video_status(self, session: AsyncSession, video: Video, cf_service: CloudflareService):
        """检查单个视频的状态"""
        if not video.uid:
            logger.warning(f"视频 {video.id} 没有 uid，跳过检查")
            return
            
        try:
            # 从Cloudflare获取视频详情
            video_detail = await cf_service.video_details(video.uid)
            
            if not video_detail or not video_detail.status:
                logger.warning(f"无法获取视频 {video.uid} 的状态信息")
                return
                
            state = video_detail.status.state
            logger.debug(f"视频 {video.uid} 当前状态: {state}")
            
            # 如果状态是ready，更新数据库
            if state == "ready":
                await self._update_video_to_ready(session, video, video_detail)
                logger.info(f"视频 {video.uid} 状态已更新为完成")
            elif state == "error":
                # 处理错误状态
                logger.error(f"视频 {video.uid} 处理失败")
                # 可以在这里添加错误处理逻辑
                
        except Exception as e:
            logger.error(f"检查视频 {video.uid} 状态时发生错误: {str(e)}")
            raise
            
    async def _update_video_to_ready(self, session: AsyncSession, video: Video, video_detail):
        """将视频状态更新为已完成"""
        try:
            # 构建更新数据
            update_data = {
                "processing_status": ProcessingStatus.COMPLETED
            }
                
            # 更新视频尺寸
            if hasattr(video_detail, 'input') and video_detail.input:
                if hasattr(video_detail.input, 'width') and video_detail.input.width:
                    update_data["width"] = video_detail.input.width
                if hasattr(video_detail.input, 'height') and video_detail.input.height:
                    update_data["height"] = video_detail.input.height
                    
            # 更新元数据
            metadata = {}
            if hasattr(video_detail, 'creator') and video_detail.creator:
                metadata["creator"] = video_detail.creator
            if hasattr(video_detail, 'size') and video_detail.size:
                metadata["size"] = video_detail.size
                
            if metadata:
                update_data["metainfo"] = json.dumps(metadata)

            cover_url = video_detail.get("thumbnail")
            if cover_url:
                try:
                    cf_service = await self._get_cloudflare_service(session)
                    cover_url = await cf_service.upload_thumbnail_to_r2(video_detail.get("thumbnail"))
                    logger.info(f"Successfully uploaded thumbnail to R2 cover_url: {cover_url}")
                    update_data["cover"] = cover_url
                except Exception as thumbnail_error:
                    logger.error(f"Failed to upload thumbnail to R2: {str(thumbnail_error)}")
                    # 如果上传失败，使用原始的 thumbnail URL
                
            # 执行更新
            stmt = update(Video).where(Video.uid == video.uid).values(**update_data)
            await session.execute(stmt)
            
        except Exception as e:
            logger.error(f"更新视频 {video.uid} 状态时发生错误: {str(e)}")
            raise
            
    async def _get_cloudflare_service(self, session: AsyncSession) -> CloudflareService:
        """获取Cloudflare服务实例"""
        # 这里需要手动创建依赖项
        from src.media_core.repos import CloudflareRepo
        from src.media_core.cloudflare_helper import CloudflareHelper
        
        cf_helper = CloudflareHelper()
        cf_repo = CloudflareRepo(session)
        return CloudflareService(cf_repo, cf_helper)


# 全局轮询服务实例
video_polling_service = VideoPollingService() 