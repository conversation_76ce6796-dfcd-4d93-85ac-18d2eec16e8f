from decimal import Decimal

from pydantic_settings import BaseSettings, SettingsConfigDict

from src.common.constants import Environment


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")

    IM_CHAT_URL: str = None
    IM_SERVER_URL: str = None
    IM_CHAT_FATHER_PASSWORD: str = None
    IM_X_TOKEN: str = None

    IM_ADMIN_SECRET: str = None
    IM_ADMIN_USER_ID: str = None

settings = Settings()
