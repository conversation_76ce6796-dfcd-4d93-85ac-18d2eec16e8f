import logging
import json
import os

from fastapi.params import Depends
import httpx
from uuid import uuid4
from typing import Any, List, Dict

from fastapi import status
from fastapi.exceptions import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

from src.database.session import get_session
from src.authors.service import AuthorService
from .logger import logger

from .schemas import RegisterResponse, LoginResponse, TociImBase, CreateGroupRequest, CreateGroupResponse, \
    UpdateUserInfoRequest, UpdateUserInfoResponse
from .constants import EMessageType
from .settings import settings
from ..common.caching.caching_client import CachingClient, CachingClientGetter
from ..database.models import Collection, Author, User, Pair
from ..database.models.TociIm import TociIm
from ..database.models.ImReport import ImReport
from ..database.schemas import NotificationType
from ..notifications.push import get_push_service, PushService
from ..posts.collections.service import CollectionService


class TociImService:
    login_info: LoginResponse | None = None

    @property
    def model(self):
        return TociIm

    def __init__(self, session: AsyncSession, caching_client: CachingClient, push_service: PushService):
        self.caching_client = caching_client
        self.session = session
        self.push_service = push_service
        # 初始化admin配置
        self.admin_secret = os.environ.get("IM_ADMIN_SECRET")
        self.admin_user_id = os.environ.get("IM_ADMIN_USER_ID")

    async def create(self, create_data):
        if hasattr(create_data, 'dict'):
            data_dict = create_data.dict()
        elif hasattr(create_data, 'model_dump'):
            data_dict = create_data.model_dump()
        else:
            data_dict = vars(create_data)

        model = TociIm(**data_dict)
        self.session.add(model)
        await self.session.commit()
        await self.session.refresh(model)
        return model

    async def get(self, id: str):
        return await self.session.get(TociIm, id)

    async def get_admin_token(self) -> str:
        """
        获取管理员令牌（带Redis缓存）

        Returns:
            str: 管理员令牌
        """
        cache_key = f"openim:admin_token:{self.admin_user_id}"

        if self.caching_client:
            cached_token = await self.caching_client.client.get(cache_key)
            if cached_token:
                return cached_token

        # 缓存中没有，请求新的令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.IM_SERVER_URL}/auth/get_admin_token",
                headers={
                    "operationID": str(uuid4())
                },
                json={
                    "secret": self.admin_secret,
                    "userID": self.admin_user_id
                }
            )

        data = response.json()['data']

        token = data['token']
        expire_time_seconds = int(data['expireTimeSeconds'])

        if self.caching_client:
            # 设置缓存，提前10分钟过期以避免令牌失效
            cache_ttl = max(expire_time_seconds - 600, 0)
            await self.caching_client.client.set(cache_key, token, cache_ttl)

        return token

    async def create_group(self, request: CreateGroupRequest) -> CreateGroupResponse:
        """
        创建群组

        Args:
            request: 创建群组的请求数据

        Returns:
            CreateGroupResponse: 创建群组的响应
        """
        try:
            # 获取管理员令牌
            admin_token = await self.get_admin_token()

            # 如果没有指定groupID，生成一个
            if not request.groupInfo.groupID:
                request.groupInfo.groupID = str(uuid4()).replace("-", "_")

            # 构建请求数据
            data = request.model_dump()

            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.post(
                    f"{settings.IM_SERVER_URL}/group/create_group",
                    headers={
                        "operationID": str(uuid4()),
                        "token": admin_token
                    },
                    json=data
                )

            result = response.json()
            logger.debug(result)
            return CreateGroupResponse(
                errCode=result.get('errCode', 0),
                errMsg=result.get('errMsg', ''),
                errDlt=result.get('errDlt', ''),
                data=result.get('data')
            )

        except Exception as e:
            logger.error(f"create_group error: {e}")
            return CreateGroupResponse(
                errCode=500,
                errMsg="Internal server error",
                errDlt=str(e),
                data=None
            )

    async def get_login_info(self) -> LoginResponse:
        admin_token = await self.get_admin_token()
        return LoginResponse(
            im_token=admin_token,
            chat_token=admin_token,
            id=self.admin_user_id,
        )

    async def get_im_by_toci(self, toci_id: str) -> str | None:
        stmt = select(TociIm).where(TociIm.toci_id == toci_id)
        result = await self.session.execute(stmt)
        toci_im = result.scalar_one_or_none()
        if toci_im is None:
            return None
        return toci_im.im_id

    async def get_toci_by_im(self, im_id: str) -> str | None:
        stmt = select(TociIm).where(TociIm.im_id == im_id)
        result = await self.session.execute(stmt)
        toci_im = result.scalar_one_or_none()
        if toci_im is None:
            return None
        return toci_im.toci_id

    async def get_im_by_toci_batch(self, toci_ids: List[str]) -> dict[str, str]:
        stmt = select(TociIm).where(TociIm.toci_id.in_(toci_ids))
        result = await self.session.execute(stmt)
        toci_ims = result.scalars().all()

        ids_dict = {}
        for toci_im in toci_ims:
            ids_dict[toci_im.toci_id] = toci_im.im_id

        return ids_dict

    async def register(self, toci_id: str, username: str = None, avatar_url: str = "") -> RegisterResponse:
        login = await self.generate_login()
        password = self.generate_password()

        nickname = username if username else login

        async with httpx.AsyncClient() as client:
            im_response = await client.post(
                f"{settings.IM_CHAT_URL}/account/register",
                headers={
                    "operationID": str(uuid4())
                },
                json={
                    "autoLogin": True,
                    "platform": 1,
                    "user": {
                        "areaCode": "+86",
                        "faceURL": avatar_url,
                        "nickname": nickname,
                        "password": password,
                        "phoneNumber": login
                    },
                    "verifyCode": "666666"
                }
            )

        data = im_response.json()['data']

        im_schema = TociImBase(
            toci_id=toci_id,
            im_id=data['userID'],
            im_login=login,
            im_password=password
        )
        await self.create(im_schema)

        return RegisterResponse(
            id=data['userID'],
            login=login,
            password=password
        )

    async def update_user_info_ex(self, request: UpdateUserInfoRequest) -> UpdateUserInfoResponse:
        """
        更新用户信息（头像、名称、扩展字段）。只传递需要更新的字段，允许空字符串。

        Args:
            request (UpdateUserInfoRequest): 包含 userInfo 的更新请求

        Returns:
            UpdateUserInfoResponse: OpenIM 标准响应
        """
        try:
            admin_token = await self.get_admin_token()

            # 仅包含非 None 的字段；空字符串应保留
            payload_user = {"userID": request.userInfo.userID}
            if request.userInfo.nickname is not None:
                payload_user["nickname"] = request.userInfo.nickname
            if request.userInfo.faceURL is not None:
                payload_user["faceURL"] = request.userInfo.faceURL
            if request.userInfo.ex is not None:
                payload_user["ex"] = request.userInfo.ex

            async with httpx.AsyncClient(timeout=3) as client:
                response = await client.post(
                    f"{settings.IM_SERVER_URL}/user/update_user_info_ex",
                    headers={
                        "operationID": str(uuid4()),
                        "token": admin_token
                    },
                    json={"userInfo": payload_user}
                )

            result = response.json()
            return UpdateUserInfoResponse(
                errCode=result.get('errCode', 500),
                errMsg=result.get('errMsg', ''),
                errDlt=result.get('errDlt', '')
            )
        except Exception as e:
            logger.error(f"update_user_info_ex error: {e}")
            return UpdateUserInfoResponse(
                errCode=500,
                errMsg="Internal server error",
                errDlt=str(e)
            )

    async def login(self, login: str, password: str) -> LoginResponse:
        async with httpx.AsyncClient() as client:
            im_response = await client.post(
                f"{settings.IM_CHAT_URL}/account/login",
                headers={
                    "operationID": str(uuid4())
                },
                json={
                    "platform": 1,
                    "areaCode": "+86",
                    "password": password,
                    "phoneNumber": login
                }
            )

        data = im_response.json()['data']

        return LoginResponse(
            im_token=data['imToken'],
            chat_token=data['chatToken'],
            id=data['userID']
        )

    async def generate_login(self) -> str:
        result = await self.session.execute(text("SELECT nextval('short_id_seq') AS next_login"))
        return str(result.scalar())

    def generate_password(self) -> str:
        return "password_123"

    async def create_im_accounts(self):
        result = await self.session.execute(text("""
            SELECT id FROM users
            LEFT JOIN toci_im ti on users.id = ti.toci_id
            WHERE ti.im_id IS NULL
        """))
        rows = result.all()

        for row in rows:
            try:
                await self.register(str(row.id))
            except Exception:
                logger.log(logging.ERROR, 'OpenIM create_account error')

    async def get_collections_ids_without_chat(self):
        chats = await self.get_collections_chats()
        result = await self.session.execute(text("SELECT id FROM collections"))
        rows = result.all()

        diff: List[str] = []

        for row in rows:
            exists = False
            for chat in chats:
                if self.prepate_toci_id_for_im(row.id) == chat['groupID']:
                    exists = True
                    break
            if not exists:
                diff.append(row.id)

        return diff

    async def get_collections_chats(self):
        login_info = await self.get_login_info()
        im_token = login_info.im_token
        owner_id = login_info.id

        async with httpx.AsyncClient() as client:
            resp = await client.post(
                url=f'{settings.IM_SERVER_URL}/conversation/get_all_conversations',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "ownerUserID": owner_id
                }
            )

        resp_data = resp.json()

        if resp_data['errCode'] != 0:
            logger.error("get_collections_chats error")
            logger.error(f"{resp_data}")
            return []

        all_chats = resp_data['data']['conversations']

        if all_chats is None:
            return []

        return list(filter(lambda chat: (chat['groupID'] != ''), all_chats))

    # this is temporary method
    async def remove_grops_chats_with_broken_id(self):
        chats = await self.get_collections_chats()
        for chat in chats:
            if "-" in chat['groupID']:
                logger.info(f"found group chat with broken ID ({chat['groupID']}) - deleting")
                await self.remove_group_chat(chat['groupID'])
        return

    # this is temporary method
    async def remove_group_chat(self, chat_id: str):
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        async with httpx.AsyncClient() as client:
            resp_dismiss = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/dismiss_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id
                }
            )

        resp_dismiss_data = resp_dismiss.json()

        if resp_dismiss_data['errCode'] != 0:
            if resp_dismiss_data['errCode'] == 1004:
                logger.info(f"remove_group_chat ({chat_id}) - already deleted")
                return
            else:
                logger.error("remove_group_chat error")
                logger.error(f"{resp_dismiss_data}")

        async with httpx.AsyncClient() as client:
            resp_quit = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/quit_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id
                }
            )

        resp_quit_data = resp_quit.json()

        if resp_quit_data['errCode'] != 0:
            logger.error("remove_group_chat error")
            logger.error(f"{resp_quit_data}")

        return

    async def get_collection_contributors_ids(self, collection_id: str) -> List[str]:
        collection_service = CollectionService(self.session)

        collection: Collection = await collection_service.get(collection_id)
        contributors: List[Author] = collection.contributors

        toci_users_ids = list(map(lambda author: author.id, contributors))

        stmt = select(TociIm).where(TociIm.toci_id.in_(toci_users_ids))
        result = await self.session.execute(stmt)
        im_users: List[TociIm] = list(result.scalars().all())

        return list(map(lambda user: user.im_id, im_users))

    async def get_collection_author_id(self, collection_id: str) -> str:
        collection_service = CollectionService(self.session)

        collection: Collection = await collection_service.get(collection_id)
        author: Author = collection.author

        return author.id

    async def create_collection_chat(self, collection_id: str, user_ids: List[str]):
        login_info = await self.get_login_info()
        im_token = login_info.im_token
        owner_id = login_info.id

        data = {
            "memberUserIDs": user_ids,
            "ownerUserID": owner_id,
            "groupInfo": {
                "groupID": self.prepate_toci_id_for_im(collection_id),
                "groupName": "collection_chat",
                "notification": "notification",
                "introduction": "introduction",
                "faceURL": "",
                "ex": "ex",
                "groupType": 2,
                "needVerification": 0,
                "lookMemberInfo": 0,
                "applyMemberFriend": 0
            }
        }

        async with httpx.AsyncClient() as client:
            resp = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/create_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json=data
            )

        return resp.json()['data']

    async def create_chat(self, title: str, admin: str, user_ids: List[str], avatar_url=""):
        login_info = await self.get_login_info()
        im_token = login_info.im_token
        owner_id = login_info.id

        group_name = json.dumps({
            "type": "chat",
            "title": title
        })

        data = {
            "memberUserIDs": user_ids,
            "ownerUserID": owner_id,
            "adminUserIDs": [admin],
            "groupInfo": {
                "groupID": str(uuid4()).replace("-", "_"),
                "groupName": group_name,
                "notification": "notification",
                "introduction": "introduction",
                "faceURL": avatar_url,
                "ex": "ex",
                "groupType": 2,
                "needVerification": 0,
                "lookMemberInfo": 0,
                "applyMemberFriend": 0
            }
        }

        async with httpx.AsyncClient() as client:
            res = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/create_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json=data
            )

        result = res.json()
        if result['errCode'] != 0:
            logger.log(logging.ERROR, f"{result['errMsg']} - {result['errDlt']}")
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR)

        return res.json()['data']

    async def join_chat(self, chat_id: str, user_id: str):
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        async with httpx.AsyncClient() as client:
            await client.post(
                url=f'{settings.IM_SERVER_URL}/group/invite_user_to_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id,
                    "reason": "reason",
                    "invitedUserIDs": [user_id]
                }
            )

    async def join_collection_chat(self, collection_id: str, user_id):
        try:
            chat_id = self.prepate_toci_id_for_im(collection_id)
            im_user_id = await self.get_im_by_toci(user_id)
            await self.join_chat(
                chat_id=chat_id,
                user_id=im_user_id
            )
        except Exception as e:
            logger.error(f"join_collection_chat: {e}")

    async def invite_to_chat(self, chat_id: str, users_ids: List[int]):
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        if len(users_ids) == 0:
            return

        async with httpx.AsyncClient() as client:
            await client.post(
                url=f'{settings.IM_SERVER_URL}/group/invite_user_to_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id,
                    "reason": "reason",
                    "invitedUserIDs": users_ids
                }
            )

        return

    async def kick_user_from_chat(self, chat_id: str, user_id: str):
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        async with httpx.AsyncClient() as client:
            await client.post(
                url=f'{settings.IM_SERVER_URL}/group/kick_group',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id,
                    "reason": "reason",
                    "kickedUserIDs": [user_id]
                }
            )

        return

    async def get_chats_info(self, chats_ids: List[str]) -> List[Any]:
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        async with httpx.AsyncClient() as client:
            res = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/get_groups_info',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupIDs": chats_ids
                }
            )

        return res.json()['data']['groupInfos']

    async def get_chat_type(self, chat_id: str):
        chat_info = (await self.get_chats_info([chat_id]))[0]

        if chat_info['groupName'] == "collection_chat":
            return "collection_chat"

        try:
            group_meta = json.loads(chat_info['groupName'])
            if group_meta['type'] == "chat":
                return "default_chat"
        except Exception as e:
            logger.error(f"get_chat_type: {e}")

        return "user_chat"

    async def get_chat_members(self, chat_id: str):
        login_info = await self.get_login_info()
        im_token = login_info.im_token

        async with httpx.AsyncClient() as client:
            res = await client.post(
                url=f'{settings.IM_SERVER_URL}/group/get_group_member_list',
                headers={
                    "token": im_token,
                    "operationID": str(uuid4())
                },
                json={
                    "groupID": chat_id,
                    "pagination": {
                        "pageNumber": 1,
                        "showNumber": 100
                    }
                }
            )

        users: List[Any] = res.json()['data']['members']
        return list(filter(lambda user: (user['nickname'] != '999999'), users))

    def prepate_toci_id_for_im(self, id: str) -> str:
        return id.replace("-", "_r_")

    async def chat_init(self):
        await self.create_im_accounts()

        await self.remove_grops_chats_with_broken_id()
        ids = await self.get_collections_ids_without_chat()

        for id in ids:
            author_id = await self.get_collection_author_id(id)
            im_author_id = await self.get_im_by_toci(author_id)
            await self.create_collection_chat(
                collection_id=id,
                user_ids=[im_author_id]
            )

    # Starting from a queue
    async def send_push_notification(self, data: Any):
        try:
            if data['contentType'] != EMessageType.text and data['contentType'] != EMessageType.picture:
                logger.info("[Im] send_push_notification: unsupported content")
                logger.info(f"[Im] send_push_notification (contentType): {data['contentType']}")
                return

            author_service: AuthorService = AuthorService(self.session)

            recipient_id = await self.get_toci_by_im(data['recvID'])
            sender_id = await self.get_toci_by_im(data['sendID'])

            sender: Author = await author_service.get(sender_id)

            message = json.loads(data['content'])
            contents = {}

            if data['contentType'] == EMessageType.text:
                contents = {
                    "en": message['content'],
                    "ru": message['content'],
                }

            if data['contentType'] == EMessageType.picture:
                contents = {
                    "en": "Sent you an image 🖼",
                    "ru": "Прислал вам изображение 🖼",
                }

            headings = {
                "en": sender.name,
                "ru": sender.name
            }

            custom = {
                "type": NotificationType.new_message,
                "message": data
            }

            res = await self.push_service.send([recipient_id], contents, headings, custom)

            logger.info(f"[Im] onesignal.send (headings): {headings}")
            logger.info(f"[Im] onesignal.send (content): {contents}")
            logger.info(f"[Im] onesignal.send (custom): {custom}")
            logger.info(f"[Im] onesignal.send (res): {res}")
        except Exception as e:
            logger.error(f"[Im] send_push_notification (error): {e}")

    async def create_im_report(self, reporter_id: str, reported_user_id: str,
                               conversation_id: str, chat_type: str,
                               message_content: str, reason: str,
                               comment: str = None) -> ImReport:
        """
        创建IM举报记录

        Args:
            reporter_id: 举报者ID
            reported_user_id: 被举报者ID
            conversation_id: 会话ID
            chat_type: 聊天类型（私聊/群聊）
            message_content: 举报的消息内容
            reason: 举报原因
            comment: 举报者备注

        Returns:
            ImReport: 创建的举报记录
        """
        try:
            # 检查是否已经举报过相同的内容
            existing_report = await self.session.execute(
                select(ImReport).where(
                    ImReport.reporter_id == reporter_id,
                    ImReport.reported_user_id == reported_user_id,
                    ImReport.conversation_id == conversation_id,
                    ImReport.message_content == message_content
                )
            )

            if existing_report.scalar_one_or_none() is not None:
                logger.info(f"[Im] create_im_report: duplicate report from {reporter_id}")
                raise ValueError("You have already reported this content")

            # 创建举报记录
            report = ImReport(
                reporter_id=reporter_id,
                reported_user_id=reported_user_id,
                conversation_id=conversation_id,
                chat_type=chat_type,
                message_content=message_content,
                reason=reason,
                comment=comment
            )

            self.session.add(report)
            await self.session.commit()
            await self.session.refresh(report)

            logger.info(f"[Im] create_im_report: created report {report.id}")
            return report

        except Exception as e:
            logger.error(f"[Im] create_im_report (error): {e}")
            await self.session.rollback()
            raise e

    async def get_im_group_info(self, group_id: str) -> Dict[str, Any]:
        try:
            collection_id = group_id
            stmt = select(Collection).where(Collection.id == collection_id)
            result = await self.session.execute(stmt)
            coll = result.scalar_one_or_none()

            if coll is None:
                return {
                    "chat_join_threshold": 0,
                    "token_address": None,
                    "token_symbol": None,
                    "image_url": None,
                }

            holdview_amount = int(getattr(coll, "holdview_amount", 0) or 0)

            stmt_token = (
                select(Pair.base, Pair.base_symbol, Pair.base_image_url)
                .where(Pair.collection_id == collection_id)
                .limit(1)
            )
            token_result = await self.session.execute(stmt_token)
            row = token_result.first()

            token_address = row[0] if row else None
            token_symbol = row[1] if row else None
            base_image_url = row[2] if row else None

            return {
                "chat_join_threshold": holdview_amount,
                "token_address": token_address,
                "token_symbol": token_symbol,
                "image_url": base_image_url,
            }
        except Exception as e:
            logger.error(f"get_im_group_info error: {e}")
            return {
                "chat_join_threshold": 0,
                "token_address": None,
                "token_symbol": None,
                "image_url": None,
            }



def get_toci_im_service(
    session: AsyncSession = Depends(get_session),
    caching_client: CachingClient = Depends(CachingClientGetter(logger)),
    push_service: PushService = Depends(get_push_service)
) -> TociImService:
    return TociImService(session, caching_client, push_service)
