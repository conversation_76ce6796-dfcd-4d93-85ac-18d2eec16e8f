from fastapi import <PERSON><PERSON><PERSON>, Depends, status, Request, HTTPException
from fastapi.openapi.utils import get_openapi
from src.infra.app import create_app
from src.infra.logger import get_logger

from src.auth import current_user
from src.common.dependencies import get_im_service
from src.common.im_service import ImService
from src.database.models import User
logger = get_logger("im", level="INFO", file_path="latest.log")
from src.im.schemas import NewMessageRequest, CreateGroupRequest, CreateGroupResponse, ImReportRequest, \
    ImReportResponse, GroupReqResponse, UpdateUserInfoRequest, UpdateUserInfoResponse
from src.im.service import get_toci_im_service, TociImService
from src.im.settings import settings

app = create_app(title="IM", description="All endpoints related to instant-messaging.", version="0.1", request_logger=logger)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="IM Service",
        version="1.0.1",
        description="IM Service API",
        routes=app.routes,
    )

    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }

    openapi_schema["security"] = [{"BearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}


@app.get("/by_im_batch", summary="Get IMs by batch of ids.", tags=["IM"])
async def get(
    im_ids: str,
    im_service: ImService = Depends(get_im_service),
):
    ids_dict = await im_service.get_by_im_batch(
        im_ids,
    )

    return ids_dict


@app.get("/by_im", summary="Get TociID by IMID", tags=["IM"])
async def get_toci_by_im_id(
    im_id: str,
    im_service: TociImService = Depends(get_toci_im_service),
):
    toci_id = await im_service.get_toci_by_im(im_id)

    if toci_id is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {"id": toci_id}


@app.get("/by_toci", summary="Get IMID by TociID", tags=["IM"])
async def get_im_by_toci_id(
    toci_id: str,
    im_service: TociImService = Depends(get_toci_im_service),
):
    im_id = await im_service.get_im_by_toci(toci_id)

    if im_id is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {"id": im_id}


@app.get("/by_toci_batch", summary="Get IMIDs by batch of TociIDs", tags=["IM"])
async def get_im_by_toci_batch(
    toci_ids: str,
    im_service: TociImService = Depends(get_toci_im_service),
):
    toci_id_list = toci_ids.split(",")[:50]
    ids_dict = await im_service.get_im_by_toci_batch(toci_id_list)

    return ids_dict


@app.post("/im/new_message", status_code=status.HTTP_204_NO_CONTENT)
async def new_message(
        message: NewMessageRequest,
        request: Request,
        im_service: TociImService = Depends(get_toci_im_service),
):
    if (request.headers.get("X-TOKEN") != settings.IM_X_TOKEN):
        request_val = request.headers.get("X-TOKEN")
        config_val = settings.IM_X_TOKEN

        logger.error("X-TOKEN is invalid")
        logger.error(f"X-TOKEN: value in request - {request_val}")
        logger.error(f"X-TOKEN: value in config - {config_val}")

        return

    data = message.dict()
    if data['recvID'] == "":
        return

    await im_service.send_push_notification(data)
    logger.info("Sent to Queue")

    return


@app.post("/create_group", summary="Create a new group", tags=["IM"], response_model=CreateGroupResponse)
async def create_group(
    request: CreateGroupRequest,
    user: User = Depends(current_user),
    im_service: TociImService = Depends(get_toci_im_service),
):
    """
    创建群组

    根据提供的群组信息和成员列表创建一个新的群组。
    需要用户认证，只有认证用户才能创建群组。
    """
    result = await im_service.create_group(request)

    if result.errCode != 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "errCode": result.errCode,
                "errMsg": result.errMsg,
                "errDlt": result.errDlt
            }
        )

    return result


@app.post("/report", summary="举报IM消息 (已弃用)", tags=["IM"], response_model=ImReportResponse, deprecated=True)
async def report_im_message(
    request: ImReportRequest,
    user: User = Depends(current_user),
    im_service: TociImService = Depends(get_toci_im_service),
):
    """
    举报IM消息 (已弃用)

    ⚠️ **此接口已弃用** - 请使用统一的举报接口 `POST /reports/` 替代。该接口将在未来版本中移除。

    用户可以举报私聊或群聊中的不当言论。
    系统会记录举报信息并防止重复举报。
    """
    try:
        # 创建举报记录
        report = await im_service.create_im_report(
            reporter_id=user.id,
            reported_user_id=request.reported_user_id,
            conversation_id=request.conversation_id,
            chat_type=request.chat_type,
            message_content=request.message_content,
            reason=request.reason,
            comment=request.comment
        )

        logger.info(f"[Im] report_im_message: created report {report.id} by user {user.id}")

        return ImReportResponse(
            success=True,
            message="Report submitted successfully, we will process it as soon as possible",
            report_id=report.id
        )

    except ValueError as e:
        # 处理已存在的举报
        logger.warning(f"[Im] report_im_message: duplicate report by user {user.id}")
        return ImReportResponse(
            success=False,
            message=str(e)
        )

    except Exception as e:
        logger.error(f"[Im] report_im_message: error - {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit report, please try again later"
        )

@app.get("/get_group_info",
         response_model=GroupReqResponse,
         summary="Get group info",
         tags=["IM"])
async def get_im_group_info(
    group_id: str,
    im_service: TociImService = Depends(get_toci_im_service),
):
    data = await im_service.get_im_group_info(group_id)
    return data


@app.post("/user/update_user_info_ex", summary="更新用户信息（OpenIM 代理）", tags=["IM"], response_model=UpdateUserInfoResponse)
async def update_user_info_ex(
    request: UpdateUserInfoRequest,
    im_service: TociImService = Depends(get_toci_im_service),
):
    """
    更新用户信息：头像、昵称、扩展字段（ex）。
    - 仅需提供需要修改的字段（包括可为空字符串）。
    - 该接口代理调用 OpenIM `/user/update_user_info_ex`。
    """
    result = await im_service.update_user_info_ex(request)

    if result.errCode != 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "errCode": result.errCode,
                "errMsg": result.errMsg,
                "errDlt": result.errDlt,
            }
        )

    return result