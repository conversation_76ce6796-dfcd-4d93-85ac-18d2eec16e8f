from typing import List, Optional
from pydantic import BaseModel
from src.reports.enums import ReportReason
from src.database.models.ImReport import ChatType

class TociImBase(BaseModel):
    toci_id: str
    im_id: str
    im_login: str
    im_password: str

class LoginResponse:
    def __init__(self, im_token: str, chat_token: str, id: str):
        self.im_token = im_token
        self.chat_token = chat_token
        self.id = id

class RegisterResponse:
    def __init__(self, id: str, login: str, password: str):
        self.id = id
        self.login = login
        self.password = password

class ChatCreateRequest(BaseModel):
    avatar: str
    title: str
    admin: str
    members: List[str]

class NewMessageRequest(BaseModel):
    conversationID: str
    sendID: str
    recvID: str
    groupID: str
    contentType: str
    content: str

class GroupInfo(BaseModel):
    groupID: Optional[str] = None
    groupName: str
    notification: Optional[str] = "notification"
    introduction: Optional[str] = "introduction"
    faceURL: Optional[str] = ""
    ex: Optional[str] = "ex"
    groupType: int = 2
    needVerification: Optional[int] = 0
    lookMemberInfo: Optional[int] = 0
    applyMemberFriend: Optional[int] = 0

class CreateGroupRequest(BaseModel):
    memberUserIDs: Optional[List[str]] = []
    adminUserIDs: Optional[List[str]] = []
    ownerUserID: str
    groupInfo: GroupInfo

class GroupInfoResponse(BaseModel):
    groupID: str
    groupName: str
    notification: str
    introduction: str
    faceURL: str
    ownerUserID: str
    createTime: int
    memberCount: int
    ex: str
    status: int
    creatorUserID: str
    groupType: int
    needVerification: int
    lookMemberInfo: int
    applyMemberFriend: int
    notificationUpdateTime: int
    notificationUserID: str

class CreateGroupResponse(BaseModel):
    errCode: int
    errMsg: str
    errDlt: str
    data: Optional[dict] = None


class ImReportRequest(BaseModel):
    """IM举报请求模型"""
    reported_user_id: str  # 被举报者ID
    conversation_id: str  # 会话ID
    chat_type: ChatType  # 聊天类型（私聊/群聊）
    message_content: Optional[str] = None  # 举报的消息内容
    reason: ReportReason  # 举报原因
    comment: Optional[str] = None  # 举报者备注


class ImReportResponse(BaseModel):
    """IM举报响应模型"""
    success: bool
    message: str
    report_id: Optional[str] = None


class GroupReqResponse(BaseModel):
    chat_join_threshold: int = 0
    token_address: Optional[str] = None
    token_symbol: Optional[str] = None
    image_url: Optional[str] = None


# --- Update User Info (OpenIM compatible) ---
class UpdateUserInfoUser(BaseModel):
    userID: str
    nickname: Optional[str] = None
    faceURL: Optional[str] = None
    ex: Optional[str] = None


class UpdateUserInfoRequest(BaseModel):
    userInfo: UpdateUserInfoUser


class UpdateUserInfoResponse(BaseModel):
    errCode: int
    errMsg: str
    errDlt: str