"""
Celery应用实例
自动发现各模块的任务，支持RabbitMQ优先级队列
"""
from celery import Celery
from celery.schedules import crontab

from . import config
from celery.signals import worker_process_init


# 在每个 Celery worker 进程启动时初始化数据库异步引擎
@worker_process_init.connect
def _init_db_engines(**kwargs):
    # 避免在导入期初始化；仅在进程就绪时显式初始化
    from src.database.session import init_engines
    init_engines(echo=False)

# 创建Celery应用实例
celery_app = Celery('memefans_worker')

# 加载配置
celery_app.config_from_object(config)

# 自动发现任务
celery_app.autodiscover_tasks(['src.worker.tasks'])

# 配置定时任务
celery_app.conf.beat_schedule = {
    # 清理旧的搜索历史索引（如果存在的话）
    "cleanup-search-history-indices-task": {
        "task": "src.worker.tasks.search.cleanup_search_history_indices_task",
        "schedule": crontab(hour=2, minute=0, day_of_week=1),  # 每周一凌晨2点执行
        "options": {
            "priority": 3,  # 中等优先级
            "queue": "search",
        }
    },
    
    # 更新TokenCache中的历史市值字段
    "update-token-cache-historical-mc": {
        "task": "src.worker.tasks.memecoin.update_token_cache_historical_mc",
        "schedule": crontab(hour=0, minute=5),  # 每天00:05执行
        "options": {
            "priority": 4,  # 中等优先级
            "queue": "memecoin",
        }
    },
    
    # 预热排行榜缓存
    "warm-up-rankings-cache": {
        "task": "src.worker.tasks.memecoin.warm_up_rankings_cache",
        "schedule": crontab(minute="*/2"),  # 每2分钟执行
        "options": {
            "priority": 5,  # 中高优先级
            "queue": "memecoin",
        }
    },
    
    # 可以根据需要添加其他定时任务，例如：
    # "elasticsearch-health-check": {
    #     "task": "src.worker.tasks.elasticsearch_sync.check_elasticsearch_health",
    #     "schedule": crontab(minute="*/30"),
    #     "options": {"priority": 7, "queue": "elasticsearch_sync"}
    # },

    # 轮询内容购买交易状态，更新 ContentPurchase
    "poll-content-purchase-status": {
        "task": "src.worker.tasks.payments.poll_unconfirmed_purchases_task",
        "schedule": crontab(minute="*/1"),  # 每1分钟执行一次
        "options": {
            "priority": 5,
            "queue": "payments",
        }
    },
}

# 启用任务路由
celery_app.conf.task_routes = config.task_routes

# 确保队列配置生效
celery_app.conf.task_queues = config.task_queues

# 优先级队列配置
celery_app.conf.broker_transport_options = config.broker_transport_options