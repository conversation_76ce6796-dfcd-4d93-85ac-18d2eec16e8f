"""
Celery配置文件
使用RabbitMQ作为消息代理，支持优先级队列
"""
import os

# 消息代理设置 - 使用RabbitMQ
broker_url = os.getenv("CELERY_BROKER_URL", "amqp://guest:<EMAIL>:5672//")

# 结果后端 - 可以使用RabbitMQ或其他存储
result_backend = os.getenv("CELERY_RESULT_BACKEND", "rpc://")

# 序列化设置
task_serializer = "json"
accept_content = ["json"]
result_serializer = "json"

# 时区设置
timezone = "UTC"
enable_utc = True

# 任务执行设置
worker_prefetch_multiplier = 1
task_acks_late = True
task_ignore_result = False  # 保留任务结果以支持状态查询
result_expires = 3600  # 任务结果保存1小时

# 日志设置
worker_log_format = "[%(asctime)s: %(levelname)s/%(processName)s] %(message)s"
worker_task_log_format = "[%(asctime)s: %(levelname)s/%(processName)s] [%(task_name)s(%(task_id)s)] %(message)s"

# ==================== 优先级队列配置 ====================

# RabbitMQ传输选项 - 支持优先级队列
broker_transport_options = {
    'priority_steps': list(range(10)),  # 支持0-9优先级（数字越小优先级越高）
    'queue_order_strategy': 'priority',  # 按优先级排序队列
    'master_name': 'memefans_celery',
    'visibility_timeout': 3600,  # 消息可见性超时
}

# 任务路由 - 将不同类型的任务分配到不同队列
task_routes = {
    # Elasticsearch同步任务 - 专用队列，支持优先级
    'src.worker.tasks.elasticsearch_sync.sync_*': {
        'queue': 'elasticsearch_sync',
        'routing_key': 'elasticsearch_sync',
    },
    'src.worker.tasks.elasticsearch_sync.queue_*': {
        'queue': 'elasticsearch_queue',  # 队列化同步任务使用专门的队列
        'routing_key': 'elasticsearch_queue',
    },
    'src.worker.tasks.elasticsearch_sync.bulk_*': {
        'queue': 'elasticsearch_bulk',
        'routing_key': 'elasticsearch_bulk',
    },
    'src.worker.tasks.elasticsearch_sync.batch_*': {
        'queue': 'elasticsearch_batch',
        'routing_key': 'elasticsearch_batch',
    },
    
    # 搜索相关任务
    'src.worker.tasks.search.*': {
        'queue': 'search',
        'routing_key': 'search',
    },
    
    # 代币相关任务
    'src.worker.tasks.memecoin.*': {
        'queue': 'memecoin',
        'routing_key': 'memecoin',
    },
    
    # Collection相关任务
    'src.worker.tasks.collection.*': {
        'queue': 'collection',
        'routing_key': 'collection',
    },
    
    # Reports相关任务
    'src.worker.tasks.reports.*': {
        'queue': 'reports',
        'routing_key': 'reports',
    },
    
    # Payments相关任务
    'src.worker.tasks.payments.*': {
        'queue': 'payments',
        'routing_key': 'payments',
    },
    
    # 默认队列
    'src.worker.tasks.*': {
        'queue': 'default',
        'routing_key': 'default',
    },
}

# 队列配置 - 为不同队列设置优先级支持
task_create_missing_queues = True
task_default_queue = 'default'
task_default_exchange = 'memefans_tasks'
task_default_exchange_type = 'direct'
task_default_routing_key = 'default'

# 队列定义 - 明确定义队列及其属性
task_queues = {
    # Elasticsearch相关队列
    'elasticsearch_sync': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'elasticsearch_sync',
        'queue_arguments': {'x-max-priority': 9},  # 支持优先级0-9
    },
    'elasticsearch_queue': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'elasticsearch_queue',
        'queue_arguments': {'x-max-priority': 9},  # 支持优先级0-9
    },
    'elasticsearch_bulk': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'elasticsearch_bulk',
        'queue_arguments': {'x-max-priority': 5},  # 批量任务优先级较低
    },
    'elasticsearch_batch': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'elasticsearch_batch',
        'queue_arguments': {'x-max-priority': 5},
    },
    
    # 其他业务队列
    'search': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'search',
        'queue_arguments': {'x-max-priority': 5},
    },
    'memecoin': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'memecoin',
        'queue_arguments': {'x-max-priority': 5},
    },
    'collection': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'collection',
        'queue_arguments': {'x-max-priority': 5},
    },
    'reports': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'reports',
        'queue_arguments': {'x-max-priority': 5},
    },
    'payments': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'payments',
        'queue_arguments': {'x-max-priority': 6},  # 支付任务较高优先级
    },
    
    # 默认队列
    'default': {
        'exchange': 'memefans_tasks',
        'exchange_type': 'direct',
        'routing_key': 'default',
        'queue_arguments': {'x-max-priority': 3},
    },
}

# ==================== 性能优化配置 ====================

# Worker并发设置
worker_concurrency = os.getenv("CELERY_WORKER_CONCURRENCY", 8)  # 工作进程数
worker_max_tasks_per_child = 1000  # 每个工作进程最大任务数，防止内存泄漏

# 任务批处理设置（用于批量任务优化）
task_always_eager = False  # 生产环境关闭，测试环境可开启
task_eager_propagates = True
task_store_eager_result = True

# 连接池设置
broker_pool_limit = 10  # 连接池大小
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10

# ==================== 监控和日志配置 ====================

# 任务事件监控
worker_send_task_events = True
task_send_sent_event = True

# 任务超时设置
task_soft_time_limit = 60 * 30  # 30分钟软超时
task_time_limit = 60 * 35  # 35分钟硬超时

# 任务重试配置
task_annotations = {
    # Elasticsearch同步任务重试配置
    'src.worker.tasks.elasticsearch_sync.*': {
        'rate_limit': '100/m',  # 每分钟最多100个任务
        'time_limit': 300,  # 5分钟超时
        'soft_time_limit': 240,  # 4分钟软超时
    },
    
    # 搜索任务配置
    'src.worker.tasks.search.*': {
        'rate_limit': '200/m',
        'time_limit': 180,
        'soft_time_limit': 150,
    },
}

# ==================== 开发环境特殊配置 ====================

# 如果是开发环境，启用更详细的日志
if os.getenv("ENVIRONMENT") == "development":
    worker_log_level = "DEBUG"
    worker_hijack_root_logger = False
    worker_log_color = True
    
    # 开发环境可以降低并发数
    worker_concurrency = 4
    
    # 开发环境使用内存结果存储
    result_backend = "cache+memcached://127.0.0.1:11211/"