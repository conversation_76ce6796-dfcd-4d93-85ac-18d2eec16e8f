from typing import Optional
import asyncio

from src.worker.celery_app import celery_app
from src.database.session import get_sync_context
from src.database.models.UserTransaction import UserTransaction
from src.database.models.ContentPurchase import ContentPurchase
from src.database.models.Post import Post
from src.database.models.Author import Author
from src.database.models.UserWallet import UserWallet
from src.common.constants import (
    UserTransactionType,
    UserTransactionStatus,
    ContentPurchaseStatus,
)

# memecoin service dependencies
from src.memecoin.dependencies import meme_service_context
from src.memecoin.settings import settings
from src.worker.logger import logger
from src.common.redis_cli import RedisCli


@celery_app.task(bind=True, name="src.worker.tasks.payments.submit_content_purchase_task", max_retries=3, default_retry_delay=60)
def submit_content_purchase_task(self,
    purchase_id: str,
    user_id: str,
    post_id: str,
    token_address: str,
    usd_amount_cents: int,
    tx_type: UserTransactionType
):
    """
    Create UserTransaction for content purchase and set ContentPurchase to PROCESSING.
    Optionally accept a client-provided tx_hash.
    """
    logger.info(f"Starting content purchase task - purchase_id: {purchase_id}, user_id: {user_id}, post_id: {post_id}")
    
    try:
        with get_sync_context() as session:
            # Load purchase
            purchase: ContentPurchase = session.get(ContentPurchase, purchase_id)
            if not purchase:
                logger.error(f"Purchase not found - purchase_id: {purchase_id}")
                return {"success": False, "error": "Purchase not found"}

            # Check if purchase is already in PROCESSING
            if purchase.status == ContentPurchaseStatus.PROCESSING or purchase.status == ContentPurchaseStatus.CONFIRMED:
                logger.info(f"Purchase already processed - purchase_id: {purchase_id}, status: {purchase.status}")
                return {"success": True, "status": purchase.status, "message": "Already processed"}
            
            # Resolve author wallet (recipient)
            post: Optional[Post] = session.get(Post, post_id)
            if not post:
                logger.error(f"Post not found - post_id: {post_id}")
                return {"success": False, "error": "Post not found"}
            
            author: Optional[Author] = session.get(Author, post.author_id)
            if not author:
                logger.error(f"Author not found - author_id: {post.author_id}")
                return {"success": False, "error": "Author not found"}
            
            # 按当前链类型筛选作者的钱包
            chain_wallet_type = settings.BLOCKCHAIN_TYPE.lower()
            recipient_wallet: Optional[UserWallet] = (
                session.query(UserWallet)
                .filter(UserWallet.user_id == author.user_id, UserWallet.type == chain_wallet_type)
                .first()
            )
            if not recipient_wallet:
                logger.error(f"Author wallet not found - user_id: {author.user_id}, wallet_type: bsc")
                return {"success": False, "error": "Author wallet not found"}

            to_address = recipient_wallet.pubkey
            logger.debug(f"Found recipient wallet - to_address: {to_address}")

            # 使用 meme_service 的一体化 purchase 完成支付
            logger.info(f"Starting purchase processing - purchase_id: {purchase_id}")

            async def _purchase_async() -> Optional[str]:
                try:
                    async with meme_service_context(logger) as ctx:
                        meme_service = ctx.service
                        resp = await meme_service.purchase(
                            user_id=user_id,
                            token_address=token_address,
                            to_address=to_address,
                            usd_amount_cents=usd_amount_cents,
                            order_id=purchase.id,
                        )
                        return getattr(resp, "txid", None)
                except Exception as e:
                    logger.error(f"Purchase processing failed - purchase_id: {purchase_id}, error: {str(e)}")
                    logger.exception("Purchase processing exception:")
                    raise

            tx_hash = asyncio.run(_purchase_async())
            logger.info(f"Purchase processing completed - tx_hash: {tx_hash}")
            if not tx_hash:
                raise Exception(f"Payment processing failed - tx_hash: {tx_hash}")

            # Create UserTransaction
            user_tx = UserTransaction(
                user_id=user_id,
                type=tx_type,
                chain=settings.BLOCKCHAIN_TYPE,
                token_address=token_address,
                base_amount=0,
                quote_amount=usd_amount_cents,
                status=UserTransactionStatus.AWAITING_CONFIRMATION,
                reason=f"Purchase content: {post_id}",
                order_id=purchase.id,
                tx_hash=tx_hash,
                from_address=user_id,
                to_address=author.id,
                payment_source=user_id,
            )
            session.add(user_tx)
            session.flush()

            # Link to purchase and advance status to PROCESSING
            purchase.user_transaction_id = user_tx.id
            purchase.status = ContentPurchaseStatus.PROCESSING
            session.add(purchase)

            # Update Redis cache to PROCESSING
            try:
                r = RedisCli.sync()
                status_str = str(ContentPurchaseStatus.PROCESSING)
                ttl = 86400
                key_user_post = f"purchase:by_user_post:{user_id}:{post_id}"
                r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                key_by_id = f"purchase:status:{purchase.id}"
                r.set_json(key_by_id, {"status": status_str, "user_id": user_id, "post_id": post_id}, ex=ttl)
            except Exception as e:
                logger.warning(f"Failed to update Redis to PROCESSING - purchase_id: {purchase_id}, error: {e}")
            
            # Transaction will be automatically committed by get_sync_context()
            logger.info(f"Database changes prepared - purchase_id: {purchase_id}, user_transaction_id: {user_tx.id}")

            # Schedule status polling task
            try:
                poll_content_purchase_status_task.apply_async(args=[purchase.id], countdown=10)
                logger.debug(f"Scheduled polling task - purchase_id: {purchase_id}")
            except Exception as e:
                logger.warning(f"Failed to schedule polling task - purchase_id: {purchase_id}, error: {str(e)}")

            return {
                "success": True, 
                "purchase_id": purchase_id, 
                "user_transaction_id": user_tx.id,
                "tx_hash": tx_hash,
                "status": "PROCESSING"
            }
            
    except Exception as e:
        logger.error(f"Content purchase task failed - purchase_id: {purchase_id}, error: {str(e)}")
        logger.exception("Task exception details:")
        # Try to mark purchase as failed
        try:
            with get_sync_context() as session:
                purchase = session.get(ContentPurchase, purchase_id)
                if purchase and purchase.status == ContentPurchaseStatus.PENDING:
                    purchase.status = ContentPurchaseStatus.FAILED
                    session.add(purchase)
                    # get_sync_context() will automatically commit
                    logger.info(f"Marked purchase as failed - purchase_id: {purchase_id}")
                    # Update Redis cache to FAILED
                    try:
                        r = RedisCli.sync()
                        status_str = str(ContentPurchaseStatus.FAILED)
                        ttl = 86400
                        key_user_post = f"purchase:by_user_post:{purchase.user_id}:{purchase.post_id}"
                        r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                        key_by_id = f"purchase:status:{purchase.id}"
                        r.set_json(key_by_id, {"status": status_str, "user_id": purchase.user_id, "post_id": purchase.post_id}, ex=ttl)
                    except Exception as e2:
                        logger.warning(f"Failed to update Redis to FAILED - purchase_id: {purchase_id}, error: {e2}")
        except Exception as cleanup_error:
            logger.error(f"Failed to mark purchase as failed - purchase_id: {purchase_id}, error: {str(cleanup_error)}")
        
        return {"success": False, "error": str(e), "purchase_id": purchase_id}


@celery_app.task(bind=True, name="src.worker.tasks.payments.poll_content_purchase_status_task", max_retries=12, default_retry_delay=5)
def poll_content_purchase_status_task(self, purchase_id: str):
    """轮询检查 UserTransaction 状态，成功则将 ContentPurchase 置为 CONFIRMED。"""
    with get_sync_context() as session:
        purchase: Optional[ContentPurchase] = session.get(ContentPurchase, purchase_id)
        if not purchase:
            return

        if purchase.status == ContentPurchaseStatus.CONFIRMED:
            return

        if not purchase.user_transaction_id:
            # 交易尚未创建，稍后重试
            raise self.retry()

        user_tx: Optional[UserTransaction] = session.get(UserTransaction, purchase.user_transaction_id)
        if not user_tx:
            # 事务未可见，重试
            raise self.retry()

        if user_tx.status == UserTransactionStatus.CONFIRMED:
            purchase.status = ContentPurchaseStatus.CONFIRMED
            session.add(purchase)
            # Update Redis to CONFIRMED
            try:
                r = RedisCli.sync()
                status_str = str(ContentPurchaseStatus.CONFIRMED)
                ttl = 86400
                key_user_post = f"purchase:by_user_post:{purchase.user_id}:{purchase.post_id}"
                r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                key_by_id = f"purchase:status:{purchase.id}"
                r.set_json(key_by_id, {"status": status_str, "user_id": purchase.user_id, "post_id": purchase.post_id}, ex=ttl)
            except Exception as e:
                logger.warning(f"Failed to update Redis to CONFIRMED - purchase_id: {purchase_id}, error: {e}")
            return

        if user_tx.status == UserTransactionStatus.AWAITING_CONFIRMATION:
            # 继续轮询
            raise self.retry()

        if user_tx.status == UserTransactionStatus.FAILED:
            # 失败则停止轮询，如需退款可在此扩展
            purchase.status = ContentPurchaseStatus.FAILED
            session.add(purchase)
            # Update Redis to FAILED
            try:
                r = RedisCli.sync()
                status_str = str(ContentPurchaseStatus.FAILED)
                ttl = 86400
                key_user_post = f"purchase:by_user_post:{purchase.user_id}:{purchase.post_id}"
                r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                key_by_id = f"purchase:status:{purchase.id}"
                r.set_json(key_by_id, {"status": status_str, "user_id": purchase.user_id, "post_id": purchase.post_id}, ex=ttl)
            except Exception as e:
                logger.warning(f"Failed to update Redis to FAILED - purchase_id: {purchase_id}, error: {e}")
            return


@celery_app.task(bind=True, name="src.worker.tasks.payments.poll_unconfirmed_purchases_task", max_retries=0)
def poll_unconfirmed_purchases_task(self):
    """批量扫描处于 PROCESSING 的 ContentPurchase，更新其状态根据关联的 UserTransaction 状态。"""
    with get_sync_context() as session:
        # 批量处理，限制一次最多500条，避免长事务
        try:
            # 查询所有 PROCESSING 状态的 ContentPurchase 及其关联的 UserTransaction
            query = (
                session.query(ContentPurchase, UserTransaction)
                .join(UserTransaction, ContentPurchase.user_transaction_id == UserTransaction.id)
                .filter(ContentPurchase.status == ContentPurchaseStatus.PROCESSING)
            )
            results = query.limit(500).all()
            if not results:
                return

            for purchase, user_tx in results:
                if user_tx.status == UserTransactionStatus.CONFIRMED:
                    purchase.status = ContentPurchaseStatus.CONFIRMED
                    session.add(purchase)
                    # Update Redis to CONFIRMED
                    try:
                        r = RedisCli.sync()
                        status_str = str(ContentPurchaseStatus.CONFIRMED)
                        ttl = 86400
                        key_user_post = f"purchase:by_user_post:{purchase.user_id}:{purchase.post_id}"
                        r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                        key_by_id = f"purchase:status:{purchase.id}"
                        r.set_json(key_by_id, {"status": status_str, "user_id": purchase.user_id, "post_id": purchase.post_id}, ex=ttl)
                    except Exception as e:
                        logger.warning(f"Failed to update Redis to CONFIRMED - purchase_id: {purchase.id}, error: {e}")
                elif user_tx.status == UserTransactionStatus.FAILED:
                    purchase.status = ContentPurchaseStatus.FAILED
                    session.add(purchase)
                    # Update Redis to FAILED
                    try:
                        r = RedisCli.sync()
                        status_str = str(ContentPurchaseStatus.FAILED)
                        ttl = 86400
                        key_user_post = f"purchase:by_user_post:{purchase.user_id}:{purchase.post_id}"
                        r.set_json(key_user_post, {"purchase_id": purchase.id, "status": status_str}, ex=ttl)
                        key_by_id = f"purchase:status:{purchase.id}"
                        r.set_json(key_by_id, {"status": status_str, "user_id": purchase.user_id, "post_id": purchase.post_id}, ex=ttl)
                    except Exception as e:
                        logger.warning(f"Failed to update Redis to FAILED - purchase_id: {purchase.id}, error: {e}")
                # 对于 AWAITING_CONFIRMATION 状态的交易，保持 PROCESSING 状态不变
            # commit 在 context 退出时统一完成
        except Exception as e:
            logger.error(f"poll_unconfirmed_purchases_task failed: {e}")
