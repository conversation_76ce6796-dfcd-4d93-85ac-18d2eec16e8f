"""
Reports相关的后台任务
在worker模块中定义，包含任务定义和业务逻辑处理
"""
import asyncio
from typing import Dict, Any
from src.worker.celery_app import celery_app
from src.reports.services import ReportService
from src.reports.repos import ReportRepo
from src.reports.dto import ReportCreateDTO
from src.reports.enums import ReportReason
from src.reports.exceptions import CantCreateReport
from src.database.session import get_sync_context


def process_create_report_task(report_data: Dict[str, Any]):
    """
    处理创建举报任务的核心业务逻辑（同步版本）
    """
    try:
        with get_sync_context() as session:
            # 创建DTO
            dto = ReportCreateDTO(
                type=report_data["type"],
                current_user_id=report_data["current_user_id"],
                reason=ReportReason(report_data["reason"]),
                comment=report_data.get("comment"),
                target_id=report_data["target_id"],
                ex=report_data.get("ex")
            )
            
            # 使用同步方式创建举报
            result = _create_report_sync(dto, session)
            
            return {
                "status": "success",
                "message": "Report created successfully",
                "report_id": result.id if hasattr(result, 'id') else str(result)
            }
    except Exception as e:
        # 记录错误并重新抛出
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to create report: {str(e)}")
        raise


def _create_report_sync(dto: ReportCreateDTO, session):
    """
    同步创建举报的实现 - 统一存储到 Report 模型中
    """
    from src.database.models import User, Post, Comment
    from src.database.models.Report import Report
    from sqlalchemy import select, text
    
    # 防止自己举报自己
    if dto.target_id == dto.current_user_id:
        raise CantCreateReport("Cannot report yourself")
    
    # 根据举报类型验证目标对象是否存在
    if dto.type == "post":
        stmt = select(Post).where(Post.id == dto.target_id)
        target = session.execute(stmt).first()
        if not target:
            raise CantCreateReport("Invalid post_id")
    elif dto.type == "comment":
        stmt = select(Comment).where(Comment.id == dto.target_id)
        target = session.execute(stmt).first()
        if not target:
            raise CantCreateReport("Invalid comment_id")
    elif dto.type == "user":
        stmt = select(User).where(User.id == dto.target_id)
        target = session.execute(stmt).first()
        if not target:
            raise CantCreateReport("Invalid user_id")
    # group 和 private 类型不需要验证，因为它们的 target_id 可能是群组ID或用户ID
    
    # 构建 ex 字段，包含举报类型和其他额外信息
    ex_data = dto.ex or {}
    ex_data["report_type"] = dto.type  # 添加举报类型到 ex 字段中
    
    # 统一创建 Report 记录（id 将由数据库自动生成）
    report = Report(
        target_id=dto.target_id,  # 被举报的对象ID
        creator_id=dto.current_user_id,  # 举报者ID
        reason=dto.reason,
        comment=dto.comment,
        ex=ex_data,
        status="pending"
    )
    
    session.add(report)
    session.flush()  # 刷新以获取HasID mixin生成的 ID，但不提交事务
    session.refresh(report)  # 刷新对象以获取生成的 ID
    
    # 如果是帖子举报，更新帖子的投诉数量（保持向后兼容）
    if dto.type == "post":
        stmt = text(f"""
            UPDATE posts
            SET complaint_count = complaint_count + 1
            WHERE id = '{dto.target_id}'
        """)
        session.execute(stmt)
    
    return report


@celery_app.task(bind=True, name="src.worker.tasks.reports.create_report")
def create_report_task(self, report_data: Dict[str, Any]):
    """
    创建举报的后台任务
    
    Args:
        report_data: 举报数据字典
    """
    try:
        # 运行同步任务处理函数
        result = process_create_report_task(report_data)
        return result
    except Exception as e:
        # 如果失败，可以选择重试
        self.retry(countdown=60, max_retries=3)
        return {"status": "error", "message": f"Failed to create report: {str(e)}"} 