"""
Collection相关的Celery任务
处理collection中的帖子清理等操作
"""
from datetime import datetime
import asyncio

from celery import current_app
from sqlalchemy import select, delete
from src.common.redis_cli import RedisCli

from src.database.session import get_sync_context
from src.database.models import Collection, Author
from src.database.models.Post import Post as PostModel
from src.database.models.SavedPosts import SavedPost
from src.worker.logger import logger
from src.common.holdview import invalidate_post_cache


async def _invalidate_post_cache_async(post_id: str):
    """
    异步缓存失效辅助函数
    在Celery任务中调用以失效缓存
    """
    try:
        # 创建Redis连接
        redis_client = RedisCli.async_()
        
        # 失效缓存
        await invalidate_post_cache(post_id, redis_client)
        
        await redis_client.close()
        logger.debug(f"Cache invalidated for deleted post: {post_id}")
        
    except Exception as e:
        logger.error(f"Error invalidating cache for post {post_id}: {str(e)}")


def _invalidate_post_cache_sync(post_id: str):
    """
    同步缓存失效包装函数
    在Celery任务中使用
    """
    try:
        asyncio.run(_invalidate_post_cache_async(post_id))
    except Exception as e:
        logger.error(f"Error in sync cache invalidation for post {post_id}: {str(e)}")


@current_app.task(bind=True, name="src.worker.tasks.collection.post_deletion_cleanup")
def post_deletion_cleanup(self, post_id: str):
    """
    帖子删除后的完整清理任务
    包括：collection清理、search同步删除、recommender同步删除
    """
    try:
        logger.info(f"开始帖子删除清理: {post_id}")
        
        # 获取帖子信息用于后续操作
        post_type = None
        with get_sync_context() as session:
            post = session.get(PostModel, post_id)
            if post:
                post_type = post.type
                logger.info(f"帖子类型: {post_type}")
            
            # 1. 查找所有包含该帖子的collection
            from sqlalchemy import any_
            stmt = select(Collection).where(
                post_id == any_(Collection.contents)
            )
            result = session.execute(stmt)
            collections = result.scalars().all()
            
            # 2. 从每个collection中移除该帖子
            for collection in collections:
                if post_id in collection.contents:
                    collection.contents.remove(post_id)
                    collection.contents_count = max(0, collection.contents_count - 1)
                    collection.updated_at = datetime.utcnow()
                    session.add(collection)
                    
                    # 如果被移除的帖子作者不是collection作者，需要减少引用计数
                    if post and post.author_id != collection.author_id:
                        # 更新author的citations_count
                        author = session.get(Author, post.author_id)
                        if author and author.citations_count > 0:
                            author.citations_count -= 1
                            session.add(author)
            
            # 3. 删除相关的SavedPost记录
            session.execute(
                delete(SavedPost).where(SavedPost.post_id == post_id)
            )
            
            # 4. 更新帖子的collections_count
            if post:
                post.collections_count = max(0, post.collections_count - len(collections))
                session.add(post)
            
            # session.commit() 在 get_sync_context 中自动处理
            logger.info(f"Collection清理完成: {post_id}")
        
        # 5. 失效相关缓存
        _invalidate_post_cache_sync(post_id)
        
        # 6. 同步删除到search(elasticsearch)
        try:
            from src.database.constants import PostType
            if post_type in [PostType.IMAGE, PostType.VIDEO]:
                from src.worker.tasks.elasticsearch_sync import sync_processor
                success = sync_processor.delete_from_elasticsearch(post_id, "posts")
                if success:
                    logger.info(f"Search删除同步成功: {post_id}")
                else:
                    logger.warning(f"Search删除同步失败: {post_id}")
            else:
                logger.info(f"帖子类型 {post_type} 不需要search同步删除")
        except Exception as e:
            logger.error(f"Search删除同步失败: {post_id}, error: {e}")
        
        # 7. 同步删除到recommender
        try:
            from src.common.settings import settings
            
            # 检查是否配置了推荐服务token
            if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN:
                # 使用同步HTTP客户端（因为在Celery任务中）
                import httpx
                with httpx.Client(
                    headers={"X-Token": settings.RECOMMENDER_TOKEN},
                    base_url="http://recommender-api:8000/",
                    trust_env=False
                ) as client:
                    response = client.delete(f"posts/{post_id}")
                    if response.status_code < 300:
                        logger.info(f"Recommender删除同步成功: {post_id}")
                    else:
                        logger.warning(f"Recommender删除同步失败: {post_id}, status: {response.status_code}")
            else:
                logger.info("Recommender token未配置，跳过同步删除")
        except Exception as e:
            logger.error(f"Recommender删除同步失败: {post_id}, error: {e}")
        
        logger.info(f"帖子删除清理任务完成: {post_id}")
        return {"success": True, "message": f"Post {post_id} deletion cleanup completed"}
        
    except Exception as e:
        logger.error(f"帖子删除清理任务失败: {post_id}, error: {e}")
        self.retry(exc=e, countdown=60, max_retries=3)
        raise e


@current_app.task(bind=True, name="src.worker.tasks.collection.post_update_sync")
def post_update_sync(self, post_id: str, update_fields: dict):
    """
    帖子编辑后的同步任务
    同步更新到search(elasticsearch)和recommender
    """
    try:
        logger.info(f"开始帖子编辑同步: {post_id}, 更新字段: {list(update_fields.keys())}")
        
        # 获取帖子信息
        post_type = None
        recommender_post_data = None
        with get_sync_context() as session:
            post = session.get(PostModel, post_id)
            if not post:
                logger.warning(f"帖子不存在: {post_id}")
                return {"success": False, "message": f"Post {post_id} not found"}
            
            post_type = post.type
            # 构建推荐服务期望的PostSchema格式
            recommender_post_data = {
                "id": post.id,
                "type": post.type,
                "region": post.region,
                "title": getattr(post, 'title', None),
                "text": getattr(post, 'text', None),
                "description": post.description,
                "language": post.language
            }
            logger.info(f"帖子类型: {post_type}")
        
        # 1. 同步更新到search(elasticsearch)
        try:
            from src.database.constants import PostType
            if post_type in [PostType.IMAGE, PostType.VIDEO]:
                from src.worker.tasks.elasticsearch_sync import sync_processor
                # 使用sync_post_to_elasticsearch进行upsert操作（存在则更新，不存在则创建）
                success = sync_processor.sync_post_to_elasticsearch(post_id, post_type)
                if success:
                    logger.info(f"Search更新同步成功: {post_id}")
                else:
                    logger.warning(f"Search更新同步失败: {post_id}")
            else:
                logger.info(f"帖子类型 {post_type} 不需要search同步更新")
        except Exception as e:
            logger.error(f"Search更新同步失败: {post_id}, error: {e}")
        
        # 2. 同步更新到recommender（使用删除+重新添加的方式）
        try:
            from src.common.settings import settings
            
            # 检查是否配置了推荐服务token
            if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN:
                # 使用同步HTTP客户端（因为在Celery任务中）
                import httpx
                
                with httpx.Client(
                    headers={"X-Token": settings.RECOMMENDER_TOKEN},
                    base_url="http://recommender-api:8000/",
                    trust_env=False
                ) as client:
                    # 使用推荐服务期望的PUT /posts接口和PostSchema格式
                    response = client.put("posts", json=recommender_post_data)
                    if response.status_code < 300:
                        logger.info(f"Recommender更新同步成功: {post_id}")
                    else:
                        logger.warning(f"Recommender更新同步失败: {post_id}, status: {response.status_code}")
            else:
                logger.info("Recommender token未配置，跳过同步更新")
        except Exception as e:
            logger.error(f"Recommender更新同步失败: {post_id}, error: {e}")
        
        logger.info(f"帖子编辑同步任务完成: {post_id}")
        return {"success": True, "message": f"Post {post_id} update sync completed"}
        
    except Exception as e:
        logger.error(f"帖子编辑同步任务失败: {post_id}, error: {e}")
        self.retry(exc=e, countdown=60, max_retries=3)
        raise e 