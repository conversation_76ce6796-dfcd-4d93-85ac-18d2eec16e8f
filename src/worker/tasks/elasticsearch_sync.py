"""
Elasticsearch同步任务 - 语言独立索引架构（同步版本）
使用 ingest pipeline 进行自动语言检测和路由
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

from celery import Celery
from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError, TransportError

from src.common.elasticsearch.mappings import SUPPORTED_LANGUAGES, get_prefixed_index_name
from src.common.settings import settings
from src.database.models import Author, Video, Image, User
from src.database.models.Pair import Pair
from src.database.session import get_sync_context
from src.worker.celery_app import celery_app
from src.worker.logger import logger


def create_sync_elasticsearch_client() -> Elasticsearch:
    """创建同步Elasticsearch客户端"""
    try:
        # 准备连接参数
        client_params = {
            "request_timeout": settings.ELASTICSEARCH_TIMEOUT,
            "max_retries": settings.ELASTICSEARCH_MAX_RETRIES,
            "retry_on_timeout": True,
            # 连接池配置
            "http_compress": True,
            "verify_certs": False,  # 开发环境可以禁用证书验证
        }
        
        # 如果提供了认证信息，则添加基本认证
        if settings.ELASTICSEARCH_USERNAME and settings.ELASTICSEARCH_PASSWORD:
            client_params["basic_auth"] = (settings.ELASTICSEARCH_USERNAME, settings.ELASTICSEARCH_PASSWORD)
        
        client = Elasticsearch(
            [settings.ELASTICSEARCH_URL],
            **client_params
        )
        
        # 测试连接
        if client.ping():
            logger.info(f"Elasticsearch连接成功: {settings.ELASTICSEARCH_URL}")
            return client
        else:
            raise ConnectionError("Elasticsearch连接失败")
            
    except Exception as e:
        logger.error(f"创建Elasticsearch客户端失败: {str(e)}")
        raise


class LanguageRoutedSyncProcessor:
    """语言路由同步处理器（同步版本）"""
    
    def __init__(self):
        self.es_client = None
    
    def _get_es_client(self):
        """获取Elasticsearch客户端"""
        if self.es_client is None:
            self.es_client = create_sync_elasticsearch_client()
        return self.es_client
    
    def _build_post_document(self, post, post_type: str) -> Dict[str, Any]:
        """
        构建Posts文档（用于ingest pipeline处理）
        
        Args:
            post: Video或Image对象
            post_type: 'Video' 或 'Image'
            
        Returns:
            Dict: 待处理的文档
        """
        # 获取作者信息
        author_data = {}
        if hasattr(post, 'author') and post.author:
            author_data = {
                "id": str(post.author.id),
                "name": post.author.name or "",
                "username": post.author.username or "",
                "avatar": getattr(post.author, 'avatar', None)
            }
        
        # 基础文档结构 - 让ingest pipeline处理语言检测
        # 确保包含pipeline需要的所有字段：title, description, text
        doc = {
            "id": str(post.id),
            "type": post_type,
            "status": post.status or "draft",
            "title": getattr(post, 'title', '') or "",  # Pipeline需要此字段进行语言检测
            "description": post.description or "",
            "text": getattr(post, 'text', '') or "",  # Pipeline需要此字段进行语言检测
            "tags": post.tags or [],
            "author": author_data,
            "created_at": post.created_at.isoformat() if post.created_at else datetime.utcnow().isoformat(),
            "updated_at": post.updated_at.isoformat() if post.updated_at else datetime.utcnow().isoformat(),
            "region": getattr(post, 'region', '') or "",
            
            # 媒体文件字段（Video和Image共有）
            "cover": getattr(post, 'cover', None),
            "height": getattr(post, 'height', None),
            "width": getattr(post, 'width', None),
            "images_data": getattr(post, 'images_data', None),  # images专用
            
            "indexed_at": datetime.utcnow().isoformat()
        }
        
        # Video特有字段
        if post_type == "Video":
            doc["url"] = getattr(post, 'url', None)
        
        return doc
    
    def _build_author_document(self, author: Author) -> Dict[str, Any]:
        """
        构建Authors文档（用于ingest pipeline处理）
        
        Args:
            author: Author对象
            
        Returns:
            Dict: 待处理的文档
        """
        # 获取status - 通过user关系获取，如果没有则默认为active
        status = 'active'
        if hasattr(author, 'user') and author.user:
            status = getattr(author.user, 'status', 'active')
        
        doc = {
            "id": str(author.id),
            "status": status,
            "name": author.name or "",
            "username": author.username or "",
            "avatar": getattr(author, 'avatar', None),
            "description": getattr(author, 'description', '') or "",
            "country": getattr(author, 'country', '') or "",
            "language": getattr(author, 'language', '') or "",
            "phone_number": getattr(author, 'phone_number', '') or "",
            "email": getattr(author, 'email', '') or "",
            "created_at": author.created_at.isoformat() if author.created_at else datetime.utcnow().isoformat(),
            "updated_at": author.updated_at.isoformat() if author.updated_at else datetime.utcnow().isoformat(),
            "region": getattr(author, 'region', '') or "",
            "indexed_at": datetime.utcnow().isoformat()
        }
        
        return doc
    
    def _build_memecoin_document(self, memecoin) -> Dict[str, Any]:
        """
        构建MemeCoin文档（用于ingest pipeline处理）
        
        Args:
            memecoin: Pair对象（代表MemeCoin）
            
        Returns:
            Dict: 待处理的文档
        """
        doc = {
            "id": str(memecoin.id),
            "status": str(getattr(memecoin, 'status', 0)),
            "chain": str(getattr(memecoin, 'chain', 0)),
            "dex": getattr(memecoin, 'dex', '') or "",
            "address": getattr(memecoin, 'base', '') or "",  # base字段映射为address
            
            # 映射字段：数据库字段 -> ES索引字段
            "name": getattr(memecoin, 'base_name', '') or "",
            "symbol": getattr(memecoin, 'base_symbol', '') or "",
            "description": getattr(memecoin, 'base_description', '') or "",
            "image_url": getattr(memecoin, 'base_image_url', '') or "",
            
            "creator": getattr(memecoin, 'creator', '') or "",
            "bonding_curve": getattr(memecoin, 'bonding_curve', 0),
            "collection_id": getattr(memecoin, 'collection_id', '') or "",
            "creator_id": getattr(memecoin, 'creator_id', '') or "",
            
            "created_at": memecoin.created_at.isoformat() if memecoin.created_at else datetime.utcnow().isoformat(),
            "indexed_at": datetime.utcnow().isoformat()
        }
        
        return doc
    
    def sync_post_to_elasticsearch(self, post_id: str, post_type: str) -> bool:
        """
        同步单个Post到Elasticsearch
        
        Args:
            post_id: Post ID
            post_type: 'Video' 或 'Image'
            
        Returns:
            bool: 是否同步成功
        """
        try:
            es_client = self._get_es_client()
            if not es_client:
                logger.error("ES客户端不可用")
                return False
            
            # 获取数据库会话
            with get_sync_context() as session:
                # 根据类型查询对象，包含作者信息
                if post_type == "Video":
                    stmt = select(Video).options(selectinload(Video.author)).where(Video.id == post_id)
                    result = session.execute(stmt)
                    post = result.scalar_one_or_none()
                elif post_type == "Image":
                    stmt = select(Image).options(selectinload(Image.author)).where(Image.id == post_id)
                    result = session.execute(stmt)
                    post = result.scalar_one_or_none()
                else:
                    logger.error(f"不支持的Post类型: {post_type}")
                    return False
                
                if not post:
                    logger.warning(f"未找到 {post_type} with ID: {post_id}")
                    return False
                
                # 构建文档
                doc = self._build_post_document(post, post_type)
                
                # 使用pipeline写入 - ES会自动检测语言并路由到对应索引
                pipeline_name = "posts_language_detection"
                temp_index = get_prefixed_index_name("posts", "temp")
                
                response = es_client.index(
                    index=temp_index,  # 临时索引名，pipeline会重新路由
                    document=doc,
                    id=post_id,
                    pipeline=pipeline_name,
                    refresh="wait_for"
                )
                
                logger.info(f"同步 {post_type} 到ES成功: {post_id}")
                return True
                
        except Exception as e:
            logger.error(f"同步 {post_type} 到ES失败: {post_id}, error: {e}")
            return False
    
    def sync_author_to_elasticsearch(self, author_id: str) -> bool:
        """
        同步作者到Elasticsearch
        
        Args:
            author_id: 作者ID
            
        Returns:
            bool: 是否同步成功
        """
        try:
            es_client = self._get_es_client()
            if not es_client:
                logger.error("ES客户端不可用")
                return False
            
            # 获取数据库会话
            with get_sync_context() as session:
                stmt = select(Author).options(selectinload(Author.user)).where(Author.id == author_id)
                result = session.execute(stmt)
                author = result.scalar_one_or_none()
                
                if not author:
                    logger.warning(f"未找到作者: {author_id}")
                    return False
                
                # 构建文档
                doc = self._build_author_document(author)
                
                # 使用pipeline写入
                pipeline_name = "authors_language_detection"
                temp_index = get_prefixed_index_name("authors", "temp")
                
                response = es_client.index(
                    index=temp_index,  # 临时索引名，pipeline会重新路由
                    document=doc,
                    id=author_id,
                    pipeline=pipeline_name,
                    refresh="wait_for"
                )
                
                logger.info(f"同步作者到ES成功: {author_id}")
                return True
                
        except Exception as e:
            logger.error(f"同步作者到ES失败: {author_id}, error: {e}")
            return False
    
    def sync_memecoin_to_elasticsearch(self, memecoin_id: str) -> bool:
        """
        同步MemeCoin到Elasticsearch
        
        Args:
            memecoin_id: MemeCoin ID
            
        Returns:
            bool: 是否同步成功
        """
        try:
            es_client = self._get_es_client()
            if not es_client:
                logger.error("ES客户端不可用")
                return False
            
            # 获取数据库会话
            with get_sync_context() as session:
                # 查询Pair对象（代表MemeCoin）
                stmt = select(Pair).where(Pair.id == int(memecoin_id))
                result = session.execute(stmt)
                memecoin = result.scalar_one_or_none()
                
                if not memecoin:
                    logger.warning(f"未找到MemeCoin: {memecoin_id}，可能MemeCoin模型未定义")
                    return False
                
                # 构建文档
                doc = self._build_memecoin_document(memecoin)
                
                # 使用pipeline写入
                pipeline_name = "memecoins_language_detection"
                temp_index = get_prefixed_index_name("memecoins", "temp")
                
                response = es_client.index(
                    index=temp_index,  # 临时索引名，pipeline会重新路由
                    document=doc,
                    id=memecoin_id,
                    pipeline=pipeline_name,
                    refresh="wait_for"
                )
                
                logger.info(f"同步MemeCoin到ES成功: {memecoin_id}")
                return True
                
        except Exception as e:
            logger.error(f"同步MemeCoin到ES失败: {memecoin_id}, error: {e}")
            return False
    
    def delete_from_elasticsearch(self, entity_id: str, entity_type: str) -> bool:
        """
        从所有语言索引中删除文档
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型 ('posts', 'authors', 'memecoins')
            
        Returns:
            bool: 是否删除成功
        """
        try:
            es_client = self._get_es_client()
            if not es_client:
                logger.error("ES客户端不可用")
                return False
            
            # 从所有可能的语言索引中删除
            success_count = 0
            total_count = 0
            
            for language in SUPPORTED_LANGUAGES:
                index_name = get_prefixed_index_name(entity_type, language)
                # 检查索引是否存在
                try:
                    if es_client.indices.exists(index=index_name):
                        total_count += 1
                        response = es_client.delete(
                            index=index_name,
                            id=entity_id,
                            refresh="wait_for"
                        )
                        success_count += 1
                except Exception as e:
                    logger.warning(f"删除文档失败 {index_name}/{entity_id}: {e}")
            
            logger.info(f"从ES删除 {entity_type} 成功: {entity_id}, 成功删除: {success_count}/{total_count}")
            return True
            
        except Exception as e:
            logger.error(f"从ES删除 {entity_type} 失败: {entity_id}, error: {e}")
            return False


# 全局同步处理器实例
sync_processor = LanguageRoutedSyncProcessor()


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_video_to_elasticsearch(self, video_id: str):
    """
    同步Video到Elasticsearch的Celery任务
    
    Args:
        video_id: Video ID
    """
    try:
        success = sync_processor.sync_post_to_elasticsearch(video_id, "Video")
        
        if not success:
            logger.error(f"同步Video失败: {video_id}")
            raise Exception(f"同步Video失败: {video_id}")
        
        logger.info(f"Video同步任务完成: {video_id}")
        
    except Exception as e:
        logger.error(f"Video同步任务失败: {video_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_image_to_elasticsearch(self, image_id: str):
    """
    同步Image到Elasticsearch的Celery任务
    
    Args:
        image_id: Image ID
    """
    try:
        success = sync_processor.sync_post_to_elasticsearch(image_id, "Image")
        
        if not success:
            logger.error(f"同步Image失败: {image_id}")
            raise Exception(f"同步Image失败: {image_id}")
        
        logger.info(f"Image同步任务完成: {image_id}")
        
    except Exception as e:
        logger.error(f"Image同步任务失败: {image_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_author_to_elasticsearch(self, author_id: str):
    """
    同步Author到Elasticsearch的Celery任务
    
    Args:
        author_id: Author ID
    """
    try:
        success = sync_processor.sync_author_to_elasticsearch(author_id)
        
        if not success:
            logger.error(f"同步Author失败: {author_id}")
            raise Exception(f"同步Author失败: {author_id}")
        
        logger.info(f"Author同步任务完成: {author_id}")
        
    except Exception as e:
        logger.error(f"Author同步任务失败: {author_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def sync_memecoin_to_elasticsearch(self, memecoin_id: str):
    """
    同步MemeCoin到Elasticsearch的Celery任务
    
    Args:
        memecoin_id: MemeCoin ID
    """
    try:
        success = sync_processor.sync_memecoin_to_elasticsearch(memecoin_id)
        
        if not success:
            logger.error(f"同步MemeCoin失败: {memecoin_id}")
            raise Exception(f"同步MemeCoin失败: {memecoin_id}")
        
        logger.info(f"MemeCoin同步任务完成: {memecoin_id}")
        
    except Exception as e:
        logger.error(f"MemeCoin同步任务失败: {memecoin_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def delete_from_elasticsearch(self, entity_id: str, entity_type: str):
    """
    从Elasticsearch删除实体的Celery任务
    
    Args:
        entity_id: 实体ID
        entity_type: 实体类型 ('posts', 'authors', 'memecoins')
    """
    try:
        success = sync_processor.delete_from_elasticsearch(entity_id, entity_type)
        
        if not success:
            logger.error(f"删除 {entity_type} 失败: {entity_id}")
            raise Exception(f"删除 {entity_type} 失败: {entity_id}")
        
        logger.info(f"{entity_type} 删除任务完成: {entity_id}")
        
    except Exception as e:
        logger.error(f"{entity_type} 删除任务失败: {entity_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=2, default_retry_delay=120)
def bulk_sync_entities_task(self, entity_ids: List[str], entity_type: str):
    """
    批量同步实体到Elasticsearch的Celery任务
    
    Args:
        entity_ids: 实体ID列表
        entity_type: 实体类型 ('videos', 'images', 'authors', 'memecoins')
    """
    try:
        success_count = 0
        failed_count = 0
        
        for entity_id in entity_ids:
            try:
                if entity_type == 'videos':
                    success = sync_processor.sync_post_to_elasticsearch(entity_id, "Video")
                elif entity_type == 'images':
                    success = sync_processor.sync_post_to_elasticsearch(entity_id, "Image")
                elif entity_type == 'authors':
                    success = sync_processor.sync_author_to_elasticsearch(entity_id)
                elif entity_type == 'memecoins':
                    success = sync_processor.sync_memecoin_to_elasticsearch(entity_id)
                else:
                    logger.error(f"不支持的实体类型: {entity_type}")
                    continue
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"批量同步单个实体失败: {entity_type}:{entity_id}, error: {e}")
                failed_count += 1
        
        logger.info(f"批量同步 {entity_type} 完成: 成功={success_count}, 失败={failed_count}")
        
        if failed_count > 0 and self.request.retries < self.max_retries:
            # 如果有失败的，重试一次
            raise Exception(f"批量同步部分失败: {failed_count}/{len(entity_ids)}")
        
    except Exception as e:
        logger.error(f"批量同步 {entity_type} 任务失败: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=120, exc=e)
        raise e




# ================== RabbitMQ消息队列方案 ==================

@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_sync_video_to_elasticsearch(self, video_id: str, priority: int = 5):
    """
    队列化的Video同步任务（使用RabbitMQ消息队列）
    
    Args:
        video_id: Video ID
        priority: 优先级 (0-9, 数字越小优先级越高)
    """
    try:
        success = sync_processor.sync_post_to_elasticsearch(video_id, "Video")
        
        if not success:
            logger.error(f"队列同步Video失败: {video_id}")
            raise Exception(f"队列同步Video失败: {video_id}")
        
        logger.info(f"队列Video同步任务完成: {video_id}")
        
    except Exception as e:
        logger.error(f"队列Video同步任务失败: {video_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_sync_image_to_elasticsearch(self, image_id: str, priority: int = 5):
    """
    队列化的Image同步任务（使用RabbitMQ消息队列）
    
    Args:
        image_id: Image ID
        priority: 优先级 (0-9, 数字越小优先级越高)
    """
    try:
        success = sync_processor.sync_post_to_elasticsearch(image_id, "Image")
        
        if not success:
            logger.error(f"队列同步Image失败: {image_id}")
            raise Exception(f"队列同步Image失败: {image_id}")
        
        logger.info(f"队列Image同步任务完成: {image_id}")
        
    except Exception as e:
        logger.error(f"队列Image同步任务失败: {image_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_sync_author_to_elasticsearch(self, author_id: str, priority: int = 5):
    """
    队列化的Author同步任务（使用RabbitMQ消息队列）
    
    Args:
        author_id: Author ID
        priority: 优先级 (0-9, 数字越小优先级越高)
    """
    try:
        success = sync_processor.sync_author_to_elasticsearch(author_id)
        
        if not success:
            logger.error(f"队列同步Author失败: {author_id}")
            raise Exception(f"队列同步Author失败: {author_id}")
        
        logger.info(f"队列Author同步任务完成: {author_id}")
        
    except Exception as e:
        logger.error(f"队列Author同步任务失败: {author_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_sync_memecoin_to_elasticsearch(self, memecoin_id: str, priority: int = 5):
    """
    队列化的MemeCoin同步任务（使用RabbitMQ消息队列）
    
    Args:
        memecoin_id: MemeCoin ID
        priority: 优先级 (0-9, 数字越小优先级越高)
    """
    try:
        success = sync_processor.sync_memecoin_to_elasticsearch(memecoin_id)
        
        if not success:
            logger.error(f"队列同步MemeCoin失败: {memecoin_id}")
            raise Exception(f"队列同步MemeCoin失败: {memecoin_id}")
        
        logger.info(f"队列MemeCoin同步任务完成: {memecoin_id}")
        
    except Exception as e:
        logger.error(f"队列MemeCoin同步任务失败: {memecoin_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_delete_from_elasticsearch(self, entity_id: str, entity_type: str, priority: int = 3):
    """
    队列化的删除任务（使用RabbitMQ消息队列）
    
    Args:
        entity_id: 实体ID
        entity_type: 实体类型 ('posts', 'authors', 'memecoins')
        priority: 优先级 (0-9, 数字越小优先级越高，删除操作默认优先级更高)
    """
    try:
        success = sync_processor.delete_from_elasticsearch(entity_id, entity_type)
        
        if not success:
            logger.error(f"队列删除 {entity_type} 失败: {entity_id}")
            raise Exception(f"队列删除 {entity_type} 失败: {entity_id}")
        
        logger.info(f"队列{entity_type}删除任务完成: {entity_id}")
        
    except Exception as e:
        logger.error(f"队列{entity_type}删除任务失败: {entity_id}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60, priority=5)
def queue_sync_entity(self, entity_type: str, entity_id: str, operation: str = "update", priority: int = 5):
    """
    统一的实体同步任务（可通过 celery_client 调用）
    
    Args:
        entity_type: 实体类型 ('Video', 'Image', 'Author', 'MemeCoin')
        entity_id: 实体ID
        operation: 操作类型 ('create', 'update', 'delete')
        priority: 优先级 (0-9, 数字越小优先级越高)
    """
    try:
        if operation == "delete":
            # 删除操作，映射到posts/authors/memecoins
            es_entity_type = entity_type.lower()
            if entity_type in ["Video", "Image"]:
                es_entity_type = "posts"
            elif entity_type == "MemeCoin":
                es_entity_type = "memecoins"
            
            success = sync_processor.delete_from_elasticsearch(entity_id, es_entity_type)
        else:
            # 创建或更新操作
            if entity_type == "Video":
                success = sync_processor.sync_post_to_elasticsearch(entity_id, "Video")
            elif entity_type == "Image":
                success = sync_processor.sync_post_to_elasticsearch(entity_id, "Image")
            elif entity_type == "Author":
                success = sync_processor.sync_author_to_elasticsearch(entity_id)
            elif entity_type == "MemeCoin":
                success = sync_processor.sync_memecoin_to_elasticsearch(entity_id)
            else:
                logger.error(f"不支持的实体类型: {entity_type}")
                raise Exception(f"不支持的实体类型: {entity_type}")
        
        if not success:
            logger.error(f"队列同步实体失败: {entity_type}#{entity_id}, operation={operation}")
            raise Exception(f"队列同步实体失败: {entity_type}#{entity_id}")
        
        logger.info(f"队列实体同步任务完成: {entity_type}#{entity_id}, operation={operation}")
        
    except Exception as e:
        logger.error(f"队列实体同步任务失败: {entity_type}#{entity_id}, operation={operation}, error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e


# ================== 批量队列处理 ==================

@celery_app.task(bind=True, max_retries=2, default_retry_delay=60)
def batch_queue_sync_entities(self, entity_data_list: List[Dict[str, Any]]):
    """
    批量将同步任务加入队列
    
    Args:
        entity_data_list: 实体数据列表，每个元素包含:
            - entity_type: 实体类型
            - entity_id: 实体ID
            - operation: 操作类型 (默认 'update')
            - priority: 优先级 (默认 5)
    """
    try:
        success_count = 0
        failed_count = 0
        
        for entity_data in entity_data_list:
            try:
                entity_type = entity_data.get("entity_type")
                entity_id = entity_data.get("entity_id")
                operation = entity_data.get("operation", "update")
                priority = entity_data.get("priority", 5)
                
                # 直接调用同步处理器
                if operation == "delete":
                    es_entity_type = entity_type.lower()
                    if entity_type in ["Video", "Image"]:
                        es_entity_type = "posts"
                    elif entity_type == "MemeCoin":
                        es_entity_type = "memecoins"
                    
                    success = sync_processor.delete_from_elasticsearch(entity_id, es_entity_type)
                else:
                    if entity_type == "Video":
                        success = sync_processor.sync_post_to_elasticsearch(entity_id, "Video")
                    elif entity_type == "Image":
                        success = sync_processor.sync_post_to_elasticsearch(entity_id, "Image")
                    elif entity_type == "Author":
                        success = sync_processor.sync_author_to_elasticsearch(entity_id)
                    elif entity_type == "MemeCoin":
                        success = sync_processor.sync_memecoin_to_elasticsearch(entity_id)
                    else:
                        logger.error(f"不支持的实体类型: {entity_type}")
                        continue
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"批量同步单个实体失败: {entity_data}, error: {e}")
                failed_count += 1
        
        logger.info(f"批量同步完成: 成功={success_count}, 失败={failed_count}")
        
        if failed_count > 0 and self.request.retries < self.max_retries:
            raise Exception(f"批量同步部分失败: {failed_count}/{len(entity_data_list)}")
        
    except Exception as e:
        logger.error(f"批量同步任务失败: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        raise e





 