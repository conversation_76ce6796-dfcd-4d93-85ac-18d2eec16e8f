"""
Memecoin相关的Celery任务
处理代币创建后的异步操作
"""
import asyncio
import json
from decimal import Decimal
from typing import Optional, Dict

from celery import Celery
from celery.utils.log import get_task_logger

from src.worker.celery_app import celery_app
from src.common.constants import UserTransactionType, UserTransactionStatus, PairStatus, CollectionContentType
from redis.asyncio import Redis
from src.common.caching.caching_client import CachingClient
from src.database.session import get_session_context
from src.memecoin.repos import MemeRepo
from src.im.service import TociImService
from src.im.schemas import CreateGroupRequest, GroupInfo
from src.memecoin.settings import settings
from src.notifications.push import PushService
from src.worker.logger import logger
from src.memecoin.services import MemeService
from src.common.blockchain import BlockchainConnectorGetter
from src.memecoin.timescale_db import SessionLocal as KlineSessionLocal
from src.common.redis_cli import RedisCli


def _create_blockchain_connector(logger):
    """
    创建区块链连接器的辅助方法

    :param logger: 日志记录器
    :return: BlockchainConnectorGetter 实例
    """
    if settings.BLOCKCHAIN_TYPE == "SOL":
        return BlockchainConnectorGetter(
            logger=logger,
            chain_type="sol",
            rpc_url=settings.SOL_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH
            }
        )
    elif settings.BLOCKCHAIN_TYPE == "ETH":
        return BlockchainConnectorGetter(
            logger=logger,
            chain_type="eth",
            rpc_url=settings.ETH_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.ETH_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
                "reward_contract_address": settings.REWARD_CONTRACT_ADDRESS,
                "reward_abi_path": settings.REWARD_ABI_PATH,
            }
        )
    else:  # BSC
        return BlockchainConnectorGetter(
            logger=logger,
            chain_type="bsc",
            rpc_url=settings.BSC_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.BSC_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
                "reward_contract_address": settings.REWARD_CONTRACT_ADDRESS,
                "reward_abi_path": settings.REWARD_ABI_PATH,
            }
        )


async def _process_post_meme_creation_async(
    user_id: str,
    collection_id: str,
    name: str,
    symbol: str,
    avatar: str,
    about: str,
    tx_hash: str,
    wallet_address: str,
    amount_to_buy: Decimal,
    gas: int,
    chain_id: int,
    social_links: Optional[Dict[str, str]] = None,
    order_id: Optional[str] = None,
    is_with_usdt: Optional[bool] = True,
    buy_usd_value: Optional[float] = None,
    user_region: Optional[str] = None,
    holdview_amount: Optional[Decimal] = None,
):
    """
    异步处理代币创建后的操作
    """
    async with get_session_context("write") as session:
        kline_session = None
        repo = MemeRepo(session, kline_session)

        # 1. Create collection first (now that meme creation succeeded)
        logger.info(f"Creating collection {collection_id} for successful meme {name} ({symbol})")
        try:
            # Import Collection model
            from src.database.models.Collection import Collection
            from src.common.constants import PostStatus, CarnivalStatus

            # Create collection object manually with pre-generated ID
            collection = Collection(
                id=collection_id,  # Use pre-generated ID
                author_id=user_id,
                title=name,  # Use name as collection title
                description=about,  # Use about as collection description
                cover=avatar,  # Use avatar as collection cover
                content_type=CollectionContentType.MIXED,  # Fixed to Mixed
                original_cover=avatar,  # Use avatar as original cover too
                carnival_status=CarnivalStatus.NEW,
                subscriber_count=0,
                contributor_count=0,
                contents_count=0,
                contents=[],
                tags_list=[],
                status=PostStatus.POSTED,
                region=user_region or "US",
                holdview_amount=int(holdview_amount),
            )
            
            session.add(collection)
            await session.flush()
            await session.refresh(collection)
            logger.info(f"Successfully created collection {collection_id} for meme {name} ({symbol})")
            
        except Exception as e:
            logger.error(f"Failed to create collection {collection_id}: {str(e)}", exc_info=True)
            # Don't fail the entire task if collection creation fails, continue with other operations
            # The token will still work, just without a properly created collection

        logger.info(f"创建配对记录: name={name}, symbol={symbol}, collection_id={collection_id}")

        social_links_json = json.dumps(social_links) if social_links else None

        await repo.create_pair(
            name,
            symbol,
            avatar,
            about,
            PairStatus.NOT_READY,
            txid=tx_hash,
            user_id=user_id,
            collection_id=collection_id,
            chain_id=chain_id,
            social_links=social_links_json,
        )

        # 3. 创建IM群组
        try:
            # 为IM服务创建独立的session和依赖
            async with get_session_context("write") as im_session:
                try:
                    # 创建IM服务实例
                    im_service = TociImService(im_session, None, None)
                    im_user_id = await im_service.get_im_by_toci(user_id)

                    if not im_user_id:
                        logger.warning(f"User {user_id} not found in IM, skip creating group.")
                    else:
                        logger.info(f"Group owner: {im_user_id}")
                        response = await im_service.create_group(CreateGroupRequest(
                            adminUserIDs=[], # should not add ownerUserID here, there is duplicated error
                            ownerUserID=im_user_id,
                            groupInfo=GroupInfo(
                                groupID=collection_id,
                                groupName=symbol,
                                faceURL=avatar,
                                needVerification=2, # direct join
                                lookMemberInfo=0, # allowed
                                applyMemberFriend=0, # allowed
                            )
                        ))

                        logger.info(f"Created IM group for meme {name} with groupID {collection_id}, errCode={response.errCode}, errMsg={response.errMsg}, data={response.data}")

                except Exception as e:
                    logger.error(f"Error creating IM group: {str(e)}", exc_info=True)
                    raise

        except Exception as e:
            logger.warning(f"Failed to create group {name}, {collection_id}: {str(e)}", exc_info=True)
            # IM群组创建失败不应该影响整个任务，继续执行

        logger.info(f"Post meme creation processing completed for {name} ({symbol})")


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def process_post_meme_creation(
    self,
    user_id: str,
    collection_id: str,
    name: str,
    symbol: str,
    avatar: str,
    about: str,
    tx_hash: str,
    wallet_address: str,
    amount_to_buy: Decimal,
    gas: int,
    chain_id: int,
    social_links: Optional[Dict[str, str]] = None,
    order_id: Optional[str] = None,
    is_with_usdt: Optional[bool] = True,
    buy_usd_value: Optional[float] = None,
    user_region: Optional[str] = None,
    holdview_amount: Optional[Decimal] = None,
):
    """
    处理代币创建后的异步操作（Celery同步任务）

    这个任务处理代币在区块链上创建成功后的所有后续操作：
    1. 插入交易历史记录
    2. 创建配对记录
    3. 创建IM群组

    Args:
        user_id: 用户ID
        collection_id: 集合ID
        name: 代币名称
        symbol: 代币符号
        avatar: 代币头像URL
        about: 代币描述
        tx_hash: 交易哈希
        wallet_address: 钱包地址
        amount_to_buy: 购买数量
        gas: Gas限制
        chain_id: 链ID
        social_links: 社交链接
        order_id: 订单ID
        is_with_usdt: 是否使用USDT
        buy_usd_value: 购买的USD价值
    """
    try:
        # 在同步任务中运行异步代码
        asyncio.run(_process_post_meme_creation_async(
            user_id=user_id,
            collection_id=collection_id,
            name=name,
            symbol=symbol,
            avatar=avatar,
            about=about,
            tx_hash=tx_hash,
            wallet_address=wallet_address,
            amount_to_buy=amount_to_buy,
            gas=gas,
            chain_id=chain_id,
            social_links=social_links,
            order_id=order_id,
            is_with_usdt=is_with_usdt,
            buy_usd_value=buy_usd_value,
            user_region=user_region,
            holdview_amount=holdview_amount,
        ))

        logger.info(f"Successfully processed post meme creation for {name} ({symbol})")
        return True

    except Exception as e:
        logger.error(f"Failed to process post meme creation: {str(e)}", exc_info=True)

        if self.request.retries < self.max_retries:
            # 重试任务
            logger.info(f"Retrying task, attempt {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=e)
        else:
            # 达到最大重试次数
            logger.error(f"Max retries reached for post meme creation: user_id={user_id}, collection_id={collection_id}")
            raise e


async def _update_token_cache_historical_mc_async():
    """
    异步更新所有token的TokenCache中的历史市值字段
    """
    try:


        async with get_session_context("write") as session:
            async with KlineSessionLocal() as kline_session:
                # 创建Redis客户端
                redis_client = RedisCli.async_()

                try:
                    # 初始化依赖
                    caching_client = CachingClient(redis_client, logger)

                    # 获取区块链连接器
                    connector_getter = _create_blockchain_connector(logger)
                    web3_connector = connector_getter()
                    await web3_connector.init_connection()

                    # 创建MemeService实例
                    repo = MemeRepo(session, kline_session)
                    meme_service = MemeService(
                        repo=repo,
                        caching_client=caching_client,
                        web3=web3_connector,
                    )

                    # 更新TokenCache中的历史市值字段
                    await meme_service.update_token_cache_historical_mc()

                    logger.info("TokenCache historical market caps update completed successfully")

                except Exception as e:
                    logger.error(f"Error in update process: {str(e)}", exc_info=True)
                    raise
                finally:
                    # 清理Redis连接
                    await redis_client.aclose()

    except Exception as e:
        logger.error(f"Error updating TokenCache historical market caps: {str(e)}", exc_info=True)
        raise


@celery_app.task(bind=True, max_retries=3, default_retry_delay=300)
def update_token_cache_historical_mc(self):
    """
    定时更新所有token的TokenCache中的历史市值字段（Celery定时任务）

    这个任务应该每天在00:05执行，用于更新daily_mc, weekly_mc, monthly_mc字段
    """
    try:
        # 在同步任务中运行异步代码
        asyncio.run(_update_token_cache_historical_mc_async())

        logger.info("Successfully updated TokenCache historical market caps")
        return True

    except Exception as e:
        logger.error(f"Failed to update TokenCache historical market caps: {str(e)}", exc_info=True)

        if self.request.retries < self.max_retries:
            # 重试任务
            logger.info(f"Retrying TokenCache historical market caps update, attempt {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=300, exc=e)
        else:
            # 达到最大重试次数
            logger.error("Max retries reached for TokenCache historical market caps update")
            raise e


async def _warm_up_rankings_cache_async():
    """
    异步预热排行榜缓存
    """
    try:
        async with get_session_context("write") as session:
            async with KlineSessionLocal() as kline_session:
                # 创建Redis客户端
                redis_client = RedisCli.async_()

                try:
                    # 初始化依赖
                    caching_client = CachingClient(redis_client, logger)

                    # 获取区块链连接器
                    connector_getter = _create_blockchain_connector(logger)
                    web3_connector = connector_getter()
                    await web3_connector.init_connection()

                        # 创建MemeService实例
                    repo = MemeRepo(session, kline_session)
                    meme_service = MemeService(
                        repo=repo,
                        caching_client=caching_client,
                        web3=web3_connector,
                    )

                    # 预热排行榜缓存
                    await meme_service.warm_up_rankings_cache()

                    logger.info("Rankings cache warm-up completed successfully")

                except Exception as e:
                    logger.error(f"Error in warm-up process: {str(e)}", exc_info=True)
                    raise
                finally:
                    # 清理Redis连接
                    await redis_client.aclose()
                
    except Exception as e:
        logger.error(f"Error during rankings cache warm-up: {str(e)}", exc_info=True)
        raise


@celery_app.task(bind=True, max_retries=2, default_retry_delay=60)
def warm_up_rankings_cache(self):
    """
    定时预热排行榜缓存（Celery定时任务）
    
    这个任务应该每2分钟执行一次，用于主动预热即将过期的排行榜缓存
    """
    try:
        # 在同步任务中运行异步代码
        asyncio.run(_warm_up_rankings_cache_async())
        
        logger.info("Successfully warmed up rankings cache")
        return True
        
    except Exception as e:
        logger.error(f"Failed to warm up rankings cache: {str(e)}", exc_info=True)
        
        if self.request.retries < self.max_retries:
            # 重试任务
            logger.info(f"Retrying rankings cache warm-up, attempt {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=e)
        else:
            # 达到最大重试次数
            logger.error("Max retries reached for rankings cache warm-up")
            raise e