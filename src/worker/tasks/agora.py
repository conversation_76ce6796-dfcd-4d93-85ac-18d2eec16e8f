"""
Agora 相关任务
包括礼物购买等异步操作
"""
import numpy as np

from src.agora.memecoin_client import memecoin_client
from src.worker.celery_app import celery_app
from src.worker.logger import logger
from src.database.session import get_sync_context
from src.database.models.Agora import ChannelGiftHistory, GiftList, LiveChannel
from src.database.models.Author import Author
from datetime import datetime, timedelta
import json
import httpx
import redis
import uuid
import random
from typing import Optional, List, Dict, Any

from src.common.redis_cli import RedisCli


@celery_app.task(name="src.worker.tasks.agora.process_gift_purchase_task")
def process_gift_purchase_task(
    user_id: str,
    channel_name: str,
    channel_id: int,
    gift_id: int,
    quantity: int,
    user_token: str,
    token_address: str,
    message: str = None,
    operation_id: str = None
):
    """
    处理礼物购买的 Celery 任务
    
    Args:
        user_id: 用户ID
        channel_name: 频道名称
        channel_id: 频道ID（数据库主键）
        gift_id: 礼物ID
        quantity: 购买数量
        user_token: 用户认证token
        message: 可选的消息内容
        operation_id: 客户端生成的唯一操作ID
    """
    logger.info(f"开始处理礼物购买任务: user_id={user_id}, operation_id={operation_id}, gift_id={gift_id}, quantity={quantity}, channel={channel_name}, channel_id={channel_id}")
    
    try:
        # 直接调用同步函数处理礼物购买
        result = _process_gift_purchase(user_id, channel_name, channel_id, gift_id, quantity, user_token, token_address, message, operation_id)
        logger.info(f"礼物购买任务完成: user_id={user_id}, operation_id={operation_id}, result={result}")
        return result
            
    except Exception as e:
        logger.error(f"礼物购买任务失败: user_id={user_id}, operation_id={operation_id}, error={str(e)}")
        # 这里可以添加失败通知或重试逻辑
        raise


def _process_gift_purchase(
    user_id: str,
    channel_name: str,
    channel_id: int,
    gift_id: int,
    quantity: int,
    user_token: str,
    token_address: str,
    message: str = None,
    operation_id: str = None
):
    """
    同步处理礼物购买的实际逻辑
    """
    try:
        # 首先检查操作ID是否已存在（额外的安全检查）
        if operation_id:
            with get_sync_context() as session:
                existing_record = session.query(ChannelGiftHistory).filter(
                    ChannelGiftHistory.operation_id == operation_id
                ).first()
                
                if existing_record:
                    logger.warning(f"操作ID已存在，跳过处理: operation_id={operation_id}, user_id={user_id}")
                    return {
                        "success": False,
                        "error": "Duplicate operation ID",
                        "operation_id": operation_id,
                        "existing_txid": existing_record.txid
                    }
        
        # 查询礼物信息以确定价格和类型
        gift_list_record = None
        is_supreme_gift = False
        actual_payment_amount = quantity  # 默认支付全额
        gift_value = 0

        
        with get_sync_context() as session:
            gift_list_record = session.query(GiftList).filter(GiftList.id == gift_id).first()
            if gift_list_record:
                # 计算礼物价值
                if gift_list_record.is_active:
                    gift_price = gift_list_record.price
                    gift_value = gift_price * quantity
                    logger.debug(f"Calculated gift value: {gift_value} = {gift_price} * {quantity}")
                    actual_payment_amount = gift_value
                # 检查是否为SUPREME礼物
                if hasattr(gift_list_record, 'category') and gift_list_record.category == 'supreme':
                    is_supreme_gift = True
                    # 根据礼物价值计算实际支付金额（price存储为cents）
                    if gift_value == 99900:  # $999 stored as 99900 cents
                        actual_payment_amount = 90000  # $900 in cents
                        logger.info(f"检测到999美元SUPREME礼物: 实际支付={actual_payment_amount/100:.0f}美元, 红包={(gift_value - actual_payment_amount)/100:.0f}美元")
                    elif gift_value == 88800:  # $888 stored as 88800 cents
                        actual_payment_amount = 80000  # $800 in cents  
                        logger.info(f"检测到888美元SUPREME礼物: 实际支付={actual_payment_amount/100:.0f}美元, 红包={(gift_value - actual_payment_amount)/100:.0f}美元")
                    else:
                        # 其他supreme礼物保持10%逻辑
                        actual_payment_amount = gift_value * 0.9
                        logger.info(f"检测到其他SUPREME礼物: 实际支付={actual_payment_amount/100:.2f}美元 (90%)")
            else:
                logger.warning(f"Gift {gift_id} not found, using default values")
        
        # 调用 memecoin 服务进行购买
        # actual_payment_amount 是cents格式，需要转换为USD给memecoin服务
        usd_amount = actual_payment_amount / 100  # Convert cents to USD
        buy_txid = memecoin_client.buy_token(
            user_token=user_token,
            token_address=token_address,
            usd_amount_to_buy=str(usd_amount),  # Pass USD as string
        )
        
        logger.info(f"Memecoin 购买成功: user_id={user_id}, operation_id={operation_id}, txid={buy_txid}, is_supreme={is_supreme_gift}")
        
        # 在成功购买后，将礼物信息插入数据库
        with get_sync_context() as session:
            try:
                # 创建礼物记录（包含价值字段）
                gift_record = ChannelGiftHistory(
                    channel_id=channel_id,
                    channel_name=channel_name,
                    tipper=user_id,
                    gift_id=gift_id,
                    txid=buy_txid,
                    gift_count=quantity,
                    gift_value=gift_value,  # 使用之前计算的价值
                    message=message,
                    operation_id=operation_id,
                    created_at=datetime.utcnow()
                )
                
                session.add(gift_record)
                session.commit()
                
                # 检查是否为SUPREME礼物并创建红包
                if is_supreme_gift:
                    logger.info(f"准备创建红包: gift_id={gift_id}, operation_id={operation_id}")
                    try:
                        # 查询频道信息获取token地址
                        channel = session.query(LiveChannel).filter(LiveChannel.channel_name == channel_name).first()
                        if channel and channel.token_address:
                            # 创建红包
                            _create_red_envelope_for_supreme_gift(
                                user_id=user_id,
                                user_token=user_token,
                                channel_name=channel_name,
                                channel_token_address=channel.token_address,
                                gift_total_usd=gift_value,
                                operation_id=operation_id,
                                gift_id=gift_id
                            )
                        else:
                            logger.warning(f"频道 {channel_name} 未找到或没有绑定代币地址，跳过红包创建")
                    except Exception as red_envelope_error:
                        # 红包创建失败不影响礼物购买主流程
                        logger.error(f"创建红包失败，但礼物购买已成功: error={str(red_envelope_error)}")
                
            except Exception as db_error:
                # 显式回滚事务，确保数据一致性
                session.rollback()
                logger.error(f"数据库操作失败，已回滚: user_id={user_id}, operation_id={operation_id}, error={str(db_error)}")
                # 重新抛出异常，让外层处理
                raise db_error
            
        logger.info(f"礼物记录创建成功: user_id={user_id}, operation_id={operation_id}, channel={channel_name}, txid={buy_txid}")
        
        return {
            "success": True,
            "txid": buy_txid,
            "operation_id": operation_id,
            "user_id": user_id,
            "gift_id": gift_id,
            "quantity": quantity,
            "channel_name": channel_name
        }
        
    except Exception as e:
        # 分类处理不同类型的异常
        error_type = type(e).__name__
        error_message = str(e)
        
        # 根据异常类型进行不同的处理
        if "Database" in error_type or "SQL" in error_type:
            logger.error(f"数据库异常: user_id={user_id}, error_type={error_type}, error={error_message}")
            error_category = "database_error"
        elif "Network" in error_type or "Connection" in error_type:
            logger.error(f"网络异常: user_id={user_id}, error_type={error_type}, error={error_message}")
            error_category = "network_error"
        else:
            logger.error(f"未知异常: user_id={user_id}, error_type={error_type}, error={error_message}")
            error_category = "unknown_error"
        
        # 这里可以添加错误处理逻辑，比如：
        # 1. 发送失败通知给用户
        # 2. 记录失败日志用于后续分析
        # 3. 如果是网络错误，可以考虑重试
        
        return {
            "success": False,
            "error": error_message,
            "error_type": error_type,
            "error_category": error_category,
            "user_id": user_id,
            "gift_id": gift_id,
            "quantity": quantity,
            "channel_name": channel_name
        }


def _create_red_envelope_for_supreme_gift(
    user_id: str,
    user_token: str,
    channel_name: str,
    channel_token_address: str,
    gift_total_usd: float,
    operation_id: str,
    gift_id: int
):
    """
    为至尊礼物创建红包（同步版本）
    
    Args:
        user_id: 购买礼物的用户ID
        user_token: 用户认证token
        channel_name: 频道名称
        channel_token_address: 频道绑定的代币地址
        gift_total_usd: 礼物总价值(USD)
        operation_id: 操作ID（用于去重和追踪）
        gift_id: 礼物ID（用于获取礼物名称和图标）
    """
    try:
        # 获取用户信息和礼物信息
        with get_sync_context() as session:
            user = session.query(Author).filter(Author.id == user_id).first()
            if not user:
                logger.error(f"未找到用户: user_id={user_id}")
                raise ValueError(f"User not found: {user_id}")
            
            creator_name = user.username or f"User{user_id[:8]}"
            creator_avatar = user.avatar or ""
            
            # 获取礼物信息
            gift_name = None
            gift_image = None
            gift_record = session.query(GiftList).filter(GiftList.id == gift_id).first()
            if gift_record:
                meta = gift_record.meta or {}
                gift_name = meta.get('name', f'Gift {gift_id}')
                gift_image = gift_record.cover  # cover 字段存储礼物图标URL
                logger.debug(f"获取礼物信息: gift_id={gift_id}, name={gift_name}, image={gift_image}")
            else:
                logger.warning(f"未找到礼物信息: gift_id={gift_id}")
                gift_name = f'Gift {gift_id}'
        
        # 根据礼物总价值计算红包金额和份数（gift_total_usd实际为cents）
        if gift_total_usd == 99900:  # $999 stored as 99900 cents
            red_envelope_amount_usd = 99  # Convert to USD for distribution
            num_parts = 50
            logger.info(f"999美元礼物: 红包金额={red_envelope_amount_usd}美元, 份数={num_parts}")
        elif gift_total_usd == 88800:  # $888 stored as 88800 cents
            red_envelope_amount_usd = 88  # Convert to USD for distribution
            num_parts = 44
            logger.info(f"888美元礼物: 红包金额={red_envelope_amount_usd}美元, 份数={num_parts}")
        else:
            # 其他supreme礼物保持10%逻辑，将cents转换为USD
            red_envelope_amount_usd = (gift_total_usd * 0.1) / 100  # Convert cents to USD
            num_parts = 88
            logger.info(f"其他supreme礼物: 红包金额={red_envelope_amount_usd}美元 (10%), 份数={num_parts}")
        
        # 生成红包ID与时间
        envelope_id = str(uuid.uuid4())
        created_at = datetime.utcnow()
        expires_at = created_at + timedelta(seconds=60)
        
        # 分配红包金额到指定份数
        envelope_parts = _distribute_red_envelope(red_envelope_amount_usd, num_parts)
        
        # 连接Redis
        redis_client = RedisCli.sync()
        
        # 准备红包数据
        envelope_data = {
            'id': envelope_id,
            'creator_user_id': user_id,
            'creator_name': creator_name,
            'creator_avatar': creator_avatar,
            'channel_name': channel_name,
            'token_address': channel_token_address,
            'total_amount': str(red_envelope_amount_usd),
            'usd_amount': red_envelope_amount_usd,
            'parts': envelope_parts,
            'claimed_parts': [],
            'claimed_by': {},  # user_id -> claimed_amount
            'created_at': created_at.isoformat(),
            'operation_id': operation_id,
            'status': 'active',  # active, expired, completed
            'first_claim_at': None,
            'expires_at': expires_at.isoformat()
        }
        
        # 存储到Redis，设置3分钟过期
        redis_key = f"red_envelope:{envelope_id}"
        redis_client.setex(
            redis_key,
            180,  # 3分钟过期
            json.dumps(envelope_data)
        )
        
        # 将红包ID添加到频道的红包列表
        channel_envelopes_key = f"channel_red_envelopes:{channel_name}"
        redis_client.sadd(channel_envelopes_key, envelope_id)
        redis_client.expire(channel_envelopes_key, 180)  # 3分钟过期

        # 调度过期任务：无论是否有人领取，60秒后过期
        try:
            expire_red_envelope_task.apply_async(args=[envelope_id], eta=expires_at)
        except Exception as schedule_error:
            logger.error(f"调度红包过期任务失败: envelope_id={envelope_id}, error={str(schedule_error)}")
        
        # 获取token信息
        token_name = None
        token_symbol = None
        try:
            # 导入memecoin_client并获取token信息
            import httpx
            from src.agora.settings import settings
            
            # 调用memecoin服务获取token信息
            async def get_token_info_sync(token_address: str):
                url = f"{settings.MEMECOIN_SERVICE_URL}/token"
                params = {"token_address": token_address}
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(url, params=params)
                    if response.status_code == 200:
                        return response.json()
                    return None
            
            # 使用同步方式调用异步函数
            import asyncio
            try:
                token_info = asyncio.run(get_token_info_sync(channel_token_address))
                if token_info:
                    token_name = token_info.get('name')
                    token_symbol = token_info.get('symbol')
                    logger.debug(f"获取到token信息: name={token_name}, symbol={token_symbol}")
            except Exception as token_error:
                logger.warning(f"获取token信息失败: {str(token_error)}，将使用默认值")
        except Exception as e:
            logger.warning(f"获取token信息时发生异常: {str(e)}，将使用默认值")
        
        # 发送websocket通知给移动端APP（包括Redis发布）
        _send_red_envelope_notification_sync(
            user_id=user_id,
            creator_name=creator_name,
            creator_avatar=creator_avatar,
            channel_name=channel_name,
            token_address=channel_token_address,
            token_amount=str(0),
            usd_amount=red_envelope_amount_usd,
            operation_id=operation_id,
            envelope_id=envelope_id,
            gift_name=gift_name,
            gift_image=gift_image,
            token_name=token_name,
            token_symbol=token_symbol
        )
        
        logger.info(
            f"红包创建成功: envelope_id={envelope_id}, user={user_id}, channel={channel_name}, "
            f"usd_amount=${red_envelope_amount_usd:.2f}, token_amount={0}, operation_id={operation_id}"
        )
        
    except Exception as e:
        logger.error(f"创建红包失败: user={user_id}, error={str(e)}")
        raise


def _distribute_red_envelope(total_amount: float, num_parts: int = 88) -> List[str]:
    """
    将金额分成指定份数的红包

    Args:
        total_amount: 总金额（美元）
        num_parts: 红包份数（默认88份）

    Returns:
        List[str]: 指定数量的红包金额，格式为 "x.xx"

    Example:
        >>> envelopes = distribute_red_envelope(88, 44)
        >>> print(envelopes[:5])  # ['1.23', '1.45', '2.34', ...]
    """
    CENTS = 100
    MIN_CENT = 100  # $1.00
    MIN_TOTAL_CENTS = num_parts * MIN_CENT  # 最小总金额

    total_cent = int(round(total_amount * CENTS))

    if total_cent < MIN_TOTAL_CENTS:
        raise ValueError(f"总金额不足：{num_parts}份至少需要 ${MIN_TOTAL_CENTS / CENTS:.2f}")

    available = total_cent - MIN_TOTAL_CENTS
    if available == 0:
        return ["1.00"] * num_parts

    # 根据金额选择分布策略
    if total_amount < 50:
        # 小额红包：使用均匀一些的分布
        weights = np.random.uniform(0.8, 1.5, num_parts)
    else:
        # 大额红包：使用对数正态分布，差异更大
        weights = np.random.lognormal(0, 0.6, num_parts)

    weights = weights / weights.sum()

    # 累积和分配
    cum = (weights.cumsum() * available).astype(np.int64)
    parts = np.diff(np.concatenate(([0], cum)))
    parts[-1] += available - parts.sum()  # 修正舍入误差
    parts += MIN_CENT

    # 处理约束
    max_single = total_cent // 2
    over_max = parts > max_single
    if np.any(over_max):
        excess = np.sum(parts[over_max] - max_single)
        parts[over_max] = max_single

        # 重分配
        under_max = parts < max_single
        if np.any(under_max):
            can_add = np.minimum(max_single - parts[under_max], excess)
            parts[under_max] += can_add

    # 打乱并格式化
    np.random.shuffle(parts)
    return [f"{p / CENTS:.2f}" for p in parts]


def _send_red_envelope_notification_sync(
    user_id: str,
    creator_name: str,
    creator_avatar: str,
    channel_name: str,
    token_address: str,
    token_amount: str,
    usd_amount: float,
    operation_id: str,
    envelope_id: str,
    gift_name: str = None,
    gift_image: str = None,
    token_name: str = None,
    token_symbol: str = None
):
    """
    发送红包创建的websocket通知（同步版本）
    
    Args:
        user_id: 创建红包的用户ID
        creator_name: 创建者用户名 (即hostname)
        creator_avatar: 创建者头像URL (即avatar)
        channel_name: 频道名称
        token_address: 代币地址
        token_amount: 代币数量
        usd_amount: USD金额
        operation_id: 操作ID
        envelope_id: 红包ID
        gift_name: 礼物名称（可选）
        gift_image: 礼物图标URL（可选）
        token_name: 代币名称（可选）
        token_symbol: 代币符号（可选）
    """
    try:
        # 发布Redis消息（同步方式）
        _publish_red_envelope_created_sync(
            envelope_id=envelope_id,
            channel_name=channel_name,
            creator_user_id=user_id,
            creator_name=creator_name,
            creator_avatar=creator_avatar,
            token_address=token_address,
            total_amount=token_amount,
            usd_amount=usd_amount,
            operation_id=operation_id,
            gift_name=gift_name,
            gift_image=gift_image,
            token_name=token_name,
            token_symbol=token_symbol
        )
        
        # 计算创建时间和过期时间
        created_at = datetime.utcnow()
        expires_at = created_at + timedelta(seconds=60)
        
        # 构造红包通知消息（用于RTM发送）
        notification_message = {
            "type": "red_envelope_created",
            "data": {
                "envelope_id": envelope_id,
                "creator_user_id": user_id,
                "channel_name": channel_name,
                "token_address": token_address,
                "token_amount": token_amount,
                "usd_amount": usd_amount,
                "operation_id": operation_id,
                "gift_name": gift_name,
                "gift_image": gift_image,
                "total_parts": 88,
                "claimed_parts": 0,
                "status": "active",
                "created_at": created_at.isoformat(),
                "expires_at": expires_at.isoformat(),
                "message": f"A red envelope worth ${usd_amount:.2f} has been created in {channel_name}!"
            }
        }
        
        # 创建一个新的Celery任务来发送RTM通知
        send_red_envelope_notification_task.delay(
            channel_name=channel_name,
            notification_message=notification_message
        )
        
        logger.info(f"红包通知已提交到队列: channel={channel_name}")
        
    except Exception as e:
        logger.error(f"发送红包通知失败: {str(e)}")
        # 通知失败不影响主流程


def _publish_red_envelope_created_sync(
    envelope_id: str,
    channel_name: str,
    creator_user_id: str,
    creator_name: str,
    creator_avatar: str,
    token_address: str,
    total_amount: str,
    usd_amount: float,
    operation_id: str,
    gift_name: str = None,
    gift_image: str = None,
    token_name: str = None,
    token_symbol: str = None
):
    """
    同步发布红包创建的Redis消息
    
    Args:
        envelope_id: 红包ID
        channel_name: 频道名称
        creator_user_id: 创建者用户ID
        creator_name: 创建者用户名 (即hostname)
        creator_avatar: 创建者头像URL (即avatar)
        token_address: 代币地址
        total_amount: 代币总数量
        usd_amount: USD金额
        operation_id: 操作ID
        gift_name: 礼物名称（可选）
        gift_image: 礼物图标URL（可选）
        token_name: 代币名称（可选）
        token_symbol: 代币符号（可选）
    """
    try:
        # 使用同步版本的Redis消息发布
        from src.agora.redis_messenger import redis_messenger
        
        redis_messenger.publish_red_envelope_created_sync(
            envelope_id=envelope_id,
            channel_name=channel_name,
            creator_user_id=creator_user_id,
            creator_name=creator_name,
            creator_avatar=creator_avatar,
            token_address=token_address,
            total_amount=total_amount,
            usd_amount=usd_amount,
            operation_id=operation_id,
            gift_name=gift_name,
            gift_image=gift_image,
            token_name=token_name,
            token_symbol=token_symbol
        )
        
        logger.info(f"Redis红包创建消息发布成功: envelope_id={envelope_id}")
        
    except Exception as e:
        logger.error(f"发布Redis红包创建消息失败: {str(e)}")
        # Redis发布失败不影响主流程，只记录错误


@celery_app.task(name="src.worker.tasks.agora.send_red_envelope_notification_task")
def send_red_envelope_notification_task(
    channel_name: str,
    notification_message: dict
):
    """
    发送红包通知的Celery任务
    
    Args:
        channel_name: 频道名称
        notification_message: 通知消息内容
    """
    logger.info(f"发送红包通知到频道: {channel_name}")
    
    try:
        # TODO: 实现RTM消息发送
        # 这里需要调用Agora RTM API发送消息到指定频道
        # 由于需要异步操作，可能需要使用asyncio.run()或创建事件循环
        
        logger.info(f"红包通知发送成功: channel={channel_name}")
        return {"success": True, "channel": channel_name}
        
    except Exception as e:
        logger.error(f"发送红包通知失败: channel={channel_name}, error={str(e)}")
        raise


@celery_app.task(name="src.worker.tasks.agora.expire_red_envelope_task")
def expire_red_envelope_task(envelope_id: str):
    """
    处理红包过期逻辑，将未领取的金额返还给发送者
    
    Args:
        envelope_id: 红包ID
    """
    logger.info(f"处理红包过期: envelope_id={envelope_id}")
    
    try:
        # 连接Redis
        from src.common.redis_cli import RedisCli
        redis_client = RedisCli.sync()
        
        # 获取红包数据
        redis_key = f"red_envelope:{envelope_id}"
        envelope_data = redis_client.get(redis_key)
        
        if not envelope_data:
            logger.warning(f"红包不存在或已过期: envelope_id={envelope_id}")
            return
        
        envelope = json.loads(envelope_data)
        
        # 检查状态
        if envelope['status'] != 'active':
            logger.info(f"红包状态不是active，无需处理: envelope_id={envelope_id}, status={envelope['status']}")
            return
        
        # 计算未领取的金额
        total_amount = float(envelope['total_amount'])
        claimed_amount = sum(float(amount) for amount in envelope['claimed_by'].values())
        remaining_amount = total_amount - claimed_amount
        
        if remaining_amount > 0:
            # TODO: 调用memecoin服务将剩余金额返还给创建者
            logger.info(f"返还剩余金额给创建者: envelope_id={envelope_id}, "
                       f"creator={envelope['creator_user_id']}, amount={remaining_amount}")
            
            # 这里应该调用实际的转账接口
            # memecoin_client.send_asset_sync(
            #     from_system=True,
            #     to_user_id=envelope['creator_user_id'],
            #     token_address=envelope['token_address'],
            #     amount=str(remaining_amount)
            # )
        
        # 更新红包状态为已过期
        envelope['status'] = 'expired'
        envelope['expired_at'] = datetime.utcnow().isoformat()
        envelope['remaining_amount'] = str(remaining_amount)
        envelope['claimed_count'] = len(envelope['claimed_by'])
        
        # 更新Redis
        redis_client.setex(redis_key, 180, json.dumps(envelope))
        
        # 从频道的活跃红包列表中移除
        channel_envelopes_key = f"channel_red_envelopes:{envelope['channel_name']}"
        redis_client.srem(channel_envelopes_key, envelope_id)
        
        # 发送过期通知（包括Redis发布）
        _send_red_envelope_expired_notification_sync(
            envelope_id=envelope_id,
            channel_name=envelope['channel_name'],
            remaining_amount=remaining_amount,
            claimed_count=len(envelope['claimed_by']),
            claimed_by=envelope.get('claimed_by', {})
        )
        
        logger.info(f"红包过期处理完成: envelope_id={envelope_id}, "
                   f"claimed={len(envelope['claimed_by'])}/88, "
                   f"returned={remaining_amount}")
        
    except Exception as e:
        logger.error(f"处理红包过期失败: envelope_id={envelope_id}, error={str(e)}")
        raise


def _send_red_envelope_expired_notification_sync(
    envelope_id: str,
    channel_name: str,
    remaining_amount: float,
    claimed_count: int,
    claimed_by: Dict[str, str] = None
):
    """发送红包过期通知（同步版本）"""
    try:
        # 构建领取详情（包含用户信息）
        claim_details = []
        if claimed_by:
            # 批量获取用户信息
            user_ids = list(claimed_by.keys())
            users_info = _get_users_info_batch_sync(user_ids)
            
            for user_id, amount in claimed_by.items():
                user_info = users_info.get(user_id, {})
                claim_details.append({
                    "user_id": user_id,
                    "username": user_info.get("username", f"User_{user_id[:8]}"),
                    "avatar": user_info.get("avatar"),
                    "amount": amount
                })
            # 按金额降序排序
            claim_details.sort(key=lambda x: float(x["amount"]), reverse=True)
        
        # 发布Redis消息（使用新的参数）
        _publish_red_envelope_expired_sync(
            envelope_id=envelope_id,
            channel_name=channel_name,
            remaining_amount=remaining_amount,
            claimed_count=claimed_count,
            claim_details=claim_details
        )
        
        notification_message = {
            "type": "red_envelope_expired",
            "data": {
                "envelope_id": envelope_id,
                "channel_name": channel_name,
                "remaining_amount": str(remaining_amount),
                "claimed_count": claimed_count,
                "total_parts": 88,
                "claim_details": claim_details,
                "expired_at": datetime.utcnow().isoformat(),
                "message": f"Red envelope {envelope_id} has expired. {claimed_count}/88 parts were claimed."
            }
        }
        
        # 创建Celery任务发送通知
        send_red_envelope_notification_task.delay(
            channel_name=channel_name,
            notification_message=notification_message
        )
        
        logger.info(f"红包过期通知已提交: envelope_id={envelope_id}")
        
    except Exception as e:
        logger.error(f"发送红包过期通知失败: {str(e)}")


def _get_users_info_batch_sync(user_ids: List[str]) -> Dict[str, Dict[str, str]]:
    """
    同步批量获取用户信息
    
    Args:
        user_ids: 用户ID列表
        
    Returns:
        Dict[str, Dict[str, str]]: 用户ID到用户信息的映射
    """
    if not user_ids:
        return {}
    
    try:
        from sqlalchemy import select
        
        with get_sync_context() as session:
            # 批量查询Author表
            stmt = select(Author.user_id, Author.name, Author.username, Author.avatar).where(Author.user_id.in_(user_ids))
            result = session.execute(stmt)
            authors = result.fetchall()
            
            # 构建映射
            users_info = {}
            for user_id, name, username, avatar in authors:
                users_info[user_id] = {
                    "name": name,
                    "username": username or f"User_{user_id[:8]}",
                    "avatar": avatar
                }
            
            # 为没有找到的用户设置默认值
            for user_id in user_ids:
                if user_id not in users_info:
                    users_info[user_id] = {
                        "name": None,
                        "username": f"User_{user_id[:8]}",
                        "avatar": None
                    }
            
            return users_info
            
    except Exception as e:
        logger.error(f"批量获取用户信息失败: {str(e)}")
        # 错误情况下返回默认值
        return {
            user_id: {
                "name": None,
                "username": f"User_{user_id[:8]}",
                "avatar": None
            }
            for user_id in user_ids
        }


def _publish_red_envelope_expired_sync(
    envelope_id: str,
    channel_name: str,
    remaining_amount: float,
    claimed_count: int,
    claim_details: List[Dict[str, Any]]
):
    """
    同步发布红包过期的Redis消息
    """
    try:
        from src.agora.redis_messenger import redis_messenger
        
        redis_messenger.publish_red_envelope_expired_sync(
            envelope_id=envelope_id,
            channel_name=channel_name,
            remaining_amount=remaining_amount,
            claimed_count=claimed_count,
            claim_details=claim_details
        )
        
        logger.info(f"Redis红包过期消息发布成功: envelope_id={envelope_id}")
        
    except Exception as e:
        logger.error(f"发布Redis红包过期消息失败: {str(e)}")


@celery_app.task(name="src.worker.tasks.agora.send_gift_notification_task")
def send_gift_notification_task(
    channel_name: str,
    sender_user_id: str,
    gift_id: int,
    quantity: int,
    txid: str
):
    """
    发送礼物通知的 Celery 任务
    
    Args:
        channel_name: 频道名称
        sender_user_id: 发送者用户ID
        gift_id: 礼物ID
        quantity: 数量
        txid: 交易ID
    """
    logger.info(f"发送礼物通知: channel={channel_name}, sender={sender_user_id}, gift_id={gift_id}")
    
    try:
        # 这里可以添加实际的通知逻辑，比如：
        # 1. 发送 RTM 消息到频道
        # 2. 推送通知给主播
        # 3. 更新直播间礼物显示
        
        # 示例：可以调用 RTM 发送消息
        # rtm_message = {
        #     "type": "gift",
        #     "gift_id": gift_id,
        #     "quantity": quantity,
        #     "sender": sender_user_id,
        #     "txid": txid
        # }
        # await send_rtm_channel_message(channel_name, rtm_message)
        
        logger.info(f"礼物通知发送成功: channel={channel_name}, txid={txid}")
        return {"success": True, "txid": txid}
        
    except Exception as e:
        logger.error(f"发送礼物通知失败: channel={channel_name}, error={str(e)}")
        raise 