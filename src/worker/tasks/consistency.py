"""
Celery tasks for saved_posts consistency checking and maintenance
"""
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.worker.celery_app import celery_app
from src.common.consistency.saved_posts_validator import SavedPostsConsistencyService
from src.common.settings import settings

logger = logging.getLogger(__name__)


def create_async_session():
    """Create async database session for Celery tasks"""
    engine = create_async_engine(settings.DATABASE_URL_ASYNC)
    SessionLocal = sessionmaker(
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    return SessionLocal(), engine


@celery_app.task(bind=True, name="consistency.check_saved_posts")
def check_saved_posts_consistency(
    self,
    collection_ids: Optional[List[str]] = None,
    auto_repair: bool = False,
    dry_run: bool = True
) -> Dict[str, Any]:
    """
    Check saved_posts consistency and optionally repair issues
    
    Args:
        collection_ids: Optional list of specific collection IDs to check
        auto_repair: Whether to automatically repair found issues
        dry_run: If auto_repair is True, whether to run in dry-run mode
    
    Returns:
        Dictionary with consistency report and repair results
    """
    task_id = self.request.id
    logger.info(f"Task {task_id}: Starting saved_posts consistency check")
    
    async def _run_check():
        session, engine = create_async_session()
        try:
            service = SavedPostsConsistencyService(session)
            
            # Run consistency check
            report = await service.check_consistency(collection_ids)
            
            logger.info(f"Task {task_id}: Found {report.total_issues} consistency issues")
            
            result = {
                "task_id": task_id,
                "started_at": datetime.utcnow().isoformat(),
                "consistency_report": report.get_summary(),
                "issues": {
                    "missing_saved_posts": len(report.missing_saved_posts),
                    "orphaned_saved_posts": len(report.orphaned_saved_posts),
                    "incorrect_counts": len(report.incorrect_counts)
                }
            }
            
            # Auto-repair if requested
            if auto_repair and report.has_issues:
                logger.info(f"Task {task_id}: Starting auto-repair (dry_run={dry_run})")
                
                repair_counts = await service.repair_inconsistencies(report, dry_run=dry_run)
                result["repair_performed"] = True
                result["repair_dry_run"] = dry_run
                result["repair_counts"] = repair_counts
                
                logger.info(f"Task {task_id}: Repair completed: {repair_counts}")
            else:
                result["repair_performed"] = False
                result["repair_reason"] = "auto_repair=False" if not auto_repair else "no_issues_found"
            
            result["completed_at"] = datetime.utcnow().isoformat()
            logger.info(f"Task {task_id}: Consistency check completed successfully")
            
            return result
        finally:
            await session.close()
            await engine.dispose()
    
    try:
        return asyncio.run(_run_check())
    except Exception as e:
        logger.error(f"Task {task_id}: Error in consistency check: {str(e)}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="consistency.repair_saved_posts")
def repair_saved_posts_consistency(
    self,
    collection_ids: Optional[List[str]] = None,
    dry_run: bool = False
) -> Dict[str, Any]:
    """
    Repair saved_posts consistency issues
    
    Args:
        collection_ids: Optional list of specific collection IDs to repair
        dry_run: Whether to run in dry-run mode (no actual changes)
    
    Returns:
        Dictionary with repair results
    """
    task_id = self.request.id
    logger.info(f"Task {task_id}: Starting saved_posts consistency repair (dry_run={dry_run})")
    
    async def _run_repair():
        session, engine = create_async_session()
        try:
            service = SavedPostsConsistencyService(session)
            
            # First, check for consistency issues
            report = await service.check_consistency(collection_ids)
            
            if not report.has_issues:
                logger.info(f"Task {task_id}: No consistency issues found")
                return {
                    "task_id": task_id,
                    "started_at": datetime.utcnow().isoformat(),
                    "completed_at": datetime.utcnow().isoformat(),
                    "issues_found": 0,
                    "repair_performed": False,
                    "reason": "no_issues_found"
                }
            
            # Perform repairs
            repair_counts = await service.repair_inconsistencies(report, dry_run=dry_run)
            
            result = {
                "task_id": task_id,
                "started_at": datetime.utcnow().isoformat(),
                "completed_at": datetime.utcnow().isoformat(),
                "issues_found": report.total_issues,
                "repair_performed": True,
                "dry_run": dry_run,
                "repair_counts": repair_counts,
                "consistency_report": report.get_summary()
            }
            
            logger.info(f"Task {task_id}: Repair completed: {repair_counts}")
            return result
        finally:
            await session.close()
            await engine.dispose()
    
    try:
        return asyncio.run(_run_repair())
    except Exception as e:
        logger.error(f"Task {task_id}: Error in consistency repair: {str(e)}")
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="consistency.get_metrics")
def get_consistency_metrics(self) -> Dict[str, Any]:
    """
    Get consistency metrics for monitoring
    
    Returns:
        Dictionary with consistency metrics
    """
    task_id = self.request.id
    logger.info(f"Task {task_id}: Getting consistency metrics")
    
    async def _get_metrics():
        session, engine = create_async_session()
        try:
            service = SavedPostsConsistencyService(session)
            metrics = await service.get_consistency_metrics()
            
            result = {
                "task_id": task_id,
                "timestamp": datetime.utcnow().isoformat(),
                "metrics": metrics
            }
            
            logger.info(f"Task {task_id}: Retrieved consistency metrics")
            return result
        finally:
            await session.close()
            await engine.dispose()
    
    try:
        return asyncio.run(_get_metrics())
    except Exception as e:
        logger.error(f"Task {task_id}: Error getting consistency metrics: {str(e)}")
        raise self.retry(exc=e, countdown=30, max_retries=2)


# Periodic task to check consistency (runs every 6 hours)
@celery_app.task(bind=True, name="consistency.periodic_check")
def periodic_consistency_check(self) -> Dict[str, Any]:
    """
    Periodic consistency check task (intended to be run via celery beat)
    
    Returns:
        Dictionary with check results
    """
    task_id = self.request.id
    logger.info(f"Task {task_id}: Running periodic consistency check")
    
    # Run the main consistency check task
    return check_saved_posts_consistency.apply_async(
        kwargs={
            "collection_ids": None,  # Check all collections
            "auto_repair": False,    # Don't auto-repair in periodic checks
            "dry_run": True         # Always dry run for periodic checks
        }
    ).get()


# Task to validate specific collections after bulk operations
@celery_app.task(bind=True, name="consistency.validate_collections")
def validate_collections_consistency(
    self,
    collection_ids: List[str],
    auto_repair: bool = True
) -> Dict[str, Any]:
    """
    Validate consistency for specific collections (e.g., after bulk operations)
    
    Args:
        collection_ids: List of collection IDs to validate
        auto_repair: Whether to automatically repair issues
    
    Returns:
        Dictionary with validation results
    """
    task_id = self.request.id
    logger.info(f"Task {task_id}: Validating consistency for {len(collection_ids)} collections")
    
    return check_saved_posts_consistency.apply_async(
        kwargs={
            "collection_ids": collection_ids,
            "auto_repair": auto_repair,
            "dry_run": False  # Actual repairs for targeted validation
        }
    ).get()