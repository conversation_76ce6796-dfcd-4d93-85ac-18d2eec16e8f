"""
搜索相关任务（ES版本）
"""
import asyncio
from datetime import datetime

from src.worker.celery_app import celery_app
from src.worker.logger import logger
from src.common.elasticsearch import get_elasticsearch_client, SearchHistoryIndexManager
from src.common.utils import detect_language


@celery_app.task(name="src.worker.tasks.search.record_search_task")
def record_search_task(query: str, search_type: str = None, user_id: str = None, results_count: int = 0):
    """
    将用户搜索记录保存到Elasticsearch
    
    Args:
        query: 搜索查询
        search_type: 搜索类型
        user_id: 用户ID（可选）
        results_count: 搜索结果数量
    """
    logger.info(f"记录搜索到ES: query='{query}', type={search_type}")
    
    try:
        # 由于这是同步任务，需要使用asyncio.run运行异步代码
        result = asyncio.run(_record_search_to_es_async(query, search_type, user_id, results_count))
        if result:
            logger.info(f"成功记录搜索历史到ES: query='{query}', type={search_type}")
            return "搜索记录到ES完成"
        else:
            logger.warning(f"记录搜索历史到ES失败: query='{query}', type={search_type}")
            return "搜索记录到ES失败"
    except Exception as e:
        logger.error(f"记录搜索历史任务失败: {e}")
        raise


async def _record_search_to_es_async(query: str, search_type: str = None, user_id: str = None, results_count: int = 0) -> bool:
    """
    异步将搜索记录存储到ES的内部函数
    """
    try:
        # 获取ES客户端
        es_client = await get_elasticsearch_client()
        if not es_client:
            logger.warning("ES客户端获取失败")
            return False
        
        # 创建搜索历史索引管理器
        search_history_manager = SearchHistoryIndexManager(es_client.client)
        
        # 确保当前索引存在
        current_index = await search_history_manager.ensure_current_index_exists()
        
        # 使用common utils中的语言检测
        language = detect_language(query)
        
        # 构建搜索历史文档
        doc = {
            "query": query,
            "search_type": search_type,
            "language": language,
            "created_at": datetime.utcnow().isoformat(),
            "results_count": results_count
        }
        
        # 添加可选字段
        if user_id:
            doc["user_id"] = user_id
        
        # 写入ES
        await es_client.index_document(
            index=current_index,
            document=doc
        )
        
        return True
        
    except Exception as e:
        logger.error(f"异步记录搜索历史到ES失败: {e}")
        return False


@celery_app.task(name="src.worker.tasks.search.cleanup_search_history_indices_task")
def cleanup_search_history_indices_task(keep_months: int = 3):
    """
    清理旧的搜索历史索引
    
    Args:
        keep_months: 保留几个月的数据，默认3个月
    """
    logger.info(f"开始清理搜索历史索引，保留{keep_months}个月的数据")
    
    try:
        # 由于这是同步任务，需要使用asyncio.run运行异步代码
        result = asyncio.run(_cleanup_search_history_indices_async(keep_months))
        if result:
            logger.info("搜索历史索引清理完成")
            return "搜索历史索引清理完成"
        else:
            logger.warning("搜索历史索引清理失败")
            return "搜索历史索引清理失败"
    except Exception as e:
        logger.error(f"搜索历史索引清理任务失败: {e}")
        raise


async def _cleanup_search_history_indices_async(keep_months: int = 3) -> bool:
    """
    异步清理搜索历史索引的内部函数
    """
    try:
        # 获取ES客户端
        es_client = await get_elasticsearch_client()
        if not es_client:
            logger.warning("ES客户端获取失败")
            return False
        
        # 创建搜索历史索引管理器
        search_history_manager = SearchHistoryIndexManager(es_client.client)
        
        # 清理旧索引
        result = await search_history_manager.delete_old_indices(keep_months)
        
        return result
        
    except Exception as e:
        logger.error(f"异步清理搜索历史索引失败: {e}")
        return False 