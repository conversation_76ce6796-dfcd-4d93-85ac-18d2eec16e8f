import time
from decimal import Decimal, ROUND_DOWN
from typing import Optional, Any

from redis.asyncio import Redis
from starlette.websockets import WebSocket

from src.common.constants import MEMECOIN_REDIS_ENDPOINT
from src.memecoin.constants import BNB_USD_PRICE, SOL_USD_PRICE, ETH_USD_PRICE
from src.memecoin.settings import settings

_last_bnb_price: Optional[float] = None
_last_bnb_price_ts: float = 0

_last_sol_price: Optional[float] = None
_last_sol_price_ts: float = 0

_last_eth_price: Optional[float] = None
_last_eth_price_ts: float = 0


def decimal_to_string(decimal_value: Decimal, places: int | None = None) -> str:
    """
    Convert a Decimal to a regular string, avoiding scientific notation (e.g., 0E-66).
    Optionally round to a fixed number of decimal places.
    """
    if decimal_value.is_zero():
        return "0" if not places else f"{0:.{places}f}"

    if places is not None:
        # 保留指定的小数位，使用量化（quantize）
        q = Decimal("1." + "0" * places)  # 比如 places=3 -> Decimal("1.000")
        value = decimal_value.quantize(q, rounding=ROUND_DOWN)
        return format(value, f".{places}f")
    else:
        # 不指定就直接格式化为普通字符串
        normalized = decimal_value.normalize()
        return format(normalized, "f")


async def get_bnb_price(client: Redis) -> float:
    """
    Retrieve the BNB price from Redis or return the default price.
    """
    global _last_bnb_price, _last_bnb_price_ts
    now = time.time()
    # If the cache exists and is not older than 5 minutes (300 seconds), return it.
    if _last_bnb_price is not None and (now - _last_bnb_price_ts) < 300:
        return _last_bnb_price

    try:
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{BNB_USD_PRICE}:price"
        price_list = await client.zrevrange(key, 0, 0)
        if not price_list:
            _last_bnb_price = settings.DEFAULT_BNB_PRICE
        else:
            _last_bnb_price = float(
                price_list[0].decode()
                if isinstance(price_list[0], bytes)
                else price_list[0]
            )
    except Exception as e:
        _last_bnb_price = settings.DEFAULT_BNB_PRICE

    # Update the timestamp for the cache.
    _last_bnb_price_ts = now
    return _last_bnb_price


async def get_sol_price(client: Redis) -> float:
    """
    Retrieve the SOL price from Redis or return the default price.
    """
    global _last_sol_price, _last_sol_price_ts
    now = time.time()
    # If the cache exists and is not older than 5 minutes (300 seconds), return it.
    if _last_sol_price is not None and (now - _last_sol_price_ts) < 300:
        return _last_sol_price

    try:
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{SOL_USD_PRICE}:price"
        price_list = await client.zrevrange(key, 0, 0)
        if not price_list:
            _last_sol_price = settings.DEFAULT_SOL_PRICE
        else:
            _last_sol_price = float(
                price_list[0].decode()
                if isinstance(price_list[0], bytes)
                else price_list[0]
            )
    except Exception as e:
        _last_sol_price = settings.DEFAULT_SOL_PRICE

    # Update the timestamp for the cache.
    _last_sol_price_ts = now
    return _last_sol_price


async def get_eth_price(client: Redis) -> float:
    """
    Retrieve the ETH price from Redis or return the default price.
    """
    global _last_eth_price, _last_eth_price_ts
    now = time.time()
    # If the cache exists and is not older than 5 minutes (300 seconds), return it.
    if _last_eth_price is not None and (now - _last_eth_price_ts) < 300:
        return _last_eth_price

    try:
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{ETH_USD_PRICE}:price"
        price_list = await client.zrevrange(key, 0, 0)
        if not price_list:
            _last_eth_price = settings.DEFAULT_ETH_PRICE
        else:
            _last_eth_price = float(
                price_list[0].decode()
                if isinstance(price_list[0], bytes)
                else price_list[0]
            )
    except Exception as e:
        _last_eth_price = settings.DEFAULT_ETH_PRICE

    # Update the timestamp for the cache.
    _last_eth_price_ts = now
    return _last_eth_price


async def _get_msg(websocket: WebSocket) -> dict[str, Any]:
    """统一以 JSON 接收首条消息，改进错误处理。"""
    try:
        # 尝试接收 JSON 消息
        message = await websocket.receive_json()
        return message
    except Exception as e:
        # 不要在这里直接关闭连接，让调用者处理
        raise ValueError(f"Failed to receive or parse initial JSON message: {str(e)}")

def _extract_token(headers_auth: str | None, msg: dict | None) -> str | None:
    """从 header 或首条消息里提取 Bearer token。"""
    if headers_auth and headers_auth.startswith("Bearer "):
        return headers_auth[7:]
    if msg:
        auth_msg = msg.get("authorization")
        if isinstance(auth_msg, str) and auth_msg.startswith("Bearer "):
            return auth_msg[7:]
    return None

def _extract_interval(msg: dict) -> int:
    """安全提取 interval，限制最小 1 秒。"""
    iv = msg.get("interval", 5)
    return iv if isinstance(iv, (int, float)) and iv >= 1 else 5


async def get_initial_token_price(_: Optional[Redis] = None) -> float:
    """
    计算初始发行时（曲线未发生交易）基于 bonding curve 的价格（USDT/Token）。
    返回买入含费价（考虑合约 tradingFeeRate 1%），用于种子价格与初始市值估算。
    """
    try:
        # 初始虚拟储备（与合约一致）
        virtual_x = Decimal('1108000000') * (Decimal('10') ** Decimal('18'))  # 1,108,000,000 * 1e18
        virtual_y = Decimal('14_000') * (Decimal('10') ** Decimal('6'))         # 14,000 * 1e6 (USDT)

        # 中间价（USDT/Token，不含费）。单位换算：× 1e12
        scale = Decimal('1000000000000')  # 1e12
        mid = (virtual_y / virtual_x) * scale

        # 手续费（bps -> rate）默认 1%
        fee_bps = Decimal('100')
        fee_rate = fee_bps / Decimal('10000')

        # 买入含费价
        one = Decimal('1')
        buy_after_fee = mid / (one - fee_rate) if (one - fee_rate) > 0 else Decimal('0')
        return float(buy_after_fee) if buy_after_fee > 0 else 0.0
    except Exception:
        return 0.0
