from pydantic_settings import BaseSettings, SettingsConfigDict
from decimal import Decimal


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")

    DATABASE_URL: str
    TIMESCALE_DB_URL: str

    # Blockchain
    ENABLE_CREATE_MEME_LIMIT: bool

    BLOCKCHAIN_TYPE: str
    BSC_RPC_URL: str
    BSC_CHAIN_ID: int
    BSC_CHAIN: str
    SOL_CHAIN: str
    MEME_CONTRACT_ADDRESS: str
    MEME_CONTRACT_ABI_PATH: str
    RELAYER_CONTRACT_ADDRESS: str
    RELAYER_CONTRACT_ABI_PATH: str
    L1_LAUNCHER_CONTRACT_ADDRESS: str
    L1_LAUNCHER_ABI_PATH: str
    ERC20_CONTRACT_ADDRESS: str
    ERC20_CONTRACT_ABI_PATH: str
    KMS_ADDRESS: str
    LAUNCH_SIGN_SECRET: str
    GAS_PAYER_SECRET: str
    GAS_PAYER_PUBKEY: str
    SMART_CONTRACT_VERSION: str

    REWARD_ABI_PATH: str
    REWARD_CONTRACT_ADDRESS: str
    REWARD_OPERATOR_PUBKEY: str
    REWARD_OPERATOR_SECRET: str
    REWARD_SIGNER_SECRET: str

    MORALIS_API_KEY: str

    DEFAULT_LAUNCH_GAS_LIMIT: int = 8_000_000
    DEFAULT_GAS_LIMIT: int = 1_000_000
    DEFAULT_GAS_PRICE: int = 10_000_000
    DEFAULT_DECIMALS: int = 1_000_000_000_000_000_000
    USDT_DECIMALS: int = 1_000_000
    DEFAULT_TOTAL_SUPPLY: int = 1_000_000_000
    DEFAULT_TOKEN_DECIMALS: int = 18

    DEFAULT_BNB_PRICE: float = 606.35
    USDT_ADDRESS: str 
    WBNB_ADDRESS: str = "******************************************"
    # SOLANA
    SOL_RPC_URL: str
    DEFAULT_SOL_PRICE: float = 133.89
    SOLANA_CHAIN_ID: int = 103

    # ETHEREUM
    ETH_L1_RPC_URL: str = "https://tiniest-muddy-dawn.ethereum-hoodi.quiknode.pro/7054570c2452fdd7c4bce0b7c2d27c5b694f507d"
    ETH_RPC_URL: str = "http://memefans-layer2-03.aurora:8545"
    ETH_CHAIN_ID: int = 5918
    DEFAULT_ETH_PRICE: float = 2758.74
    WETH_ADDRESS: str = "******************************************"

    # DEX Subgraph URLs
    MEMESWAP_SUBGRAPH_URL: str = ""

    # Kafka
    KAFKA_BROKERS: str = "meme-kafka-01.aurora:9092"
    KAFKA_MEMETRACE_GROUP_ID: str = "kafka_memetrace_group"
    KAFKA_BSC_TOPIC: str = "kafka_bsc_events"
    KAFKA_SOL_TOPIC: str
    KAFKA_ETH_TOPIC: str = "kafka_eth_events"

    REDIS_URL: str

    # Frontend URL configuration
    BASE_URL: str  # Base URL for generating collection links

    # MoonPay API 配置
    MOONPAY_SECRET_KEY: str  # MoonPay Secret Key，用于签名


settings = Settings()
