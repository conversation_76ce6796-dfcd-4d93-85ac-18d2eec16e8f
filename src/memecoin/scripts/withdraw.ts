#!/usr/bin/env node

console.log('Starting withdraw script...');

import { Command } from 'commander';
import { parseEther, parseUnits } from 'ethers';
import { createPublicClient, createWalletClient, http, defineChain } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { 
  chainConfig, 
  publicActionsL1, 
  publicActionsL2, 
  walletActionsL1, 
  walletActionsL2 
} from 'viem/op-stack';
import { hoodi } from 'viem/chains';
import { getWithdrawals } from 'viem/op-stack';

// USDT合约地址
const L1_USDT_TOKEN = '******************************************';
const L2_USDT_TOKEN = '******************************************';

const L1_RPC = 'https://tiniest-muddy-dawn.ethereum-hoodi.quiknode.pro/7054570c2452fdd7c4bce0b7c2d27c5b694f507d/';
const MFN_TESTNET_RPC = 'https://hoodi.rpc.mfn.fun/';
const MFN_TESTNET_EXPLORER_URL = 'https://mfn-hoodi-testnet.cloud.blockscout.com/';

// ERC20 ABI（简化版，只包含必要函数）
const erc20ABI = [
  {
    inputs: [
      {
        internalType: 'address',
        name: 'account',
        type: 'address',
      },
    ],
    name: 'balanceOf',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'spender',
        type: 'address',
      },
      {
        internalType: 'uint256',
        name: 'value',
        type: 'uint256',
      },
    ],
    name: 'approve',
    outputs: [
      {
        internalType: 'bool',
        name: '',
        type: 'bool',
      },
    ],
    stateMutability: 'nonpayable',
    type: 'function',
  },
];

const MFN_TESTNET = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 5918,
  name: 'MFN-testnet',
  nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: [MFN_TESTNET_RPC],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: MFN_TESTNET_EXPLORER_URL,
      apiUrl: MFN_TESTNET_EXPLORER_URL + '/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [hoodi.id]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [hoodi.id]: {
        address: '******************************************',
      },
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 0,
    },
    portal: {
      [hoodi.id]: {
        address: '******************************************',
        blockCreated: 761427,
      },
    },
    l1StandardBridge: {
      [hoodi.id]: {
        address: '******************************************',
        blockCreated: 761427,
      },
    },
  },
  sourceId: hoodi.id,
});

// 初始化命令行参数解析器
const program = new Command();

program
  .option('--network <network>', 'Network type')
  .option('--from <from>', 'From address')
  .option('--to <to>', 'To address')
  .option('--amount <amount>', 'Amount to withdraw')
  .option('--token <token>', 'Token type (ETH or USDT)')
  .option('--gas <gas>', 'Gas limit')
  .option('--gasPrice <gasPrice>', 'Gas price')
  .option('--privateKey <privateKey>', 'Private key for the account')
  .parse();

const options = program.opts();

// 验证必要参数
if (!options.from || !options.to || !options.amount || !options.token || !options.privateKey) {
  console.error(JSON.stringify({ error: 'Missing required parameters: from, to, amount, token, privateKey' }));
  process.exit(1);
}

// 验证私钥格式
if (!options.privateKey.startsWith('0x') || options.privateKey.length !== 66) {
  console.error(JSON.stringify({ error: 'Invalid private key format. Private key must start with 0x and be 64 characters long.' }));
  process.exit(1);
}

// 获取私钥
const privateKey = options.privateKey as `0x${string}`;

// 创建账户
const account = privateKeyToAccount(privateKey);

// 创建客户端
const publicClientL1 = createPublicClient({
  chain: hoodi,
  transport: http(L1_RPC),
}).extend(publicActionsL1());

const walletClientL1 = createWalletClient({
  account,
  chain: hoodi,
  transport: http(L1_RPC),
}).extend(walletActionsL1());

const publicClientL2 = createPublicClient({
  chain: MFN_TESTNET,
  transport: http(MFN_TESTNET_RPC),
}).extend(publicActionsL2());

const walletClientL2 = createWalletClient({
  account,
  chain: MFN_TESTNET,
  transport: http(MFN_TESTNET_RPC),
}).extend(walletActionsL2());

// ETH提现函数
async function withdrawETH(amount: string) {
  try {
    // console.log(`Withdrawing ${amount} ETH from ${options.from} to L1...`);
    
    // 构建提现参数
    const args = await publicClientL1.buildInitiateWithdrawal({
      to: options.to as `0x${string}`,
      value: parseEther(amount),
      chain: hoodi,
    });

    // 执行提现交易
    const hash = await walletClientL2.initiateWithdrawal({
      ...args,
      chain: MFN_TESTNET,
    });
    
    // 等待交易确认
    const receipt = await publicClientL2.waitForTransactionReceipt({ hash });
    
    console.log(JSON.stringify({
      txHash: hash,
      detail: `ETH withdrawal initiated in L2 block ${receipt.blockNumber}`
    }));
    
    return hash;
  } catch (error: any) {
    console.error(JSON.stringify({ error: `ETH withdrawal failed: ${error.message || error}` }));
    process.exit(1);
  }
}

// USDT提现函数
async function withdrawUSDT(amount: string) {
  try {
    // console.log(`Withdrawing ${amount} USDT from ${options.from} to L1...`);
    
    // 解析USDT金额（6位小数）
    const amountParsed = parseUnits(amount, 6);
    
    // 构建ERC20提现参数
    const args = await publicClientL1.buildInitiateWithdrawal({
      to: L1_USDT_TOKEN as `0x${string}`, // L1 USDT 合约地址
      value: 0n, // ERC20 提现时 value 为 0
      data: `0xa9059cbb${options.to.slice(2).padStart(64, '0')}${amountParsed.toString(16).padStart(64, '0')}` as `0x${string}`, // transfer(to, amount) 的编码
      chain: hoodi,
    });

    // 执行提现交易
    const hash = await walletClientL2.initiateWithdrawal({
      ...args,
      chain: MFN_TESTNET,
    });
    
    // 等待交易确认
    const receipt = await publicClientL2.waitForTransactionReceipt({ hash });
    
    console.log(JSON.stringify({
      txHash: hash,
      detail: `USDT withdrawal initiated in L2 block ${receipt.blockNumber}`
    }));
    
    return hash;
  } catch (error: any) {
    console.error(JSON.stringify({ error: `USDT withdrawal failed: ${error.message || error}` }));
    process.exit(1);
  }
}

// 主函数
async function main() {
  try {
    // 根据token参数决定提现类型
    if (options.token.toUpperCase() === 'ETH') {
      await withdrawETH(options.amount);
    } else if (options.token.toUpperCase() === 'USDT') {
      await withdrawUSDT(options.amount);
    } else {
      console.error(JSON.stringify({ error: `Unsupported token type: ${options.token}` }));
      process.exit(1);
    }
  } catch (error: any) {
    console.error(JSON.stringify({ error: `Withdrawal process failed: ${error.message || error}` }));
    process.exit(1);
  }
}

// 执行主函数
main();