from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal
import json
import logging
import sys
import os
from typing import Any, Dict, List, Optional, Union
import aiohttp
from pydantic import BaseModel, Field
import asyncio

from web3 import Web3

# 创建日志目录
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "buyback.log")),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("buyback")


class BuyBackStrategy(BaseModel):
    """回购策略"""
    total_buy_amount_BNB_wei: Decimal # 总买入的BNB 金额
    buy_steps: int # 每次买入的批次大小
    min_interval_seconds: int
    buyback_tokens: List[Dict[str, Union[str, float]]] = Field(
        default_factory=lambda: [{"token_address": "0x000000", "buy_percentage": 0.01}],
        description="回购代币配置列表，每个配置包含代币地址和回购比例"
    )

class ServiceConfig(BaseModel):
    """交易机器人配置"""
    api_base_url: str 
    api_token: str 
    transaction_history_file: str = "buy_back_transaction_history.json"
    min_bnb_amount: float = 0.001
    max_bnb_amount: float = 1000
    keep_min_bnb: float = 0.05
    enable_buy: bool = True
    enable_sell: bool = True
    web3_provider_url: str = "https://data-seed-prebsc-1-s3.bnbchain.org:8545"  # BSC主网节点URL
    burn_address: str = "0x0000000000000000000000000000000000000000"

class Transaction(BaseModel):
    """交易记录"""
    txid: str
    token_address: str
    token_symbol: str
    action: str  # "buy" 或 "sell"
    amount: str
    step_index: int
    timestamp: datetime = Field(default_factory=datetime.now)

class TokenBalance(BaseModel):
    """代币余额信息"""
    token_address: str
    token_name: str
    token_symbol: str
    balance: str
    ui_balance: str
    last_updated: datetime = Field(default_factory=datetime.now)

class ServiceState(BaseModel):
    """机器人状态"""
    bnb_balance: str = "0"
    ui_bnb_balance: str = "0"
    token_balances: Dict[str, TokenBalance] = Field(default_factory=dict)
    transactions: List[Transaction] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)
    last_transaction_time: Optional[datetime] = None
    is_active: bool = False

class BuyBackService:
    def __init__(self, config: ServiceConfig):
        self.config = config
        self.state = ServiceState()
        self.headers = {
            "Authorization": f"Bearer {self.config.api_token}",
            "Content-Type": "application/json"
        }

         # 初始化Web3连接
        self.web3 = Web3(Web3.HTTPProvider(self.config.web3_provider_url))
        if not self.web3.is_connected():
            logger.warning(f"无法连接到Web3提供者: {self.config.web3_provider_url}")
            raise Exception(f"无法连接到Web3提供者: {self.config.web3_provider_url}")
        
        self.transaction_history: List[Transaction] = []


    def sync_transaction_history_to_local_file(self) -> None:
        """同步交易历史到本地文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config.transaction_history_file) or '.', exist_ok=True)
            
            # 将 Transaction 对象转换为可序列化的字典
            serializable_transactions = []
            for tx in self.transaction_history:
                tx_dict = tx.model_dump()
                # 将 datetime 转换为 ISO 格式字符串
                if isinstance(tx_dict.get('timestamp'), datetime):
                    tx_dict['timestamp'] = tx_dict['timestamp'].isoformat()
                serializable_transactions.append(tx_dict)
            
            # 写入文件
            with open(self.config.transaction_history_file, 'w') as f:
                json.dump(serializable_transactions, f, indent=4)
            logger.info("交易历史已同步到本地文件")
        except Exception as e:  
            logger.error(f"同步交易历史到本地文件失败: {str(e)}")
            
    def load_transaction_history_from_local_file(self) -> None:
        """从本地文件加载交易历史"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.config.transaction_history_file):
                logger.info(f"交易历史文件不存在，创建新文件: {self.config.transaction_history_file}")
                # 创建空的历史记录
                self.transaction_history = []
                # 保存空的历史记录到文件
                self.sync_transaction_history_to_local_file()
                return
                
            # 文件存在，加载历史记录
            with open(self.config.transaction_history_file, 'r') as f:    
                tx_data = json.load(f)
                # 将 ISO 格式字符串转换回 datetime
                for tx in tx_data:
                    if 'timestamp' in tx and isinstance(tx['timestamp'], str):
                        tx['timestamp'] = datetime.fromisoformat(tx['timestamp'])
                self.transaction_history = [Transaction(**tx) for tx in tx_data]
            logger.info("交易历史已从本地文件加载")
        except Exception as e:
            logger.error(f"从本地文件加载交易历史失败: {str(e)}")
            # 发生错误时，初始化为空列表
            self.transaction_history = []

    def search_transaction(self, token_address: str, step_index: int) -> Transaction:
        """检查交易是否存在"""
        for tx in self.transaction_history:
            if tx.token_address == token_address and tx.step_index == step_index:
                return tx
        return None
            
    async def _api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Any:
        """发送API请求"""
        url = f"{self.config.api_base_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method.upper() == "GET":
                    async with session.get(url, headers=self.headers) as response:
                        response.raise_for_status()
                        return await response.json()
                elif method.upper() == "POST":
                    async with session.post(url, headers=self.headers, json=data) as response:
                        response.raise_for_status()
                        return await response.json()
                else:
                    logger.error(f"不支持的HTTP方法: {method}")
                    return None
        except aiohttp.ClientError as e:
            logger.error(f"API请求异常 ({method} {endpoint}): {str(e)}, URL: {url}, Headers: {self.headers}")
            return None
        except Exception as e:
            logger.error(f"API请求未知错误 ({method} {endpoint}): {str(e)}, URL: {url}, Headers: {self.headers}")
            return None

    async def update_bnb_balance(self) -> None:
        """更新BNB余额"""
        response = await self._api_request("GET", "/bnb_balance")
        if not response:
            logger.error("获取BNB余额失败")
            return

        self.state.bnb_balance = response.get("amount", "0")
        self.state.ui_bnb_balance = response.get("ui_amount", "0")
        logger.info(f"BNB余额已更新: {self.state.ui_bnb_balance}")
        
    async def execute_buy(self, step_index: int, token_address: str, buy_amount: str) -> Optional[Transaction]:
        """执行买入操作"""
        logger.info(f"执行买入操作: 步骤 {step_index}, 代币 {token_address}, 金额 {buy_amount} BNB")
        try:
            # 检查BNB余额是否足够
            bnb_balance = Decimal(self.state.ui_bnb_balance)
            if bnb_balance <= Decimal(str(self.config.keep_min_bnb)):
                logger.warning(f"BNB余额不足，无法进行买入操作。当前余额: {bnb_balance}")
                return None
                
            # 构建请求数据
            data = {
                "token_address": token_address,
                "amount": buy_amount
            }
            
            # 执行买入
            logger.info(f"准备买入: 代币 {token_address}, 金额 {buy_amount} BNB")
            result = await self._api_request("POST", "/buy_token", data)
            logger.info(f"买入请求 {data} 结果: {result}")
            
            if not result or "txid" not in result:
                logger.error(f"买入请求失败: {result}")
                return None
                
            # 获取代币信息
            token_info = await self._api_request("GET", f"/token?token_address={token_address}")
            token_symbol = token_info.get("token_symbol", "Unknown") if token_info else "Unknown"
                
            # 记录交易
            tx_record = Transaction(
                txid=result["txid"],
                token_address=token_address,
                token_symbol=token_symbol,
                action="buy",
                amount=str(buy_amount),
                step_index=step_index,
                timestamp=datetime.now(),
            )
            
            self.state.transactions.append(tx_record)
            self.state.last_transaction_time = datetime.now()
            
            logger.info(f"买入交易已提交: TXID {result['txid']}, 金额 {buy_amount} BNB")
            return tx_record
            
        except Exception as e:
            logger.error(f"买入操作失败: {str(e)}")
            return None
        
    async def execute_burn_token(self, token_address: str, balance: Decimal) -> Optional[Transaction]:
        """执行销毁操作"""
        raise NotImplementedError("销毁操作未实现")
    
            
    async def generate_buyback_steps(self, strategy: BuyBackStrategy) -> List[Dict[str, Any]]:
        """
        基于回购策略生成批量购买执行步骤
        
        Args:
            strategy: 回购策略对象
            
        Returns:
            List[Dict[str, Any]]: 购买执行步骤列表，每个步骤包含:
                - token_address: 代币地址
                - buy_amount: 购买数量(wei)
                - percentage: 占总购买量的百分比
                - step_index: 当前是第几步
                - total_steps: 总步数
        """
        all_steps = []  # 存储所有代币的所有步骤
        total_bnb_wei = int(strategy.total_buy_amount_BNB_wei)  # 总BNB数量(wei)
        
        # 验证所有百分比之和不超过1
        total_percentage = sum(Decimal(token["buy_percentage"]) for token in strategy.buyback_tokens)
        if total_percentage > 1.0:
            logger.warning(f"警告: 所有代币的购买百分比之和({total_percentage})超过100%")
            return None
        
        # 为每个代币生成购买步骤
        for token in strategy.buyback_tokens:
            token_address = token["token_address"]
            percentage = token["buy_percentage"]
            total_buy_amount_wei = Decimal(int(total_bnb_wei * percentage))  # 计算wei单位的购买金额
            
            # 检查购买金额是否在允许范围内（转换为BNB单位进行比较）
            total_buy_amount_bnb = total_buy_amount_wei / Decimal(1e18)  # 转换为BNB单位
            if total_buy_amount_bnb < Decimal(self.config.min_bnb_amount):
                logger.warning(f"代币 {token_address} 的购买金额 {total_buy_amount_bnb} BNB 低于最小限制 {self.config.min_bnb_amount} BNB，将被跳过")
                continue
                
            if total_buy_amount_bnb > Decimal(self.config.max_bnb_amount):
                logger.warning(f"代币 {token_address} 的购买金额 {total_buy_amount_bnb} BNB 超过最大限制 {self.config.max_bnb_amount} BNB，将被限制")
                total_buy_amount_wei = Decimal(self.config.max_bnb_amount) * Decimal(1e18)  # 转换回wei单位
            
            # 计算每步的购买金额，确保总和不超过计划购买量
            step_amount_wei = total_buy_amount_wei // Decimal(strategy.buy_steps)
            remainder_wei = total_buy_amount_wei % Decimal(strategy.buy_steps)
            
            # 生成每一步的购买记录
            for step in range(strategy.buy_steps):
                # 将余数分配到前面的步骤中
                current_step_amount = step_amount_wei
                if step < remainder_wei:
                    current_step_amount += 1
                
                # 检查单步金额是否太小
                if current_step_amount / Decimal(1e18) < Decimal(self.config.min_bnb_amount):
                    logger.warning(f"代币 {token_address} 第 {step+1} 步的购买金额 {current_step_amount/1e18} BNB 低于最小限制，将被跳过")
                    continue
                
                all_steps.append({
                    "token_address": token_address,
                    "buy_amount": Decimal(str(current_step_amount)),  # 保持wei单位
                    "percentage": percentage,
                    "step_index": step + 1,
                    "total_steps": strategy.buy_steps
                })
        
        # 按 step_index 重新组织步骤
        buy_steps = []
        for step_index in range(1, strategy.buy_steps + 1):
            # 获取当前步骤的所有代币购买操作
            current_step_operations = [step for step in all_steps if step["step_index"] == step_index]
            buy_steps.extend(current_step_operations)
        
        logger.info(f"生成了 {len(buy_steps)} 个购买步骤，总购买量: {total_bnb_wei/1e18} BNB")
        return buy_steps
    
    async def check_transaction_status(self, tx: Transaction) -> bool:
        """检查交易状态"""
        # 异步转同步查询，可以考虑使用异步web3库进一步优化
        receipt = self.web3.eth.get_transaction_receipt(tx.txid)
        
        if receipt is None:
            # 交易尚未被打包，仍处于pending状态
            logger.info(f"交易尚未被打包，仍处于pending状态: {tx.txid}")
            return False
        
        # 检查交易状态
        if receipt.status == 1:
            logger.info(f"交易已确认: {tx.txid}")
            return True
        else:
            logger.info(f"交易失败: {tx.txid}")
            return False
    async def execute_buyback_strategy(self, strategy: BuyBackStrategy) -> None:
        """
        执行完整的回购策略
        
        Args:
            strategy: 回购策略对象
            
        Returns:
            None
        """
        buy_steps = await self.generate_buyback_steps(strategy)

        self.load_transaction_history_from_local_file()
        
        for step in buy_steps:
            await self.update_bnb_balance()
            try:
                # 检查余额是否足够
                bnb_balance = Decimal(self.state.ui_bnb_balance) * Decimal(1e18)
                if bnb_balance < step["buy_amount"]:
                    logger.warning(f"BNB余额不足, 无法执行购买步骤。需要: {step['buy_amount'] / Decimal(1e18)} BNB, 当前余额: {bnb_balance} BNB")
                    continue
                
                # 执行购买
                # tx = await self.execute_buy(step["step_index"], step["token_address"], step["buy_amount"])
                old_tx = self.search_transaction(step["token_address"], step["step_index"])
                if old_tx:
                    logger.info(f"交易已存在，跳过执行: {step['token_address']} {step['step_index']}")
                    continue
                # tx = await self.mock_execute_buy(step["step_index"], step["token_address"], step["buy_amount"])
                tx = await self.execute_buy(step["step_index"], step["token_address"], str(step["buy_amount"] / Decimal(1e18)))
                if tx:
                    self.transaction_history.append(tx)
                    logger.info(f"成功执行购买步骤 {step['step_index']}: 代币 {step['token_address']}, 金额 {step['buy_amount'] / Decimal(1e18)} BNB  TxID {tx.txid}")
                # 检查交易状态
                while not await self.check_transaction_status(tx):
                    await asyncio.sleep(1)
                self.sync_transaction_history_to_local_file()


                
                # 等待最小间隔时间
                await asyncio.sleep(strategy.min_interval_seconds)
                
            except Exception as e:
                logger.error(f"执行购买步骤失败: {str(e)}")
                self.sync_transaction_history_to_local_file()
                raise e
            

with open('buyback.json', 'r') as f:
    buyback_config_data = json.load(f)

# 创建策略对象
strategy = BuyBackStrategy(
    total_buy_amount_BNB_wei=Decimal(buyback_config_data['buyback_strategy']['total_buy_amount_BNB_wei']),
    buy_steps=buyback_config_data['buyback_strategy']['buy_steps'],
    min_interval_seconds=buyback_config_data['buyback_strategy']['min_interval_seconds'],
    buyback_tokens=buyback_config_data['buyback_strategy']['buyback_tokens']
)

service_config = ServiceConfig(
    api_base_url=buyback_config_data['service_config']['api_base_url'],
    api_token=buyback_config_data['service_config']['api_token'],
    transaction_history_file=buyback_config_data['service_config']['transaction_history_file'],
    min_bnb_amount=buyback_config_data['service_config']['min_bnb_amount'],
    max_bnb_amount=buyback_config_data['service_config']['max_bnb_amount'],
    keep_min_bnb=buyback_config_data['service_config']['keep_min_bnb'],
    web3_provider_url=buyback_config_data['service_config']['web3_provider_url'],
    burn_address=buyback_config_data['service_config']['burn_address'],
    enable_buy=buyback_config_data['service_config']['enable_buy'],
    enable_sell=buyback_config_data['service_config']['enable_sell'],
)

async def main():
    service = BuyBackService(service_config)
    await service.execute_buyback_strategy(strategy)
if __name__ == "__main__":
    asyncio.run(main())