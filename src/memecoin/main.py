import asyncio
import json
from decimal import Decimal
from typing import List, Optional, Any, Dict
from datetime import datetime, timedelta
import random # Added for random interval in notifications count WebSocket

from fastapi import FastAPI, Depends, Query, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.params import Path, Body
from starlette import status
from starlette.websockets import WebSocket, WebSocketDisconnect, WebSocketState
from sqlalchemy import text # Added for notifications count WebSocket

from src.auth import current_user, verify_token, optional_user
from src.common.schemas import PaginationParams
from src.common.caching.caching_client import CachingClient, CachingClientGetter
from src.database.models import User
from src.database.session import get_session_context
from src.common.constants import MEMECOIN_REDIS_ENDPOINT
from src.infra.app import create_app
from src.memecoin.dependencies import (
    get_meme_service,
    get_redis_client,
    get_blockchain_getter_singleton,
    ensure_blockchain_inited,
)
from src.memecoin.logger import logger
from src.memecoin.schemas import (
    EstimateGasResponseSchema,
    OutTokenAmountResponseSchema,
    SendAssetRequest,
    TokenSchema,
    BuybackTokenSchema,
    TokenListType,
    RankingType,
    HolderSchema,
    TokenPriceSchema,
    Interval,
    CreationResponseSchema,
    AssetResponseSchema,
    SendTokenRequest,
    SellTokenRequest,
    BuyTokenRequest,
    CreateMemeRequest,
    TransactionHistoryResponseSchema,
    EstimateGasType,
    MemeTokenSchema,
    BaseTokenSchema,
    BalanceResponseSchema,
    AmountResponseSchema, SendNativeRequest, RecentSendAddressSchema, ExportMnemonicResponseSchema,
    TokenWatchlistResponse, SortCriteria, TokenWatchlistSchema,
    EditMemeRequest, EditMemeResponse,
    TokenWatchlistWithTotalResponse,
    Web3TransactionStatusSchema,
    EthPriceResponseSchema,
    ConversionResponseSchema,
    CashAmountResponseSchema,
    WalletAddressResponseSchema,
    # MoonPay related schemas
    MoonPaySignatureRequest,
    MoonPaySignatureResponse, WithdrawRequest,
)
from src.memecoin.services import MemeService
from src.memecoin.utils import _get_msg, _extract_token, _extract_interval, decimal_to_string
from src.common.idempotency import create_meme_idempotent, buy_token_idempotent, sell_token_idempotent, send_asset_idempotent, send_token_idempotent, send_native_idempotent, DuplicateOrderError, withdraw_idempotent
from src.notifications.service import NotificationService, get_notification_service
from src.posts.collections.service import CollectionService, get_collection_service
from src.common.elasticsearch.sync_client import create_es_sync_client

async def _startup_hook(app: FastAPI) -> None:
    # 通过单例工厂预热重资源，并存放到 app.state 便于观测/统一关闭
    app.state.redis = get_redis_client()  # 单例 Redis 客户端
    connector = get_blockchain_getter_singleton()()  # 单例 Getter 生成连接器实例
    await ensure_blockchain_inited(connector)  # 只初始化一次
    app.state.blockchain = connector


async def _shutdown_hook(app: FastAPI) -> None:
    # Cleanup heavy resources
    try:
        await app.state.redis.aclose()
    except Exception:
        pass


app = create_app(
    title="Memecoin",
    version="1.0",
    description="Memecoin API",
    request_logger=logger,
    cors_allow_origin_regex=".*",
    startup_hook=_startup_hook,
    shutdown_hook=_shutdown_hook,
)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get(
    "/token",
    tags=["Token Info"],
    summary="Get token info",
    description="Endpoint for getting token info.",
    status_code=status.HTTP_200_OK,
    response_model=TokenSchema,
)
async def get_token_info(
    token_address: Optional[str] = Query(None, title="Token address"),
    collection_id: Optional[str] = Query(None, title="Collection ID"),
    user: Optional[User] = Depends(optional_user),  # 添加可选的用户认证
    meme_service: MemeService = Depends(get_meme_service),
):
    if not (token_address or collection_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of token_address or collection_id must be provided.",
        )

    token_info = await meme_service.get_token_info(
        token_address=token_address,
        collection_id=collection_id,
        user_id=user.id if user else None,  # 传入用户ID
    )
    return token_info


@app.get(
    "/token_address",
    tags=["Token Info"],
    summary="Get token address by collection ID",
    description="Endpoint for getting token address by collection ID.",
    status_code=status.HTTP_200_OK,
    response_model=str,
)
async def get_token_address(
    collection_id: str = Query(..., title="Collection ID"),
    meme_service: MemeService = Depends(get_meme_service),
):
    token_address = await meme_service.get_token_address_by_collection_id(collection_id)
    return token_address


@app.get(
    "/{post_id}/tokens",
    tags=["Token Info"],
    summary="Retrieve tokens associated with the post id",
    description="Endpoint for getting token list by post id",
    status_code=status.HTTP_200_OK,
    response_model=List[TokenSchema],
)
async def get_token_list_by_post_id(
    post_id: str = Path(..., title="Post ID", description="The ID of the post"),
    meme_service: MemeService = Depends(
        get_meme_service,
    ),
):
    return await meme_service.get_tokens_by_post_id(post_id)


@app.get(
    "/token_list",
    tags=["Token Info"],
    summary="Get token list",
    description="Endpoint for getting token list",
    status_code=status.HTTP_200_OK,
    response_model=List[TokenSchema],
)
async def get_token_list(
    list_type: TokenListType = Query(title="Token List Type"),
    pagination_params: PaginationParams = Depends(),
    meme_service: MemeService = Depends(
        get_meme_service,
    ),
):
    return await meme_service.get_token_list(list_type, pagination_params)


@app.get(
    "/token_rankings",
    tags=["Token Info"],
    summary="Get token rankings",
    description="Endpoint for getting token rankings (daily/weekly/buyback charts)",
    status_code=status.HTTP_200_OK,
)
async def get_token_rankings(
    ranking_type: RankingType = Query(title="Ranking Type", description="Type of ranking: daily, weekly, or buyback"),
    pagination_params: PaginationParams = Depends(),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    获取排行榜数据
    
    - **daily**: 日排行榜 - 基于从今天00:00到现在的市值增量
    - **weekly**: 周排行榜 - 基于从本周一00:00到现在的市值增量  
    - **buyback**: 回购排行榜 - 基于从本月1号00:00到现在的市值增量，显示前50名，前10名显示固定的回购比例分配
    
    回购比例分配规则（固定值）：
    - 第1名: 25%, 第2名: 18%, 第3名: 12%, 第4名: 10%, 第5名: 8%
    - 第6-10名: 各5.4%（均分剩余27%）
    
    排序规则：所有排行榜都按照市值增量（当前市值 - 历史市值）从高到低排序
    市值计算：市值 = token价格 × 总供应量（10亿）
    历史数据：使用K线数据中最接近目标时间点的收盘价计算历史市值
    """
    return await meme_service.get_token_rankings(ranking_type, pagination_params)


@app.post(
    "/rankings/invalidate-cache",
    tags=["Rankings"],
    summary="Invalidate rankings cache",
    description="Manual endpoint to invalidate all rankings cache for immediate refresh",
    status_code=status.HTTP_200_OK,
)
async def invalidate_rankings_cache(
    reason: str = Query(default="manual", description="Reason for cache invalidation"),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    手动清除排行榜缓存
    
    这个端点允许手动清除所有排行榜缓存，确保下次请求时获取最新数据。
    通常在以下情况使用：
    - 新币创建后需要立即在排行榜中显示
    - Token信息发生重大变化
    - 调试和测试目的
    
    缓存清除后会自动触发后台预热，确保用户体验不受影响。
    """
    try:
        deleted_count = await meme_service.invalidate_rankings_cache(reason=f"manual_{reason}_by_{user.id}")
        
        return {
            "success": True,
            "message": f"Successfully invalidated {deleted_count} rankings cache keys",
            "deleted_count": deleted_count,
            "reason": reason,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to invalidate rankings cache: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to invalidate cache: {str(e)}"
        )


@app.get(
    "/rankings/cache-status",
    tags=["Rankings"],
    summary="Get rankings cache status",
    description="Get current status of rankings cache including TTL and memory usage",
    status_code=status.HTTP_200_OK,
)
async def get_rankings_cache_status(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    获取排行榜缓存状态
    
    返回各类排行榜缓存的详细状态信息：
    - 缓存是否存在
    - 剩余生存时间 (TTL)
    - 内存使用情况
    - 系统统计信息
    """
    try:
        cache_status = await meme_service.get_cache_status()
        return cache_status
        
    except Exception as e:
        logger.error(f"Failed to get cache status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache status: {str(e)}"
        )


@app.get(
    "/kline",
    tags=["Market Data"],
    summary="Get token kline",
    description="Endpoint for getting memecoin kline data for fixed time ranges: 1H (30s bars), 4H (1m bars), 1D (5m bars), 1W (1h bars), 1M (4h bars), Max (12h bars)",
    status_code=status.HTTP_200_OK,
    response_model=List[TokenPriceSchema],
)
async def get_token_kline(
    token_address: str = Query(title="Token Address"),
    interval: Optional[Interval] = Query(None, title="Interval", description="Optional: specify single interval, including 1H for last 1H with 30s bars"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    获取Token的K线数据。不再使用分页，固定返回5个时间范围的数据：
    
    - **1H**: 最近1小时的数据，使用30秒bar（仅在指定interval时返回）
    - **4H**: 最近4小时的数据，使用1分钟bar
    - **1D**: 最近1天的数据，使用5分钟bar
    - **1W**: 最近1周的数据，使用1小时bar
    - **1M**: 最近1个月的数据，使用4小时bar（暂时用1小时）
    - **Max**: 所有历史数据，使用12小时bar（暂时用1小时）
    
    如果指定了interval参数，则只返回该时间范围的数据。
    """
    return await meme_service.get_token_kline(token_address, interval)


@app.get(
    "/holder_list",
    tags=["Token Info"],
    summary="Get token holders",
    description="Endpoint for getting the list of memecoin holders",
    status_code=status.HTTP_200_OK,
    response_model=List[HolderSchema],
)
async def get_holder_list(
    token_address: str = Query(title="Token Address"),
    pagination_params: PaginationParams = Depends(),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_token_holders(token_address, pagination_params)


@app.get(
    "/profile/assets",
    tags=["User Assets"],
    summary="Get User's assets",
    description="Endpoint for getting User's balance and token amount.",
    status_code=status.HTTP_200_OK,
    response_model=AssetResponseSchema,
)
async def get_user_assets(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_user_assets(user.id)


@app.get(
    "/profile/memes",
    tags=["User Assets"],
    summary="Get the memes created by specified user",
    description="Endpoint for getting the memes created by specified user.",
    status_code=status.HTTP_200_OK,
    response_model=List[MemeTokenSchema],
)
async def get_my_memes(
    user_id: str = Query(None, title="User ID"),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    if user_id:
        return await meme_service.get_my_memes(user_id)
    else:
        return await meme_service.get_my_memes(user.id)


@app.get(
    "/my_tokens",
    tags=["User Assets"],
    summary="Get user's created and held tokens",
    description="Endpoint for getting all tokens that the user has created or currently holds.",
    status_code=status.HTTP_200_OK,
    response_model=List[BaseTokenSchema],
)
async def get_my_tokens(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_my_tokens(user.id)


@app.post(
    "/create_meme",
    tags=["Token Operations"],
    summary="Create token with new collection",
    description="Endpoint for creating a meme token. Always creates a new collection using the provided name (as title), about (as description), and avatar (as cover). The repo_url is automatically generated as BASE_URL/collection/{collection_id}.",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@create_meme_idempotent
async def create_meme(
    request: CreateMemeRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    holdview_amount = Decimal(request.holdview_amount) if request.holdview_amount else Decimal(0)
    # 装饰器自动处理幂等性检查
    result = await meme_service.create_meme(
        user.id,
        request.name,
        request.symbol,
        request.about,
        request.avatar,
        Decimal(request.usdt_amount or "0"),  # Default to "0" if None
        request.gas,
        request.gas_price,
        user.region,  # Pass user region for collection creation
        request.social_links,
        request.order_id,
        holdview_amount,
        True,  # is_with_usdt - always True for USDT
    )
    
    # 异步同步到 Elasticsearch
    # 从结果中获取 memecoin ID - 需要根据实际返回结构调整
    memecoin_id = None
    if hasattr(result, 'id'):
        memecoin_id = str(result.id)
    elif isinstance(result, dict) and 'id' in result:
        memecoin_id = str(result['id'])
    elif hasattr(result, 'token_id'):
        memecoin_id = str(result.token_id)
    elif isinstance(result, dict) and 'token_id' in result:
        memecoin_id = str(result['token_id'])
    
    if memecoin_id:
        es_sync_client = create_es_sync_client("memecoin_service")
        es_sync_client.sync_memecoin(memecoin_id, "create", priority=5)
    
    return result


@app.post(
    "/edit_meme",
    tags=["Token Operations"],
    summary="Edit meme information",
    description="Endpoint for editing meme token information (about and social links). Only creator can edit.",
    status_code=status.HTTP_200_OK,
    response_model=EditMemeResponse,
)
async def edit_meme(
    request: EditMemeRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    if request.about is None and request.social_links is None and request.avatar is None:
        return EditMemeResponse(
            detail="No changes were provided to update."
        )
        
    return await meme_service.edit_meme(
        user.id,
        request.token_address,
        request.about,
        request.social_links,
        request.avatar,
    )


@app.post(
    "/buy_token",
    tags=["Token Operations"],
    summary="Buy token",
    description="Endpoint for buying token",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@buy_token_idempotent
async def buy_token(
    request: BuyTokenRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    # 装饰器自动处理幂等性检查
    return await meme_service.buy_token(
        user.id, request.token_address, request.amount, request.gas, request.gas_price, request.order_id, request.is_with_usdt
    )


@app.post(
    "/sell_token",
    tags=["Token Operations"],
    summary="Sell token",
    description="Endpoint for selling token",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@sell_token_idempotent
async def sell_token(
    request: SellTokenRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    if Decimal(request.amount) > 50_000_000:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Sell token amount exceeds the maximum limit of 50,000,000.",
        )
    
    # 装饰器自动处理幂等性检查
    return await meme_service.sell_token(
        user.id, request.token_address, request.amount, request.gas, request.gas_price, request.order_id, request.is_with_usdt
    )

@app.post(
        "/send_asset",
        tags=["Token Operations"],
        summary="Send asset including BNB and ERC20 TOKEN",
        description="Endpoint for sending asset, token_address='native_token' for sending native asset. Supports sending by username (priority) or to_address.",
        status_code=status.HTTP_201_CREATED,
        response_model=CreationResponseSchema,
)
@send_asset_idempotent
async def send_asset(
    request: SendAssetRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    # 装饰器自动处理幂等性检查
    return await meme_service.send_asset(
        user.id,
        request.token_address,
        request.to_address,
        request.amount,
        request.gas,
        request.gas_price,
        request.order_id,
        request.username
    )
    

@app.post(
    "/send_token",
    tags=["Token Operations"],
    summary="Send token",
    description="Endpoint for sending token",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@send_token_idempotent
async def send_token(
    request: SendTokenRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    # 装饰器自动处理幂等性检查
    return await meme_service.send_token(
        user.id,
        request.token_address,
        request.to_address,
        request.amount,
        request.gas,
        request.gas_price,
        request.order_id,
    )


@app.post(
    "/send_native",
    tags=["Token Operations"],
    summary="Send native token (ETH, BNB, etc)",
    description="Endpoint for sending native token",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@send_native_idempotent
async def send_native(
    request: SendNativeRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    # 装饰器自动处理幂等性检查
    return await meme_service.send_native(
        user.id,
        request.to_address,
        request.amount,
        request.gas,
        request.gas_price,
        request.order_id,
    )


@app.post(
    "/withdraw",
    tags=["Token Operations"],
    summary="Withdraw from L2 to L1",
    description="Withdraw user's assets from L2 to L1. Uses external TypeScript script under the hood (placeholder).",
    status_code=status.HTTP_201_CREATED,
    response_model=CreationResponseSchema,
)
@withdraw_idempotent
async def withdraw(
    request: WithdrawRequest,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.withdraw_to_l1(
        user.id,
        request.network,
        request.amount,
        request.to_address,
        request.gas,
        request.gas_price,
        request.order_id,
    )


@app.get(
    "/receive_token",
    tags=["Token Operations"],
    summary="Receive token",
    description="Endpoint for getting wallet address for target token",
    status_code=status.HTTP_200_OK,
    response_model=str,
)
async def receive_token(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.receive_token(user.id)


@app.get(
    "/wallet/{username}",
    tags=["User Assets"],
    summary="Get wallet address by username",
    description="Endpoint for getting user's wallet address by username. Does not require authentication.",
    status_code=status.HTTP_200_OK,
    response_model=WalletAddressResponseSchema,
)
async def get_wallet_address_by_username(
    username: str = Path(..., title="Username", description="The username to lookup"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get user's wallet address by username"""
    return await meme_service.get_wallet_address_by_username(username)


@app.websocket("/ws/memecoin/activity")
async def ws_updating_memecoin_activity(
    websocket: WebSocket,
    meme_service: MemeService = Depends(get_meme_service),
    caching_client: CachingClient = Depends(CachingClientGetter(logger)),
):
    """
    WebSocket endpoint for real-time memecoin activity updates and notifications count.
    
    Usage:
    1. Connect to WebSocket: ws://localhost:8000/ws/memecoin/activity
    2. Immediately starts receiving token_created events and notifications count (no subscription required)
    3. Send subscription message to receive token_prices events
    
    Client Messages:
    - Subscribe to token prices: {"subscribe": ["0x123...", "0x456..."], "interval": 5}
    - Update interval: {"interval": 10}
    
    Server Messages:
    1. token_created (sent immediately after connection):
    {
        "type": "token_created",
        "data": {
            "token_address": "0x403D250545C70279b9af0f71683619c35d0E4f75",
            "token_symbol": "MEME",
            "avatar": "https://...",
            "txid": "0xabc123...",
            "creator_id": "user123",
            "collection_id": "abcdEf"
        }
    }
    
    2. token_prices (sent only after subscription):
    {
        "type": "token_prices",
        "data": [
            "0x403D250545C70279b9af0f71683619c35d0E4f75": {
                "price": 2.671148011666446e-05,
                "24h_change_percent": "2.4",
                "market_cap": 26711.480116664457
            }
        ]
    }
    
    3. notifications_count (sent periodically):
    {
        "type": "notifications_count",
        "data": {
            "unread_count": 5,
            "timestamp": "2024-01-01T12:00:00Z"
        }
    }
    
    4. error (when invalid messages are sent):
    {
        "type": "error",
        "message": "Subscription list is empty"
    }
    
    Features:
    - Non-blocking connection: No need to wait for subscription to start receiving token_created events
    - Triple message types: token_created (always) + token_prices (on-demand) + notifications_count (periodic)
    - Configurable interval: Default 6 seconds, can be updated via client message
    - Redis-based deduplication: Prevents sending duplicate token_created events per connection
    - Notifications count: Random interval (6-10 seconds) for notifications updates
    """
    await websocket.accept()
    client = websocket.client
    should_continue = True

    jwt_token = _extract_token(websocket.headers.get("Authorization"), None)
    user_id = None

    if not jwt_token:
        await websocket.send_json({"error": "Missing authorization token"})
        return

    try:
        async with get_session_context("read") as session:
            user = await verify_token("GET", jwt_token, session, caching_client)
            user_id = user.id if user else None
    except Exception as e:
        logger.error(f"Token verification failed: {str(e)}", exc_info=True)

    if not user_id:
        await websocket.send_json({"error": "Invalid token"})
        return

    logger.debug(f"User assets WebSocket connected: user_id={user_id}")
    
    # 初始化状态变量
    subscribed_tokens = []
    interval = 6  # 默认间隔6秒
    is_subscribed = False  # 订阅状态标志
    last_notification_check = datetime.utcnow()  # 上次检查通知的时间
    notification_interval = random.randint(6, 10)  # 通知检查间隔（6-10秒随机）
    
    try:
        while should_continue:
            try:
                # 非阻塞方式接收客户端消息
                data = await asyncio.wait_for(websocket.receive_text(), timeout=0.1)
                message = json.loads(data)
                
                if "subscribe" in message:
                    subscribed_tokens = message.get("subscribe", [])
                    is_subscribed = True  # 标记为已订阅
                    logger.info(f"Client {client} subscribed to tokens: {subscribed_tokens}")
                    
                    if not subscribed_tokens:
                        await websocket.send_text(json.dumps({
                            "type": "error", 
                            "message": "Subscription list is empty"
                        }))
                        is_subscribed = False
                        continue
                    
                if "interval" in message:
                    new_interval = message.get("interval")
                    if isinstance(new_interval, (int, float)) and new_interval >= 1:
                        interval = new_interval
                        logger.info(f"Updated interval for client {client}: {interval}")
                    else:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": "Invalid interval value"
                        }))
                        
            except asyncio.TimeoutError:
                pass
            except WebSocketDisconnect:
                logger.info(f"Token activity WebSocket disconnected: {client}")
                break
            except Exception as e:
                logger.error(f"Error handling client message: {str(e)}", exc_info=True)
                break

            # 始终推送 token_created 事件（无需等待订阅）
            new_tokens = await meme_service.get_unsent_token_creations(user_id, limit=5)
            if new_tokens:
                for token in new_tokens:
                    await websocket.send_text(json.dumps({
                        "type": "token_created",
                        "data": {
                            "token_address": token.get("address"),
                            "token_symbol": token.get("symbol"),
                            "avatar": token.get("avatar"),
                            "creator_id": token.get("creator_id"),
                            "collection_id": token.get("collection_id")
                        }
                    }))
            
            # 只有在客户端订阅后才推送 token_prices
            if is_subscribed and subscribed_tokens:
                token_prices = await meme_service.ws_token_prices(subscribed_tokens)
                await websocket.send_text(json.dumps({
                    "type": "token_prices",
                    "data": token_prices
                }))
            
            # 检查是否需要推送通知数量
            current_time = datetime.utcnow()
            if (current_time - last_notification_check).total_seconds() >= notification_interval:
                try:
                    # 查询未读通知数量
                    async with get_session_context("read") as session:
                        stmt = text(
                            f"""
                            SELECT COUNT(*)
                            FROM notifications
                            WHERE status = 'created'
                                AND recipient_id = '{user_id}'
                        """
                        )
                        result = await session.execute(stmt)
                        unread_count = result.scalar() or 0
                        
                        if unread_count > 0 :
                            # 发送通知数量数据
                            await websocket.send_text(json.dumps({
                                "type": "notifications_count",
                                "data": {
                                    "unread_count": unread_count,
                                    "timestamp": current_time.isoformat() + "Z"
                                }
                            }))
                    
                    # 更新检查时间和间隔
                    last_notification_check = current_time
                    notification_interval = random.randint(6, 10)  # 重新随机化间隔
                    
                except Exception as e:
                    logger.error(f"Error checking notifications count: {str(e)}", exc_info=True)
            
            await asyncio.sleep(interval)

    except WebSocketDisconnect:
        logger.info(f"Token prices WebSocket disconnected during setup: {client}")
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {str(e)}", exc_info=True)
    finally:
        # 清理资源
        should_continue = False
        # 确保 WebSocket 关闭
        try:
            await websocket.close()
        except Exception:
            pass
            
        logger.info(f"Token prices WebSocket cleanup completed for client {client}")


@app.websocket("/ws/token_prices")
async def ws_updating_token_price(
    websocket: WebSocket, meme_service: MemeService = Depends(get_meme_service)
):
    await websocket.accept()
    client = websocket.client
    should_continue = True
    
    try:
        # Wait for the client to send a subscription message, e.g., {"subscribe": ["BTC", "ETH"], "interval": 5}
        data = await websocket.receive_text()
        message = json.loads(data)
        subscribed_tokens = message.get("subscribe", [])
        interval = message.get("interval", 6)
        
        if not isinstance(interval, (int, float)) or interval < 1:
            interval = 6  # default 6 seconds
        
        if not subscribed_tokens:
            await websocket.send_text(json.dumps({"error": "No subscribe tokens"}))
            return

        while should_continue:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=0.1)
                message = json.loads(data)
                if "subscribe" in message:
                    subscribed_tokens = message.get("subscribe", [])
                    logger.info(f"Updated subscription for client {client}: {subscribed_tokens}")
                    if not subscribed_tokens:
                        await websocket.send_text(json.dumps({"message": "Subscription list is empty"}))
                        continue
                if "interval" in message:
                    new_interval = message.get("interval")
                    if isinstance(new_interval, (int, float)) and new_interval >= 1:
                        interval = new_interval
                        logger.info(f"Updated interval for client {client}: {interval}")
                    else:
                        await websocket.send_text(json.dumps({"message": "Invalid interval value"}))
            except asyncio.TimeoutError:
                pass
            except WebSocketDisconnect:
                logger.info(f"Token prices WebSocket disconnected: {client}")
                break
            except Exception as e:
                logger.error(f"Error handling client message: {str(e)}", exc_info=True)
                break

            token_prices = await meme_service.ws_token_prices(subscribed_tokens)
            await websocket.send_text(json.dumps(token_prices))
            await asyncio.sleep(interval)

    except WebSocketDisconnect:
        logger.info(f"Token prices WebSocket disconnected during setup: {client}")
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {str(e)}", exc_info=True)
    finally:
        # 清理资源
        should_continue = False
        
        # 确保 WebSocket 关闭
        try:
            await websocket.close()
        except Exception:
            pass
            
        logger.info(f"Token prices WebSocket cleanup completed for client {client}")


@app.get(
    "/transaction_history",
    tags=["Transactions"],
    summary="Get transaction history",
    description="Endpoint for getting user's transaction history",
    status_code=status.HTTP_200_OK,
    response_model=List[TransactionHistoryResponseSchema],
)
async def get_transaction_history(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_transaction_history(user.id)


@app.post(
    "/transaction_status",
    tags=["Transactions"],
    summary="Check transaction status",
    description="Endpoint for getting target transaction status for multiple transactions",
    status_code=status.HTTP_200_OK,
    response_model=List[TransactionHistoryResponseSchema],
)
async def get_transaction_status(
    tx_hash_list: List[str] = Body(..., title="A List of Transaction Hash"),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    if not tx_hash_list:
        return []
    return await meme_service.get_transaction_history(user.id, tx_hash_list)


@app.get(
    "/web3_transaction_status/{tx_hash}",
    tags=["Transactions"],
    summary="Get Web3 transaction status",
    description="Endpoint for directly calling web3 to get transaction status - checks if transaction succeeded or failed",
    status_code=status.HTTP_200_OK,
    response_model=Web3TransactionStatusSchema,
)
async def get_web3_transaction_status(
    tx_hash: str = Path(..., title="Transaction Hash", description="The transaction hash to check"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get transaction status directly from web3 to determine if transaction succeeded or failed"""
    return await meme_service.get_web3_transaction_status(tx_hash)


@app.get(
    "/estimated_gas",
    tags=["Token Operations"],
    summary="Get estimated gas",
    description="Endpoint for getting estimated gas (in wei) for target transaction type",
    status_code=status.HTTP_200_OK,
    response_model=EstimateGasResponseSchema,
)
async def get_estimated_gas(
    gas_type: EstimateGasType = Query(..., description="Type of gas estimation"),
    function_params: List[str] = Query(None, description="Function parameters (optional)"),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_estimate_gas(gas_type, function_params)


@app.get(
    "/bnb_balance",
    tags=["User Assets"],
    summary="Get BNB balance",
    description="Endpoint for getting user's BNB balance",
    status_code=status.HTTP_200_OK,
    response_model=BalanceResponseSchema,
)
async def get_bnb_balance(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_bnb_balance(user.id)


@app.get(
    "/token_amount",
    tags=["User Assets"],
    summary="Get token amount",
    description="Endpoint for getting target token amount",
    status_code=status.HTTP_200_OK,
    response_model=BalanceResponseSchema,
)
async def get_token_amount(
    token_address: str,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.get_token_amount(user.id, token_address)


@app.get(
    "/get_out_token_amount",
    tags=["Swap"],
    summary="Get out token amount when buying tokens",
    description="Get the amount of tokens you can receive with a given USD amount",
    status_code=status.HTTP_200_OK,
    response_model=OutTokenAmountResponseSchema,
)
async def get_out_token_amount(
    token_address: Optional[str] = Query(None, title="Token address"),
    usd_amount: str = Query(..., title="USD amount to swap"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get the amount of tokens you can receive for a given USD amount"""
    return await meme_service.get_out_token_amount(token_address, usd_amount)


@app.get(
    "/get_in_bnb_amount",
    tags=["Swap"],
    summary="Get required BNB amount when buying tokens",
    description="Get the amount of BNB needed for buying a specific token amount (excluding fees)",
    status_code=status.HTTP_200_OK,
    response_model=AmountResponseSchema,
)
async def get_in_bnb_amount(
    token_address: str = Query(..., title="Token address"),
    token_amount: str = Query(..., title="Token amount you want to receive"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get the amount of BNB needed to receive a specific token amount (excluding fees)"""
    return await meme_service.get_in_native_amount(token_address, token_amount)


@app.get(
    "/get_out_bnb_amount",
    tags=["Swap"],
    summary="Get BNB amount when selling tokens",
    description="Get the amount of BNB you can receive for selling given token amount",
    status_code=status.HTTP_200_OK,
    response_model=AmountResponseSchema,
)
async def get_out_bnb_amount(
    token_address: str = Query(..., title="Token address"),
    token_amount: str = Query(..., title="Token amount to swap"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get the amount of USDT you can receive for a given token amount (including fees)"""
    if Decimal(token_amount) > 50_000_000:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Token amount exceeds the maximum limit of 50,000,000.",
        )
    return await meme_service.get_out_native_amount(token_address, token_amount)


@app.get(
    "/get_out_bnb_amount_after_fee",
    tags=["Swap"],
    summary="Get BNB amount after fees when selling tokens",
    description="Get the amount of BNB you can receive for selling given token amount after fees",
    status_code=status.HTTP_200_OK,
    response_model=AmountResponseSchema,
)
async def get_out_bnb_amount_after_fee(
    token_address: str = Query(..., title="Token address"),
    token_amount: str = Query(..., title="Token amount to swap"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get the amount of BNB you can receive for a given token amount (excluding fees)"""
    if Decimal(token_amount) > 50_000_000:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Token amount exceeds the maximum limit of 50,000,000.",
        )
    return await meme_service.get_out_bnb_amount_after_fee(token_address, token_amount)


@app.get(
    "/get_token_progress",
    tags=["Token Info"],
    summary="Get token progress",
    description="Get the current bonding curve progress of a token (0-100)",
    status_code=status.HTTP_200_OK,
    response_model=int,
)
async def get_token_progress(
    token_address: str = Query(..., title="Token address"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Get the current progress of a token (0-100)"""
    return await meme_service.get_token_progress(token_address)


@app.get(
    "/graduate",
    tags=["Graduate"],
    summary="Graduate token to PancakeSwap",
    description="Graduate a token from the bonding curve to PancakeSwap DEX. The token must have reached 100% progress.",
    status_code=status.HTTP_200_OK,
    response_model=bool,
)
async def token_graduate(
    token_address: str = Query(..., title="Token address"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Graduate a token to PancakeSwap when it has reached 100% progress"""
    return await meme_service.token_graduate(token_address)

@app.get(
    "/recent_send_address",
    tags=["Transactions"],
    summary="Get recent send addresses",
    description="Endpoint for retrieving user's recently used send addresses for transactions.",
    status_code=status.HTTP_200_OK,
    response_model=List[RecentSendAddressSchema],
)
async def recent_send_address(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Retrieve a list of addresses the user has recently sent tokens or BNB to"""
    return await meme_service.recent_send_address(user.id)

@app.get(
    "/popular_meme",
    tags=["Market Data"],
    summary="Get popular meme tokens",
    description="Endpoint for retrieving popular meme tokens based on trading activity and user interest.",
    status_code=status.HTTP_200_OK,
    response_model=List[TokenSchema],
)
async def popular_meme(
    meme_service: MemeService = Depends(get_meme_service),
):
    """Retrieve a list of popular meme tokens based on trading activity and user interest"""
    return await meme_service.get_popular_meme()


@app.get(
    "/eth_price",
    tags=["Market Data"],
    summary="Get ETH price",
    description="Endpoint for getting current ETH price in USD",
    status_code=status.HTTP_200_OK,
    response_model=EthPriceResponseSchema,
)
async def get_eth_price(
    meme_service: MemeService = Depends(get_meme_service),
):
    """获取当前ETH价格（USD）"""
    return await meme_service.get_eth_price()

@app.get(
    "/cash_amount",
    tags=["User Assets"],
    summary="Get user's cash in USD",
    description="Endpoint for getting user's cash in USD",
    status_code=status.HTTP_200_OK,
    response_model=CashAmountResponseSchema,
)
async def get_cash_amount(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """获取用户ETH余额对应的USD价值"""
    wallet_address, usd_value = await meme_service.get_cash_amount(user.id)
    return CashAmountResponseSchema(
        wallet_address=wallet_address,
        cash_amount=decimal_to_string(usd_value)
    )

@app.get(
    "/export_key",
    tags=["User Assets"],
    summary="Export user's mnemonic phrase",
    description="Endpoint for exporting user's wallet mnemonic phrase. Requires authentication and a security phrase.",
    status_code=status.HTTP_200_OK,
    response_model=ExportMnemonicResponseSchema,
)
async def export_key(
    phrase: str,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Retrieve user's wallet mnemonic phrase"""
    if not phrase:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Phrase is required and can't be empty.")
    return await meme_service.export_key(user, phrase)

@app.post(
    "/claim",
    tags=["Reward"],
    summary="Claim specific amount of tokens",
    description="Endpoint for claiming a specific amount of tokens. User needs to specify token address and the amount to claim. Requires authentication.",
    status_code=status.HTTP_200_OK,
    response_model=CreationResponseSchema,
)
async def claim_reward(
    token_address: str,
    amount: str,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Claim a specific amount of tokens for the authenticated user"""
    amount_dec = Decimal(amount)
    if amount_dec <= 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Reward amount must be greater than 0.")
    return await meme_service.claim_reward(user.id, token_address, amount_dec)

@app.post(
    "/subscribe",
    tags=["Watchlist"],
    summary="Add token to watchlist",
    description="Add a token to user's watchlist",
    status_code=status.HTTP_200_OK,
    response_model=TokenWatchlistResponse,
)
async def subscribe_token(
    request: TokenWatchlistSchema,
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
    collection_service: CollectionService = Depends(get_collection_service),
    notification_service: NotificationService = Depends(get_notification_service),
):
    is_subscribed = await meme_service.add_token_to_watchlist(user.id, request.token_address, collection_service, notification_service)
    return TokenWatchlistResponse(is_subscribed=is_subscribed)

@app.delete(
    "/unsubscribe",
    tags=["Watchlist"],
    summary="Remove token from watchlist",
    description="Remove a token from user's watchlist",
    status_code=status.HTTP_200_OK,
    response_model=TokenWatchlistResponse,
)
async def unsubscribe_token(
    token_address: str = Query(title="Token Address"),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
    collection_service: CollectionService = Depends(get_collection_service),
    notification_service: NotificationService = Depends(get_notification_service),
):
    is_removed = await meme_service.remove_token_from_watchlist(user.id, token_address, collection_service, notification_service)
    return TokenWatchlistResponse(is_subscribed=False if is_removed else True)

@app.get(
    "/watchlist",
    tags=["Watchlist"],
    summary="Get user's watchlisted tokens",
    description="Get list of tokens in user's watchlist with various sort options. Can query any user's watchlist by providing user_id parameter.",
    status_code=status.HTTP_200_OK,
    response_model=TokenWatchlistWithTotalResponse,
)
async def get_token_watchlist(
    user_id: Optional[str] = Query(None, title="User ID", description="Target user ID to query watchlist. If not provided, uses current user's ID."),
    sort_by: SortCriteria = Query(SortCriteria.DEFAULT, title="Sort Criteria"),
    filter: Optional[str] = Query(None, title="Filter by token name"),
    pagination_params: PaginationParams = Depends(),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    获取用户的watchlist
    
    - 如果不提供user_id参数，则获取当前登录用户的watchlist
    - 如果提供user_id参数，则获取指定用户的watchlist
    - 返回的每个token都会包含关注者数量和当前用户是否关注的状态
    """
    target_user_id = user_id or user.id
    tokens, total = await meme_service.get_token_watchlist(
        target_user_id=target_user_id,
        current_user_id=user.id,  # 总是传入当前用户ID用于判断关注状态
        sort_criteria=sort_by, 
        pagination_params=pagination_params, 
        filter=filter
    )
    return TokenWatchlistWithTotalResponse(tokens=tokens, total=total, target_user_id=target_user_id)

@app.get(
    "/is_subscribed/{token_address}",
    tags=["Watchlist"],
    summary="Check if token is in watchlist",
    description="Check if a specific token is in user's watchlist",
    status_code=status.HTTP_200_OK,
    response_model=TokenWatchlistResponse,
)
async def check_token_watchlist(
    token_address: str = Path(..., title="Token Address"),
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    is_subscribed = await meme_service.check_token_watchlist(user.id, token_address)
    return TokenWatchlistResponse(is_subscribed=is_subscribed)

@app.get(
    "/is_holder",
    tags=["Token Info"],
    summary="Check if user holds token",
    description="Check if a specific user holds the given token. Does not require authentication.",
    status_code=status.HTTP_200_OK,
    response_model=bool,
)
async def is_token_holder(
    token_address: str = Query(..., title="Token Address"),
    user_id: str = Query(..., title="User ID"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """Check if a user holds a specific token, returns a boolean value"""
    return await meme_service.is_token_holder(user_id, token_address)

@app.get(
    "/can_create_meme",
    tags=["Token Operations"],
    summary="Check if user is eligible to create meme token",
    description="Check if the current user has eligibility to create a new meme token",
    status_code=status.HTTP_200_OK,
    response_model=bool,
)
async def can_create_meme(
    user: User = Depends(current_user),
    meme_service: MemeService = Depends(get_meme_service),
):
    return await meme_service.check_create_meme_eligibility(user.id)


@app.get(
    "/convert",
    tags=["Swap"],
    summary="Convert between token amount and USD amount",
    description="双向转换接口：传入token_amount计算USD价值，或传入usd_amount计算token数量。支持普通代币和原生代币(ETH)。必须提供其中一个参数，不能同时提供或都不提供。",
    status_code=status.HTTP_200_OK,
    response_model=ConversionResponseSchema,
)
async def convert_token_usd(
    token_address: str = Query(..., title="Token Address", description="代币合约地址，或'native_token'表示ETH/BNB/SOL"),
    token_amount: Optional[str] = Query(None, title="Token Amount", description="代币数量（可选，与usd_amount二选一）"),
    usd_amount: Optional[str] = Query(None, title="USD Amount", description="USD金额（可选，与token_amount二选一）"),
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    双向转换接口：
    - 传入 token_amount: 计算指定token数量对应的USD价值
    - 传入 usd_amount: 计算指定USD金额能购买的token数量
    
    支持的token_address类型：
    - 普通代币：使用具体的合约地址，如 0x1234...
    - 原生代币：使用 "native_token" 表示 ETH/BNB/SOL（根据当前区块链类型）
    
    示例:
    - /convert?token_address=0x123&token_amount=1000 (计算1000个token值多少USD)
    - /convert?token_address=0x123&usd_amount=100 (计算100USD能买多少个token)
    - /convert?token_address=native_token&token_amount=1 (计算1个ETH值多少USD)
    - /convert?token_address=native_token&usd_amount=100 (计算100USD能买多少个ETH)
    """
    return await meme_service.convert_token_usd(token_address, token_amount, usd_amount)

# ==================== MOONPAY 相关端点 ====================

@app.post(
    "/moonpay/sign_url",
    tags=["MoonPay"],
    summary="Sign MoonPay URL",
    description="对MoonPay URL进行HMAC-SHA256签名。",
    status_code=status.HTTP_200_OK,
    response_model=MoonPaySignatureResponse,
)
async def sign_moonpay_url(
    request: MoonPaySignatureRequest,
    meme_service: MemeService = Depends(get_meme_service),
):
    """
    对MoonPay URL进行签名
    
    根据MoonPay的安全要求，当URL包含敏感信息（如钱包地址、邮箱等）时，
    必须使用HMAC-SHA256算法对URL进行签名。
    
    签名算法：
    1. 使用secret key作为密钥
    2. 使用URL的查询字符串作为消息
    3. 生成HMAC-SHA256哈希
    4. 转换为Base64编码
    
    示例URL:
    ```
    https://buy-sandbox.moonpay.com?apiKey=pk_test_key&currencyCode=eth&walletAddress=0x123...
    ```
    """
    signature = meme_service.sign_moonpay_url(request.url)
    
    return MoonPaySignatureResponse(signature=signature)

@app.websocket("/ws/user_assets")
async def ws_user_assets(
    websocket: WebSocket,
    meme_service: MemeService = Depends(get_meme_service),
    caching_client: CachingClient = Depends(CachingClientGetter(logger)),
):
    await websocket.accept()
    
    # 用于追踪缓存刷新任务
    background_tasks = set()
    
    try:
        # 初始化验证 - 添加超时处理
        try:
            first_msg = await asyncio.wait_for(_get_msg(websocket), timeout=10.0)
        except asyncio.TimeoutError:
            logger.warning(f"WebSocket initial message timeout for client {websocket.client}")
            await websocket.send_json({"error": "Initial message timeout"})
            return
        except ValueError as e:
            # 处理 JSON 解析错误或连接异常
            if "1006" in str(e):
                logger.info(f"WebSocket client {websocket.client} disconnected unexpectedly during handshake")
            else:
                logger.warning(f"WebSocket initial message error for client {websocket.client}: {e}")
            return
        
        jwt_token = _extract_token(websocket.headers.get("authorization"), first_msg)
        interval = _extract_interval(first_msg)
        user_id = None

        if not jwt_token:
            await websocket.send_json({"error": "Missing authorization token"})
            return
            
        try:
            async with get_session_context("read") as session:
                user = await verify_token("GET", jwt_token, session, caching_client)
                user_id = user.id if user else None
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            
        if not user_id:
            await websocket.send_json({"error": "Invalid token"})
            return

        logger.debug(f"User assets WebSocket connected: user_id={user_id}, interval={interval}s")
        
        cache_key = f"user_assets:{user_id}"
        refresh_lock = asyncio.Lock()
        should_continue = True

        async def refresh_cache() -> None:
            """后台刷新缓存；已加锁，避免并发刷爆下游服务。"""
            if not should_continue:  # 检查是否应该继续
                return
                
            async with refresh_lock:
                try:
                    if not should_continue:  # 双重检查
                        return
                    
                    # 手动构建新的服务实例来避免数据库事务冲突
                    from src.memecoin.timescale_db import SessionLocal as KlineSessionRead
                    from src.memecoin.repos import MemeRepo
                    from src.memecoin.services import MemeService
                    
                    async with get_session_context("read") as fresh_session:
                        async with KlineSessionRead() as fresh_kline_session:
                            fresh_repo = MemeRepo(fresh_session, fresh_kline_session)
                            # 复用现有的缓存客户端和区块链连接器
                            fresh_service = MemeService(fresh_repo, caching_client, meme_service._web3)
                            new_assets = await fresh_service.get_user_assets(user_id)
                    if new_assets:  # 只有在获取到资产时才缓存
                        await caching_client.set(
                            endpoint=MEMECOIN_REDIS_ENDPOINT,
                            key=cache_key,
                            value=new_assets.dict(),
                            expiration_time=timedelta(minutes=5),
                        )
                except Exception as exc:
                    logger.error(f"Error refreshing user assets cache for user {user_id}: {exc}", exc_info=True)

        # 主循环：发送资产数据
        while should_continue:
            try:
                # 检查 WebSocket 连接状态
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.info(f"WebSocket connection lost for user {user_id}, stopping loop")
                    break
                
                # 获取资产数据
                assets = await caching_client.get(endpoint=MEMECOIN_REDIS_ENDPOINT, key=cache_key)
                
                if not assets:
                    # 缓存未命中，同步获取
                    assets = await meme_service.get_user_assets(user_id)
                    await caching_client.set(
                        endpoint=MEMECOIN_REDIS_ENDPOINT,
                        key=cache_key,
                        value=assets.dict(),
                        expiration_time=timedelta(minutes=5),
                    )
                else:
                    # 缓存命中，异步刷新
                    task = asyncio.create_task(refresh_cache())
                    background_tasks.add(task)
                    
                    # 清理已完成的任务
                    done_tasks = {t for t in background_tasks if t.done()}
                    background_tasks -= done_tasks

                # 发送数据
                await websocket.send_text(json.dumps(jsonable_encoder(assets)))
                
                # 等待下一次发送
                await asyncio.sleep(interval)

            except WebSocketDisconnect:
                logger.info(f"User assets WebSocket disconnected: {websocket.client}")
                break
            except Exception as exc:
                logger.error(f"Unexpected WS error: {exc}")
                break

    except WebSocketDisconnect:
        logger.info(f"User assets WebSocket disconnected during setup: {websocket.client}")
    except Exception as exc:
        # 区分不同类型的错误
        exc_str = str(exc)
        if "1006" in exc_str:
            logger.info(f"WebSocket client {websocket.client} disconnected unexpectedly (code 1006)")
        elif "1001" in exc_str:
            logger.info(f"WebSocket client {websocket.client} went away (code 1001)")
        elif "1000" in exc_str:
            logger.debug(f"WebSocket client {websocket.client} closed normally (code 1000)")
        else:
            logger.error(f"Error during WebSocket handling: {exc}", exc_info=True)
    finally:
        # 清理资源
        should_continue = False
        
        # 取消所有后台任务
        for task in background_tasks:
            if not task.done():
                task.cancel()
        
        # 等待所有任务完成
        if background_tasks:
            await asyncio.gather(*background_tasks, return_exceptions=True)
        
        # 确保 WebSocket 关闭
        try:
            await websocket.close()
        except Exception:
            pass
            
        logger.info(f"WebSocket cleanup completed for user {user_id if 'user_id' in locals() else 'unknown'}")



        