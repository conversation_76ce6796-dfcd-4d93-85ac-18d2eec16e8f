# Memecoin Service - Simplified using enhanced base
# Reduced from 76 lines to ~25 lines!

ARG PYTHON_VERSION=3.10.12
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv and Node.js in builder stage
USER root
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    gnupg \
    ca-certificates && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y --no-install-recommends nodejs && \
    pip install --no-cache-dir uv && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy service-specific requirements
COPY src/memecoin/requirements.txt /tmp/memecoin-requirements.txt
COPY src/auth/requirements.txt /tmp/auth-requirements.txt
COPY src/im/requirements.txt /tmp/im-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/memecoin-requirements.txt
RUN uv pip install --no-cache -r /tmp/auth-requirements.txt
RUN uv pip install --no-cache -r /tmp/im-requirements.txt

# Install npm dependencies for withdraw script
COPY src/memecoin/scripts/package.json /src/memecoin/scripts/package.json
COPY src/memecoin/scripts/package-lock.json /src/memecoin/scripts/package-lock.json
RUN cd /src/memecoin/scripts && npm install

# Switch to appuser for subsequent operations
USER appuser

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Copy Node.js and npm from builder
COPY --from=builder /usr/bin/node /usr/bin/
COPY --from=builder /usr/lib/node_modules /usr/lib/node_modules

# Create symlinks for npm and npx, and set up user home
USER root
RUN ln -s /usr/lib/node_modules/npm/bin/npm-cli.js /usr/bin/npm && \
    ln -s /usr/lib/node_modules/npm/bin/npx-cli.js /usr/bin/npx && \
    mkdir -p /home/<USER>
    rm -rf /home/<USER>/.npm && \
    mkdir -p /home/<USER>/.npm && \
    mkdir -p /home/<USER>/.npm/_cacache && \
    chown -R appuser:appuser /home/<USER>

# Set home directory for the app user
ENV HOME=/home/<USER>
ENV NPM_CONFIG_CACHE=/home/<USER>/.npm

# Add service scripts to PATH and install tsx globally
ENV PATH="/src/memecoin/scripts/node_modules/.bin:$PATH"
RUN npm install -g tsx typescript

# Add service scripts to PATH
ENV PATH="$PATH:/src/memecoin/scripts"

# Switch back to the non-privileged user
USER appuser

# Copy service-specific code
COPY --chown=appuser:appuser src/memecoin /src/memecoin

# Install npm dependencies in prod stage after copying scripts
USER root
COPY src/memecoin/scripts/package.json /src/memecoin/scripts/package.json
COPY src/memecoin/scripts/package-lock.json /src/memecoin/scripts/package-lock.json
COPY src/memecoin/scripts/tsconfig.json /src/memecoin/scripts/tsconfig.json
RUN chown -R appuser:appuser /src/memecoin/scripts && \
    rm -rf /home/<USER>/.npm && \
    mkdir -p /home/<USER>/.npm && \
    chown -R appuser:appuser /home/<USER>/.npm
USER appuser
RUN cd /src/memecoin/scripts && npm install
USER root
RUN rm -f /src/memecoin/scripts/package.json /src/memecoin/scripts/package-lock.json && \
    chown -R appuser:appuser /src/memecoin/scripts/node_modules && \
    chown -R appuser:appuser /home/<USER>/.npm
USER appuser

# Copy dependencies based on docker-compose.yml volumes
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database
COPY --chown=appuser:appuser src/infra /src/infra
COPY --chown=appuser:appuser src/auth /src/auth
COPY --chown=appuser:appuser src/authors /src/authors
COPY --chown=appuser:appuser src/notifications /src/notifications
COPY --chown=appuser:appuser src/posts /src/posts
COPY --chown=appuser:appuser src/agora /src/agora
COPY --chown=appuser:appuser src/reports /src/reports
COPY --chown=appuser:appuser src/im /src/im

# Set executable permissions for start script
RUN chmod +x /src/memecoin/scripts/start.sh
