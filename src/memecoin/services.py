import datetime
import time
import json
from decimal import Decimal, ROUND_DOWN
from typing import List, Optional, Dict, Union, Any, Tu<PERSON>
from datetime import timedelta

import httpx
import numpy as np
from eth_typing import ChecksumAddress
from fastapi import HTTPException
from pydantic import <PERSON><PERSON><PERSON>pter
from starlette import status
from starlette.status import HTTP_404_NOT_FOUND, HTTP_409_CONFLICT, HTTP_400_BAD_REQUEST
from web3.exceptions import Web3RPCError

from src.common.blockchain import BlockchainConnector
from src.common.caching.caching_client import CachingClient
from src.common.constants import UserTransactionType, UserTransactionStatus, Network
from src.common.utils import format_web3_error
from src.database.mixins import generate_shortid
from src.database.models import User
from src.database.models.Agora import LiveChannel
from src.database.models.Pair import Pair
from src.database.schemas import NotificationType
from src.agora.constants import ChannelStatus
from sqlalchemy import select

from src.common.constants import Pair<PERSON>tatus, MEMECOIN_REDIS_ENDPOINT
from src.memecoin.constants import BNB_USD_PRICE, SOL_USD_PRICE, ETH_USD_PRICE
from src.memecoin.gas_estimator import GasEstimator
from src.memecoin.logger import logger
from src.memecoin.repos import MemeRepo
from src.memecoin.schemas import (
    EstimateGasResponseSchema,
    OutTokenAmountResponseSchema,
    TokenSchema,
    BuybackTokenSchema,
    TokenPriceSchema,
    HolderSchema,
    CreationResponseSchema,
    AssetResponseSchema,
    ProfileTokenSchema,
    TransactionHistoryResponseSchema,
    EstimateGasType,
    TokenCache,
    MemeTokenSchema,
    BaseTokenSchema,
    BalanceResponseSchema,
    AmountResponseSchema, Interval, TokenListType, RankingType, ExportMnemonicResponseSchema, SortCriteria, EditMemeResponse,
    Web3TransactionStatusSchema,
    EthPriceResponseSchema,
    ConversionResponseSchema,
    CashAmountResponseSchema,
    WalletAddressResponseSchema,
)
from src.memecoin.settings import settings
from datetime import timedelta

from src.memecoin.utils import decimal_to_string, get_bnb_price, get_eth_price
from src.common.celery_client import CommonCeleryClient

import asyncio
import hashlib
import hmac
import base64
from urllib.parse import urlencode, quote, urlparse
import re


class TradeUSDTGas:
    LAUNCH_GAS = Decimal(0.02)
    BUY_WITH_USDT_GAS = Decimal(0.015)
    SELL_FOR_USDT_GAS = Decimal(0.015)
    SEND_WITH_USDT_GAS = Decimal(0.01)
    PURCHASE_GAS = Decimal(0.02)

class MemeService:
    export_key_phone_verified_endpoint = "export_key_phone_verified"
    export_key_email_verified_endpoint = "export_key_email_verified"
    settings_verified_endpoint = "settings_verified"

    def __init__(
        self, repo: MemeRepo, caching_client: CachingClient, web3: BlockchainConnector,
        initialize_celery_client: bool = True
    ):
        self._repo = repo
        self._caching_client = caching_client
        self._web3 = web3
        self._gas_estimator = GasEstimator(web3)
        self._celery_client = None
        if initialize_celery_client:
            self._celery_client = CommonCeleryClient(client_name="memecoin_service", logger=logger)


        if settings.BLOCKCHAIN_TYPE == "SOL":
            self._default_native_usd_price = settings.DEFAULT_SOL_PRICE
            self._native_usd_price_prefix = SOL_USD_PRICE
            self._quantizer = Decimal("0.000000001")  # 9 decimal places
            self._chain_id = settings.SOLANA_CHAIN_ID
        elif settings.BLOCKCHAIN_TYPE == "ETH":
            self._default_native_usd_price = settings.DEFAULT_ETH_PRICE
            self._native_usd_price_prefix = ETH_USD_PRICE
            self._quantizer = Decimal("0.000000000000000001")  # 18 decimal places
            self._chain_id = settings.ETH_CHAIN_ID
        else:  # Default to BSC/BNB
            self._default_native_usd_price = settings.DEFAULT_BNB_PRICE
            self._native_usd_price_prefix = BNB_USD_PRICE
            self._quantizer = Decimal("0.000000000000000001")  # 18 decimal places
            self._chain_id = settings.BSC_CHAIN_ID
    async def _get_price_change_percent(
        self, token_price: float, token_address: str
    ) -> float:
        """Get price change percentage, adding cache to improve performance"""
        # Add a cache key using token_address and current hour timestamp
        # This refreshes the cache automatically every hour
        current_hour = int(time.time()) // 3600
        cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:price_change:{token_address}:{current_hour}"
        
        cached_value = await self._caching_client.get(MEMECOIN_REDIS_ENDPOINT, cache_key)
        if cached_value and "percent" in cached_value:
            return float(cached_value["percent"])
            
        previous_24h_price = await self.get_nearest_price(
            token_address, int(time.time()), 86400
        )
        # 防止除以 0 或缺失数据导致异常
        if previous_24h_price is None or previous_24h_price == 0:
            price_change_percent = 0.0
        else:
            price_change_percent = (
                (token_price - previous_24h_price) / previous_24h_price
            ) * 100
            
        await self._caching_client.set(
            MEMECOIN_REDIS_ENDPOINT,
            cache_key,
            {"percent": str(price_change_percent)},
            expiration_time=timedelta(minutes=3),
        )
        
        return price_change_percent

    async def get_nearest_price(
        self, token_address: str, ts: int, interval: int
    ) -> Optional[float]:
        """
        get nearest price to ts-interval
        """
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}:price"
        return await self.get_nearest_value(key, ts, interval)

    async def get_nearest_value(
        self, key: str, ts: int, interval: int
    ) -> Optional[float]:
        """
        get nearest value to ts-interval
        """
        tolerance = interval
        target_ts = ts - interval
        min_ts = max(0, target_ts - tolerance)
        data = await self._caching_client.client.zrangebyscore(
            name=key, max=target_ts + tolerance, min=min_ts, start=0, num=1, withscores=False
        )

        if not data:
            data = await self._caching_client.client.zrange(
                name=key, start=1, end=1, desc=True, withscores=False
            )
            return None if not data else float(data[0])

        return float(data[0])

    async def _extract_social_links(self, pair) -> Optional[Dict[str, str]]:
        """
        Extract social links from the pair's social_links field.
        
        :param pair: Pair object that may contain social links
        :return: Dictionary of social links or None if not found
        """
        if not pair or not pair.social_links:
            return None
        
        try:
            return json.loads(pair.social_links)
        except json.JSONDecodeError:
            return None
    
    async def _create_token_from_pair(self, pair, user_id: Optional[str] = None) -> TokenSchema:
        """Create a TokenSchema object from a Pair object."""
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{pair.base}"
        cache = await self._caching_client.client.hgetall(key)
        if cache:
            token_cache = TokenCache.model_validate(cache)
            mc = token_cache.market_cap
            price = token_cache.price
            price_float = float(price)
            price_in_quote = token_cache.price_in_quote
            progress = token_cache.progress
        else:
            mc = pair.mc
            price = pair.price_usd
            progress = pair.bonding_curve
            quote_price = await get_bnb_price(self._caching_client.client)
            price_float = float(price)
            price_in_quote = price_float / quote_price

        # Extract any social links from the pair
        social_links = await self._extract_social_links(pair)
        price_change_percent = await self._get_price_change_percent(price_float, pair.base)
        
        # 获取关注者数量和当前用户关注状态
        follower_count = 0
        is_followed_by_current_user = False
        
        if user_id:
            # 批量获取关注者数量和关注状态
            follower_counts = await self._repo.batch_get_token_follower_counts([pair.base])
            followed_by_current_user = await self._repo.check_tokens_followed_by_user(user_id, [pair.base])
            
            follower_count = follower_counts.get(pair.base, 0)
            is_followed_by_current_user = followed_by_current_user.get(pair.base, False)
        
        return TokenSchema(
            token_name=pair.base_name,
            token_symbol=pair.base_symbol,
            token_address=pair.base,
            description=pair.base_description,
            image_url=pair.base_image_url,
            collection_id=pair.collection_id,
            market_cap=str(mc),
            price=f"{price_float:.20f}",
            price_in_quote=f"{price_in_quote:.20f}",
            price_change_percent=f"{price_change_percent:.1f}",
            progress=progress,
            created_at=int(pair.created_at.timestamp()) if pair.created_at else 0,
            social_links=social_links,
            follower_count=follower_count,  # 设置关注者数量
            is_followed_by_current_user=is_followed_by_current_user,  # 设置关注状态
        )

    async def get_token_address_by_collection_id(self, collection_id: str) -> str:
        """
        Fast method to get token address by collection ID.
        Only queries the database without additional price/liquidity calculations.
        """
        pairs = await self._repo.fetch_pairs_by_collection_ids([collection_id], self._chain_id)
        if not pairs:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND, 
                detail=f"Token not found for collection_id={collection_id}."
            )
        return pairs[0].base

    async def get_token_info(
        self, token_address: str, collection_id: str, user_id: Optional[str] = None
    ) -> TokenSchema:
        pair = None
        if token_address:
            pair = await self._repo.fetch_pair(token_address)
        else:
            pairs = await self._repo.fetch_pairs_by_collection_ids([collection_id], self._chain_id)
            if pairs:
                pair = pairs[0]
        if not pair:
            msg = (
                f"token_address={token_address}"
                if token_address
                else f"collection_id={collection_id}"
            )
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND, detail=f"Pair not found for {msg}."
            )

        token_schema = await self._create_token_from_pair(pair, user_id)  # 传入用户ID
        liq = await self._web3.get_liquidity(pair.base)
        token_schema.liquidity = f"{liq:.8f}"

        # Get 24h volume from kline_1m data
        volume_24h = await self._repo.fetch_24h_volume(pair.base)
        token_schema.volume_24h = f"{volume_24h:.8f}"

        token_schema.created_at = int(pair.base_created_at.timestamp()) if pair.base_created_at else 0
        return token_schema

    async def get_tokens_by_post_id(self, post_id: str) -> List[TokenSchema]:
        collection_ids = await self._repo.fetch_collection_id_by_post_id(post_id)
        pairs = await self._repo.fetch_pairs_by_collection_ids(collection_ids, self._chain_id)
        tokens: List[TokenSchema] = []
        for pair in pairs:
            token = await self._create_token_from_pair(pair)
            tokens.append(token)
        return tokens

    async def get_token_list(self, list_type, page_params) -> List[TokenSchema]:
        offset = (page_params.page - 1) * page_params.page_size
        pairs = await self._repo.fetch_token_list(
            list_type, offset, page_params.page_size, self._chain_id
        )
        tokens: List[TokenSchema] = []
        for pair in pairs:
            token = await self._create_token_from_pair(pair)
            tokens.append(token)
        return tokens

    async def update_token_cache_historical_mc(self):
        """
        更新所有token的TokenCache中的历史市值字段（daily_mc, weekly_mc, monthly_mc）
        这个方法应该在定时任务中调用，建议在每天00:05运行
        
        优化版本：使用更高效的批量处理和错误恢复机制
        """
        try:
            now = datetime.datetime.utcnow()
            
            # 计算关键时间点
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            days_since_monday = now.weekday()
            monday_start = now - timedelta(days=days_since_monday)
            monday_start = monday_start.replace(hour=0, minute=0, second=0, microsecond=0)
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # 获取所有活跃的token（增加更多以提高命中率）
            pairs = await self._repo.fetch_token_list(
                TokenListType.top, 0, 5000, self._chain_id  # 增加到5000个token
            )
            
            if not pairs:
                logger.info("No tokens found to update historical market caps")
                return
            
            token_addresses = [pair.base for pair in pairs]
            logger.info(f"Updating historical market caps for {len(token_addresses)} tokens")
            
            # 并行获取历史市值数据，提高效率
            daily_task = self._repo.fetch_batch_historical_market_caps(token_addresses, today_start)
            weekly_task = self._repo.fetch_batch_historical_market_caps(token_addresses, monday_start)
            monthly_task = self._repo.fetch_batch_historical_market_caps(token_addresses, month_start)
            
            # 等待所有任务完成
            daily_market_caps, weekly_market_caps, monthly_market_caps = await asyncio.gather(
                daily_task, weekly_task, monthly_task,
                return_exceptions=True
            )
            
            # 处理可能的异常
            if isinstance(daily_market_caps, Exception):
                logger.error(f"Failed to get daily market caps: {daily_market_caps}")
                daily_market_caps = {addr: 0.0 for addr in token_addresses}
            
            if isinstance(weekly_market_caps, Exception):
                logger.error(f"Failed to get weekly market caps: {weekly_market_caps}")
                weekly_market_caps = {addr: 0.0 for addr in token_addresses}
                
            if isinstance(monthly_market_caps, Exception):
                logger.error(f"Failed to get monthly market caps: {monthly_market_caps}")
                monthly_market_caps = {addr: 0.0 for addr in token_addresses}
            
            # 批量更新TokenCache，使用更大的批次
            batch_size = 100
            for i in range(0, len(token_addresses), batch_size):
                batch_addresses = token_addresses[i:i + batch_size]
                pipeline = self._caching_client.client.pipeline()
                
                for addr in batch_addresses:
                    cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                    
                    # 总是更新daily_mc（每天都可能变化）
                    daily_mc = daily_market_caps.get(addr, 0.0)
                    pipeline.hset(cache_key, "daily_mc", str(daily_mc))
                    
                    # 更新weekly_mc（只在周一或缺失时更新）
                    if now.weekday() == 0 or addr not in weekly_market_caps:
                        weekly_mc = weekly_market_caps.get(addr, 0.0)
                        pipeline.hset(cache_key, "weekly_mc", str(weekly_mc))
                    
                    # 更新monthly_mc（只在月初或缺失时更新）
                    if now.day == 1 or addr not in monthly_market_caps:
                        monthly_mc = monthly_market_caps.get(addr, 0.0)
                        pipeline.hset(cache_key, "monthly_mc", str(monthly_mc))
                
                # 执行批次更新
                try:
                    await pipeline.execute()
                    logger.debug(f"Updated batch {i//batch_size + 1}: {len(batch_addresses)} tokens")
                except Exception as e:
                    logger.error(f"Failed to update batch {i//batch_size + 1}: {str(e)}")
            
            logger.info(f"Successfully updated historical market caps for {len(token_addresses)} tokens")
            
            # 预热排行榜缓存
            try:
                logger.info("Pre-warming rankings cache after TokenCache update")
                await self.warm_up_rankings_cache()
            except Exception as e:
                logger.warning(f"Failed to pre-warm rankings cache: {str(e)}")
            
        except Exception as e:
            logger.error(f"Error updating token cache historical market caps: {str(e)}", exc_info=True)

    async def _get_historical_market_caps_batch(
        self, 
        token_addresses: List[str], 
        timestamp: datetime.datetime, 
        mc_field: str
    ) -> Dict[str, float]:
        """
        批量获取历史市值，带完整错误处理和多层降级机制
        
        :param token_addresses: token地址列表
        :param timestamp: 目标时间点
        :param mc_field: 缓存字段名 (daily_mc, weekly_mc, monthly_mc)
        :return: {token_address: market_cap} 字典
        """
        historical_market_caps = {}
        missing_addresses = []
        
        # 第一层：尝试批量从缓存获取
        try:
            pipeline = self._caching_client.client.pipeline()
            for addr in token_addresses:
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                pipeline.hget(cache_key, mc_field)
            
            # 设置较短的超时时间避免阻塞
            mc_values = await asyncio.wait_for(pipeline.execute(), timeout=5.0)
            
            # 解析缓存结果
            for addr, mc_value in zip(token_addresses, mc_values):
                if mc_value:
                    try:
                        historical_market_caps[addr] = float(mc_value)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Invalid {mc_field} value for {addr}: {mc_value}, error: {e}")
                        missing_addresses.append(addr)
                else:
                    missing_addresses.append(addr)
            
        except asyncio.TimeoutError:
            logger.error(f"Redis pipeline timeout for {mc_field}, using database fallback")
            missing_addresses = token_addresses.copy()
        except Exception as e:
            logger.error(f"Redis error for {mc_field}: {str(e)}, using database fallback")
            missing_addresses = token_addresses.copy()
        
        # 第二层：数据库批量查询降级
        if missing_addresses:
            cache_hit_rate = (len(token_addresses) - len(missing_addresses)) / len(token_addresses) * 100
            logger.info(f"Cache hit rate: {cache_hit_rate:.1f}% for {mc_field}, querying {len(missing_addresses)} from DB")
            
            try:
                # 使用超时保护数据库查询
                db_market_caps = await asyncio.wait_for(
                    self._repo.fetch_batch_historical_market_caps(missing_addresses, timestamp),
                    timeout=15.0
                )
                
                # 合并结果并验证数据有效性
                for addr, mc_value in db_market_caps.items():
                    if isinstance(mc_value, (int, float)) and mc_value >= 0:
                        historical_market_caps[addr] = float(mc_value)
                    else:
                        logger.warning(f"Invalid DB market cap for {addr}: {mc_value}")
                        historical_market_caps[addr] = 0.0
                
                # 异步回写缓存，使用低优先级任务
                if db_market_caps:
                    asyncio.create_task(
                        self._update_cache_with_retry(db_market_caps, mc_field),
                        name=f"cache_update_{mc_field}"
                    )
                    
            except asyncio.TimeoutError:
                logger.error(f"Database query timeout for {mc_field}, using zero fallback")
                for addr in missing_addresses:
                    if addr not in historical_market_caps:
                        historical_market_caps[addr] = 0.0
            except Exception as e:
                logger.error(f"Database query failed for {mc_field}: {str(e)}", exc_info=True)
                # 降级：为缺失的地址设置0值
                for addr in missing_addresses:
                    if addr not in historical_market_caps:
                        historical_market_caps[addr] = 0.0
        
        # 最终验证：确保所有地址都有值
        for addr in token_addresses:
            if addr not in historical_market_caps:
                historical_market_caps[addr] = 0.0
                logger.warning(f"Missing market cap data for {addr}, defaulting to 0.0")
        
        logger.debug(f"Retrieved {len(historical_market_caps)} historical market caps for {mc_field}")
        return historical_market_caps

    async def _update_cache_with_retry(self, market_caps: Dict[str, float], mc_field: str, max_retries: int = 3):
        """带重试机制的异步缓存更新"""
        for attempt in range(max_retries):
            try:
                await asyncio.wait_for(self._update_cache_async(market_caps, mc_field), timeout=10.0)
                logger.debug(f"Cache updated successfully for {mc_field} on attempt {attempt + 1}")
                return
            except asyncio.TimeoutError:
                logger.warning(f"Cache update timeout for {mc_field}, attempt {attempt + 1}/{max_retries}")
            except Exception as e:
                logger.warning(f"Cache update failed for {mc_field}, attempt {attempt + 1}/{max_retries}: {str(e)}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        logger.error(f"All cache update attempts failed for {mc_field}")

    async def _update_cache_async(self, market_caps: Dict[str, float], mc_field: str):
        """异步更新缓存，不阻塞主流程"""
        try:
            pipeline = self._caching_client.client.pipeline()
            for addr, market_cap in market_caps.items():
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                pipeline.hset(cache_key, mc_field, str(market_cap))
            await pipeline.execute()
        except Exception as e:
            logger.error(f"Failed to update cache for {mc_field}: {str(e)}")

    async def _create_tokens_with_increment_optimized(
        self, 
        pairs: List[Any], 
        historical_market_caps: Dict[str, float]
    ) -> List[Dict[str, Any]]:
        """
        优化的创建带增量信息的token列表 - 批量获取缓存数据减少网络调用
        """
        try:
            # 仅对 READY 且有有效 base 的地址做批量缓存读取/价格变化计算
            ready_addresses = [
                pair.base
                for pair in pairs
                if getattr(pair, "base", None) and getattr(pair, "status", None) == PairStatus.READY
            ]
            cache_data = await self._batch_get_token_cache(ready_addresses)
            price_change_data = await self._batch_get_price_changes(ready_addresses)
            
            # 获取BNB价格用于fallback计算
            quote_price = await get_bnb_price(self._caching_client.client)
            
            tokens_with_increment = []
            
            for pair in pairs:
                try:
                    is_ready_with_base = bool(getattr(pair, "base", None)) and getattr(pair, "status", None) == PairStatus.READY
                    # 从批量缓存中获取数据（仅对就绪条目）
                    token_cache = cache_data.get(pair.base) if is_ready_with_base else None
                    price_change = price_change_data.get(pair.base, 0.0) if is_ready_with_base else 0.0
                    
                    # 确定市值和价格数据
                    if is_ready_with_base:
                        if token_cache:
                            mc = token_cache.market_cap
                            price_float = token_cache.price
                            price_in_quote = token_cache.price_in_quote
                            progress = token_cache.progress
                        else:
                            mc = pair.mc
                            price_float = float(pair.price_usd)
                            price_in_quote = price_float / quote_price if quote_price else 0.0
                            progress = pair.bonding_curve
                    else:
                        # NOT_READY 或无有效 base：按需求将统计数值置为 0
                        mc = 0.0
                        price_float = 0.0
                        price_in_quote = 0.0
                        progress = 0
                    
                    # 获取历史市值并计算增量
                    historical_market_cap = historical_market_caps.get(pair.base, 0.0) if is_ready_with_base else 0.0
                    current_market_cap = float(mc)
                    market_cap_increment = current_market_cap - historical_market_cap
                    
                    # 异步提取社交链接 (不阻塞主流程)
                    social_links = await self._extract_social_links(pair)
                    
                    # 创建TokenSchema
                    token = TokenSchema(
                        token_name=pair.base_name,
                        token_symbol=pair.base_symbol,
                        token_address=pair.base,
                        description=pair.base_description,
                        image_url=pair.base_image_url,
                        collection_id=pair.collection_id,
                        market_cap=str(current_market_cap),
                        market_cap_increment=f"{market_cap_increment:.2f}",
                        price=f"{price_float:.20f}",
                        price_in_quote=f"{price_in_quote:.20f}",
                        price_change_percent=f"{price_change:.1f}",
                        progress=progress,
                        created_at=int(pair.created_at.timestamp()) if pair.created_at else 0,
                        social_links=social_links,
                    )
                    
                    tokens_with_increment.append({
                        'token': token,
                        'increment': market_cap_increment,
                        'current_mc': current_market_cap
                    })
                    
                except (ValueError, TypeError, AttributeError) as e:
                    logger.error(f"Error processing token {pair.base}: {str(e)}")
                    continue
            
            return tokens_with_increment
            
        except Exception as e:
            logger.error(f"Critical error in _create_tokens_with_increment_optimized: {str(e)}")
            return []

    async def _batch_get_token_cache(self, token_addresses: List[str]) -> Dict[str, TokenCache]:
        """批量获取token缓存数据"""
        cache_data = {}
        try:
            pipeline = self._caching_client.client.pipeline()
            for addr in token_addresses:
                key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                pipeline.hgetall(key)
            
            results = await pipeline.execute()
            
            for addr, cache in zip(token_addresses, results):
                if cache:
                    try:
                        cache_data[addr] = TokenCache.model_validate(cache)
                    except Exception as e:
                        logger.warning(f"Invalid cache data for {addr}: {e}")
                        
        except Exception as e:
            logger.error(f"Error in _batch_get_token_cache: {str(e)}")
            
        return cache_data

    async def _batch_get_price_changes(self, token_addresses: List[str]) -> Dict[str, float]:
        """批量获取价格变化百分比"""
        price_changes = {}
        current_hour = int(time.time()) // 3600
        
        try:
            pipeline = self._caching_client.client.pipeline()
            for addr in token_addresses:
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:price_change:{addr}:{current_hour}"
                pipeline.hget(MEMECOIN_REDIS_ENDPOINT, cache_key)
            
            results = await pipeline.execute()
            
            missing_addresses = []
            for addr, cached_value in zip(token_addresses, results):
                if cached_value and "percent" in cached_value:
                    try:
                        price_changes[addr] = float(cached_value["percent"])
                    except (ValueError, TypeError):
                        missing_addresses.append(addr)
                else:
                    missing_addresses.append(addr)
            
            # 对于缺失的地址，批量计算价格变化
            if missing_addresses:
                await self._batch_calculate_price_changes(missing_addresses, price_changes, current_hour)
                
        except Exception as e:
            logger.error(f"Error in _batch_get_price_changes: {str(e)}")
            # 降级：返回0值
            price_changes = {addr: 0.0 for addr in token_addresses}
            
        return price_changes

    async def _batch_calculate_price_changes(self, token_addresses: List[str], price_changes: Dict[str, float], current_hour: int):
        """批量计算价格变化百分比"""
        try:
            current_time = int(time.time())
            tasks = []
            
            # 并发获取24小时前价格
            for addr in token_addresses:
                task = self.get_nearest_price(addr, current_time, 86400)
                tasks.append(task)
            
            previous_prices = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 批量更新缓存
            pipeline = self._caching_client.client.pipeline()
            
            for addr, previous_price in zip(token_addresses, previous_prices):
                if isinstance(previous_price, Exception) or previous_price is None:
                    price_change_percent = 0.0
                else:
                    # 获取当前价格 (从已有的缓存或计算)
                    current_price_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                    current_cache = await self._caching_client.client.hget(current_price_key, "price")
                    if current_cache:
                        try:
                            current_price = float(current_cache)
                            price_change_percent = ((current_price - previous_price) / previous_price) * 100
                        except (ValueError, TypeError, ZeroDivisionError):
                            price_change_percent = 0.0
                    else:
                        price_change_percent = 0.0
                
                price_changes[addr] = price_change_percent
                
                # 缓存结果
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:price_change:{addr}:{current_hour}"
                pipeline.hset(
                    MEMECOIN_REDIS_ENDPOINT,
                    cache_key,
                    json.dumps({"percent": str(price_change_percent)})
                )
                pipeline.expire(f"{MEMECOIN_REDIS_ENDPOINT}:{cache_key}", 3600)  # 1小时过期
            
            await pipeline.execute()
            
        except Exception as e:
            logger.error(f"Error in _batch_calculate_price_changes: {str(e)}")
            # 降级处理
            for addr in token_addresses:
                if addr not in price_changes:
                    price_changes[addr] = 0.0

    async def get_token_rankings(self, ranking_type: RankingType, page_params) -> Union[List[TokenSchema], List[BuybackTokenSchema]]:
        """
        获取排行榜数据，全局缓存优化版本 - 所有用户共享相同的排行榜结果
        
        :param ranking_type: 排行榜类型 (daily, weekly, buyback)
        :param page_params: 分页参数
        :return: 根据排行榜类型返回相应的 token 列表
        """
        start_time = datetime.datetime.utcnow()
        
        # 性能监控指标
        metrics = {
            'ranking_type': ranking_type.value,
            'page': page_params.page,
            'page_size': page_params.page_size,
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'tokens_processed': 0
        }
        
        try:
            # 参数验证
            if not isinstance(ranking_type, RankingType):
                logger.error(f"Invalid ranking type: {ranking_type}")
                return []
            
            # 第一层：尝试从全局排行榜缓存获取完整结果
            global_cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type.value}"
            cached_rankings = await self._get_cached_global_rankings(global_cache_key)
            
            if cached_rankings:
                metrics['cache_hits'] = 1
                # 从缓存的完整排行榜中提取分页结果
                paginated_result = self._paginate_cached_rankings(cached_rankings, page_params, ranking_type)
                
                duration = (datetime.datetime.utcnow() - start_time).total_seconds()
                logger.info(f"Rankings from global cache: type={ranking_type}, page={page_params.page}, duration={duration:.3f}s")
                
                # 异步记录缓存命中指标
                asyncio.create_task(self._record_performance_metrics(metrics))
                return paginated_result
            
            metrics['cache_misses'] = 1
            
            # 第二层：缓存未命中，需要重新计算完整排行榜
            logger.info(f"Global cache miss for {ranking_type}, rebuilding rankings")
            
            # 获取足够多的tokens来生成完整排行榜 (而不是只获取当前页)
            # 这样可以服务所有分页请求
            max_tokens_for_rankings = 1000  # 可配置的最大排行榜token数量
            
            pairs = await self._repo.fetch_token_list(
                TokenListType.top, 0, max_tokens_for_rankings, self._chain_id
            )
            
            if not pairs:
                logger.info(f"No pairs found for ranking_type={ranking_type}")
                return []
            
            metrics['db_queries'] += 1
            metrics['tokens_processed'] = len(pairs)
            
            # 计算时间窗口
            now = datetime.datetime.utcnow()
            timestamp_map = {
                RankingType.daily: now.replace(hour=0, minute=0, second=0, microsecond=0),
                RankingType.weekly: (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0),
                RankingType.buyback: now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            }
            
            mc_field_map = {
                RankingType.daily: "daily_mc",
                RankingType.weekly: "weekly_mc",
                RankingType.buyback: "monthly_mc"
            }
            
            timestamp = timestamp_map[ranking_type]
            mc_field = mc_field_map[ranking_type]
            # 仅对就绪且有有效 base 的条目做缓存预热与历史市值查询
            token_addresses = [
                pair.base
                for pair in pairs
                if getattr(pair, "base", None) and getattr(pair, "status", None) == PairStatus.READY
            ]
            
            # 预热缓存 (异步任务，不阻塞主流程)
            asyncio.create_task(
                self._warm_up_cache_for_addresses(token_addresses, mc_field),
                name=f"cache_warmup_{ranking_type.value}"
            )
            
            # 并行执行历史市值获取
            historical_task = self._get_historical_market_caps_batch(token_addresses, timestamp, mc_field)
            
            # 等待历史市值数据
            historical_market_caps = await historical_task
            
            # 创建带增量的token列表 (使用优化版本)
            tokens_with_increment = await self._create_tokens_with_increment_optimized(pairs, historical_market_caps)
            
            if not tokens_with_increment:
                logger.warning(f"No valid tokens created for ranking_type={ranking_type}")
                return []
            
            # 排序：主排序按增量，次排序按当前市值
            tokens_with_increment.sort(key=lambda x: (x['increment'], x['current_mc']), reverse=True)
            
            # 根据排行榜类型创建完整排行榜
            if ranking_type == RankingType.buyback:
                full_rankings = self._create_buyback_tokens(tokens_with_increment)
            else:
                full_rankings = [item['token'] for item in tokens_with_increment]
            
            # 异步缓存完整排行榜 (全局缓存，服务所有用户)
            if full_rankings:
                asyncio.create_task(
                    self._cache_global_rankings(global_cache_key, full_rankings, ranking_type),
                    name=f"cache_global_{ranking_type.value}"
                )
            
            # 从完整排行榜中提取当前页结果
            paginated_result = self._paginate_cached_rankings(full_rankings, page_params, ranking_type)
            
            # 记录性能指标和监控数据
            duration = (datetime.datetime.utcnow() - start_time).total_seconds()
            metrics['duration'] = duration
            metrics['result_count'] = len(paginated_result)
            metrics['full_rankings_count'] = len(full_rankings)
            
            # 异步记录指标
            asyncio.create_task(
                self._record_performance_metrics(metrics),
                name="record_metrics"
            )
            
            logger.info(f"Token rankings rebuilt: type={ranking_type}, full_count={len(full_rankings)}, page_result={len(paginated_result)}, duration={duration:.3f}s")
            
            return paginated_result
            
        except Exception as e:
            duration = (datetime.datetime.utcnow() - start_time).total_seconds()
            metrics['duration'] = duration
            metrics['error'] = str(e)
            
            # 记录错误指标
            asyncio.create_task(
                self._record_performance_metrics(metrics),
                name="record_error_metrics"
            )
            
            logger.error(f"Critical error in get_token_rankings: type={ranking_type}, duration={duration:.3f}s, error={str(e)}", exc_info=True)
            return []

    async def _get_cached_global_rankings(self, cache_key: str) -> Optional[List]:
        """获取缓存的全局排行榜结果"""
        try:
            cached_data = await asyncio.wait_for(
                self._caching_client.client.get(cache_key),
                timeout=3.0  # 稍长的超时，因为数据更大
            )
            if cached_data:
                return json.loads(cached_data)
        except (asyncio.TimeoutError, json.JSONDecodeError, Exception) as e:
            logger.debug(f"Global cache miss for rankings: {str(e)}")
        return None

    async def _cache_global_rankings(self, cache_key: str, full_rankings: List, ranking_type: RankingType):
        """缓存完整的全局排行榜结果"""
        try:
            # 根据排行榜类型设置不同的缓存时间
            cache_times = {
                RankingType.daily: 180,     # 3分钟 (更频繁更新)
                RankingType.weekly: 600,    # 10分钟  
                RankingType.buyback: 1200   # 20分钟 (月度数据变化较慢)
            }
            
            cache_time = cache_times.get(ranking_type, 180)
            
            # 序列化结果
            serialized_result = []
            for item in full_rankings:
                if hasattr(item, 'model_dump'):
                    serialized_result.append(item.model_dump())
                else:
                    serialized_result.append(item)
            
            # 使用压缩存储以节省内存 (可选)
            json_data = json.dumps(serialized_result)
            
            await asyncio.wait_for(
                self._caching_client.client.setex(cache_key, cache_time, json_data),
                timeout=10.0  # 更长的超时，因为数据更大
            )
            
            logger.info(f"Cached global rankings: type={ranking_type}, count={len(full_rankings)}, cache_time={cache_time}s")
            
        except Exception as e:
            logger.warning(f"Failed to cache global rankings: {str(e)}")

    def _paginate_cached_rankings(self, full_rankings: List, page_params, ranking_type: RankingType) -> List:
        """从缓存的完整排行榜中提取分页结果"""
        try:
            if not full_rankings:
                return []
            
            # 计算分页偏移
            offset = (page_params.page - 1) * page_params.page_size
            end_offset = offset + page_params.page_size
            
            # 对于buyback类型，限制为前50名
            if ranking_type == RankingType.buyback:
                full_rankings = full_rankings[:50]
                end_offset = min(end_offset, 50)
            
            # 提取分页结果
            paginated_result = full_rankings[offset:end_offset]
            
            # 如果是从字典反序列化的数据，需要重新创建Schema对象
            if paginated_result and isinstance(paginated_result[0], dict):
                schema_class = BuybackTokenSchema if ranking_type == RankingType.buyback else TokenSchema
                paginated_result = [schema_class(**item) for item in paginated_result]
            
            logger.debug(f"Paginated rankings: type={ranking_type}, page={page_params.page}, size={page_params.page_size}, result_count={len(paginated_result)}")
            
            return paginated_result
            
        except Exception as e:
            logger.error(f"Error paginating cached rankings: {str(e)}")
            return []


    async def _warm_up_cache_for_addresses(self, token_addresses: List[str], mc_field: str):
        """为指定地址预热缓存数据"""
        try:
            # 检查哪些地址的缓存数据即将过期或缺失
            pipeline = self._caching_client.client.pipeline()
            for addr in token_addresses:
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                pipeline.ttl(cache_key)
            
            ttls = await pipeline.execute()
            
            # 找出需要预热的地址 (TTL < 300秒或不存在)
            addresses_to_warm = []
            for addr, ttl in zip(token_addresses, ttls):
                if ttl == -2 or ttl < 300:  # -2表示不存在，<300表示即将过期
                    addresses_to_warm.append(addr)
            
            if addresses_to_warm:
                logger.debug(f"Warming up cache for {len(addresses_to_warm)} addresses")
                # 这里可以添加预热逻辑，比如提前获取价格数据等
                
        except Exception as e:
            logger.debug(f"Cache warm-up failed: {str(e)}")

    async def _record_performance_metrics(self, metrics: Dict[str, Any]):
        """记录性能监控指标"""
        try:
            # 将指标存储到Redis
            timestamp = int(time.time())
            metrics_key = f"{MEMECOIN_REDIS_ENDPOINT}:metrics:rankings:{timestamp}"
            
            # 存储详细指标
            await asyncio.wait_for(
                self._caching_client.client.setex(metrics_key, 3600, json.dumps(metrics)),
                timeout=2.0
            )
            
            # 更新聚合统计 (用于监控面板)
            await self._update_performance_stats(metrics)
            
            # 性能告警
            duration = metrics.get('duration', 0)
            if duration > 10.0:
                logger.error(f"Very slow rankings query (>{duration:.1f}s): {metrics}")
            elif duration > 5.0:
                logger.warning(f"Slow rankings query (>{duration:.1f}s): {metrics}")
                
        except Exception as e:
            logger.debug(f"Failed to record metrics: {str(e)}")

    async def warm_up_rankings_cache(self):
        """
        主动预热排行榜缓存 - 可以通过定时任务调用
        为所有排行榜类型生成并缓存完整排行榜
        """
        logger.info("Starting proactive rankings cache warm-up")
        
        success_count = 0
        results = []
        
        # 串行预热所有排行榜类型以避免会话并发问题
        for ranking_type in [RankingType.daily, RankingType.weekly, RankingType.buyback]:
            try:
                result = await self._warm_up_single_ranking(ranking_type)
                results.append(result)
                success_count += 1
                logger.info(f"Successfully warmed up {ranking_type} rankings: {result} tokens")
            except Exception as e:
                logger.error(f"Failed to warm up {ranking_type} rankings: {str(e)}")
                results.append(e)
        
        logger.info(f"Cache warm-up completed: {success_count}/3 ranking types successful")

    async def _warm_up_single_ranking(self, ranking_type: RankingType) -> int:
        """预热单个排行榜类型的缓存"""
        try:
            # 检查缓存是否即将过期
            global_cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type.value}"
            ttl = await self._caching_client.client.ttl(global_cache_key)
            
            # 如果缓存还有超过30秒的生命周期，跳过预热
            if ttl > 30:
                logger.debug(f"Skipping warm-up for {ranking_type}, cache still valid for {ttl}s")
                return 0
            
            logger.info(f"Warming up {ranking_type} rankings cache (TTL: {ttl}s)")
            
            # 获取完整的token列表
            max_tokens_for_rankings = 1000
            pairs = await self._repo.fetch_token_list(
                TokenListType.top, 0, max_tokens_for_rankings, self._chain_id
            )
            
            if not pairs:
                logger.warning(f"No pairs found for warm-up: {ranking_type}")
                return 0
            
            # 计算时间窗口
            now = datetime.datetime.utcnow()
            timestamp_map = {
                RankingType.daily: now.replace(hour=0, minute=0, second=0, microsecond=0),
                RankingType.weekly: (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0),
                RankingType.buyback: now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            }
            
            mc_field_map = {
                RankingType.daily: "daily_mc",
                RankingType.weekly: "weekly_mc",
                RankingType.buyback: "monthly_mc"
            }
            
            timestamp = timestamp_map[ranking_type]
            mc_field = mc_field_map[ranking_type]
            token_addresses = [pair.base for pair in pairs]
            
            # 获取历史市值数据
            historical_market_caps = await self._get_historical_market_caps_batch(
                token_addresses, timestamp, mc_field
            )
            
            # 创建带增量的token列表
            tokens_with_increment = await self._create_tokens_with_increment_optimized(
                pairs, historical_market_caps
            )
            
            if not tokens_with_increment:
                logger.warning(f"No valid tokens created for warm-up: {ranking_type}")
                return 0
            
            # 排序
            tokens_with_increment.sort(key=lambda x: (x['increment'], x['current_mc']), reverse=True)
            
            # 创建完整排行榜
            if ranking_type == RankingType.buyback:
                full_rankings = self._create_buyback_tokens(tokens_with_increment)
            else:
                full_rankings = [item['token'] for item in tokens_with_increment]
            
            # 缓存完整排行榜
            await self._cache_global_rankings(global_cache_key, full_rankings, ranking_type)
            
            logger.info(f"Warm-up completed for {ranking_type}: {len(full_rankings)} tokens cached")
            return len(full_rankings)
            
        except Exception as e:
            logger.error(f"Error warming up {ranking_type} rankings: {str(e)}")
            raise

    async def get_cache_status(self) -> Dict[str, Any]:
        """
        获取排行榜缓存状态信息
        """
        try:
            cache_status = {
                'rankings_cache': {},
                'system_stats': {
                    'total_cache_keys': 0,
                    'memory_usage_mb': 0
                }
            }
            
            # 检查各排行榜缓存状态
            for ranking_type in [RankingType.daily, RankingType.weekly, RankingType.buyback]:
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type.value}"
                
                try:
                    ttl = await self._caching_client.client.ttl(cache_key)
                    exists = await self._caching_client.client.exists(cache_key)
                    
                    cache_status['rankings_cache'][ranking_type.value] = {
                        'exists': bool(exists),
                        'ttl_seconds': ttl if ttl > 0 else 0,
                        'status': 'active' if ttl > 30 else 'expiring_soon' if ttl > 0 else 'expired'
                    }
                    
                    if exists:
                        # 获取缓存大小信息 (可选)
                        memory_usage = await self._caching_client.client.memory_usage(cache_key)
                        if memory_usage:
                            cache_status['rankings_cache'][ranking_type.value]['memory_bytes'] = memory_usage
                            cache_status['system_stats']['memory_usage_mb'] += memory_usage / (1024 * 1024)
                        
                except Exception as e:
                    cache_status['rankings_cache'][ranking_type.value] = {
                        'exists': False,
                        'ttl_seconds': 0,
                        'status': 'error',
                        'error': str(e)
                    }
            
            # 系统统计
            try:
                info = await self._caching_client.client.info('memory')
                cache_status['system_stats']['redis_memory_mb'] = info.get('used_memory', 0) / (1024 * 1024)
                cache_status['system_stats']['redis_peak_memory_mb'] = info.get('used_memory_peak', 0) / (1024 * 1024)
            except:
                pass
            
            return cache_status
            
        except Exception as e:
            logger.error(f"Error getting cache status: {str(e)}")
            return {'error': str(e)}

    async def invalidate_rankings_cache(self, reason: str = "manual"):
        """
        手动清除排行榜全局缓存
        
        :param reason: 清除缓存的原因，用于日志记录
        :return: 清除的缓存键数量
        """
        try:
            ranking_types = ['daily', 'weekly', 'buyback']
            pipeline = self._caching_client.client.pipeline()
            
            for ranking_type in ranking_types:
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type}"
                pipeline.delete(cache_key)
            
            # 执行批量删除
            deleted_keys = await pipeline.execute()
            deleted_count = sum(deleted_keys)
            
            logger.info(f"Invalidated {deleted_count} rankings cache keys (reason: {reason})")
            
            # 异步触发缓存预热，确保下次请求能快速响应
            if deleted_count > 0:
                asyncio.create_task(
                    self.warm_up_rankings_cache(),
                    name=f"cache_warmup_after_invalidation_{reason}"
                )
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to invalidate rankings cache (reason: {reason}): {str(e)}")
            return 0

    async def _update_performance_stats(self, metrics: Dict[str, Any]):
        """更新性能统计数据"""
        try:
            ranking_type = metrics.get('ranking_type', 'unknown')
            duration = metrics.get('duration', 0)
            
            # 更新计数器和平均响应时间
            pipeline = self._caching_client.client.pipeline()
            
            # 每小时统计
            hour_key = f"{MEMECOIN_REDIS_ENDPOINT}:stats:hourly:{int(time.time()) // 3600}"
            pipeline.hincrby(hour_key, f"{ranking_type}_count", 1)
            pipeline.hincrbyfloat(hour_key, f"{ranking_type}_duration", duration)
            pipeline.expire(hour_key, 86400)  # 24小时过期
            
            # 每日统计
            day_key = f"{MEMECOIN_REDIS_ENDPOINT}:stats:daily:{int(time.time()) // 86400}"
            pipeline.hincrby(day_key, f"{ranking_type}_count", 1) 
            pipeline.hincrbyfloat(day_key, f"{ranking_type}_duration", duration)
            pipeline.expire(day_key, 86400 * 7)  # 7天过期
            
            # 错误统计
            if 'error' in metrics:
                pipeline.hincrby(hour_key, f"{ranking_type}_errors", 1)
                pipeline.hincrby(day_key, f"{ranking_type}_errors", 1)
            
            # 缓存命中率统计
            if metrics.get('cache_hits', 0) > 0:
                pipeline.hincrby(hour_key, f"{ranking_type}_cache_hits", 1)
                pipeline.hincrby(day_key, f"{ranking_type}_cache_hits", 1)
            else:
                pipeline.hincrby(hour_key, f"{ranking_type}_cache_misses", 1)
                pipeline.hincrby(day_key, f"{ranking_type}_cache_misses", 1)
            
            await pipeline.execute()
            
        except Exception as e:
            logger.debug(f"Failed to update performance stats: {str(e)}")

    async def get_performance_dashboard(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取性能监控面板数据
        
        :param hours: 查询最近多少小时的数据
        :return: 性能统计数据
        """
        try:
            current_hour = int(time.time()) // 3600
            dashboard_data = {
                'overview': {
                    'total_requests': 0,
                    'avg_response_time': 0.0,
                    'error_rate': 0.0,
                    'cache_hit_rate': 0.0
                },
                'by_type': {},
                'hourly_trend': []
            }
            
            # 获取指定时间范围内的统计数据
            for hour_offset in range(hours):
                hour_key = f"{MEMECOIN_REDIS_ENDPOINT}:stats:hourly:{current_hour - hour_offset}"
                stats = await self._caching_client.client.hgetall(hour_key)
                
                if not stats:
                    continue
                
                # 处理每小时数据
                hour_data = {'timestamp': (current_hour - hour_offset) * 3600}
                
                for ranking_type in ['daily', 'weekly', 'buyback']:
                    count_key = f"{ranking_type}_count"
                    duration_key = f"{ranking_type}_duration"
                    error_key = f"{ranking_type}_errors"
                    hit_key = f"{ranking_type}_cache_hits"
                    miss_key = f"{ranking_type}_cache_misses"
                    
                    count = int(stats.get(count_key, 0))
                    total_duration = float(stats.get(duration_key, 0))
                    errors = int(stats.get(error_key, 0))
                    hits = int(stats.get(hit_key, 0))
                    misses = int(stats.get(miss_key, 0))
                    
                    if count > 0:
                        avg_duration = total_duration / count
                        error_rate = (errors / count) * 100
                        total_cache_requests = hits + misses
                        cache_hit_rate = (hits / total_cache_requests * 100) if total_cache_requests > 0 else 0
                        
                        # 更新按类型统计
                        if ranking_type not in dashboard_data['by_type']:
                            dashboard_data['by_type'][ranking_type] = {
                                'total_requests': 0,
                                'avg_response_time': 0.0,
                                'error_rate': 0.0,
                                'cache_hit_rate': 0.0
                            }
                        
                        type_stats = dashboard_data['by_type'][ranking_type]
                        type_stats['total_requests'] += count
                        type_stats['avg_response_time'] = ((type_stats['avg_response_time'] * (type_stats['total_requests'] - count)) + total_duration) / type_stats['total_requests']
                        type_stats['error_rate'] = ((type_stats['error_rate'] * (type_stats['total_requests'] - count)) + (errors * 100)) / type_stats['total_requests']
                        if total_cache_requests > 0:
                            type_stats['cache_hit_rate'] = ((type_stats['cache_hit_rate'] * (type_stats['total_requests'] - count)) + (hits * 100)) / type_stats['total_requests']
                        
                        # 更新总体统计
                        dashboard_data['overview']['total_requests'] += count
                        hour_data[f'{ranking_type}_requests'] = count
                        hour_data[f'{ranking_type}_avg_duration'] = avg_duration
                        hour_data[f'{ranking_type}_error_rate'] = error_rate
                        hour_data[f'{ranking_type}_cache_hit_rate'] = cache_hit_rate
                
                dashboard_data['hourly_trend'].append(hour_data)
            
            # 计算总体平均值
            if dashboard_data['overview']['total_requests'] > 0:
                total_requests = dashboard_data['overview']['total_requests']
                dashboard_data['overview']['avg_response_time'] = sum(
                    stats['avg_response_time'] * stats['total_requests'] 
                    for stats in dashboard_data['by_type'].values()
                ) / total_requests
                
                dashboard_data['overview']['error_rate'] = sum(
                    stats['error_rate'] * stats['total_requests'] 
                    for stats in dashboard_data['by_type'].values()
                ) / total_requests / 100  # 转换为小数
                
                dashboard_data['overview']['cache_hit_rate'] = sum(
                    stats['cache_hit_rate'] * stats['total_requests'] 
                    for stats in dashboard_data['by_type'].values()
                ) / total_requests
            
            # 排序小时趋势数据
            dashboard_data['hourly_trend'].sort(key=lambda x: x['timestamp'])
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting performance dashboard: {str(e)}", exc_info=True)
            return {
                'overview': {'total_requests': 0, 'avg_response_time': 0.0, 'error_rate': 0.0, 'cache_hit_rate': 0.0},
                'by_type': {},
                'hourly_trend': []
            }

    def _create_buyback_tokens(self, tokens_with_increment: List[Dict[str, Any]]) -> List[BuybackTokenSchema]:
        """创建回购排行榜tokens"""
        buyback_ratios = ["25%", "18%", "12%", "10%", "8%", "5.4%", "5.4%", "5.4%", "5.4%", "5.4%"]
        
        buyback_tokens = []
        for i, item in enumerate(tokens_with_increment):
            try:
                base_token = item['token']
                buyback_data = {
                    "token_name": base_token.token_name,
                    "token_symbol": base_token.token_symbol,
                    "token_address": base_token.token_address,
                    "description": base_token.description,
                    "image_url": base_token.image_url,
                    "collection_id": base_token.collection_id,
                    "market_cap": base_token.market_cap,
                    "market_cap_increment": base_token.market_cap_increment,
                    "price": base_token.price,
                    "price_in_quote": base_token.price_in_quote,
                    "price_change_percent": base_token.price_change_percent,
                    "progress": base_token.progress,
                    "created_at": base_token.created_at,
                    "liquidity": base_token.liquidity,
                    "volume_24h": base_token.volume_24h,
                    "social_links": base_token.social_links,
                    "buyback_ratio": buyback_ratios[i] if i < 10 else None,
                }
                
                buyback_tokens.append(BuybackTokenSchema(**buyback_data))
                
            except Exception as e:
                logger.error(f"Error creating buyback token at index {i}: {str(e)}")
                continue
        
        return buyback_tokens

    def _generate_kline_cache_key(self, token_address: str, interval: str) -> str:
        """
        生成K线数据的缓存键
        
        Args:
            token_address: 代币地址
            interval: 时间间隔 (H4, D1, W1, M1, Max)
            
        Returns:
            str: 缓存键格式 "memecoin:kline:{token_address}:{interval}"
        """
        return f"memecoin:kline:{token_address}:{interval}"

    def _calculate_next_period_expiration(self, interval: str) -> int:
        """
        计算当前时间到下一个 bar 时间点的精确秒数作为缓存过期时间
        
        根据API文档的实际bar间隔：
        - 4H: 使用1分钟bar → 计算到下一个分钟边界
        - 1D: 使用5分钟bar → 计算到下一个5分钟边界
        - 1W: 使用1小时bar → 计算到下一个小时边界
        - 1M: 使用4小时bar (暂时1小时) → 计算到下一个小时边界
        - 1H: 使用30秒bar → 计算到下一个30秒边界
        - Max: 使用12小时bar (暂时1小时) → 计算到下一个小时边界
        
        Args:
            interval: 时间间隔 (4H, 1D, 1W, 1M, Max)
            
        Returns:
            int: 到下一个bar时间点的秒数
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        
        if interval == "1H":
            # 1H_30S 使用30秒bar → 到下一个30秒边界
            next_sec = 30 if now.second < 30 else 60
            next_boundary = now.replace(second=0 if next_sec == 60 else 30, microsecond=0)
            if next_sec == 60:
                next_boundary = next_boundary + timedelta(minutes=1)

        elif interval == "4H":
            # 4H interval 使用1分钟bar → 到下一个分钟边界
            next_boundary = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            
        elif interval == "1D":
            # 1D interval 使用5分钟bar → 到下一个5分钟边界
            current_minute = now.minute
            next_5min_boundary = ((current_minute // 5) + 1) * 5
            if next_5min_boundary >= 60:
                next_boundary = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            else:
                next_boundary = now.replace(minute=next_5min_boundary, second=0, microsecond=0)
                
        elif interval == "1W":
            # 1W interval 使用1小时bar → 到下一个小时边界
            next_boundary = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            
        elif interval == "1M":
            # 1M interval 使用4小时bar → 到下一个4小时间隔边界
            current_hour = now.hour
            next_4h_boundary_hour = ((current_hour // 4) + 1) * 4
            if next_4h_boundary_hour >= 24:
                next_boundary = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            else:
                next_boundary = now.replace(hour=next_4h_boundary_hour, minute=0, second=0, microsecond=0)
            
        elif interval == "Max":
            # Max interval 使用12小时bar → 到下一个12小时间隔边界
            current_hour = now.hour
            next_12h_boundary_hour = ((current_hour // 12) + 1) * 12
            if next_12h_boundary_hour >= 24:
                next_boundary = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            else:
                next_boundary = now.replace(hour=next_12h_boundary_hour, minute=0, second=0, microsecond=0)
            
        else:
            # 默认：到下一个5分钟边界
            current_minute = now.minute
            next_5min_boundary = ((current_minute // 5) + 1) * 5
            if next_5min_boundary >= 60:
                next_boundary = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            else:
                next_boundary = now.replace(minute=next_5min_boundary, second=0, microsecond=0)
        
        # 计算剩余秒数
        expiration_seconds = int((next_boundary - now).total_seconds())
        return max(expiration_seconds, 30)  # 最少30秒过期时间

    async def get_token_kline(self, token_address, interval=None) -> List['TokenPriceSchema']:
        """
        获取Token的K线数据（当前保持你原有的返回：最终只返回一次循环里的最后一个区间）
        支持Redis缓存，缓存过期时间为到下一个时间段的边界
        """
        # 首先尝试从缓存获取数据
        if interval:
            cache_key = self._generate_kline_cache_key(token_address, interval.value)
            try:
                cached_data = await self._caching_client.get("kline", cache_key)
                if cached_data:
                    # 缓存命中，直接返回缓存的数据
                    adapter = TypeAdapter(List[TokenPriceSchema])
                    return adapter.validate_python(cached_data)
            except Exception as e:
                # 缓存读取失败，记录日志但继续执行原有逻辑
                logger.warning(f"Failed to read kline cache for {token_address}:{interval}: {str(e)}")

        now = datetime.datetime.now(tz=datetime.timezone.utc)

        # 绑定局部变量，减少全局查找
        td = timedelta
        fetch = self._repo.fetch_token_kline

        time_ranges: Dict['Interval', Dict[str, object]] = {
            Interval.H1: {'duration': td(hours=1),   'bar_interval': Interval.H1,  'limit': 120},
            Interval.H4: {'duration': td(hours=4),   'bar_interval': Interval.H4,  'limit': 240},
            Interval.D1: {'duration': td(days=1),    'bar_interval': Interval.D1,  'limit': 288},
            Interval.W1: {'duration': td(weeks=1),   'bar_interval': Interval.W1,  'limit': 168},
            Interval.M1: {'duration': td(days=30),   'bar_interval': Interval.M1,  'limit': 720},
            Interval.Max:{'duration': td(days=365*2),'bar_interval': Interval.Max, 'limit': 17520},
        }

        # 若指定interval，仅处理该区间
        if interval:
            cfg = time_ranges.get(interval)
            if not cfg:
                raise ValueError(f"Unsupported interval: {interval}")
            time_ranges = {interval: cfg}

        prices: List[dict] = []  # 先装dict，最后一次性校验成模型

        # 遍历区间（注意：此处仍会"覆盖"prices，只保留最后一次循环的结果，与原逻辑一致）
        for time_interval, cfg in time_ranges.items():
            end_time = now
            start_time = end_time - cfg['duration']  # type: ignore

            # 拉取数据
            records = await fetch(
                token_address,
                cfg['bar_interval'],          # type: ignore
                start_time,
                end_time,
                0,
                cfg['limit']                  # type: ignore
            )

            # 找到第一根有效K线收盘价作为基准
            base_price: Optional[float] = None
            for r in records:
                c = r[4]
                if c is not None:
                    base_price = float(c)
                    break

            # 没有有效close，返回空
            if base_price is None:
                prices = []
                continue

            # 单趟构造：避免numpy、中间数组、重复格式化
            out: List[dict] = []
            fmt10 = "{:.10f}".format
            fmt2  = "{:.2f}".format
            bp = base_price
            bp_is_nonzero = (bp != 0.0)

            append = out.append  # 局部绑定以减少查找
            for r in records:
                # r结构假设: (ts, open, high, low, close, volume, ...)
                ts, o, h, l, c, v = r[0], r[1], r[2], r[3], r[4], r[5]
                if c is None:
                    continue
                c_val = float(c)
                chg_abs = c_val - bp
                chg_pct = (chg_abs / bp * 100.0) if bp_is_nonzero else 0.0

                append({
                    "token_address": token_address,
                    "timestamp": int(ts.timestamp()),
                    "open":   fmt10(float(o)),
                    "high":   fmt10(float(h)),
                    "low":    fmt10(float(l)),
                    "close":  fmt10(c_val),
                    "volume": fmt2(float(v)),
                    "chg_abs": fmt10(chg_abs),  # 使用自定义格式化函数
                    "chg_pct": fmt2(chg_pct),
                })

            # 你的原代码这里有个 reverse；保留它
            out.reverse()
            prices = out  # 覆盖保留最后一个区间

        # 批量一次性校验转模型，避免循环内实例化成本
        adapter = TypeAdapter(List[TokenPriceSchema])
        validated_prices = adapter.validate_python(prices)
        
        # 如果指定了单个interval且有数据，将结果保存到缓存
        if interval and validated_prices:
            try:
                cache_key = self._generate_kline_cache_key(token_address, interval.value)
                expiration_seconds = self._calculate_next_period_expiration(interval.value)
                
                # 将验证后的数据转换为可序列化的格式保存到缓存
                cache_data = [price.model_dump() for price in validated_prices]
                await self._caching_client.set(
                    "kline", 
                    cache_key, 
                    cache_data, 
                    timedelta(seconds=expiration_seconds)
                )
                
                logger.debug(f"Cached kline data for {token_address}:{interval} with TTL {expiration_seconds}s")
                
            except Exception as e:
                # 缓存写入失败不影响主要功能，只记录日志
                logger.warning(f"Failed to cache kline data for {token_address}:{interval}: {str(e)}")
        
        return validated_prices

    async def get_token_kline_from_redis(
        self, token_address, interval, page_params
    ) -> List[TokenPriceSchema]:
        """
        Retrieve the token's candlestick (kline) data.
        This function fetches data from Redis based on the kline generation logic.
        If there isn't enough data for the requested page, it will be expanded by computing
        the expected time intervals and filling in missing entries with the previously available data.

        :param token_address: The token's contract address.
        :param interval: The candlestick interval (e.g., "1m", "5m", "30m", "1H", "1D").
        :param page_params: Pagination parameters with attributes 'page' and 'page_size'.
        :return: A list of TokenPriceSchema objects.
        """
        # Define the duration (in seconds) for each supported interval.
        periods = {"1m": 60, "5m": 300, "30m": 1800, "1H": 3600, "1D": 86400}
        if interval not in periods:
            raise ValueError("Invalid interval")
        period_seconds = periods[interval]

        # Calculate pagination offset.
        offset = (page_params.page - 1) * page_params.page_size

        # Redis sorted set key that holds period start timestamps.
        sorted_set_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}:kline:{interval}"

        # Retrieve a page of period start values (in descending order, i.e. latest first).
        period_starts = await self._caching_client.client.zrevrange(
            sorted_set_key, offset, offset + page_params.page_size - 1
        )
        if not period_starts:
            return []
        previous_kline_data = await self._caching_client.client.hgetall(
            f"{sorted_set_key}:{period_starts[-1]}"
        )
        previous_kline = TokenPriceSchema(
            token_address=token_address,
            open=previous_kline_data.get("o"),
            close=previous_kline_data.get("c"),
            high=previous_kline_data.get("h"),
            low=previous_kline_data.get("l"),
            volume=previous_kline_data.get("v"),
            timestamp=previous_kline_data.get("ts"),
        )
        # If the retrieved period_starts are not enough, generate expected periods.
        if len(period_starts) < page_params.page_size:
            now = int(time.time())
            current_period_start = (now // period_seconds) * period_seconds
            start_index = (page_params.page - 1) * page_params.page_size
            expected_periods = [
                current_period_start - i * period_seconds
                for i in range(start_index, start_index + page_params.page_size)
            ]
            period_starts = [str(p) for p in expected_periods]

        prices = []

        for period_start in period_starts:
            kline_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}:kline:{interval}:{period_start}"
            kline_data = await self._caching_client.client.hgetall(kline_key)
            if kline_data:
                record = TokenPriceSchema(
                    token_address=token_address,
                    open=kline_data.get("o"),
                    close=kline_data.get("c"),
                    high=kline_data.get("h"),
                    low=kline_data.get("l"),
                    volume=kline_data.get("v"),
                    timestamp=kline_data.get("ts"),
                )
                previous_kline = record
                prices.append(record)
            else:
                if previous_kline:
                    record = TokenPriceSchema(
                        token_address=token_address,
                        open=previous_kline.open,
                        close=previous_kline.close,
                        high=previous_kline.high,
                        low=previous_kline.low,
                        volume="0",
                        timestamp=period_start,
                    )
                else:
                    record = TokenPriceSchema(
                        token_address=token_address,
                        open="0",
                        close="0",
                        high="0",
                        low="0",
                        volume="0",
                        timestamp=0,
                    )
                prices.append(record)
        return prices

    async def get_token_holders(self, token_address, page_params) -> List[HolderSchema]:
        offset = (page_params.page - 1) * page_params.page_size
        token_holders = await self._repo.fetch_token_holders(
            token_address, offset, page_params.page_size
        )
        pair = await self._repo.fetch_pair(token_address)
        if pair:
            token_total_supply = pair.base_total_supply or settings.DEFAULT_TOTAL_SUPPLY
        else:
            token_total_supply = Decimal(0)

        # 获取所有持币者的 user_id 列表
        user_ids = [user_id for user_id, _, _ in token_holders]
        
        # 查询哪些用户正在直播该token相关的内容
        live_users = set()
        if user_ids:
            stmt = select(LiveChannel.user_id).where(
                LiveChannel.user_id.in_(user_ids),
                LiveChannel.status == ChannelStatus.LIVE,
                LiveChannel.token_address == token_address
            )
            result = await self._repo._session.execute(stmt)
            live_users = {row[0] for row in result.fetchall()}

        holders_schema: List[HolderSchema] = []
        for user_id, holder, username in token_holders:
            holder_amount = Decimal(holder.amount)
            holder_ui_amount = holder_amount / Decimal(10**holder.decimals)
            if token_total_supply > 0:
                percent = (holder_ui_amount / token_total_supply) * Decimal(100)
            else:
                percent = Decimal(0)
            percent_str = str(percent.quantize(Decimal("0.01"), rounding=ROUND_DOWN))
            
            # 检查该用户是否在直播
            is_live = user_id in live_users

            hs = HolderSchema(
                name=holder.name,
                username=username,
                avatar=holder.avatar,
                user_id=user_id,
                address=holder.address,
                balance=decimal_to_string(holder_amount),
                ui_amount=decimal_to_string(holder_ui_amount),
                decimals=holder.decimals,
                percent=percent_str,
                is_live=is_live,
            )
            holders_schema.append(hs)
        return holders_schema

    async def set_token_info(self, token_cache: TokenCache):
        try:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_cache.token_address}"
            data = token_cache.model_dump()
            await self._caching_client.client.hset(key, mapping=data)
            logger.info("Token info set in hash for key %s", key)
        except Exception as e:
            logger.exception(
                "Failed to set token info for %s: %s", token_cache.symbol, e
            )

    async def _check_rate_limit(self, wallet_address: str) -> bool:
        """
        Check if the wallet has exceeded rate limit (1 request per hour)
        Returns True if rate limit is exceeded
        """
        rate_limit_key = f"rate_limit:{wallet_address}"

        # Check if rate limit exists
        rate_limit = await self._caching_client.get(
            MEMECOIN_REDIS_ENDPOINT, rate_limit_key
        )
        if rate_limit:
            return True

        # Set rate limit for 1 hour
        await self._caching_client.set(
            MEMECOIN_REDIS_ENDPOINT,
            rate_limit_key,
            {"timestamp": int(time.time())},
            expiration_time=timedelta(hours=1),
        )
        return False

    async def get_user_assets(self, user_id: str):
        wallet = await self._repo.fetch_user_wallet(user_id)
        if wallet is None:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"Wallet not found for user ({user_id})",
            )

        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)

        # Check rate limit, allow 1 request per hour
        rate_limited = await self._check_rate_limit(wallet_address)
        if not rate_limited:
            try:
                await self._repo.update_wallet_tokens(wallet_address, self._chain_id)
            except Exception as e:
                logger.error(f"Failed to update wallet tokens: {str(e)}")

        tokens, cash_amount = await asyncio.gather(
            self._repo.fetch_user_tokens(wallet_address, self._chain_id),
            self._web3.get_cash_balance(wallet_address)
        )

        total_current_usd = cash_amount
        total_initial_usd = cash_amount
        tokens_usd_amount = Decimal(0)  # 仅统计非原生代币的USD金额

        valid_tokens = [token for token in tokens if token.amount > 0]
        
        if not valid_tokens:
            return AssetResponseSchema(
                wallet_address=wallet_address,
                total_usd_amount=decimal_to_string(total_current_usd),
                tokens_usd_amount=decimal_to_string(tokens_usd_amount),
                cash=decimal_to_string(cash_amount),
                usd_change="0.0000",
                usd_change_percent="0.0",
                tokens=[],
            )
        
        cache_keys = [f"{MEMECOIN_REDIS_ENDPOINT}:{token.token_address}" for token in valid_tokens]
        default_cache = {
            "name": "",
            "symbol": "",
            "image_url": "",
            "price": 0,
            "price_change_percent": 0,
            "market_cap": 0,
        }
        token_cache_map = await self._batch_get_cache(cache_keys, default_cache)
        
        missing_name_addresses = []
        missing_name_indices = []
        
        for i, token in enumerate(valid_tokens):
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token.token_address}"
            cache = token_cache_map.get(cache_key, default_cache)
            
            if not cache.get("name") or not cache.get("image_url") or not cache.get("collection_id") or not cache.get("progress"):
                missing_name_addresses.append(token.token_address)
                missing_name_indices.append(i)
        
        if missing_name_addresses:
            missing_pairs = await asyncio.gather(*[
                self._repo.fetch_pair(addr) for addr in missing_name_addresses
            ], return_exceptions=True)
            
            for addr, pair, idx in zip(missing_name_addresses, missing_pairs, missing_name_indices):
                logger.debug(f"{addr} missing cache")
                if not isinstance(pair, Exception) and pair:
                    cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
                    cache = token_cache_map.get(cache_key, default_cache.copy())
                    cache["name"] = pair.base_name
                    cache["symbol"] = pair.base_symbol
                    cache["image_url"] = pair.base_image_url
                    cache["collection_id"] = pair.collection_id
                    cache["market_cap"] = float(pair.mc)
                    cache["progress"] = pair.bonding_curve
                    token_cache_map[cache_key] = cache

                    try:
                        await self._caching_client.client.hset(cache_key, mapping=cache)
                        logger.debug(f"Updated token cache in Redis for {addr}")
                    except Exception as e:
                        logger.error(f"Failed to update token cache in Redis for {addr}: {str(e)}")
        
        token_schemas = []
        
        for token in valid_tokens:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token.token_address}"
            cache = token_cache_map.get(cache_key, default_cache)
            
            try:
                token_info = TokenCache.model_validate(cache)
                if not token_info.name and not token_info.symbol:
                    continue

                price = Decimal(token_info.price)
                price_change_percent = await self._get_price_change_percent(float(price), token.token_address)
                
                token_amount = Decimal(token.amount) / Decimal(10**token.decimals)
                usd_amount = token_amount * price
                
                denominator = Decimal(100 + price_change_percent) / 100
                if denominator == 0:
                    raise ValueError(f"Division by zero error: price change percent for token {token.token_address} is {price_change_percent}")
                
                initial_price = price / denominator
                initial_usd = token_amount * initial_price
                
                total_current_usd += usd_amount
                total_initial_usd += initial_usd
                tokens_usd_amount += usd_amount  # 累加非原生代币的USD金额
                
                token_schemas.append(
                    ProfileTokenSchema(
                        token_name=token_info.name,
                        token_symbol=token_info.symbol,
                        token_address=token.token_address,
                        image_url=token_info.image_url,
                        price=f"{price:.20f}",
                        price_change_percent=f"{price_change_percent:.1f}",
                        amount=decimal_to_string(token.amount),
                        ui_amount=decimal_to_string(token_amount),
                        decimals=token.decimals,
                        usd_amount=decimal_to_string(usd_amount),
                        market_cap=str(token_info.market_cap),
                        collection_id=token_info.collection_id,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing token {token.token_address}: {str(e)}")
                continue
        
        if total_initial_usd == 0:
            usd_change_total = Decimal(0)
            usd_change_percent_total = Decimal(0)
        else:
            usd_change_total = total_current_usd - total_initial_usd
            usd_change_percent_total = (usd_change_total / total_initial_usd) * 100
        
        sorted_tokens = sorted(
            token_schemas, key=lambda token: float(token.usd_amount), reverse=True
        )
        
        return AssetResponseSchema(
            wallet_address=wallet_address,
            total_usd_amount=decimal_to_string(total_current_usd),
            tokens_usd_amount=decimal_to_string(tokens_usd_amount),
            cash=decimal_to_string(cash_amount),
            usd_change=f"{usd_change_total:.4f}",
            usd_change_percent=f"{usd_change_percent_total:.1f}",
            tokens=sorted_tokens,
        )

    async def get_my_tokens(self, user_id: str) -> List[BaseTokenSchema]:
        """
        Get all tokens that the user has created or currently holds
        Performance optimized version that avoids calling get_my_memes and get_user_assets
        Only returns tokens that exist in the Pair table
        
        :param user_id: The user ID
        :return: List of BaseTokenSchema including both created and held tokens that exist in Pair table
        """
        # Get user wallet first
        wallet = await self._repo.fetch_user_wallet(user_id)
        if not wallet:
            return []
        
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        
        # Parallel fetch: created tokens and held tokens
        created_pairs, held_tokens = await asyncio.gather(
            self._repo.fetch_user_created_memes(user_id, self._chain_id),
            self._repo.fetch_user_tokens(wallet_address, self._chain_id),
            return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(created_pairs, Exception):
            logger.error(f"Error fetching created memes: {str(created_pairs)}")
            created_pairs = []
            
        if isinstance(held_tokens, Exception):
            logger.error(f"Error fetching held tokens: {str(held_tokens)}")
            held_tokens = []
        
        # Collect token addresses
        created_addresses = set()
        held_addresses = set()
        
        # Add created token addresses (these are guaranteed to be in Pair table)
        for pair in created_pairs:
            if pair.base:
                created_addresses.add(pair.base)
        
        # Add held token addresses (only non-zero balances)
        for token in held_tokens:
            if token.amount > 0 and token.token_address:
                held_addresses.add(token.token_address)
        
        # Remove created addresses from held addresses to avoid duplication
        held_only_addresses = held_addresses - created_addresses
        
        # Check which held tokens exist in Pair table
        valid_held_addresses = set()
        if held_only_addresses:
            # Batch check if held tokens exist in Pair table
            held_pairs = await asyncio.gather(*[
                self._repo.fetch_pair(addr) for addr in held_only_addresses
            ], return_exceptions=True)
            
            for addr, pair in zip(held_only_addresses, held_pairs):
                if not isinstance(pair, Exception) and pair and pair.status == PairStatus.READY:
                    valid_held_addresses.add(addr)
        
        # Final set of valid token addresses
        all_valid_addresses = created_addresses | valid_held_addresses
        
        if not all_valid_addresses:
            return []
        
        # Batch get cache for all valid tokens
        cache_keys = [f"{MEMECOIN_REDIS_ENDPOINT}:{addr}" for addr in all_valid_addresses]
        default_cache = {
            "name": "",
            "symbol": "",
            "image_url": "",
            "price": 0,
            "price_change_percent": 0,
            "market_cap": 0,
        }
        token_cache_map = await self._batch_get_cache(cache_keys, default_cache)
        
        # Find tokens that need database lookup for missing info
        missing_addresses = []
        for addr in all_valid_addresses:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{addr}"
            cache = token_cache_map.get(cache_key, default_cache)
            if not cache.get("name") or not cache.get("symbol") or not cache.get("image_url"):
                missing_addresses.append(addr)
        
        # Batch fetch missing token info from database
        missing_pairs_map = {}
        if missing_addresses:
            missing_pairs = await asyncio.gather(*[
                self._repo.fetch_pair(addr) for addr in missing_addresses
            ], return_exceptions=True)
            
            for addr, pair in zip(missing_addresses, missing_pairs):
                if not isinstance(pair, Exception) and pair:
                    missing_pairs_map[addr] = pair
        
        # Build token list
        token_dict = {}
        
        # Process all valid token addresses
        for token_address in all_valid_addresses:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
            cache = token_cache_map.get(cache_key, default_cache.copy())
            
            # Use missing pair info if needed
            if token_address in missing_pairs_map:
                pair = missing_pairs_map[token_address]
                cache["name"] = pair.base_name or ""
                cache["symbol"] = pair.base_symbol or ""
                cache["image_url"] = pair.base_image_url or ""
                cache["market_cap"] = float(pair.mc) if pair.mc else 0
            
            try:
                token_info = TokenCache.model_validate(cache)
                
                # Skip tokens without basic info
                if not token_info.name and not token_info.symbol:
                    continue
                
                base_token = BaseTokenSchema(
                    token_name=token_info.name,
                    token_symbol=token_info.symbol,
                    token_address=token_address,
                    image_url=token_info.image_url,
                    price=f"{token_info.price:.20f}",
                    price_change_percent=f"{token_info.price_change_percent:.1f}",
                    market_cap=str(token_info.market_cap),
                )
                token_dict[token_address] = base_token
                
            except Exception as e:
                logger.error(f"Error processing token {token_address}: {str(e)}")
                continue
        
        # Convert to list and sort by market cap (descending)
        all_tokens = list(token_dict.values())
        
        # Sort by market cap descending
        sorted_tokens = sorted(
            all_tokens,
            key=lambda x: -float(x.market_cap) if x.market_cap and x.market_cap != "0" else 0
        )
        
        return sorted_tokens

    async def ws_token_prices(self, subscribed_tokens: List[str]):
        token_prices = []
        for token in subscribed_tokens:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token}"
            cache = await self._caching_client.client.hgetall(key)
            if not cache:
                logger.debug(f"Token {token} not found in cache")
                continue
            token_info: TokenCache = TokenCache.model_validate(cache)
            price_change_percent = await self._get_price_change_percent(
                float(token_info.price), token
            )
            token_prices.append(
                {
                    token: {
                        "price": token_info.price,
                        "24h_change_percent": f"{price_change_percent:.1f}",
                        "market_cap": token_info.market_cap,
                        "liquidity": token_info.liquidity,
                    }
                }
            )
        return token_prices

    async def get_unsent_token_creations(self, user_id: str, limit: int = 5) -> List[dict]:
        """
        Get recently created tokens that haven't been sent to this WebSocket connection
        Uses Redis to track sent tokens per connection
        Returns a list of recently created tokens with their details
        """
        try:
            # Get recent tokens from database (last 10 minutes to catch new ones)
            recent_tokens = await self._repo.get_recent_tokens_since_timestamp(
                minutes_ago=10, 
                limit=limit * 3  # Get more to filter out already sent ones
            )
            
            if not recent_tokens:
                return []
            
            # Get Redis key for this connection's sent tokens
            sent_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:sent_tokens:{user_id}"
            
            # Get already sent tokens for this connection from Redis
            sent_tokens = await self._caching_client.client.smembers(sent_tokens_key)
            sent_tokens_set = {token.decode() if isinstance(token, bytes) else str(token) for token in sent_tokens}
            
            # Filter out tokens that have already been sent to this connection
            result = []
            new_sent_tokens = []
            
            for token in recent_tokens:
                # Only push tokens created by the current user (connection owner)
                if token.get("creator_id") != user_id:
                    continue

                token_address = token.get("token_address")
                if token_address not in sent_tokens_set:
                    result.append({
                        "address": token_address,
                        "symbol": token.get("symbol"),
                        "avatar": token.get("avatar"),
                        "creator_id": token.get("creator_id"),
                        "collection_id": token.get("collection_id")
                    })
                    new_sent_tokens.append(token_address)
                    
                    # Stop when we have enough unsent tokens
                    if len(result) >= limit:
                        break
            
            # Mark these tokens as sent in Redis
            if new_sent_tokens:
                await self._caching_client.client.sadd(sent_tokens_key, *new_sent_tokens)
                # Set expiration for the sent tokens set (1 hours)
                await self._caching_client.client.expire(sent_tokens_key, 1 * 3600)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting unsent token creations: {str(e)}")
            return []

    async def cleanup_expired_connection_tracking(self, hours_ago: int = 24):
        """
        Clean up expired connection tracking data from Redis
        This is a maintenance method that should be called periodically
        """
        try:
            # Get all keys matching the sent_tokens pattern
            pattern = f"{MEMECOIN_REDIS_ENDPOINT}:sent_tokens:*"
            keys = await self._caching_client.client.keys(pattern)
            
            if not keys:
                return 0
                
            # Delete all keys (they have TTL set automatically)
            deleted_count = await self._caching_client.client.delete(*keys)
            logger.info(f"Cleaned up {deleted_count} expired connection tracking keys")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired connection tracking: {str(e)}")
            return 0

    async def create_meme(
        self,
        user_id,
        name: str,
        symbol: str,
        about: str,
        avatar: str,
        amount_to_buy: Decimal,
        gas: int,
        gas_price: int,
        user_region: str,
        social_links: Optional[Dict[str, str]] = None,
        order_id: Optional[str] = None,
        holdview_amount: Optional[Decimal] = None,
        is_with_usdt: Optional[bool] = True,
    ):
        # 0. Generate collection ID without creating the collection yet
        # Collection will be created in Celery worker after meme creation succeeds
        collection_id = generate_shortid()
        logger.info(f"Generated collection ID {collection_id} for meme {name} ({symbol})")

        # Generate repo_url using the new collection ID
        repo_url = f"{settings.BASE_URL}/collection/{collection_id}"

        # 2. Send a request to create the token
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        # Check if user has created any tokens before
        user_created_memes = await self._repo.fetch_user_created_memes(user_id, self._chain_id)
        is_first_time_user = len(user_created_memes) == 0
        gas_usdt_amount = Decimal(0) if is_first_time_user else TradeUSDTGas.LAUNCH_GAS

        if settings.ENABLE_CREATE_MEME_LIMIT:
            eligible = await self._web3.userCreateTokenEligible(wallet_address)
            if not eligible:
                raise HTTPException(
                    status_code=HTTP_409_CONFLICT,
                    detail="You are not eligible to create a token.",
                )
        try:

            if amount_to_buy > 0:
                wallet_address, cash_balance = await self.get_cash_amount(user_id)
                if cash_balance < gas_usdt_amount + amount_to_buy:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail="Insufficient cash balance.",
                    )

            # 预付 50 USDT 到 feeToAddress
            try:
                fee_addr = await self._web3.fee_to_address()
                if not fee_addr:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail="Failed to resolve fee address"
                    )
                # 发送 50 USDT
                transfer_txid = await self._web3.transfer_token(
                    token_address=settings.USDT_ADDRESS,
                    from_pubkey=wallet_address,
                    from_secret=wallet.secret,
                    to_pubkey=fee_addr,
                    amount=Decimal(50),
                    usdt_ui_gas_amount=TradeUSDTGas.SEND_WITH_USDT_GAS,
                    gas=settings.DEFAULT_GAS_LIMIT,
                    gas_payer_secret=settings.GAS_PAYER_SECRET,
                )
                receipt = await self._web3.w3.eth.wait_for_transaction_receipt(transfer_txid)
                if not receipt or getattr(receipt, "status", 0) != 1:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Failed to prepaid 50 USDT: transaction failed"
                    )
            except Web3RPCError as e:
                formatted_error = format_web3_error(e)
                logger.error(formatted_error)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to prepaid 50 USDT: {formatted_error}"
                )

            tx_hash = await self._web3.launch_l1(
                name,
                symbol,
                repo_url,
                avatar,
                about,
                wallet_address,
                gas=gas if gas else settings.DEFAULT_LAUNCH_GAS_LIMIT,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
            )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            logger.error(formatted_error)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代币创建失败: {formatted_error}",
            )

        await self._caching_client.set(
            MEMECOIN_REDIS_ENDPOINT,
            f"L1_MEME:{tx_hash}",
            {
                "name": name,
                "symbol": symbol,
                "repo_url": repo_url,
                "avatar": avatar,
                "about": about,
                "creator": wallet_address,
                "creator_id": user_id,
                "secret": wallet.secret,
                "collection_id": collection_id,
                "amount_to_buy": int(amount_to_buy * settings.USDT_DECIMALS),
                "gas_usdt_amount": int(gas_usdt_amount * settings.USDT_DECIMALS),
                "order_id": order_id,
                "original_txid": tx_hash,
            },
            expiration_time=timedelta(days=1)
        )

        await self._repo.insert_transaction_history(
            user_id=user_id,
            type=UserTransactionType.CREATE_L1_MEME,
            status=UserTransactionStatus.AWAITING_L1_CONFIRMATION,
            from_address=wallet_address,
            tx_hash=tx_hash,
            order_amount=int(amount_to_buy*settings.USDT_DECIMALS),
            gas_limit=gas,
            payment_source=wallet_address,
            value=0.0,
            order_id=order_id,
        )
        # 3. Calculate USD value if user is buying tokens
        buy_usd_value = None
        if amount_to_buy > 0:
            # Calculate USD value for the token purchase
            buy_usd_value = await self._calculate_transaction_usd_value(
                token_address=settings.USDT_ADDRESS if is_with_usdt else None,  # USDT or 原生币交易
                amount=amount_to_buy * settings.DEFAULT_DECIMALS if not is_with_usdt else amount_to_buy * settings.USDT_DECIMALS
            )

        # 4. Queue post-creation tasks to Celery
        # Launch the Celery task for post-creation operations
        self._celery_client.send_task(
            'src.worker.tasks.memecoin.process_post_meme_creation',
            kwargs={
                'user_id': user_id,
                'collection_id': collection_id,
                'name': name,
                'symbol': symbol,
                'avatar': avatar,
                'about': about,
                'tx_hash': tx_hash,
                'wallet_address': wallet_address,
                'amount_to_buy': amount_to_buy,
                'gas': gas,
                'chain_id': self._chain_id,
                'social_links': social_links,
                'order_id': order_id,
                'is_with_usdt': is_with_usdt,
                'buy_usd_value': buy_usd_value,
                'user_region': user_region,  # Add user_region for collection creation
                'holdview_amount': holdview_amount
            },
            queue='memecoin'
        )
        
        logger.info(f"Queued post-creation tasks for meme {name} ({symbol}) with tx_hash {tx_hash}")

        return CreationResponseSchema(
            txid=tx_hash, detail="Create meme request has been sent."
        )

    async def edit_meme(
        self,
        user_id: str,
        token_address: str,
        about: Optional[str] = None,
        social_links: Optional[Dict[str, str]] = None,
        avatar: Optional[str] = None,
    ):
        """
        Edit a meme token's information (about, social links and avatar).
        
        :param user_id: ID of the user performing the edit
        :param token_address: Address of the token to edit
        :param about: New about text for the token
        :param social_links: New social links for the token
        :param avatar: New avatar URL for the token
        :return: CreationResponseSchema with success message
        """
        # 1. Fetch the token's pair information
        pair = await self._repo.fetch_pair(token_address)
        if not pair:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail="Token not found."
            )
            
        # 2. Verify the user is the creator of this token
        if pair.creator_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not authorized to edit this token. Only the creator can modify it."
            )
            
        # 3. Update the pair information
        update_data = {}
        if about is not None:
            update_data["base_description"] = about
            
        if social_links is not None:
            current_social_links = {}
            if pair.social_links:
                try:
                    current_social_links = json.loads(pair.social_links)
                except (json.JSONDecodeError, TypeError):
                    logger.error(f"Failed to parse social_links for token {token_address}: {pair.social_links}")
            current_social_links.update(social_links)
            update_data["social_links"] = json.dumps(current_social_links)
            
        if avatar is not None:
            update_data["base_image_url"] = avatar
            
        await self._repo.update_meme(token_address, update_data)
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
        if about is not None:
            await self._caching_client.client.hset(key, "about", about)
        if social_links is not None:
            await self._caching_client.client.hset(key, "social_links", update_data.get("social_links", ""))
        if avatar is not None:
            await self._caching_client.client.hset(key, "image_url", avatar)
        
        return EditMemeResponse(
            detail="Meme information updated successfully."
        )

    async def send_token(
        self,
        user_id: str,
        token_address: str,
        to_address: str,
        amount: str,
        gas: int,
        gas_price: int,
        order_id: Optional[str] = None,
    ):
        """
        Transfer tokens from the user's wallet to a recipient.

        :param user_id: The ID of the user initiating the transfer.
        :param token_address: The ERC20 token contract address.
        :param to_address: The recipient's address.
        :param amount: The amount to send (ether).
        :param gas: The gas limit for the transaction.
        :param gas_price: The gas price for the transaction.
        :return: Transaction receipt.
        """
        # Fetch the user's wallet
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        logger.info(f"send_token: {wallet_address}, {to_address}, {amount}, {gas}, {gas_price}")
        try:
            # Call the blockchain function to send tokens
            tx_hash = await self._web3.transfer_token(
                token_address=token_address,
                from_pubkey=wallet_address,
                from_secret=wallet.secret,
                to_pubkey=to_address,
                amount=Decimal(amount),
                usdt_ui_gas_amount=TradeUSDTGas.SEND_WITH_USDT_GAS,
                gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                gas_price=gas_price,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
            )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代币转账失败: {formatted_error}",
            )

        # Log the transaction (assuming receipt includes the transaction hash)
        # Calculate USD value for the transaction
        usd_value = await self._calculate_transaction_usd_value(
            token_address=token_address,
            amount=Decimal(amount) * settings.DEFAULT_DECIMALS
        )
        
        # Insert send transaction history for sender
        await self._repo.insert_transaction_history(
            user_id,
            UserTransactionType.SEND_TOKEN,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            token_address=token_address,
            to_address=to_address,
            tx_hash=tx_hash,
            gas_limit=gas,
            order_amount=int(Decimal(amount) * settings.DEFAULT_DECIMALS),
            payment_source=wallet_address,
            value=usd_value,
            order_id=order_id,
        )
        
        # Try to find recipient user and insert receive transaction history
        try:
            recipient_author = await self._repo.fetch_author_by_wallet_address(to_address)
            if recipient_author:
                await self._repo.insert_transaction_history(
                    recipient_author.user_id,
                    UserTransactionType.RECEIVE_TOKEN,
                    UserTransactionStatus.AWAITING_CONFIRMATION,
                    to_address,
                    token_address=token_address,
                    to_address=to_address,
                    tx_hash=tx_hash,
                    gas_limit=gas,
                    order_amount=int(Decimal(amount) * settings.DEFAULT_DECIMALS),
                    payment_source=wallet_address,
                    value=usd_value,
                    order_id=order_id,
                )
        except Exception:
            # If recipient is not found or any error occurs, continue without inserting receive history
            pass

        return CreationResponseSchema(
            txid=tx_hash, detail="Send token request has been sent."
        )

    async def send_cash(
            self,
            user_id: str,
            to_address: str,
            amount: str,
            gas: int,
            gas_price: int,
            order_id: Optional[str] = None,
    ):
        """
        Transfer tokens from the user's wallet to a recipient.

        :param user_id: The ID of the user initiating the transfer.
        :param token_address: The ERC20 token contract address.
        :param to_address: The recipient's address.
        :param amount: The amount to send (ether).
        :param gas: The gas limit for the transaction.
        :param gas_price: The gas price for the transaction.
        :return: Transaction receipt.
        """
        # Fetch the user's wallet
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        try:
            # Call the blockchain function to send tokens
            tx_hash = await self._web3.transfer_token(
                token_address=settings.USDT_ADDRESS,
                from_pubkey=wallet_address,
                from_secret=wallet.secret,
                to_pubkey=to_address,
                amount=Decimal(amount),
                usdt_ui_gas_amount=TradeUSDTGas.SEND_WITH_USDT_GAS,
                gas=gas,
                gas_price=gas_price,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
            )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=formatted_error,
            )

        # Log the transaction (assuming receipt includes the transaction hash)
        # Calculate USD value for the transaction
        usd_value = float(amount)

        # Insert send transaction history for sender
        await self._repo.insert_transaction_history(
            user_id,
            UserTransactionType.SEND_TOKEN,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            token_address=settings.USDT_ADDRESS,
            to_address=to_address,
            tx_hash=tx_hash,
            gas_limit=gas,
            order_amount=int(Decimal(amount) * settings.USDT_DECIMALS),
            payment_source=wallet_address,
            value=usd_value,
            order_id=order_id,
        )
        
        # Try to find recipient user and insert receive transaction history
        try:
            recipient_author = await self._repo.fetch_author_by_wallet_address(to_address)
            if recipient_author:
                await self._repo.insert_transaction_history(
                    recipient_author.user_id,
                    UserTransactionType.RECEIVE_TOKEN,
                    UserTransactionStatus.AWAITING_CONFIRMATION,
                    to_address,
                    token_address=settings.USDT_ADDRESS,
                    to_address=to_address,
                    tx_hash=tx_hash,
                    gas_limit=gas,
                    order_amount=int(Decimal(amount) * settings.USDT_DECIMALS),
                    payment_source=wallet_address,
                    value=usd_value,
                    order_id=order_id,
                )
        except Exception as e:
            # If recipient is not found or any error occurs, continue without inserting receive history
            logger.debug(f"Failed to insert receive transaction history for recipient {to_address}: {str(e)}")
            pass

        return CreationResponseSchema(
            txid=tx_hash, detail="Send cash request has been sent."
        )

    async def receive_token(self, user_id):
        """
        Return the user's wallet address for the target token.

        :param user_id: The ID of the user.
        :return: The user's wallet address.
        """
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        return wallet_address

    async def buy_token(
        self, user_id: str, token_address: str, amount: str, gas: int, gas_price: int, order_id: Optional[str] = None, is_with_usdt: Optional[bool] = True
    ):
        """
        Buy tokens from the market.

        :param user_id: The ID of the user.
        :param token_address: The token contract address.
        :param amount: The amount of USDT to spend.
        :param gas: The gas limit for the transaction.
        :param gas_price: The gas price for the transaction.
        :return: Transaction receipt.
        """
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        # dex = await self.get_token_dex(token_address)

        try:
            transaction_type = UserTransactionType.BUY_TOKEN
            tx_hash = None
            if is_with_usdt:
                tx_hash = await self._web3.buy_with_USDT(
                    token_address=token_address,
                    buyer_pubkey=wallet_address,
                    buyer_secret=wallet.secret,
                    usdt_ui_amount_to_buy=Decimal(amount),
                    usdt_ui_gas_amount=TradeUSDTGas.BUY_WITH_USDT_GAS,
                    eth_amount_out_min=Decimal(0),
                    amount_out_min=Decimal(0),
                    gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                    gas_price=gas_price if gas_price else settings.DEFAULT_GAS_PRICE,
                    gas_payer_secret=settings.GAS_PAYER_SECRET,
                )
            else:
                tx_hash = await self._web3.buy(
                    token_address,
                    wallet_address,
                    wallet.secret,
                    Decimal(amount),
                    amount_out_min=0,
                    gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                    gas_price=gas_price if gas_price else settings.DEFAULT_GAS_PRICE,
                )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Buy token Failed: {formatted_error}",
            )
        token_decimals = settings.USDT_DECIMALS if is_with_usdt else settings.DEFAULT_DECIMALS
        # Log the transaction
        # Calculate USD value for the transaction (买币花费的是原生币)
        usd_value = await self._calculate_transaction_usd_value(
            token_address=None if not is_with_usdt else settings.USDT_ADDRESS,  # 原生币交易
            amount=Decimal(amount) * (token_decimals)
        )
        
        # Get unit price for the payment token (native or USDT)
        payment_token_price = await self._get_token_unit_price(token_address)
        
        await self._repo.insert_transaction_history(
            user_id,
            transaction_type,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            tx_hash=tx_hash,
            gas_limit=gas,
            token_address=token_address,
            order_amount=int(Decimal(amount) * settings.USDT_DECIMALS),
            payment_source=wallet_address,
            value=usd_value,
            order_id=order_id,
            unit_price=payment_token_price,
        )

        return CreationResponseSchema(
            txid=tx_hash, detail="Buy token request has been sent."
        )
    
    async def purchase(
        self,
        user_id: str,
        token_address: str,
        to_address: str,
        usd_amount_cents: int,
    ):
        """
        One-step content purchase using USDT on ETH: purchase tokens and deliver to recipient.

        :param user_id: The ID of the buyer user.
        :param token_address: The meme token contract address.
        :param to_address: Recipient address (author wallet).
        :param usd_amount_cents: USD amount in cents to purchase.
        :param order_id: Optional order id for traceability.
        :return: CreationResponseSchema with txid
        """
        if settings.BLOCKCHAIN_TYPE != "ETH":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Purchase is only supported on ETH chain currently.")

        # Fetch buyer wallet
        wallet = await self._repo.fetch_user_wallet(user_id)
        buyer_address = self._web3.w3.to_checksum_address(wallet.pubkey)

        # Convert cents to UI amount in USD
        purchase_usdt_ui_amount = (Decimal(usd_amount_cents) / Decimal(100))

        # Estimate gas (reuse buy estimation as baseline)
        gas = None
        gas_price = None
        try:
            estimate = await self.get_estimate_gas(EstimateGasType.buy, [token_address])
            gas = getattr(estimate, "gas_used", None)
            gas_price = getattr(estimate, "gas_price", None)
        except Exception:
            pass
        try:
            if gas_price in (None, 0):
                gas_price = await self._web3.w3.eth.gas_price
        except Exception:
            if gas_price in (None, 0):
                gas_price = settings.DEFAULT_GAS_PRICE

        try:
            tx_hash = await self._web3.purchase(
                token_address=token_address,
                buyer_pubkey=buyer_address,
                buyer_secret=wallet.secret,
                to_address=self._web3.w3.to_checksum_address(to_address),
                purchase_usdt_ui_amount=purchase_usdt_ui_amount,
                gas_usdt_ui_amount=TradeUSDTGas.PURCHASE_GAS,
                gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                gas_price=gas_price if gas_price else settings.DEFAULT_GAS_PRICE,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
            )
        except Web3RPCError as e:
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Purchase failed: {formatted_error}",
            )

        return CreationResponseSchema(
            txid=tx_hash, detail="Purchase submitted"
        )

    async def sell_token(self, user_id, token_address, amount, gas, gas_price, order_id: Optional[str] = None, is_with_usdt: Optional[bool] = True):
        """
        Sell tokens to the market.

        :param user_id: The ID of the user.
        :param token_address: The token contract address.
        :param amount: The amount (wei) of tokens to sell.
        :param gas: The gas limit for the transaction.
        :param gas_price: The gas price for the transaction.
        :return: Transaction receipt.
        """
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)

        try:
            tx_hash = None
            transaction_type = UserTransactionType.SELL_TOKEN
            if is_with_usdt:
                tx_hash = await self._web3.sell_for_USDT(
                    token_address,
                    wallet_address,
                    wallet.secret,
                    Decimal(amount),
                    eth_amount_out_min=Decimal(0),
                    usdt_ui_amount_out_min=Decimal(0),
                    usdt_ui_gas_amount=TradeUSDTGas.SELL_FOR_USDT_GAS,
                    gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                    gas_price=gas_price,
                    gas_payer_secret=settings.GAS_PAYER_SECRET,
                )
            else:
                tx_hash = await self._web3.sell(
                    token_address,
                    Decimal(amount),
                    wallet_address,
                    wallet.secret,
                    amount_out_min=0,
                    gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                    gas_price=gas_price if gas_price else settings.DEFAULT_GAS_PRICE,
                )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"出售代币失败: {formatted_error}",
            )

        # Log the transaction
        # Calculate USD value for the transaction (卖币出售的是代币)
        usd_value = await self._calculate_transaction_usd_value(
            token_address=token_address,
            amount=Decimal(amount) * settings.DEFAULT_DECIMALS
        )
        
        # Get unit price for the token being sold
        token_unit_price = await self._get_token_unit_price(token_address=token_address)
        
        await self._repo.insert_transaction_history(
            user_id,
            transaction_type,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            tx_hash=tx_hash,
            gas_limit=gas,
            token_address=token_address,
            order_amount=int(Decimal(amount) * settings.DEFAULT_DECIMALS),
            payment_source=wallet_address,
            value=usd_value,
            order_id=order_id,
            unit_price=token_unit_price,
        )

        return CreationResponseSchema(
            txid=tx_hash, detail="Sell token request has been sent."
        )

    async def get_transaction_history(self, user_id, tx_hash_list=None, chain=settings.BLOCKCHAIN_TYPE):

        results = await self._repo.fetch_transaction_history(user_id, tx_hash_list, chain)

        status_mapping = {
            UserTransactionStatus.AWAITING_CONFIRMATION: "AwaitingConfirmation",
            UserTransactionStatus.CONFIRMED: "Confirmed",
            UserTransactionStatus.FAILED: "Failed",
        }

        type_mapping = {
            UserTransactionType.CREATE_MEME: "Create",
            UserTransactionType.SEND_TOKEN: "Send",
            UserTransactionType.BUY_TOKEN: "Buy",
            UserTransactionType.SELL_TOKEN: "Sell",
            UserTransactionType.RECEIVE_TOKEN: "Receive",
            UserTransactionType.BUY_TOKEN_DEX: "Buy",
            UserTransactionType.SELL_TOKEN_DEX: "Sell",
            UserTransactionType.GIFT_BUY_TOKEN: "BuyGift",
            UserTransactionType.WITHDRAW_CASH: "Withdraw",
            UserTransactionType.DEPOSIT_CASH: "Deposit",
            UserTransactionType.PAY_CONTENT: "PayContent",
        }

        transactions = []
        for result in results:
            if result.token_address == settings.USDT_ADDRESS:
                # Create USDT Pair directly instead of querying database
                pair = Pair(
                    base=settings.USDT_ADDRESS,
                    base_name="Cash",
                    base_symbol="Cash",
                    base_image_url="https://images.dev.memefans.ai/cash_icon.png",
                    base_decimals=6
                )
            else:
                if result.type == UserTransactionType.CREATE_MEME:
                    pair = await self._repo.fetch_pair_by_txid(result.tx_hash)
                else:
                    pair = await self._repo.fetch_pair(result.token_address)
            
            # Handle usd_amount field from database value column
            usd_amount = Decimal(result.value) if result.value is not None else Decimal(0)
            
            # Check if to_address is in our platform and get author info
            to_user_name = None
            to_user_avatar = None
            if result.to_address:
                to_author = await self._repo.fetch_author_by_wallet_address(result.to_address)
                if to_author:
                    to_user_name = to_author.username
                    to_user_avatar = to_author.avatar
            
            # Check if from_address is in our platform and get author info
            from_user_name = None
            from_user_avatar = None
            if result.from_address:
                from_author = await self._repo.fetch_author_by_wallet_address(result.from_address)
                if from_author:
                    from_user_name = from_author.name
                    from_user_avatar = from_author.avatar

            if result.type == UserTransactionType.RECEIVE_TOKEN:
                tx_hash = result.tx_hash.removeprefix("REC")
            else:
                tx_hash = result.tx_hash

            transactions.append(
                TransactionHistoryResponseSchema(
                    user_id=result.user_id,
                    type=type_mapping.get(result.type, ""),
                    status=status_mapping.get(result.status, ""),
                    timestamp=int(result.updated_at.timestamp() if result.updated_at else datetime.datetime.now().timestamp()),
                    transaction_id=tx_hash,
                    token_address=result.token_address if result.token_address else "",
                    image_url=pair.base_image_url if pair else "",
                    token_name=pair.base_name if pair else "",
                    token_symbol=pair.base_symbol if pair else "",
                    from_address=result.from_address,
                    to_address=result.to_address if result.to_address else "",
                    unit_price=decimal_to_string(Decimal(result.unit_price), 6) if result.unit_price else "",
                    order_amount=decimal_to_string(usd_amount, 6) if result.order_amount else "",
                    service_fee=decimal_to_string(result.service_fee, 6) if result.service_fee else "",
                    total_amount=decimal_to_string(result.base_amount / settings.DEFAULT_DECIMALS) if result.base_amount else "",
                    payment_source=result.payment_source,
                    supply=str(settings.DEFAULT_TOTAL_SUPPLY),
                    minting_cost=f"{decimal_to_string(result.gas_used) + ' wei' if result.gas_used else ''}",
                    usd_amount=decimal_to_string(usd_amount),
                    order_id=result.order_id,
                    from_user_name=from_user_name,
                    from_user_avatar=from_user_avatar,
                    to_user_name=to_user_name,
                    to_user_avatar=to_user_avatar
                )
            )

        return transactions

    async def get_estimate_gas(self, gas_type: EstimateGasType, function_params: List[str]) -> EstimateGasResponseSchema:

        try:
            key = f"GAS_ESTIMATE:{gas_type}" + ("".join(function_params) if function_params else "")
            # cached = await self._caching_client.get(MEMECOIN_REDIS_ENDPOINT, key)
            # if cached:
            #     return EstimateGasResponseSchema(**cached)

            if(gas_type == EstimateGasType.buy or gas_type == EstimateGasType.sell) and not function_params:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"you need to provide token address in function params: {function_params}"
                )

            if gas_type == EstimateGasType.buy:
                token_address = function_params[0]
                dex = await self.get_token_dex(token_address)
                result = await self._gas_estimator.estimate_buy_gas(token_address, dex)
            elif gas_type == EstimateGasType.sell:
                token_address = function_params[0]
                dex = await self.get_token_dex(token_address)
                result = await self._gas_estimator.estimate_sell_gas(token_address, dex)
            elif gas_type == EstimateGasType.transfer_bnb:
                # TODO: return transfer token gas
                # result = await self._gas_estimator.estimate_transfer_bnb_gas(None, None)
                result = await self._gas_estimator.estimate_transfer_token_gas(function_params)
            elif gas_type == EstimateGasType.create:
                result = await self._gas_estimator.estimate_launch_gas(function_params)
            elif gas_type == EstimateGasType.transfer_token:
                result = await self._gas_estimator.estimate_transfer_token_gas(function_params)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid gas type: {gas_type}"
                )
            # 计算gas费用的ETH价值
            eth_amount = Decimal(result['total_gas_cost']) / Decimal(settings.DEFAULT_DECIMALS)
            result['ui_amount_total_gas_cost'] = decimal_to_string(eth_amount)
            
            # 获取ETH价格并计算USD价值
            try:
                eth_price_response = await self.get_eth_price()
                eth_price_usd = Decimal(str(eth_price_response.price))
                usd_cost = eth_amount * eth_price_usd
                result['ui_amount_total_usd_cost'] = decimal_to_string(usd_cost)
            except Exception as e:
                logger.warning(f"Failed to get ETH price for gas cost calculation: {str(e)}")
                result['ui_amount_total_usd_cost'] = "0"
            await self._caching_client.set(
                MEMECOIN_REDIS_ENDPOINT, key, result, timedelta(seconds=600)
            )
            return EstimateGasResponseSchema(**result)

        except Exception as e:
            logger.error(f"Estimate gas failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
            )
    
    async def create_balance_schema(self, pubkey, amount, token_address=None):
        ui_amount = (amount / Decimal(settings.DEFAULT_DECIMALS)).quantize(self._quantizer, rounding=ROUND_DOWN)
        ui_amount_10 = (ui_amount * Decimal("0.1")).quantize(
            self._quantizer, rounding=ROUND_DOWN
        )
        ui_amount_25 = (ui_amount * Decimal("0.25")).quantize(
            self._quantizer, rounding=ROUND_DOWN
        )
        ui_amount_50 = (ui_amount * Decimal("0.5")).quantize(
            self._quantizer, rounding=ROUND_DOWN
        )

        # Calculate USD amount
        try:
            if token_address is None:
                # Native token (BNB/ETH)
                usd_value = await self._calculate_transaction_usd_value(
                    token_address=None,
                    amount=amount
                )
            else:
                # ERC20 token
                usd_value = await self._calculate_transaction_usd_value(
                    token_address=token_address,
                    amount=amount
                )
            usd_amount = decimal_to_string(Decimal(str(usd_value)) if usd_value else Decimal(0))
        except Exception as e:
            logger.warning(f"Failed to calculate USD amount: {str(e)}")
            usd_amount = "0"

        return BalanceResponseSchema(
            address=pubkey,
            amount=str(amount),
            usd_amount=usd_amount,
            ui_amount=decimal_to_string(ui_amount),
            ui_amount_10=decimal_to_string(ui_amount_10),
            ui_amount_25=decimal_to_string(ui_amount_25),
            ui_amount_50=decimal_to_string(ui_amount_50),
        )

    async def get_bnb_balance(self, user_id):
        wallet = await self._repo.fetch_user_wallet(user_id)
        amount = await self._web3.get_native_balance_in_wei(wallet.pubkey)
        return await self.create_balance_schema(wallet.pubkey, amount, token_address=None)

    async def get_token_amount(self, user_id, token_address):
        wallet = await self._repo.fetch_user_wallet(user_id)
        if not wallet:
            logger.error(f"Wallet not found for user ({user_id})")
        amount = await self._web3.get_token_balance_in_wei(wallet.pubkey, token_address)
        return await self.create_balance_schema(wallet.pubkey, amount, token_address)

    async def _get_amount_response(
        self,
        key_prefix: str,
        token_address: str,
        amount_str: str,
        web3_method,
        error_message: str,
    ) -> AmountResponseSchema:
        """
        Generic helper method to retrieve token or BNB amounts either from cache
        or by calling a specified web3 method.
        """
        try:
            key = f"{key_prefix}:{token_address}:{amount_str}"
            cached = await self._caching_client.get(MEMECOIN_REDIS_ENDPOINT, key)
            if cached:
                return AmountResponseSchema(**cached)

            amount, ui_amount = await web3_method(token_address, Decimal(amount_str))
            result = {"amount": str(amount), "ui_amount": decimal_to_string(ui_amount)}
            await self._caching_client.set(
                MEMECOIN_REDIS_ENDPOINT, key, result, timedelta(seconds=60)
            )
            return AmountResponseSchema(**result)
        except Exception as e:
            logger.error(f"{error_message}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=error_message
            )

    async def get_out_token_amount(
        self, token_address: str = None, usd_amount: str = None
    ) -> OutTokenAmountResponseSchema:
        """
        Calculate the token amount you can receive for a given USD amount.
        """
        # Validate usd_amount - if it's <= 0, return 0
        if usd_amount is not None:
            try:
                usd_amount_decimal = Decimal(usd_amount)
                if usd_amount_decimal <= 0:
                    return OutTokenAmountResponseSchema(
                        amount="0",
                        ui_amount="0",
                        max_usd_amount=""
                    )
            except (ValueError, TypeError):
                return OutTokenAmountResponseSchema(
                    amount="0",
                    ui_amount="0",
                    max_usd_amount=""
                )
        
        if not token_address:
            """
            在创建meme前，帮用户计算能买多少代币
            """

            # 用户 USDT 能兑换多少 ETH
            usdt_amount = int(Decimal(usd_amount) * Decimal(settings.USDT_DECIMALS))
            amount_out, amount_in, max_bnb_amount = self._gas_estimator.get_out_token_amount(usdt_amount)
            eth_price = await self.get_eth_price()
            return OutTokenAmountResponseSchema(
                amount=str(amount_out),
                ui_amount=decimal_to_string(amount_out / Decimal(10 ** Decimal(settings.DEFAULT_TOKEN_DECIMALS))),
                max_usd_amount=decimal_to_string(max_bnb_amount / Decimal(settings.USDT_DECIMALS))
            )

        dex = await self.get_token_dex(token_address)
        web3_method = self._web3.get_dex_out_token_amount_with_usdt if dex == "pancake" else self._web3.get_out_token_amount_with_usdt
        result = await self._get_amount_response(
            key_prefix="OUT_TOKEN_AMOUNT",
            token_address=token_address,
            amount_str=usd_amount,
            web3_method=web3_method,
            error_message="Failed to calculate token amount",
        )
        # transfer AmountResponseSchema to OutTokenAmountResponseSchema
        return OutTokenAmountResponseSchema(
            amount=result.amount,
            ui_amount=result.ui_amount,
            max_usd_amount=''
        )

    async def get_in_native_amount(
        self, token_address: str, token_amount: str
    ) -> AmountResponseSchema:
        """
        Calculate the amount of BNB needed to receive a specific token amount (excluding fees).
        """
        dex = await self.get_token_dex(token_address)
        web3_method = self._web3.get_dex_in_native_amount if dex == "pancake" else self._web3.get_in_native_amount
        return await self._get_amount_response(
            key_prefix="IN_BNB_AMOUNT",
            token_address=token_address,
            amount_str=token_amount,
            web3_method=web3_method,
            error_message="Failed to calculate BNB amount",
        )

    async def get_out_native_amount(
        self, token_address: str, token_amount: str
    ) -> AmountResponseSchema:
        """
        Calculate the amount of BNB you can receive for a given token amount (including fees).
        """
        dex = await self.get_token_dex(token_address)
        web3_method = self._web3.get_dex_out_usdt_amount if dex == "pancake" else self._web3.get_out_usdt_amount
        return await self._get_amount_response(
            key_prefix="OUT_BNB_AMOUNT",
            token_address=token_address,
            amount_str=token_amount,
            web3_method=web3_method,
            error_message="Failed to calculate BNB amount",
        )

    async def get_out_bnb_amount_after_fee(
        self, token_address: str, token_amount: str
    ) -> AmountResponseSchema:
        """
        Calculate the amount of BNB you can receive for a given token amount (excluding fees).
        """
        dex = await self.get_token_dex(token_address)
        web3_method = self._web3.get_dex_out_native_amount_after_fee if dex == "pancake" else self._web3.get_out_native_amount
        return await self._get_amount_response(
            key_prefix="OUT_BNB_AMOUNT_AFTER_FEE",
            token_address=token_address,
            amount_str=token_amount,
            web3_method=web3_method,
            error_message="Failed to calculate BNB amount",
        )

    async def get_token_progress(self, token_address: str) -> int:
        """
        Get the current progress of a token (0-100)
        """
        try:
            progress = await self._web3.progress(token_address)
            return progress
        except Web3RPCError as e:
            logger.error(f"Failed to get token progress: {str(e)}")
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"获取代币进度失败: {formatted_error}",
            )

    async def token_graduate(self, token_address: str) -> bool:
        """
        Graduate a token and launch it to PancakeSwap.

        This function calls the blockchain to graduate the token from the bonding curve
        to PancakeSwap exchange. The smart contract will verify if the token is eligible
        for graduation.

        :param token_address: The contract address of the token to graduate
        :type token_address: str
        :return: Whether the graduation was successful
        :rtype: bool
        :raises HTTPException: When graduation fails due to contract call failure or network issues
        """
        try:
            # Directly attempt to graduate the token without checking progress
            graduation_result = await self._web3.graduate(token_address)
            logger.info(f"Token {token_address} graduation successful")
            return graduation_result

        except Web3RPCError as e:
            logger.error(f"Failed to graduate token: {str(e)}")
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代币毕业失败: {formatted_error}",
            )
        except Exception as e:
            logger.error(f"Unexpected error during token graduation: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred during token graduation.",
            )
        
    async def send_asset(self, user_id, token_address, to_address=None, amount=None, gas=None, gas_price=None, order_id: Optional[str] = None, username: Optional[str] = None):
        # 优先使用 username，如果提供了 username 则通过 username 查找地址
        final_to_address = to_address
        if username:
            user_info = await self._repo.fetch_user_info_by_username(username)
            if not user_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"用户名 '{username}' 对应的钱包地址未找到"
                )
            final_to_address = user_info["wallet_address"]
        elif not to_address:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供 to_address 或 username 中的至少一个"
            )
        
        if self._web3.check_if_native_token(token_address):
            return await self.send_cash(user_id, final_to_address, amount, gas, gas_price, order_id)
        else:
            return await self.send_token(user_id, token_address, final_to_address, amount, gas, gas_price, order_id)

    async def send_native(self, user_id, to_address, amount, gas, gas_price, order_id: Optional[str] = None):
        # Fetch the user's wallet
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        try:
            # Call the blockchain function to send tokens
            tx_hash = await self._web3.transfer_native(
                wallet_address,
                wallet.secret,
                to_address,
                Decimal(amount),
                gas=gas if gas else settings.DEFAULT_GAS_LIMIT,
                gas_price=gas_price if gas_price else settings.DEFAULT_GAS_PRICE,
            )
        except Web3RPCError as e:
            # 使用格式化的错误信息，提供更友好的用户体验
            formatted_error = format_web3_error(e)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"原生代币转账失败: {formatted_error}",
            )

        # Log the transaction (assuming receipt includes the transaction hash)
        # Calculate USD value for the transaction
        usd_value = await self._calculate_transaction_usd_value(
            token_address="native_token",
            amount=Decimal(amount) * settings.DEFAULT_DECIMALS
        )
        
        await self._repo.insert_transaction_history(
            user_id,
            UserTransactionType.SEND_TOKEN,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            token_address="native_token",
            to_address=to_address,
            tx_hash=tx_hash,
            gas_limit=gas,
            order_amount=int(Decimal(amount) * settings.DEFAULT_DECIMALS),
            payment_source=wallet_address,
            value=usd_value,
            order_id=order_id,
        )

        return CreationResponseSchema(
            txid=tx_hash, detail="Send token request has been sent."
        )

    async def withdraw_to_l1(
        self,
        user_id: str,
        network: Network,
        amount: str,
        to_address: str,
        gas: int,
        gas_price: int,
        order_id: Optional[str] = None,
    ) -> CreationResponseSchema:
        """
        Withdraw funds from L2 to L1 via external TS script.
        Validates inputs, records a pending transaction, and invokes a configurable command if present.
        """
        logger.info(f"Initiating withdraw_to_l1 for user {user_id} to_address: {to_address}, amount: {amount}")
        # Validate amount
        try:
            amount_dec = Decimal(amount)
        except Exception:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid amount format")
        if amount_dec <= 0:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Amount must be greater than 0")

        if not to_address:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="to_address cannot be empty")

        if network != Network.LAYER1:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported network")

        # Fetch user's wallet
        wallet = await self._repo.fetch_user_wallet(user_id)
        from_address = self._web3.w3.to_checksum_address(wallet.pubkey)

        tx_placeholder = f"withdraw:{user_id}:{int(time.time())}"


        # Get private key using sign method
        try:
            # Try to get private key from sign method
            #logger.info(f"Attempting to sign with secret: {wallet.secret[:10]}..., raw_sign=True")
            #logger.info(f"Using tx placeholder: {tx_placeholder.encode('utf-8').hex()}")
            #logger.info(f"Using secret: {wallet.secret}")
            #logger.info(f"type:{settings.BLOCKCHAIN_TYPE.lower()}")
            private_key = await self._web3.sign(wallet.secret,
                                                tx_placeholder.encode('utf-8').hex(), 
                                                type=settings.BLOCKCHAIN_TYPE.lower(), 
                                                raw_sign=False)
            #logger.info(f"Sign method returned private key: {private_key[:10] if private_key else None}...")
            if not private_key:
                raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get private key from sign method")
        except Exception as e:
            logger.error(f"Error occurred while fetching private key: {e}", exc_info=True)
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get private key from sign method")

        # Insert a pending history entry
        await self._repo.insert_transaction_history(
            user_id,
            UserTransactionType.WITHDRAW_CASH,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            from_address,
            token_address="native_token",
            to_address=to_address,
            tx_hash=tx_placeholder,
            gas_limit=gas,
            order_amount=int(amount_dec * settings.DEFAULT_DECIMALS),
            payment_source=from_address,
            value=float(amount_dec),
            order_id=order_id,
        )

        # Determine the script path and command based on the environment
        import os
        import platform
        import asyncio
        import json as _json


        
        # Construct the path to the script
        script_path = '/src/memecoin/scripts/withdraw.ts'
        
  
        # Use tsx to directly execute TypeScript file
        cmd = "npx"
        args = [
            cmd,
            "tsx",
            script_path,
            "--network", str(network.value),
            "--from", str(from_address),
            "--to", str(to_address),
            "--amount", str(amount_dec),
            "--token", "USDT",  # 默认都是USDT
            "--gas", str(gas or settings.DEFAULT_GAS_LIMIT),
            "--gasPrice", str(gas_price or settings.DEFAULT_GAS_PRICE),
            "--privateKey", private_key
        ]

        try:
            # Log the command for debugging (without private key)
            #logger.info(f"Running compiled withdraw script with args: {[arg for arg in args if not arg.startswith('0x')]}")
            #logger.info(f"Final to_address before script execution: {to_address}")
            
            # Print the full command for debugging purposes
            #full_cmd = " ".join(args)
            #logger.info(f"Full withdraw command: {full_cmd}")
            #print(f"Full withdraw command: {full_cmd}")
            
            proc = await asyncio.create_subprocess_exec(
                *args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(proc.communicate(), timeout=120)
            if proc.returncode != 0:
                err_msg = stderr.decode().strip() or stdout.decode().strip() or "Withdraw script failed"
                logger.error(f"Withdraw script failed with error: {err_msg}")
                raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=err_msg)

            out_text = stdout.decode().strip() or "{}"
            try:
                data = _json.loads(out_text)
                if "error" in data:
                    raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=data["error"])
            except Exception:
                data = {"raw": out_text}

            tx_hash = data.get("txHash") or data.get("tx_hash") or tx_placeholder
            return CreationResponseSchema(txid=tx_hash, detail="Withdraw submitted")

        except HTTPException:
            raise
        except asyncio.TimeoutError:
            raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail="Withdraw script timeout")
        except Exception as e:
            logger.error(f"Withdraw script invocation error: {e}")
            raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail="Withdraw script error")

    async def get_token_dex(self, token_address: str):
        pair = await self._repo.fetch_pair(token_address)
        if pair and pair.dex:
            return pair.dex
        else:
            return ""

    async def recent_send_address(self, user_id):
        return await self._repo.fetch_recent_send_address(user_id)

    async def get_eth_price(self) -> EthPriceResponseSchema:
        """
        获取当前ETH价格（USD）
        
        Returns:
            EthPriceResponseSchema: ETH价格响应
        """
        
        # 从Redis获取ETH价格
        redis_client = self._caching_client.client
        price = await get_eth_price(redis_client)
        
        return EthPriceResponseSchema(
            price=price,
            currency="USD",
            timestamp=int(time.time())
        )


    async def get_popular_meme(self):
        pairs = await self._repo.fetch_token_list(
            TokenListType.new, 0, 5, self._chain_id
        )
        tokens: List[TokenSchema] = []
        for pair in pairs:
            token = await self._create_token_from_pair(pair)
            tokens.append(token)
        return tokens

    async def export_key(self, user: User, passphrase: str):
        # If email exists, only require email verification and skip phone check
        if user.email:
            is_email_verified = await self._caching_client.get(
                endpoint=self.export_key_email_verified_endpoint,
                key=user.email
            )
            if not is_email_verified or is_email_verified != "true":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Please verify your email first."
                )
        else:
            # No email set: require phone verification
            is_phone_verified = None
            if user.phone_number:
                is_phone_verified = await self._caching_client.get(
                    endpoint=self.export_key_phone_verified_endpoint,
                    key=user.phone_number
                )
            if not is_phone_verified or is_phone_verified != "true":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Please verify phone number first."
                )

        wallet = await self._repo.fetch_user_wallet(user.id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        payload = {"secret": wallet.secret, "blockchain_type": settings.BLOCKCHAIN_TYPE.lower(), "passphrase": passphrase}
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{settings.KMS_ADDRESS}/export", json=payload)
                js = response.json()
                if js.get("success"):
                    mnemonic = js.get("mnemonic")
                    return ExportMnemonicResponseSchema(
                        pubkey=wallet_address,
                        mnemonic=mnemonic
                    )
                else:
                    return None
        except httpx.HTTPError as e:
            logger.warning(f"HTTP error occurred: {e}")
            return None

    async def claim_reward(self, user_id: str, token_address: str, amount: Decimal):
        wallet = await self._repo.fetch_user_wallet(user_id)
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        raw_amount = amount * settings.DEFAULT_DECIMALS
        tx_hash = await self._web3.claim(wallet_address, wallet.secret, token_address, raw_amount, settings.DEFAULT_GAS_LIMIT, settings.DEFAULT_GAS_PRICE)
        
        # Log the transaction
        # Calculate USD value for the transaction
        usd_value = await self._calculate_transaction_usd_value(
            token_address=token_address,
            amount=raw_amount
        )
        
        await self._repo.insert_transaction_history(
            user_id,
            UserTransactionType.RECEIVE_TOKEN,
            UserTransactionStatus.AWAITING_CONFIRMATION,
            wallet_address,
            token_address=token_address,
            tx_hash=tx_hash,
            gas_limit=settings.DEFAULT_GAS_LIMIT,
            order_amount=int(raw_amount),
            payment_source=wallet_address,
            value=usd_value,
            order_id=None,
        )
        
        return CreationResponseSchema(txid=tx_hash, detail="Claim reward request has been sent.")

    async def check_create_meme_eligibility(self, user_id: str) -> bool:
        """
        检查用户是否有资格创建meme代币
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否有资格创建meme代币
        """
        wallet = await self._repo.fetch_user_wallet(user_id)
        if not wallet:
            return False
        wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
        try:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:create_eligibility:{user_id}"
            cached_result = await self._caching_client.get(MEMECOIN_REDIS_ENDPOINT, cache_key)
            
            if cached_result is not None:
                return cached_result.get("eligible", False) == "true"
            
            eligible = await self._web3.userCreateTokenEligible(wallet_address)
            
            await self._caching_client.set(
                MEMECOIN_REDIS_ENDPOINT,
                cache_key,
                {"eligible": "true" if eligible else "false"},
                expiration_time=timedelta(minutes=1)
            )
            
            return eligible
        except Exception as e:
            logger.error(f"Error checking create meme eligibility: {str(e)}", exc_info=True)
            return False

    async def add_token_to_watchlist(self, user_id: str, token_address: str, collection_service, notification_service) -> bool:
        """
        切换令牌在用户观察列表中的状态（添加或移除）
        
        Args:
            user_id: 用户ID
            token_address: 令牌地址
            
        Returns:
            bool: 操作后令牌是否在观察列表中
        """
        try:
            is_in_watchlist = await self._repo.check_token_in_watchlist(user_id, token_address)
            
            if is_in_watchlist:
                return True
            else:
                collection_id = await self._repo.fetch_collection_id_by_token_address(token_address)
                if not collection_id:
                    logger.warning(f"Token {token_address} has no collection id.")
                    return False

                await self._repo.add_token_to_watchlist(user_id, token_address)
                await collection_service._add_subscriber(collection_id, user_id)

                collection = await collection_service.get(collection_id)
                follower = await self._repo.fetch_author(user_id)

                await notification_service.create_notification(
                    notification_type=NotificationType.follow_meme,
                    recipients_ids=[collection.author_id],
                    meta={
                        "collection_id": collection.id,
                        "collection_cover": collection.cover,
                        "collection_title": collection.title,
                        "token_address": token_address,
                        "follower_id": user_id,
                        "follower_avatar": follower.avatar,
                        "follower_username": follower.username,
                        "follower_name": follower.name
                    }
                )
                return True
                
        except Exception as e:
            logger.error(f"Error toggling token watchlist: {str(e)}", exc_info=True)
            return False
    
    async def check_token_watchlist(self, user_id: str, token_address: str) -> bool:
        """
        检查令牌是否在用户的观察列表中
        
        Args:
            user_id: 用户ID
            token_address: 令牌地址
            
        Returns:
            bool: 令牌是否在观察列表中
        """
        try:
            return await self._repo.check_token_in_watchlist(user_id, token_address)
        except Exception as e:
            logger.error(f"Error checking token watchlist: {str(e)}", exc_info=True)
            return False
    
    async def get_token_watchlist(self, target_user_id: str, current_user_id: Optional[str] = None, sort_criteria=None, pagination_params=None, filter: Optional[str] = None):
        """
        获取用户的令牌观察列表
        
        Args:
            target_user_id: 要查询的目标用户ID
            current_user_id: 当前登录用户ID（用于判断是否关注）
            sort_criteria: 排序标准
            pagination_params: 分页参数
            filter: 过滤条件(token名称)
            
        Returns:
            Tuple[List[TokenSchema], int]: 符合条件的令牌列表和总数量
        """
        try:
            offset = (pagination_params.page - 1) * pagination_params.page_size
            
            # 如果是交易量相关的排序，先不使用排序标准获取数据
            need_volume_sort = sort_criteria in [SortCriteria.VOLUME_HIGH_TO_LOW, SortCriteria.VOLUME_LOW_TO_HIGH]
            sort_value = "default" if need_volume_sort else sort_criteria.value
            
            # 获取令牌列表和总数量
            pairs, total_count = await self._repo.get_token_watchlist(
                target_user_id, 
                sort_value, 
                offset, 
                pagination_params.page_size,
                filter,
                self._chain_id
            )

            # 批量获取关注者数量和当前用户关注状态
            token_addresses = [pair.base for pair in pairs]
            follower_counts = await self._repo.batch_get_token_follower_counts(token_addresses)
            followed_by_current_user = {}
            if current_user_id:
                followed_by_current_user = await self._repo.check_tokens_followed_by_user(
                    current_user_id, token_addresses
                )
            
            tokens: List[TokenSchema] = []
            for pair in pairs:
                # 创建基本令牌对象
                token = await self._create_token_from_pair(pair)
                
                # 添加流动性信息
                liq = await self._web3.get_liquidity(pair.base)
                token.liquidity = f"{liq:.8f}"
                
                # 添加24小时交易量信息
                sorted_set_key = f"{MEMECOIN_REDIS_ENDPOINT}:{pair.base}:kline:1D"
                period_start = await self._caching_client.client.zrevrange(sorted_set_key, 0, 1)
                volume = "0"
                if period_start:
                    token_data = await self._caching_client.client.hgetall(
                        f"{sorted_set_key}:{period_start[0]}"
                    )
                    if token_data:
                        volume = token_data.get("v")
                token.volume_24h = volume
                
                # 添加创建时间
                token.created_at = int(pair.base_created_at.timestamp()) if pair.base_created_at else 0
                
                # 添加新字段
                token.follower_count = follower_counts.get(pair.base, 0)
                token.is_followed_by_current_user = followed_by_current_user.get(pair.base, False)
                
                tokens.append(token)
            
            # 如果是交易量相关的排序，在服务层进行排序
            if sort_criteria == SortCriteria.VOLUME_HIGH_TO_LOW:
                tokens.sort(key=lambda x: float(x.volume_24h or "0"), reverse=True)
            elif sort_criteria == SortCriteria.VOLUME_LOW_TO_HIGH:
                tokens.sort(key=lambda x: float(x.volume_24h or "0"))
                
            return tokens, total_count
        except Exception as e:
            logger.error(f"Error getting token watchlist: {str(e)}", exc_info=True)
            return [], 0

    async def remove_token_from_watchlist(self, user_id: str, token_address: str, collection_service, notification_service) -> bool:
        """
        从用户的观察列表中移除令牌，如果令牌不在列表中则什么都不做
        
        Args:
            user_id: 用户ID
            token_address: 令牌地址
            
        Returns:
            bool: 操作后令牌是否在观察列表中（始终为False）
        """
        try:
            collection_id = await self._repo.fetch_collection_id_by_token_address(token_address)
            if not collection_id:
                logger.warning(f"Token {token_address} has no collection id.")
                return False
                
            # 直接从观察列表中移除令牌
            await self._repo.remove_token_from_watchlist(user_id, token_address)
            
            # 直接调用collection_service的内部方法直接移除订阅关系
            try:
                await collection_service._remove_subscriber(collection_id, user_id)
            except Exception as e:
                logger.error(f"Error unsubscribing from collection: {str(e)}", exc_info=True)
                # 即使取消订阅失败，仍然将token从观察列表中移除
                
            return True
        except Exception as e:
            logger.error(f"Error removing token from watchlist: {str(e)}", exc_info=True)
            return False

    async def _batch_get_cache(self, keys, default_value=None):
        """
        Batch fetch multiple cache keys and safely handle exceptions
        
        Args:
            keys: List of cache keys to fetch
            default_value: Default value when key doesn't exist or an error occurs
            
        Returns:
            Dictionary with original cache keys as keys and corresponding cache values or default values
        """
        futures = [self._caching_client.client.hgetall(key) for key in keys]
        results = await asyncio.gather(*futures, return_exceptions=True)
        
        cache_map = {}
        for key, result in zip(keys, results):
            if isinstance(result, Exception) or not result:
                cache_map[key] = default_value
            else:
                cache_map[key] = result
                
        return cache_map

    def gas_price_to_gwei(self, amount: int):
        """
        将 gas 价格从 wei 转换为 gwei

        :param amount: gas 价格，假定为 wei 单位
        :return: gwei 单位的 gas 价格
        """
        if amount < 10000:
            return amount

        return amount / 10 ** 9

    async def is_token_holder(self, user_id: str, token_address: str) -> bool:
        """
        检查用户是否持有特定token
        
        Args:
            user_id: 用户ID
            token_address: token地址
            
        Returns:
            bool: 是否持有token
        """
        try:
            return await self._repo.check_user_holds_token(user_id, token_address)
        except Exception as e:
            logger.error(f"Error checking if user is token holder: {str(e)}", exc_info=True)
            return False

    async def get_web3_transaction_status(self, tx_hash: str) -> Web3TransactionStatusSchema:
        """
        直接从web3获取交易状态
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            Web3TransactionStatusSchema: 包含交易状态信息
        """
        try:
            # 尝试获取交易收据
            receipt = await self._web3.w3.eth.get_transaction_receipt(tx_hash)
            
            if receipt:
                # 交易已被挖掘
                if receipt.status == 1:
                    return Web3TransactionStatusSchema(
                        tx_hash=tx_hash,
                        status="success",
                        is_success=True
                    )
                else:
                    return Web3TransactionStatusSchema(
                        tx_hash=tx_hash,
                        status="failed",
                        is_success=False
                    )
            else:
                # 收据未找到 - 交易可能仍在等待中
                return Web3TransactionStatusSchema(
                    tx_hash=tx_hash,
                    status="pending",
                    is_success=False
                )
                
        except Exception as e:
            # 交易未找到或其他错误
            logger.warning(f"Failed to get transaction status for {tx_hash}: {str(e)}")
            return Web3TransactionStatusSchema(
                tx_hash=tx_hash,
                status="not_found",
                is_success=False
            )

    async def get_my_memes(self, user_id):
        pairs = await self._repo.fetch_user_created_memes(user_id, self._chain_id)
        token_schemas = []
        for pair in pairs:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{pair.base}"
            cache = await self._caching_client.client.hgetall(key)
            if cache is None:
                cache = {
                    "name": "",
                    "symbol": "",
                    "image_url": "",
                    "price": 0,
                    "price_change_percent": 0,
                    "market_cap": 0,
                }
            token_info: TokenCache = TokenCache.model_validate(cache)
            try:
                price = token_info.price
                price_change_percent = token_info.price_change_percent
            except (TypeError, ValueError):
                logger.error(f"Failed to parse token info for {pair.base}")
                continue
                
            contents_count = 0
            if pair.collection_id:
                contents_count = await self._repo.fetch_collection_post_count(pair.collection_id)

            token_schemas.append(
                MemeTokenSchema(
                    token_name=token_info.name if token_info.name else pair.base_name,
                    token_symbol=token_info.symbol if token_info.symbol else pair.base_symbol,
                    token_address=pair.base,
                    image_url=pair.base_image_url,
                    collection_id=pair.collection_id,
                    price=f"{price:.20f}",
                    price_change_percent=str(price_change_percent),
                    market_cap=str(token_info.market_cap),
                    contents_count=contents_count,
                )
            )
        return token_schemas

    async def _get_token_price_from_contract(self, token_address: str) -> float:
        """
        从合约查询token的USD价格
        
        :param token_address: token地址
        :return: token的USD价格
        :raises HTTPException: 当无法获取价格时
        """
        try:
            # # Get native token price for calculation
            # native_price_data = await self._caching_client.client.zrevrange(
            #     f"{MEMECOIN_REDIS_ENDPOINT}:{self._native_usd_price_prefix}:price", 0, 0
            # )
            # if not native_price_data:
            #     raise HTTPException(
            #         status_code=HTTP_404_NOT_FOUND,
            #         detail=f"Native token price data not found.",
            #     )
            
            # Get token price from contract
            # Use 100000 tokens as base amount to calculate price for better accuracy
            standard_token_amount = Decimal(100000)
            
            # Check if token is on DEX or bonding curve
            dex = await self.get_token_dex(token_address)
            if dex == "pancake":
                # Use DEX method for PancakeSwap
                _, native_amount_decimal = await self._web3.get_dex_out_usdt_amount(
                    token_address, standard_token_amount
                )
            else:
                # Use bonding curve method
                _, native_amount_decimal = await self._web3.get_out_usdt_amount(
                    token_address, standard_token_amount
                )
            
            # Calculate token price in USD
            # price_per_token = (native_amount_per_100000_tokens / 100000) * native_price_usd
            if native_amount_decimal > 0:
                token_price_usd = float(native_amount_decimal / standard_token_amount)
            else:
                token_price_usd = 0
            
            logger.info(f"Calculated token price from contract for {token_address}: ${token_price_usd}")
            return token_price_usd
            
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Failed to get token price from contract for {token_address}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to get token price: {str(e)}",
            )

    async def convert_token_usd(
        self, 
        token_address: str, 
        token_amount: Optional[str] = None, 
        usd_amount: Optional[str] = None
    ):
        """
        Convert between token amount and USD amount bidirectionally.
        Supports both regular tokens and native tokens (ETH/BNB/SOL).
        
        :param token_address: The token contract address or "native_token" for ETH/BNB/SOL
        :param token_amount: Token amount to convert to USD (optional)
        :param usd_amount: USD amount to convert to tokens (optional)
        :return: ConversionResponseSchema with conversion result
        """
        
        # Validate input parameters
        if not ((token_amount is None) ^ (usd_amount is None)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must provide either token_amount or usd_amount, but not both or neither.",
            )

        # Handle native token (ETH/BNB/SOL)
        if token_address == "native_token":
            # Native token now is USDT
            token_price_usd = 1.0

            if token_price_usd <= 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Native token price is not available or invalid.",
                )
        else:
            # Handle regular ERC20/SPL tokens
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
            cache = await self._caching_client.client.hgetall(key)
            
            token_price_usd = None
            
            if cache:
                try:
                    token_cache = TokenCache.model_validate(cache)
                    token_price_usd = float(token_cache.price)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to parse cached token price for {token_address}: {str(e)}")
            
            # If no cache or invalid price, try to get price from contract
            token_price_usd = await self._get_token_price_from_contract(token_address)
            
            if token_price_usd <= 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Token price is not available or invalid.",
                )
        
        # Perform conversion
        if token_amount is not None:
            # Convert token amount to USD
            token_amount_decimal = Decimal(token_amount)
            if token_amount_decimal < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Token amount must be non-negative.",
                )
            
            usd_amount_decimal = token_amount_decimal * Decimal(str(token_price_usd))
            conversion_direction = "token_to_usd"
        else:
            # Convert USD amount to token amount
            usd_amount_decimal = Decimal(usd_amount)
            if usd_amount_decimal < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="USD amount must be non-negative.",
                )
            
            token_amount_decimal = usd_amount_decimal / Decimal(str(token_price_usd))
            conversion_direction = "usd_to_token"
        
        return ConversionResponseSchema(
            token_address=token_address,
            token_amount=decimal_to_string(token_amount_decimal),
            usd_amount=f"{usd_amount_decimal:.6f}",
            conversion_direction=conversion_direction,
        )

    # ==================== MOONPAY 签名服务 ====================
    
    def _generate_moonpay_signature(self, query_string: str) -> str:
        """
        生成MoonPay签名
        
        Args:
            query_string: URL的查询字符串部分
            
        Returns:
            Base64编码的HMAC-SHA256签名
        """
        if not settings.MOONPAY_SECRET_KEY:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="MoonPay Secret Key is not configured"
            )
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            settings.MOONPAY_SECRET_KEY.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # 转换为Base64字符串
        return base64.b64encode(signature).decode('utf-8')
    

    
    def sign_moonpay_url(self, url: str) -> str:
        """
        对MoonPay URL进行签名
        
        Args:
            url: 需要签名的MoonPay URL
            
        Returns:
            Base64编码的签名字符串
        """
        try:
            # 解析URL
            parsed_url = urlparse(url)
            query_string = parsed_url.query
            
            if not query_string:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="URL must contain query parameters"
                )
            
            # 添加 '?' 前缀以匹配 JavaScript 的 new URL().search 行为
            search_string = f"?{query_string}"
            
            # 生成签名
            signature = self._generate_moonpay_signature(search_string)
            
            return signature
            
        except Exception as e:
            logger.error(f"MoonPay URL signing failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"URL signing failed: {str(e)}"
            )
    


    async def get_cash_amount(self, user_id: str) -> Tuple[ChecksumAddress, Decimal]:
        """
        获取用户ETH余额对应的USD价值
        
        Args:
            user_id: 用户ID
            
        Returns:
            CashAmountResponseSchema: 包含ETH余额和USD价值的响应
        """
        try:
            # 获取用户钱包
            wallet = await self._repo.fetch_user_wallet(user_id)
            if not wallet:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户钱包未找到"
                )
            
            wallet_address = self._web3.w3.to_checksum_address(wallet.pubkey)
            
            # 获取USDT余额
            cash_balance = await self._web3.get_cash_balance(wallet_address)
            
            # 计算USD价值
            usd_value = Decimal(cash_balance)
            
            return wallet_address, usd_value
            
        except Exception as e:
            logger.error(f"获取cash_amount失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取cash_amount失败: {str(e)}"
            )

    async def get_wallet_address_by_username(self, username: str) -> WalletAddressResponseSchema:
        """
        通过用户名获取用户的钱包地址及基本信息
        
        Args:
            username: 用户名
            
        Returns:
            WalletAddressResponseSchema: 包含用户名、姓名、头像和钱包地址的响应
            
        Raises:
            HTTPException: 当用户名不存在时抛出404错误
        """
        try:
            user_info = await self._repo.fetch_user_info_by_username(username)
            
            if not user_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"用户名 '{username}' 对应的钱包地址未找到"
                )
            
            return WalletAddressResponseSchema(
                username=user_info["username"],
                name=user_info["name"],
                avatar=user_info["avatar"],
                wallet_address=user_info["wallet_address"]
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取用户钱包地址失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取用户钱包地址失败: {str(e)}"
            )

    async def _get_token_unit_price(
        self,
        token_address: Optional[str] = None,
        is_usdt: bool = False
    ) -> Optional[float]:
        """
        获取token的单价（USD）
        
        Args:
            token_address: 代币地址，如果是原生币则为None
            is_usdt: 是否为USDT交易
            
        Returns:
            float: token单价，如果无法获取则返回None
        """
        try:
            if not token_address or token_address == "native_token":
                # 原生币价格
                native_price_data = await self._caching_client.client.zrevrange(
                    f"{MEMECOIN_REDIS_ENDPOINT}:{self._native_usd_price_prefix}:price", 0, 0
                )
                if native_price_data:
                    return float(native_price_data[0])
            elif is_usdt or token_address == settings.USDT_ADDRESS:
                # USDT价格固定为1
                return 1.0
            else:
                # 代币价格
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
                cache = await self._caching_client.client.hgetall(cache_key)
                if cache:
                    token_cache = TokenCache.model_validate(cache)
                    token_price = float(token_cache.price)
                    if token_price > 0:
                        return token_price
                        
                # 如果缓存中没有价格，尝试从合约获取
                try:
                    return await self._get_token_price_from_contract(token_address)
                except:
                    # 如果从合约获取失败，返回None
                    pass
            
            return None
                
        except Exception as e:
            logger.error(f"获取token单价失败: {str(e)}")
            return None

    async def _calculate_transaction_usd_value(
        self, 
        token_address: Optional[str] = None,
        amount: Optional[Decimal] = None
    ) -> Optional[float]:
        """
        计算交易的USD价值
        
        Args:
            token_address: 代币地址，如果是原生币则为"native_token"或None
            amount: 交易金额（最小单位，如wei）
            
        Returns:
            float: USD价值，如果无法计算则返回None
        """
        try:
            if not amount or amount <= 0:
                return None
            
            is_usdt = (token_address == settings.USDT_ADDRESS)
            # 转换为UI单位
            ui_amount = amount / Decimal(settings.USDT_DECIMALS if is_usdt else settings.DEFAULT_DECIMALS)
            
            if not token_address or token_address == "native_token":
                # 原生币交易
                native_price_data = await self._caching_client.client.zrevrange(
                    f"{MEMECOIN_REDIS_ENDPOINT}:{self._native_usd_price_prefix}:price", 0, 0
                )
                if native_price_data:
                    native_price = float(native_price_data[0])
                    return float(ui_amount * Decimal(native_price))
            elif is_usdt:
                return float(ui_amount)
            else:
                # 代币交易
                cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
                cache = await self._caching_client.client.hgetall(cache_key)
                if cache:
                    token_cache = TokenCache.model_validate(cache)
                    token_price = float(token_cache.price)
                    if token_price > 0:
                        return float(ui_amount * Decimal(token_price))
            
            return None
                
        except Exception as e:
            logger.e