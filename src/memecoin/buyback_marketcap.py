import asyncio
from decimal import Decimal
import json
import logging
import os
import sys
from typing import Any, Dict, Optional
import aiohttp
from pydantic import BaseModel
from redis import Redis
from sqlalchemy import select
from dotenv import load_dotenv
from datetime import datetime, timedelta


# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), ".env"))

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.insert(0, project_root)

from src.database.models import Pair, BuyBackMCHistory
from src.database.session import get_session_context
from src.memecoin.utils import get_bnb_price
from src.memekline.cache import redis_client
from src.memecoin.settings import settings
from sqlalchemy.ext.asyncio import AsyncSession

# 创建日志目录
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "buyback.log")),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("buyback_marketcap")

class ServiceConfig(BaseModel):
    """交易机器人配置"""
    api_base_url: str 
    api_token: str

class BuybackMarketcapService:
    def __init__(self, db: AsyncSession, cache: Redis,config: ServiceConfig):
        self.db = db
        self.cache = cache
        self.config = config
        self.headers = {
            # "Authorization": f"Bearer {self.config.api_token}",
            "Content-Type": "application/json"
        }
        
    async def _api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Any:
        """发送API请求"""
        url = f"{self.config.api_base_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method.upper() == "GET":
                    async with session.get(url, headers=self.headers) as response:
                        response.raise_for_status()
                        return await response.json()
                elif method.upper() == "POST":
                    async with session.post(url, headers=self.headers, json=data) as response:
                        response.raise_for_status()
                        return await response.json()
                else:
                    logger.error(f"不支持的HTTP方法: {method}")
                    return None
        except aiohttp.ClientError as e:
            logger.error(f"API请求异常 ({method} {endpoint}): {str(e)}, URL: {url}, Headers: {self.headers}")
            return None
        except Exception as e:
            logger.error(f"API请求未知错误 ({method} {endpoint}): {str(e)}, URL: {url}, Headers: {self.headers}")
            return None

    async def get_out_token_amount(self, token_address: str, bnb_ui_amount: str) -> Decimal:
        """获取代币数量"""
        response = await self._api_request("GET", f"/get_out_token_amount?token_address={token_address}&usd_amount={bnb_ui_amount}")
        return Decimal(response.get("ui_amount", "0"))

    async def get_all_pairs(self) -> list[Pair]:
        pairs = await self.db.execute(select(Pair).where(Pair.base.isnot(None)))
        return pairs.scalars().all()

    async def get_bnb_price_by_date(self, date):
        """根据日期获取 BNB 价格，使用 CoinGecko API"""
        try:
            # 格式化日期为 DD-MM-YYYY 格式
            formatted_date = date.strftime("%d-%m-%Y")
            logger.info(f"Fetching BNB price for date: {formatted_date}")
            
            # 使用 CoinGecko API 获取历史价格
            url = f"https://api.coingecko.com/api/v3/coins/binancecoin/history?date={formatted_date}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "market_data" in data and "current_price" in data["market_data"]:
                            usd_price = data["market_data"]["current_price"]["usd"]
                            logger.info(f"BNB price on {formatted_date}: ${usd_price}")
                            return Decimal(str(usd_price))
                    else:
                        logger.error(f"Failed to fetch BNB price for {formatted_date}, status: {response.status}")
            
            # 如果 API 请求失败，返回默认值
            logger.warning(f"Using default value for BNB price on {formatted_date}")
            return Decimal("606.35")  # 默认值
            
        except Exception as e:
            logger.error(f"Error fetching BNB price for {date}: {str(e)}")
            # 如果出错，返回默认值
            return Decimal("606.35")  # 默认值
        
    async def compute_marketcap_rank(self) -> list[Dict[str, Any]]:
        """计算市值增长排名，比较当前市值与上个月五号的市值之差，排序并输出前10名"""
        # 获取所有 pairs
        pairs = await self.get_all_pairs()
        
        # 计算上个月五号UTC 12:00am的时间
        today = datetime.utcnow()
        if today.month == 1:
            last_month = 12
            last_month_year = today.year - 1
        else:
            last_month = today.month - 1
            last_month_year = today.year
            
        last_month_fifth = datetime(last_month_year, last_month, 5, 0, 0, 0)
        logger.info(f"Last month's fifth day (UTC 12:00am): {last_month_fifth}")
        
        # 计算本月五号UTC 12:00am的时间
        this_month_fifth = datetime(today.year, today.month, 5, 0, 0, 0)
        logger.info(f"This month's fifth day (UTC 12:00am): {this_month_fifth}")
        
        # 存储市值增长数据
        marketcap_growth = []
        
        for pair in pairs:
            # 获取当前市值
            current_mc = await self.db.execute(
                select(BuyBackMCHistory)
                .where(BuyBackMCHistory.base == pair.base)
                .order_by(BuyBackMCHistory.mc_update_time.desc())
                .limit(1)
            )
            current_mc = current_mc.scalar()
            
            if not current_mc:
                logger.warning(f"No current marketcap data found for {pair.base_symbol}")
                continue
            
            # 确定起始市值的时间点
            start_time = last_month_fifth
            
            # 如果token创建时间在上个月五号之后，本月五号之前，则使用创建时间作为起始点
            if pair.base_created_at and last_month_fifth < pair.base_created_at <= this_month_fifth:
                start_time = pair.base_created_at
                logger.info(f"Token {pair.base_symbol} was created between {last_month_fifth} and {this_month_fifth}, "
                           f"using creation time {pair.base_created_at} as start point")
                
            # 获取起始时间的市值
            start_mc = await self.db.execute(
                select(BuyBackMCHistory)
                .where(
                    BuyBackMCHistory.base == pair.base,
                    BuyBackMCHistory.mc_update_time <= start_time
                )
                .order_by(BuyBackMCHistory.mc_update_time.desc())
                .limit(1)
            )
            start_mc = start_mc.scalar()
            
            if not start_mc:
                logger.warning(f"No marketcap data found for {pair.base_symbol} before {start_time}")
                continue
                
            # 计算市值增长
            growth = current_mc.marketcap_usd - start_mc.marketcap_usd
            growth_percentage = (growth / start_mc.marketcap_usd) * 100 if start_mc.marketcap_usd > 0 else 0
            
            # 添加到结果列表
            marketcap_growth.append({
                "base": pair.base,
                "base_symbol": pair.base_symbol,
                "current_marketcap": current_mc.marketcap_usd,
                "start_marketcap": start_mc.marketcap_usd,
                "start_time": start_mc.mc_update_time,
                "growth": growth,
                "growth_percentage": growth_percentage
            })
            
            logger.info(f"Token: {pair.base_symbol}, Current MC: ${current_mc.marketcap_usd:.2f}, "
                       f"Start MC: ${start_mc.marketcap_usd:.2f} (at {start_mc.mc_update_time}), "
                       f"Growth: ${growth:.2f} ({growth_percentage:.2f}%)")
        
        # 按市值增长从大到小排序
        marketcap_growth.sort(key=lambda x: x["growth"], reverse=True)
        
        # 输出前10名
        logger.info("Top 10 tokens by marketcap growth:")
        for i, token in enumerate(marketcap_growth[:10], 1):
            logger.info(f"{i}. {token['base_symbol']} ({token['base']}): "
                       f"Growth: ${token['growth']:.2f} ({token['growth_percentage']:.2f}%), "
                       f"Current MC: ${token['current_marketcap']:.2f}, "
                       f"Start MC: ${token['start_marketcap']:.2f} (at {token['start_time']})")
        
        return marketcap_growth[:10]

    async def compute_cur_marketcap(self):
        pairs = await self.get_all_pairs()
        current_bnb_price = Decimal(await get_bnb_price(self.cache))
        logger.info(f"current_bnb_price: {current_bnb_price} / USD")
        bnb_amount = Decimal("0.1")
        
        # 计算上个月五号UTC 12:00am的时间
        today = datetime.utcnow()
        if today.month == 1:
            last_month = 12
            last_month_year = today.year - 1
        else:
            last_month = today.month - 1
            last_month_year = today.year
            
        last_month_fifth = datetime(last_month_year, last_month, 5, 0, 0, 0)
        logger.info(f"Last month's fifth day (UTC 12:00am): {last_month_fifth}")
        
        for pair in pairs:
            # 检查是否已有历史数据
            existing_history = await self.db.execute(
                select(BuyBackMCHistory).where(BuyBackMCHistory.base == pair.base)
            )
            existing_history = existing_history.scalar()
            
            # 计算当前市值
            out_token_amount = await self.get_out_token_amount(pair.base, bnb_amount)
            logger.info(f"pair: {pair.base}, {pair.base_symbol} out_token_amount: {out_token_amount}")
            
            if out_token_amount > 0:
                price_in_quote = Decimal(bnb_amount) / Decimal(out_token_amount)
                current_usd_price = price_in_quote * current_bnb_price
                current_marketcap = price_in_quote * current_bnb_price * Decimal(settings.DEFAULT_TOTAL_SUPPLY)
                
                logger.info(f"pair: {pair.base}, {pair.base_symbol} current price: {current_usd_price} / USD, current marketcap: {current_marketcap} USD")
                
                # 保存当前市值到 BuyBackMCHistory 表
                current_mc_history = BuyBackMCHistory(
                    pair_id=pair.id,
                    base=pair.base,
                    base_symbol=pair.base_symbol,
                    base_created_at=pair.base_created_at,
                    mc_update_time=datetime.utcnow(),
                    marketcap_usd=float(current_marketcap),
                    bnb_quote_price=float(price_in_quote),
                    usd_quote_price=float(current_usd_price)
                )
                
                self.db.add(current_mc_history)
                
                # 检查token创建时间是否晚于上个月五号UTC 12:00am
                if pair.base_created_at and pair.base_created_at > last_month_fifth:
                    logger.info(f"Token {pair.base_symbol} was created after {last_month_fifth}, adding historical data")
                    
                    # 获取创建时间同一天的 BNB 价格
                    creation_date = pair.base_created_at.date()
                    creation_bnb_price = await self.get_bnb_price_by_date(creation_date)
                    logger.info(f"creation_bnb_price for {pair.base_symbol} at {creation_date}: {creation_bnb_price} / USD")
                    
                    # 使用创建时间的 BNB 价格计算 USD 价格
                    creation_usd_price = price_in_quote * creation_bnb_price
                    creation_marketcap = price_in_quote * creation_bnb_price * Decimal(settings.DEFAULT_TOTAL_SUPPLY)
                    
                    # 使用创建时间作为市值更新时间
                    creation_mc_history = BuyBackMCHistory(
                        pair_id=pair.id,
                        base=pair.base,
                        base_symbol=pair.base_symbol,
                        base_created_at=pair.base_created_at,
                        mc_update_time=pair.base_created_at,
                        marketcap_usd=float(creation_marketcap),
                        bnb_quote_price=float(price_in_quote),
                        usd_quote_price=float(creation_usd_price)
                    )
                    
                    self.db.add(creation_mc_history)
                    logger.info(f"Added historical marketcap data for {pair.base_symbol} at creation time {pair.base_created_at}, price: {creation_usd_price} / USD, marketcap: {creation_marketcap} USD")
        
        # 提交事务
        await self.db.commit()
        logger.info("Marketcap calculation completed and saved to database")


with open('buyback.json', 'r') as f:
    buyback_marketcap_config_data = json.load(f)

service_config = ServiceConfig(
    api_base_url=buyback_marketcap_config_data['service_config']['api_base_url'],
    api_token=buyback_marketcap_config_data['service_config']['api_token'],
)

async def main():
    async with get_session_context() as db:
        service = BuybackMarketcapService(db, redis_client, service_config)
        await service.compute_cur_marketcap()
        rank = await service.compute_marketcap_rank()
        logger.info(f"marketcap_rank: {rank}")
if __name__ == "__main__":
    asyncio.run(main())
