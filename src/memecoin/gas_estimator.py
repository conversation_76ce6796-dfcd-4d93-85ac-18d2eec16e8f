from decimal import Decimal
from src.common.blockchain import BlockchainConnector
from typing import Dict, Any, Tuple
from src.common.blockchain.pancake_contracts import PancakeContracts
from src.memecoin.repos import MemeRepo
from src.memecoin.settings import settings
from eth_account import Account
import secrets
from src.memecoin.settings import settings
from src.memecoin.utils import decimal_to_string

GAS_MULTIPLIER = 1.3

class GasEstimator:
    def __init__(self, blockchain_connector: BlockchainConnector):
        self.blockchain_connector = blockchain_connector
        self.w3 = blockchain_connector.w3
        self.cache_pancake_approve_gas_used = int(46354 * GAS_MULTIPLIER)
        self.cache_pancake_buy_gas_used = int(500000 * GAS_MULTIPLIER)
        self.cache_pancake_sell_gas_used = int(500000 * GAS_MULTIPLIER)
        self.cache_bonding_curve_buy_gas_used = int(500000 * GAS_MULTIPLIER)
        self.cache_bonding_curve_sell_gas_used = int(500000 * GAS_MULTIPLIER)
        self.cache_transfer_token_gas_used = int(200000 * GAS_MULTIPLIER)
        self.cache_create_meme_gas_used = int(6000000 * GAS_MULTIPLIER)
        self.cache_transfer_bnb_gas_used = int(21000 * GAS_MULTIPLIER)

        self.cache_bonding_curve_approve_USDT_gas_used = int(5000 * GAS_MULTIPLIER)
        self.cache_bonding_curve_buy_with_USDT_gas_used = int(600000 * GAS_MULTIPLIER)
        self.cache_bonding_curve_sell_for_USDT_gas_used = int(600000 * GAS_MULTIPLIER)



        self.token_supply = Decimal('1_000_000_000') * Decimal('10') ** Decimal('18')
        self.bonding_curve_supply = self.token_supply * Decimal('20') / Decimal('100')
        self.virtual_x = Decimal('1_108_000_000') * Decimal('10') ** Decimal('18')
        self.virtual_y = Decimal('14_000') * Decimal('10') ** Decimal('6')
        self.k = self.virtual_x * self.virtual_y
        self.trading_fee_rate = Decimal('100')


    def get_max_bnb_in_amount_in_bonding_curve(self) -> Decimal:
        """
        计算在 bonding curve 中可以输入的最大 BNB 数量
        """

        new_divisor = self.virtual_x - self.bonding_curve_supply
        amount_in = (self.k + new_divisor - Decimal('1')) // new_divisor - self.virtual_y

        return amount_in
    
    def get_out_token_amount(
        self,
        bnb_amount: Decimal
    ) -> tuple[Decimal, Decimal, Decimal]:
        """
        计算给定 BNB 数量可以获得的代币数量, 仅在 launch 合约创建前使用
        
        Args:
            bnb_amount: 输入的 BNB 数量
        
        Returns:
            tuple[Decimal, Decimal]: 
                - 得到的代币数量
                - 扣除手续费后实际BNB 输入数量
        """
        # 计算手续费
        fee = bnb_amount * self.trading_fee_rate / Decimal('10000')
        actual_bnb_amount = bnb_amount - fee
        
        # 计算除数
        divisor = self.virtual_y + actual_bnb_amount
        
        # 计算输出代币数量
        amount_out = self.virtual_x - (self.k + divisor - Decimal('1')) // divisor
        if amount_out > self.bonding_curve_supply:
            amount_out = self.bonding_curve_supply
        # 计算新的除数
        new_divisor = self.virtual_x - amount_out
        
        # 计算实际需要的 BNB 输入数量
        amount_in = (self.k + new_divisor - Decimal('1')) // new_divisor - self.virtual_y
        max_bnb_amount = self.get_max_bnb_in_amount_in_bonding_curve()
        
        return amount_out, amount_in, max_bnb_amount

    def generate_default_account(self) -> Tuple[str, str, str]:
        """
        生成一个默认的以太坊账户，并返回其私钥、地址和公钥
        
        :return: (private_key, address, public_key) 元组
        """
        # 生成随机私钥
        private_key = '0x' + secrets.token_hex(32)
        
        # 从私钥创建账户
        account = Account.from_key(private_key)
        
        # 获取地址
        address = account.address
        
        # 获取公钥（去掉0x前缀）
        public_key = account.key.hex()[2:]
        
        return private_key, address, public_key
    
    def generate_rich_account(self) -> str:
        """
        生成一个富有的账户
        """
        return self.w3.to_checksum_address('******************************************')

    async def estimate_buy_gas(self, token_address: str, dex: str) -> Dict[str, int]:
        """
        预估 buy 交易的 Gas 费用
        """
        gas_used = 0
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)
        if dex == "pancake":
            # approve + buy 
            gas_used = self.cache_pancake_approve_gas_used + self.cache_pancake_buy_gas_used
        else:
            # buy in bonding curve
            gas_used = self.cache_bonding_curve_buy_with_USDT_gas_used

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }

    async def estimate_sell_gas(self, token_address: str, dex: str) -> Dict[str, int]:
        """
        预估 sell 交易的 Gas 费用
        """
        gas_used = 0
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)
        if dex == "pancake":
            # sell + approve
            gas_used = self.cache_pancake_approve_gas_used + self.cache_pancake_sell_gas_used
        else:
            # sell in bonding curve
            gas_used = self.cache_bonding_curve_sell_for_USDT_gas_used

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }

    async def _estimate_pancake_approve_gas(self, function_params: list = None) -> Dict[str, int]:
        """
        预估 pancake_approve 交易的 Gas 费用, 测试数据
        "gas_used": 46354,
        "gas_price": **********,
        "total_gas_cost": **************
        """
        _, _to_address, _ = self.generate_default_account()
        pancake_constants = PancakeContracts.MAINNET if self.blockchain_connector.chain_id == 56 else PancakeContracts.TESTNET
        PANCAKE_ROUTER_ADDRESS = self.w3.to_checksum_address(pancake_constants["PANCAKESWAP_ROUTER"]["address"])
        ERC20_CONTRACT_ADDRESS = settings.ERC20_CONTRACT_ADDRESS
        ERC20_CONTRACT_ABI = settings.ERC20_CONTRACT_ABI_PATH
        
        if function_params is None:
            APPROVE_AMOUNT = 100
            FROM_ADDRESS = "******************************************"
            function_params = [
                PANCAKE_ROUTER_ADDRESS, 
                APPROVE_AMOUNT
            ]
        gas_used = await self.blockchain_connector.generic_estimate_evm_onchain_gas(ERC20_CONTRACT_ABI,ERC20_CONTRACT_ADDRESS, "approve", {"from": FROM_ADDRESS}, function_params)
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }


    async def _estimate_pancake_buy_gas(self, function_params: list = None):
        """
        预估 pancake_swap 买币的 Gas 费用
        """
        pancake_constants = PancakeContracts.MAINNET if self.blockchain_connector.chain_id == 56 else PancakeContracts.TESTNET
        PANCAKE_ROUTER_ADDRESS = self.w3.to_checksum_address(pancake_constants["PANCAKESWAP_ROUTER"]["address"])
        PANCAKE_ROUTER_ABI = pancake_constants["PANCAKESWAP_ROUTER"]["abi"]

        if function_params is None:
            MIN_AMOUNT_OUT = 10000
            PATH = [
                "******************************************",
                "******************************************"
            ]
            _, TO_ADDRESS,_ = self.generate_default_account()
            DEADLINE = **********
            function_params = [
                MIN_AMOUNT_OUT,
                PATH,
                TO_ADDRESS,
                DEADLINE
            ]

        gas_estimation = await self.blockchain_connector.generic_estimate_evm_onchain_gas(PANCAKE_ROUTER_ABI, PANCAKE_ROUTER_ADDRESS, "swapExactETHForTokens", {},function_params)
        return gas_estimation
    
    async def _estimate_pancake_sell_gas(self, function_params: list = None):
        """
        预估 pancake_swap 卖币的 Gas 费用
        """
        _, _to_address, _ = self.generate_default_account()
        pancake_constants = PancakeContracts.MAINNET if self.blockchain_connector.chain_id == 56 else PancakeContracts.TESTNET
        PANCAKE_ROUTER_ADDRESS = self.w3.to_checksum_address(pancake_constants["PANCAKESWAP_ROUTER"]["address"])
        PANCAKE_ROUTER_ABI = pancake_constants["PANCAKESWAP_ROUTER"]["abi"]

        if function_params is None:
            AMOUNT_IN = **********000
            MIN_AMOUNT_OUT = 10000
            PATH = [
                "******************************************",
                "******************************************"
            ]
            _, TO_ADDRESS,_ = self.generate_default_account()
            DEADLINE = **********
            function_params = [
                AMOUNT_IN,
                MIN_AMOUNT_OUT,
                PATH,
                TO_ADDRESS,
                DEADLINE
            ]
        gas_estimation = await self.blockchain_connector.generic_estimate_evm_onchain_gas(PANCAKE_ROUTER_ABI, PANCAKE_ROUTER_ADDRESS, "swapExactTokensForETH", {}, function_params)
        return gas_estimation

    async def _estimate_buy_token_in_bonding_curve_gas(self, function_params: list = None):
        """
        预估 内盘购买的 Gas 费用
        "gas_used": 84773,
        "gas_price": **********,
        "total_gas_cost": **************

        """

        if function_params is None:

            FROM_ADDRESS = "******************************************"
            TOKEN_ADDRESS = "******************************************"
            AMOUNT_OUT_MIN = 10
            function_params = [
                TOKEN_ADDRESS,
                AMOUNT_OUT_MIN
            ]
        gas_used = await self.blockchain_connector.generic_estimate_evm_onchain_gas(settings.MEME_CONTRACT_ABI_PATH, settings.MEME_CONTRACT_ADDRESS, "buy", {"from": FROM_ADDRESS}, function_params)
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }
    
    async def _estimate_sell_token_in_bonding_curve_gas(self, function_params: list = None):
        """
        预估 内盘出售的 Gas 费用
        "gas_used": 86318,
        "gas_price": **********,
        "total_gas_cost": 86318000000000
        """
        if function_params is None:
            FROM_ADDRESS = "******************************************"
            TOKEN_ADDRESS = "******************************************"
            AMOUNT_TO_SELL = 10
            AMOUNT_OUT_MIN = 10
            function_params = [
                TOKEN_ADDRESS,
                AMOUNT_TO_SELL,
                AMOUNT_OUT_MIN
            ]
        gas_used = await self.blockchain_connector.generic_estimate_evm_onchain_gas(settings.MEME_CONTRACT_ABI_PATH, settings.MEME_CONTRACT_ADDRESS, "sell", {"from": FROM_ADDRESS}, function_params)
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }
        
    async def estimate_transfer_token_gas(self, function_params: list = None) -> Dict[str, int]:
        gas_used = self.cache_transfer_token_gas_used
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }

    async def _estimate_transfer_token_gas(self, function_params: list = None) -> Dict[str, int]:
        """
        预估 send_token 转账交易的 Gas 费用
        """
        _, _to_address, _ = self.generate_default_account()
        ERC20_CONTRACT_ADDRESS = settings.ERC20_CONTRACT_ADDRESS
        ERC20_CONTRACT_ABI = settings.ERC20_CONTRACT_ABI_PATH
        to_address = _to_address
        amount = 100
        
        if function_params is None:
            function_params = [to_address, amount]

        # 获取 GasUsed 和 GasPrice
        gas_used = await self.blockchain_connector.generic_estimate_evm_onchain_gas(ERC20_CONTRACT_ABI, ERC20_CONTRACT_ADDRESS, "transfer", {}, function_params)
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }
        

    async def estimate_transfer_bnb_gas(self, to_address: str = None, amount: str = None) -> Dict[str, int]:

        gas_used = self.cache_transfer_bnb_gas_used
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }
    
    async def _estimate_transfer_bnb_gas(self, to_address: str = None, amount: str = None) -> Dict[str, int]:
        """
        预估普通 transfer 转账交易的 Gas 费用
        
        :param to_address: 接收地址，可选，如果不提供则使用默认地址
        :param amount: 转账金额，可选，如果不提供则使用默认金额
        :return: 包含 GasUsed 和 GasPrice 的字典
        """
        # 构建参数列表
        function_params = []
        if to_address:
            function_params.append(to_address)
        if amount:
            function_params.append(amount)
            
        # 获取 GasUsed
        gas_used = await self.blockchain_connector.generic_estimate_evm_onchain_gas("","", "native_token_transfer", {}, function_params)
        
        # 获取当前 GasPrice
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)

        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }
    
    async def estimate_launch_gas(self, function_params: list = None) -> Dict[str, int]:

        gas_used = self.cache_create_meme_gas_used
        gas_price = int(await self.blockchain_connector.w3.eth.gas_price)
        return {
            "gas_used": gas_used,
            "gas_price": gas_price,
            "total_gas_cost": gas_used * gas_price
        }