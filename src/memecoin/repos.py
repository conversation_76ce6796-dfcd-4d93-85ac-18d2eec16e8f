from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import List, Dict, Any, Optional, Union
from sqlalchemy import select, func, update, text
from sqlalchemy.ext.asyncio import AsyncSession
from async_lru import alru_cache

from src.common.constants import UserTransactionType, UserTransactionStatus
from src.database.models import Collection
from src.database.models.Kline import (
    Kline1m,
    Kline5m,
    Kline30s,
    Kline30m,
    Kline1h,
    Kline4h,
    <PERSON>line12h,
    <PERSON><PERSON>1d,
    KlineMixin,
)
from src.database.models.Pair import Pair
from src.database.models.SavedPosts import SavedPost
from src.database.models.TokenHolder import TokenHolder
from src.database.models.UserTransaction import UserTransaction
from src.database.models.UserWallet import UserWallet
from src.database.models.Author import Author
from src.database.models.Post import Post
from src.common.constants import PairStatus
from src.memecoin.constants import WalletType
from src.memecoin.logger import logger
from src.memecoin.schemas import Token<PERSON><PERSON><PERSON><PERSON>, Interval, RecentSendAddressSchema
from src.memecoin.settings import settings
from src.common.redis_cli import RedisCli


class MemeRepo:
    """CRUD for Memecoins"""

    def __init__(
        self,
        session: AsyncSession,
        kline_session: AsyncSession,
    ):
        self._session = session
        if settings.BLOCKCHAIN_TYPE == "SOL":
            self.wallet_type = WalletType.SOL
        else:
            # BSC and ETH
            self.wallet_type = WalletType.BSC
        self._kline_session = kline_session

    async def fetch_token_list(
        self, list_type: TokenListType, offset: int, limit: int, chain_id: int = None
    ):
        """
        Retrieves the list of tokens based on the specified list type, sorting field, sorting order, and pagination parameters.
        :param list_type: Type of token list (e.g., "top", "new", "completing", "completed").
        :param sort_by: The field by which to sort.
        :param sort_order: Boolean indicating sorting order (True for ascending, False for descending).
        :param offset: Pagination offset.
        :param limit: Pagination limit.
        :param chain_id: Chain ID to filter pairs by.
        :return: A list of Token objects.
        """
        stmt = select(Pair).where(Pair.status.in_([PairStatus.READY, PairStatus.NOT_READY]))

        # Add chain filter if chain_id is provided
        if chain_id is not None:
            stmt = stmt.where(Pair.chain == chain_id)

        if list_type == TokenListType.top:
            default_order = Pair.mc.desc()
        elif list_type == TokenListType.new:
            default_order = Pair.base_created_at.desc()
        elif list_type == TokenListType.completing:
            stmt = stmt.where(Pair.bonding_curve < 100)
            default_order = Pair.bonding_curve.desc()
        elif list_type == TokenListType.completed:
            stmt = stmt.where(Pair.bonding_curve == 100)
            default_order = Pair.base_created_at.desc()
        # TODO: 添加新排行榜类型的数据库查询逻辑
        elif list_type == TokenListType.daily:
            # TODO: 实现日排行榜的数据库查询逻辑
            # TODO: 按24小时价格变化排序
            default_order = Pair.base_created_at.desc()  # 临时使用创建时间排序
        elif list_type == TokenListType.weekly:
            # TODO: 实现周排行榜的数据库查询逻辑
            # TODO: 按7天价格变化排序
            default_order = Pair.base_created_at.desc()  # 临时使用创建时间排序
        elif list_type == TokenListType.repurchase:
            # TODO: 实现回购排行榜的数据库查询逻辑
            # TODO: 按回购相关指标排序
            default_order = Pair.base_created_at.desc()  # 临时使用创建时间排序
        else:
            logger.error(f"Unsupported token list type: {list_type}")
            return []

        stmt = stmt.order_by(default_order)

        stmt = stmt.offset(offset).limit(limit)

        result = await self._session.execute(stmt)
        pairs = result.scalars().all()

        return pairs

    async def fetch_token_kline(
        self,
        token_address: str,
        interval: Interval,
        start_time: datetime,
        end_time: datetime,
        offset: int,
        limit: int,
    ):
        """
        Retrieve gap-filled price records for a token within a specified time range.
        If there is no data at the start_time, a baseline value is obtained from historical records.
        Fills gaps in Python code rather than using complex SQL.

        :param token_address: The token's contract address.
        :param interval: Time interval, as supported by the Interval enum.
        :param start_time: Query start time in 'YYYY-MM-DD HH:MI:SS' format.
        :param end_time: Query end time in 'YYYY-MM-DD HH:MI:SS' format.
        :param offset: Pagination offset.
        :param limit: Pagination limit.
        :return: A list of gap-filled records.
        """
        table_mapping = {
            Interval.H1: Kline30s,  # 1小时（30秒bar）
            Interval.H4: Kline1m,   # 4小时预览：来源仍为1m明细
            Interval.D1: Kline5m,   # 1天：5m
            Interval.W1: Kline1h,   # 1周：1h
            Interval.M1: Kline4h,   # 1月：4h 聚合表
            Interval.Max: Kline12h, # Max：12h 聚合表
        }
        bucket_interval_mapping = {
            Interval.H1: timedelta(seconds=30),   # 1小时：30秒间隔
            Interval.H4: timedelta(minutes=1),   # 4小时：1分钟间隔
            Interval.D1: timedelta(minutes=5),   # 1天：5分钟间隔
            Interval.W1: timedelta(hours=1),     # 1周：1小时间隔
            Interval.M1: timedelta(hours=4),     # 1月：4小时间隔
            Interval.Max: timedelta(hours=12),   # Max：12小时间隔
        }

        table = table_mapping.get(interval)
        if table is None:
            raise ValueError(f"Unsupported interval: {interval}")
        bucket_interval = bucket_interval_mapping.get(interval)

        start_time = self._align_time_to_interval(start_time, interval).replace(
            tzinfo=timezone.utc
        )
        end_time = self._align_time_to_interval(end_time, interval).replace(
            tzinfo=timezone.utc
        )

        logger.info(
            f"Aligned time to interval {interval}: start_time={start_time}, end_time={end_time}"
        )

        try:
            first_kline_stmt = (
                select(table)
                .where(table.token_address == token_address)
                .order_by(table.timestamp)
                .limit(1)
            )
            first_kline_result = await self._kline_session.execute(first_kline_stmt)
            first_kline = first_kline_result.scalar_one_or_none()

            if not first_kline:
                logger.info(f"No kline data found for token: {token_address}")
                return []

            actual_start_time = max(start_time, first_kline.timestamp)
            logger.info(
                f"Adjusted start time to: {actual_start_time} (earliest record: {first_kline.timestamp})"
            )

            logger.info(
                f"Fetching kline data for token: {token_address}, interval: {interval}, "
                f"adjusted start_time: {actual_start_time}, end_time: {end_time}"
            )

            stmt = (
                select(table)
                .where(table.token_address == token_address)
                .where(table.timestamp.between(actual_start_time, end_time))
                .order_by(table.timestamp)
            )
            result = await self._kline_session.execute(stmt)
            existing_klines = result.scalars().all()

            logger.info(
                f"Found {len(existing_klines)} kline records in adjusted time range"
            )

            if not existing_klines:
                recent_stmt = (
                    select(table)
                    .where(table.token_address == token_address)
                    .where(table.timestamp <= actual_start_time)
                    .order_by(table.timestamp.desc())
                    .limit(1)
                )
                recent_result = await self._kline_session.execute(recent_stmt)
                recent_kline = recent_result.scalar_one_or_none()

                if recent_kline:
                    previous_kline = recent_kline
                    logger.info(
                        f"Using recent kline from {previous_kline.timestamp} as baseline"
                    )

                    filled_klines = []
                    current_time = actual_start_time

                    while current_time <= end_time:
                        filled_klines.append(
                            (
                                current_time,
                                previous_kline.close,
                                previous_kline.close,
                                previous_kline.close,
                                previous_kline.close,
                                0,
                            )
                        )
                        current_time += bucket_interval

                    logger.info(f"Returning {len(filled_klines)} filled kline records")
                    return filled_klines
                else:
                    return []

            previous_kline = None
            filled_klines = []
            current_time = actual_start_time

            existing_dict = {kline.timestamp: kline for kline in existing_klines}

            while current_time <= end_time:
                if current_time in existing_dict:
                    kline_data = existing_dict[current_time]
                    filled_klines.append(
                        (
                            current_time,
                            kline_data.open,
                            kline_data.high,
                            kline_data.low,
                            kline_data.close,
                            kline_data.volume / 10**18,
                        )
                    )
                    previous_kline = kline_data
                else:
                    if previous_kline:
                        filled_klines.append(
                            (
                                current_time,
                                previous_kline.close,
                                previous_kline.close,
                                previous_kline.close,
                                previous_kline.close,
                                0,
                            )
                        )
                    elif len(existing_klines) > 0:
                        first_available = existing_klines[0]
                        filled_klines.append(
                            (
                                current_time,
                                first_available.open,
                                first_available.high,
                                first_available.low,
                                first_available.close,
                                0,
                            )
                        )
                        previous_kline = first_available

                current_time += bucket_interval

            logger.info(
                f"Returning {len(filled_klines)} kline records after filling (from {actual_start_time} to {end_time})"
            )
            return filled_klines

        except Exception as e:
            logger.error(f"Error fetching kline data: {str(e)}", exc_info=True)
            return []

    async def fetch_batch_historical_market_caps(
        self,
        token_addresses: List[str],
        timestamp: datetime,
        interval: Interval = Interval.W1
    ) -> Dict[str, float]:
        """
        批量获取多个token在指定时间点的市值
        使用单个SQL查询优化性能
        
        :param token_addresses: token地址列表
        :param timestamp: 目标时间点
        :param interval: 时间间隔
        :return: {token_address: market_cap} 的字典
        """
        if not token_addresses:
            return {}
        
        table_mapping = {
            Interval.H4: Kline1m,  # 4小时：使用1分钟bar
            Interval.D1: Kline5m,  # 1天：使用5分钟bar  
            Interval.W1: Kline1h,  # 1周：使用1小时bar
            Interval.M1: Kline1h,  # 1月：使用1小时bar（临时，因为没有4小时bar）
            Interval.Max: Kline1h, # Max：使用1小时bar（临时，因为没有12小时bar）
        }
        
        table = table_mapping.get(interval)
        if table is None:
            raise ValueError(f"Unsupported interval: {interval}")
        
        # 对齐时间到指定间隔
        aligned_timestamp = self._align_time_to_interval(timestamp, interval).replace(
            tzinfo=timezone.utc
        )
        
        try:
            # 使用窗口函数获取每个token在指定时间点附近最近的kline数据
            # 查询时间窗口：目标时间前后2小时（扩大窗口以提高命中率）
            start_window = aligned_timestamp - timedelta(hours=2)
            end_window = aligned_timestamp + timedelta(hours=2)
            
            logger.debug(f"Querying {table.__tablename__} for {len(token_addresses)} tokens at {aligned_timestamp}")
            
            # 构建优化的SQL查询：使用索引和窗口函数提高性能
            query = text(f"""
                WITH ranked_klines AS (
                    SELECT 
                        token_address,
                        timestamp,
                        close,
                        ROW_NUMBER() OVER (
                            PARTITION BY token_address 
                            ORDER BY ABS(EXTRACT(EPOCH FROM (timestamp - :target_timestamp)))
                        ) as rn
                    FROM {table.__tablename__}
                    WHERE token_address = ANY(:token_addresses)
                        AND timestamp BETWEEN :start_window AND :end_window
                        AND is_filled = true
                )
                SELECT 
                    token_address,
                    close
                FROM ranked_klines 
                WHERE rn = 1
                ORDER BY token_address
            """)
            
            result = await self._kline_session.execute(
                query,
                {
                    "token_addresses": token_addresses,
                    "target_timestamp": aligned_timestamp,
                    "start_window": start_window,
                    "end_window": end_window
                }
            )
            
            # 处理查询结果
            market_caps = {}
            total_supply = 1_000_000_000  # 固定总供应量
            
            for row in result.fetchall():
                token_address = row[0]
                close_price = float(row[1])
                market_cap = close_price * total_supply
                market_caps[token_address] = market_cap
            
            # 对于没有找到数据的token，设置市值为0
            for addr in token_addresses:
                if addr not in market_caps:
                    market_caps[addr] = 0.0
            
            logger.info(f"Batch fetched historical market caps for {len(token_addresses)} tokens, found data for {len([v for v in market_caps.values() if v > 0])} tokens")
            
            return market_caps
            
        except Exception as e:
            logger.error(f"Error in batch fetching historical market caps: {str(e)}", exc_info=True)
            # 降级处理：使用fallback方法
            try:
                return await self._get_fallback_market_caps(token_addresses)
            except Exception as fallback_e:
                logger.error(f"Fallback method also failed: {fallback_e}")
                return {addr: 0.0 for addr in token_addresses}

    async def _get_fallback_market_caps(self, token_addresses: List[str]) -> Dict[str, float]:
        """
        降级方法：当kline查询失败时，使用pair表中的当前市值数据
        优先使用mc字段，其次使用price_usd计算
        """
        try:
            logger.warning(f"Using fallback market caps for {len(token_addresses)} tokens (kline query failed)")
            
            # 从pair表获取当前市值信息，优化查询性能
            pairs_query = text("""
                SELECT base, 
                       COALESCE(mc, price_usd * 1000000000) as market_cap
                FROM pair 
                WHERE base = ANY(:token_addresses)
                AND (mc > 0 OR (price_usd IS NOT NULL AND price_usd > 0))
            """)
            
            result = await self._session.execute(
                pairs_query, {"token_addresses": token_addresses}
            )
            
            market_caps = {}
            for row in result.fetchall():
                token_address = row[0]
                market_cap = float(row[1]) if row[1] else 0.0
                market_caps[token_address] = market_cap
            
            # 对于没有找到的token，设置为0
            for addr in token_addresses:
                if addr not in market_caps:
                    market_caps[addr] = 0.0
            
            found_count = len([mc for mc in market_caps.values() if mc > 0])
            logger.info(f"Fallback retrieved market caps for {found_count}/{len(token_addresses)} tokens")
            return market_caps
            
        except Exception as e:
            logger.error(f"Error in fallback market caps: {e}")
            # 最终降级：所有token设置为0
            return {addr: 0.0 for addr in token_addresses}

    async def fetch_token_holders(self, token_address: str, offset: int, limit: int):
        """
        Retrieve the holders information for the given token address.
        Only match addresses that exist in UserWallet, and fetch the name and avatar from the Author table.

        :param token_address: The contract address of the token.
        :param offset: Pagination offset.
        :param limit: Pagination limit.
        :return: A list of TokenHolder objects.
        """

        stmt_token = (
            select(TokenHolder)
            .where(TokenHolder.token_address == token_address)
            .order_by(TokenHolder.amount.desc())
            .offset(offset)
            .limit(limit)
        )
        result_token = await self._session.execute(stmt_token)
        token_holders = result_token.scalars().all()

        addresses = [holder.address for holder in token_holders]
        if not addresses:
            return token_holders

        stmt_wallet = (
            select(UserWallet.pubkey, UserWallet.user_id)
            .where(UserWallet.pubkey.in_(addresses))
            .where(UserWallet.type == self.wallet_type)
        )
        result_wallet = await self._session.execute(stmt_wallet)
        wallet_mapping = {pubkey: user_id for pubkey, user_id in result_wallet.all()}

        user_ids = list(wallet_mapping.values())
        stmt_author = select(Author.user_id, Author.name, Author.username, Author.avatar).where(
            Author.user_id.in_(user_ids)
        )
        result_author = await self._session.execute(stmt_author)
        author_mapping_by_user = {
            user_id: (name, username, avatar if avatar else "", user_id)
            for user_id, name, username, avatar in result_author.all()
        }

        author_mapping = {}
        for pubkey, user_id in wallet_mapping.items():
            if user_id in author_mapping_by_user:
                author_mapping[pubkey] = author_mapping_by_user[user_id]

        existing_holders = []
        for holder in token_holders:
            if holder.address in author_mapping:
                name, username, avatar, user_id = author_mapping[holder.address]
                # 更新 holder 的 name 和 avatar，并传递额外的 username 信息
                holder.name, holder.avatar = name, avatar
                existing_holders.append((user_id, holder, username))

        return existing_holders

    async def fetch_pair(self, token_address: str):
        """
        Retrieves a pair for the given token address,
        :param token_address: The token's contract address.
        :return: A Pair object.
        """
        pair_stmt = select(Pair).where(Pair.base == token_address)
        pair_result = await self._session.execute(pair_stmt)
        return pair_result.scalar()

    async def fetch_pair_by_txid(self, txid: str):
        """
        Retrieves a pair for the given token address,
        :param txid: The creation transaction id of token.
        :return: A Pair object.
        """
        pair_stmt = select(Pair).where(Pair.creation_txid == txid)
        pair_result = await self._session.execute(pair_stmt)
        return pair_result.scalar()

    async def fetch_pairs_by_collection_ids(
        self, collection_ids: List[str], chain_id: int = None
    ):
        """
        Retrieves pairs for the given list of collection IDs.

        :param collection_ids: A list of collection IDs.
        :param chain_id: Chain ID to filter pairs by.
        :return: A list of Pair objects.
        """
        if not collection_ids:
            return []
        pair_stmt = select(Pair).where(Pair.collection_id.in_(collection_ids))

        # Add chain filter if chain_id is provided
        if chain_id is not None:
            pair_stmt = pair_stmt.where(Pair.chain == chain_id)

        pair_result = await self._session.execute(pair_stmt)
        return pair_result.scalars().all()

    async def fetch_pair_by_collection_id(self, collection_id: str):
        """
        Retrieves pair for the given collection ID.

        :param collection_id: target collection IDs.
        :return: A Pair object.
        """
        if not collection_id:
            return None
        pair_stmt = select(Pair).where(Pair.collection_id == collection_id)
        pair_result = await self._session.execute(pair_stmt)
        return pair_result.scalar_one_or_none()

    async def fetch_pairs_by_creator_id(self, creator_id: str):
        """
        Retrieves a pair for the given token address,
        :param token_address: The token's contract address.
        :return: A Pair object.
        """
        pair_stmt = select(Pair).where(Pair.creator_id == creator_id)
        pair_result = await self._session.execute(pair_stmt)
        return pair_result.scalars()

    async def get_recent_tokens_since_timestamp(self, minutes_ago: int = 10, limit: int = 15):
        """
        Get tokens created within the last N minutes from the database
        :param minutes_ago: Number of minutes to look back
        :param limit: Maximum number of tokens to return
        :return: List of recently created tokens
        """
        now_utc = datetime.now(timezone.utc)
        since_time = (now_utc - timedelta(minutes=minutes_ago)).replace(tzinfo=None)

        stmt = (
            select(Pair)
            .where(Pair.status == PairStatus.READY)
            .where(Pair.base_created_at >= since_time)
            .order_by(
                Pair.id.desc()  # 按ID降序，ID越大越新
            )
            .limit(limit)
        )
        
        result = await self._session.execute(stmt)
        tokens = result.scalars().all()

        # Convert to expected format
        result_list = []
        for token in tokens:
            result_list.append({
                "token_address": token.base,
                "symbol": token.base_symbol,
                "avatar": token.base_image_url,
                "creator_id": token.creator_id,
                "collection_id": token.collection_id
            })
        
        return result_list

    async def fetch_user_tokens(self, address: str, chain_id: int = None):
        stmt = select(TokenHolder).where(TokenHolder.address == address)

        # Add chain filter if chain_id is provided
        if chain_id is not None:
            stmt = stmt.where(TokenHolder.chain == chain_id)

        result = await self._session.execute(stmt)
        tokens = result.scalars().all()
        return tokens

    async def fetch_user_wallet(self, user_id: str):
        stmt = select(UserWallet).where(
            UserWallet.user_id == user_id, UserWallet.type == self.wallet_type
        )
        result = await self._session.execute(stmt)
        return result.scalar()

    async def create_pair(
        self,
        name: str,
        ticker: str,
        avatar: str,
        about: str,
        status: PairStatus,
        txid: str,
        user_id: str,
        collection_id: str,
        chain_id: int,
        social_links: str = None,
    ):
        pair = Pair(
            chain=chain_id,
            base_name=name,
            base_symbol=ticker,
            base_image_url=avatar,
            base_description=about,
            base_decimals=18,  # the contract decimals is 18
            base_created_at=datetime.now(timezone.utc).replace(tzinfo=None),
            base_total_supply=settings.DEFAULT_TOTAL_SUPPLY,
            status=status,
            creation_txid=txid,
            creator_id=user_id,
            collection_id=collection_id,
            social_links=social_links,
        )
        self._session.add(pair)
        await self._session.commit()
        return pair

    async def update_meme(self, token_address: str, update_data: dict):
        """
        更新meme的信息

        :param token_address: 代币地址
        :param update_data: 要更新的字段，如base_description和social_links
        :return: None
        """
        stmt = update(Pair).where(Pair.base == token_address).values(**update_data)
        await self._session.execute(stmt)
        await self._session.commit()

    async def fetch_collection_id_by_post_id(self, post_id: str):
        stmt = select(SavedPost.collection_id).where(SavedPost.post_id == post_id)
        result = await self._session.execute(stmt)
        return result.scalars().all()

    async def insert_transaction_history(
        self,
        user_id,
        type,
        status,
        from_address,
        chain=settings.BLOCKCHAIN_TYPE,
        token_address=None,
        to_address=None,
        gas_limit=None,
        tx_hash=None,
        order_amount=0,
        bnb_amount=0,
        token_amount=0,
        service_fee=0,
        payment_source=None,
        value=None,  # USD amount
        order_id=None,  # Order ID for tracking
        unit_price=None,  # Token unit price in USD
    ):
        user_tx = UserTransaction(
            user_id=user_id,
            type=type,
            chain=chain,
            status=status,
            token_address=token_address,
            from_address=from_address,
            to_address=to_address,
            gas_limit=gas_limit,
            tx_hash=tx_hash,
            order_amount=order_amount,
            base_amount=token_amount,
            quote_amount=bnb_amount,
            service_fee=service_fee,
            payment_source=payment_source,
            value=value,  # USD amount
            order_id=order_id,  # Order ID
            unit_price=unit_price,  # Token unit price
        )
        self._session.add(user_tx)
        await self._session.commit()

    async def fetch_transaction_history(self, user_id, tx_hash_list=None, chain=None):
        transaction_types = [
            UserTransactionType.CREATE_MEME,
            UserTransactionType.SEND_TOKEN,
            UserTransactionType.BUY_TOKEN,
            UserTransactionType.SELL_TOKEN,
            UserTransactionType.RECEIVE_TOKEN,
            UserTransactionType.BUY_TOKEN_DEX,
            UserTransactionType.SELL_TOKEN_DEX,
            UserTransactionType.GIFT_BUY_TOKEN,
            UserTransactionType.WITHDRAW_CASH,
            UserTransactionType.DEPOSIT_CASH,
            UserTransactionType.PAY_CONTENT,
        ]
        stmt = select(UserTransaction).where(UserTransaction.user_id == user_id).where(UserTransaction.type.in_(transaction_types))
        if chain:
            stmt = stmt.where(UserTransaction.chain == chain)
        if tx_hash_list:
            stmt = stmt.where(UserTransaction.tx_hash.in_(tx_hash_list))
        stmt = stmt.order_by(UserTransaction.created_at.desc()).limit(100)
        result = await self._session.execute(stmt)
        return result.scalars().all()

    async def fetch_user_created_memes(self, user_id: int, chain_id: int = None):
        stmt = select(Pair).where(
            Pair.creator_id == user_id, Pair.status.in_([PairStatus.READY, PairStatus.NOT_READY])
        )

        # Add chain filter if chain_id is provided
        if chain_id is not None:
            stmt = stmt.where(Pair.chain == chain_id)

        results = await self._session.execute(stmt)
        return results.scalars().all()

    async def get_wallet_balance(self, wallet_address: str) -> List[Dict[str, Any]]:
        if settings.BLOCKCHAIN_TYPE == "SOL":
            return await self.get_wallet_balance_sol(wallet_address)
        else:
            return await self.get_wallet_balance_bsc(wallet_address)

    async def get_wallet_balance_sol(self, wallet_address: str) -> List[Dict[str, Any]]:
        """
        Get all token balances for a wallet using Moralis API

        Args:
            wallet_address: The wallet address to check

        Returns:
            List of dictionaries containing token information:
            [
                {
                    "associatedTokenAddress": "4V2QhEt59AGxGN8VwbmGUvPcjPpxPC2BGZoYWWZQppRi",
                    "mint": "Doggoyb1uHFJGFdHhJf8FKEBUMv58qo98CisWgeD7Ftk",
                    "amountRaw": "25700670593",
                    "amount": "257006.70593",
                    "decimals": 5,
                    "name": "DOGGO",
                    "symbol": "DOGGO",
                    "logo": "https://d23exngyjlavgo.cloudfront.net/solana_Doggoyb1uHFJGFdHhJf8FKEBUMv58qo98CisWgeD7Ftk"
                }
            ]
        """
        try:
            # Lazy import to avoid typing issues with moralis library
            from moralis import evm_api

            params = {
                "address": wallet_address,
                "chain": settings.SOL_CHAIN,
            }

            result = evm_api.token.get_wallet_token_balances(
                api_key=settings.MORALIS_API_KEY, params=params
            )
            tokens = result.get("tokens")
            enhanced_results = []
            for token in tokens:
                token_address = token.get("mint")

                # Calculate formatted balance
                decimals = int(token.get("decimals", 6))
                raw_balance = Decimal(token.get("amountRaw", "0"))
                balance_formatted = Decimal(token.get("amount", "0"))

                enhanced_results.append(
                    {
                        "token_address": token_address,
                        "name": token.get("name"),
                        "symbol": token.get("symbol"),
                        "logo": token.get("logo"),
                        "thumbnail": "",
                        "decimals": decimals,
                        "balance": raw_balance,
                        "balance_formatted": balance_formatted,
                    }
                )

            return enhanced_results

        except Exception as e:
            logger.error(f"Error fetching wallet balance from Moralis: {str(e)}")
            raise Exception(f"Failed to fetch wallet balance: {str(e)}")

    async def get_wallet_balance_bsc(self, wallet_address: str) -> List[Dict[str, Any]]:
        """
        Get all token balances for a wallet using Moralis API

        Args:
            wallet_address: The wallet address to check

        Returns:
            List of dictionaries containing token information:
            [
                {
                    "token_address": str,
                    "name": str,
                    "symbol": str,
                    "logo": str | None,
                    "thumbnail": str | None,
                    "decimals": int,
                    "balance": str,
                    "balance_formatted": float,
                }
            ]
        """
        try:
            # Lazy import to avoid typing issues with moralis library
            from moralis import evm_api

            params = {
                "address": wallet_address,
                "chain": settings.BSC_CHAIN,
            }

            result = evm_api.token.get_wallet_token_balances(
                api_key=settings.MORALIS_API_KEY, params=params
            )

            enhanced_results = []
            for token in result:
                token_address = token.get("token_address")

                # Calculate formatted balance
                decimals = int(token.get("decimals", 18))
                raw_balance = token.get("balance", "0")
                balance_formatted = Decimal(raw_balance) / (Decimal(10) ** decimals)

                enhanced_results.append(
                    {
                        "token_address": token_address,
                        "name": token.get("name"),
                        "symbol": token.get("symbol"),
                        "logo": token.get("logo"),
                        "thumbnail": token.get("thumbnail"),
                        "decimals": decimals,
                        "balance": raw_balance,
                        "balance_formatted": balance_formatted,
                    }
                )

            return enhanced_results

        except Exception as e:
            logger.error(f"Error fetching wallet balance from Moralis: {str(e)}")
            raise Exception(f"Failed to fetch wallet balance: {str(e)}")

    async def insert_token_holder(
        self, chain, name, avatar, address, token_address, amount, decimals, creator_id, user_id=None, username=None
    ):
        """
        Insert a token holder record into the database

        Args:
            chain: Chain ID (e.g., 56 for BSC)
            name: Holder name
            avatar: Holder avatar URL
            address: Wallet address
            token_address: Token contract address
            amount: Token amount as string
            decimals: Token decimals
            creator_id: Creator ID
            user_id: User ID (optional)
            username: Username (optional)

        Returns:
            The created TokenHolder instance
        """

        # Create new TokenHolder instance
        token_holder = TokenHolder(
            chain=chain,
            name=name,
            avatar=avatar,
            address=address,
            token_address=token_address,
            amount=amount,
            decimals=decimals,
            creator_id=creator_id,
            user_id=user_id or "",
            username=username or "",
        )

        # Add to session and commit
        self._session.add(token_holder)
        await self._session.commit()

        return token_holder

    async def update_wallet_tokens(self, wallet_address: str, chain_id: int = None):
        """
        Update wallet tokens from Moralis API and store them in the database

        Args:
            wallet_address: The wallet address to update tokens for
            chain_id: Chain ID to filter and update tokens for

        Returns:
            None
        """
        try:
            wallet_tokens = await self.get_wallet_balance(wallet_address)

            existing_tokens = await self.fetch_user_tokens(wallet_address)
            existing_token_map = {t.token_address: t for t in existing_tokens}

            # Get user info from wallet
            user_wallet = await self._session.execute(
                select(UserWallet).where(
                    UserWallet.pubkey == wallet_address,
                    UserWallet.type == self.wallet_type
                )
            )
            user_wallet = user_wallet.scalar_one_or_none()
            
            user_id = ""
            username = ""
            name = ""
            avatar = ""
            
            if user_wallet:
                user_id = user_wallet.user_id
                # Get author info
                author = await self.fetch_author(user_id)
                if author:
                    username = author.username or ""
                    name = author.name or ""
                    avatar = author.avatar or ""

            for token in wallet_tokens:
                token_address = token.get("token_address")
                token_balance = int(token.get("balance", "0"))
                token_decimals = token.get("decimals", settings.DEFAULT_TOKEN_DECIMALS)

                if int(token_balance) <= 0:
                    continue

                # Check if token already exists for this wallet
                if token_address in existing_token_map:
                    # Update existing token amount
                    stmt = (
                        update(TokenHolder)
                        .where(
                            TokenHolder.address == wallet_address,
                            TokenHolder.token_address == token_address,
                        )
                        .values(
                            amount=token_balance, 
                            updated_at=func.now(),
                            user_id=user_id,
                            username=username
                        )
                    )
                    await self._session.execute(stmt)
                else:
                    # Insert new token holder
                    token_holder = TokenHolder(
                        chain=chain_id,  # Use provided chain_id
                        avatar=avatar,
                        address=wallet_address,
                        token_address=token_address,
                        amount=token_balance,
                        decimals=token_decimals,
                        creator_id="",
                        user_id=user_id,
                        username=username,
                    )
                    self._session.add(token_holder)

            await self._session.commit()

        except Exception as e:
            logger.error(f"Error updating wallet tokens from Moralis for wallet {wallet_address}: {str(e)}", exc_info=True)
            await self._session.rollback()
            raise  # 重新抛出异常以便上层处理

    async def fetch_recent_send_address(self, user_id: str):
        thirty_days_ago = datetime.now() - timedelta(days=30)
        stmt = (
            select(UserTransaction.to_address)
            .where(
                UserTransaction.user_id == user_id,
                UserTransaction.type == UserTransactionType.SEND_TOKEN,
                UserTransaction.status == UserTransactionStatus.CONFIRMED,
                UserTransaction.created_at > thirty_days_ago,
            )
            .order_by(UserTransaction.created_at.desc())
            .limit(30)
        )

        try:
            results = await self._session.execute(
                stmt, execution_options={"timeout": 5}
            )
            receivers = results.scalars().all()

            if not receivers:
                return []

            wallet_stmt = select(UserWallet.user_id, UserWallet.pubkey).where(
                UserWallet.pubkey.in_(receivers), UserWallet.type == self.wallet_type
            )
            wallet_results = await self._session.execute(
                wallet_stmt, execution_options={"timeout": 3}
            )
            address_to_user_map = {
                pubkey: user_id for user_id, pubkey in wallet_results
            }
            user_ids = list(address_to_user_map.values())
            if not user_ids:
                return []

            author_stmt = select(Author.name, Author.user_id, Author.avatar).where(
                Author.user_id.in_(user_ids)
            )
            author_results = await self._session.execute(
                author_stmt, execution_options={"timeout": 3}
            )

            user_to_author_map = {
                user_id: (name, avatar) for name, user_id, avatar in author_results
            }

            seen_addresses = {}
            unique_results = []

            for address in receivers:
                if (
                    address in address_to_user_map
                    and address not in seen_addresses
                ):
                    user_id = address_to_user_map[address]
                    if user_id in user_to_author_map:
                        name, avatar = user_to_author_map[user_id]
                        seen_addresses[address] = True
                        unique_results.append(
                            RecentSendAddressSchema(
                                name=name,
                                avatar=avatar if avatar else "",
                                user_id=user_id,
                                address=address,
                            )
                        )

                        if len(unique_results) >= 20:
                            break

            return unique_results

        except Exception as e:
            logger.error(f"Error fetching recent send addresses: {str(e)}")
            return []

    async def get_token_watchlist(
        self,
        user_id: str,
        sort_criteria: str,
        offset: int,
        limit: int,
        filter: str = None,
        chain_id: int = None,
    ):
        """获取用户的令牌观察列表

        Args:
            user_id: 用户ID
            sort_criteria: 排序标准
            offset: 分页偏移
            limit: 分页限制
            filter: 过滤条件(token名称)
            chain_id: Chain ID to filter pairs by

        Returns:
            List[Pair]: 符合条件的Pair列表
        """
        try:
            author_id = user_id

            count_query = """
                SELECT count(p.*) FROM pair p
                JOIN tokens_watchlist tw ON p.base = tw.token_address
                WHERE tw.author_id = :author_id
                AND p.status = :status
            """

            # Add chain filter to count query if chain_id is provided
            if chain_id is not None:
                count_query += " AND p.chain = :chain_id"

            exec_params = {"author_id": author_id, "status": PairStatus.READY.value}
            if chain_id is not None:
                exec_params["chain_id"] = chain_id

            exec_result = await self._session.execute(text(count_query), exec_params)
            total_count = exec_result.scalar_one() or 0

            query = """
                SELECT p.* FROM pair p
                JOIN tokens_watchlist tw ON p.base = tw.token_address
                WHERE tw.author_id = :author_id
                AND p.status = :status
            """

            # Add chain filter if chain_id is provided
            if chain_id is not None:
                query += " AND p.chain = :chain_id"

            if filter:
                query += " AND p.base_name ILIKE :filter"

            if sort_criteria == "popular":
                order_by = "ORDER BY p.mc DESC"
            elif sort_criteria == "price_max_24h":
                order_by = "ORDER BY p.price_usd DESC"
            elif sort_criteria == "price_min_24h":
                order_by = "ORDER BY p.price_usd ASC"
            elif sort_criteria == "24h_change_high_to_low":
                order_by = "ORDER BY p.price_change_percent DESC"
            elif sort_criteria == "24h_change_low_to_high":
                order_by = "ORDER BY p.price_change_percent ASC"
            elif sort_criteria == "name_a_to_z":
                order_by = "ORDER BY p.base_name ASC"
            elif sort_criteria == "name_z_to_a":
                order_by = "ORDER BY p.base_name DESC"
            elif sort_criteria == "24h_volume_high_to_low":
                order_by = "ORDER BY p.volume_24h DESC"
            elif sort_criteria == "24h_volume_low_to_high":
                order_by = "ORDER BY p.volume_24h ASC"
            else:  # default
                order_by = "ORDER BY p.mc DESC"

            paging = "LIMIT :limit OFFSET :offset"

            full_query = f"{query} {order_by} {paging}"

            query_params = {
                "author_id": author_id,
                "status": PairStatus.READY.value,
                "limit": limit,
                "offset": offset,
            }

            if chain_id is not None:
                query_params["chain_id"] = chain_id

            if filter:
                query_params["filter"] = f"%{filter}%"

            result = await self._session.execute(text(full_query), query_params)

            return result.all(), total_count

        except Exception as e:
            logger.error(f"Error fetching token watchlist: {str(e)}", exc_info=True)
            return [], 0

    async def add_token_to_watchlist(self, user_id: str, token_address: str) -> bool:
        """添加令牌到用户的观察列表

        Args:
            user_id: 用户ID
            token_address: 令牌地址

        Returns:
            bool: 操作是否成功
        """
        try:
            # 直接使用user_id作为author_id
            author_id = user_id

            # 检查令牌是否存在
            token_stmt = select(Pair).where(Pair.base == token_address)
            token_result = await self._session.execute(token_stmt)
            token = token_result.scalar_one_or_none()

            if not token:
                logger.warning(f"Token not found: {token_address}")
                return False

            # 添加到观察列表
            stmt = text(
                """
                INSERT INTO tokens_watchlist (author_id, token_address)
                VALUES (:author_id, :token_address)
                ON CONFLICT (author_id, token_address) DO NOTHING
                """
            )
            await self._session.execute(
                stmt, {"author_id": author_id, "token_address": token_address}
            )
            await self._session.commit()
            return True
        except Exception as e:
            logger.error(f"Error adding token to watchlist: {str(e)}", exc_info=True)
            await self._session.rollback()
            return False

    async def remove_token_from_watchlist(
        self, user_id: str, token_address: str
    ) -> bool:
        """从用户的观察列表中移除令牌

        Args:
            user_id: 用户ID
            token_address: 令牌地址

        Returns:
            bool: 操作是否成功
        """
        try:
            # 直接使用user_id作为author_id
            author_id = user_id

            stmt = text(
                """
                DELETE FROM tokens_watchlist
                WHERE author_id = :author_id AND token_address = :token_address
                """
            )
            await self._session.execute(
                stmt, {"author_id": author_id, "token_address": token_address}
            )
            await self._session.commit()
            return True
        except Exception as e:
            logger.error(
                f"Error removing token from watchlist: {str(e)}", exc_info=True
            )
            await self._session.rollback()
            return False

    async def check_token_in_watchlist(self, user_id: str, token_address: str) -> bool:
        """检查令牌是否在用户的观察列表中

        Args:
            user_id: 用户ID
            token_address: 令牌地址

        Returns:
            bool: 是否在观察列表中
        """
        try:
            # 直接使用user_id作为author_id
            author_id = user_id

            stmt = text(
                """
                SELECT 1 FROM tokens_watchlist
                WHERE author_id = :author_id AND token_address = :token_address
                """
            )
            result = await self._session.execute(
                stmt, {"author_id": author_id, "token_address": token_address}
            )
            return result.scalar() is not None
        except Exception as e:
            logger.error(f"Error checking token in watchlist: {str(e)}", exc_info=True)
            return False

    async def check_user_holds_token(self, user_id: str, token_address: str) -> bool:
        """检查用户是否持有特定token

        Args:
            user_id: 用户ID
            token_address: 令牌地址

        Returns:
            bool: 是否持有token
        """
        try:
            wallet = await self.fetch_user_wallet(user_id)
            if not wallet:
                return False

            stmt = text(
                """
                SELECT 1 FROM token_holder
                WHERE address = :address AND token_address = :token_address AND amount > 0
                """
            )
            result = await self._session.execute(
                stmt,
                {
                    "address": wallet.pubkey,
                    "token_address": token_address,
                },
            )
            return result.scalar() is not None
        except Exception as e:
            logger.error(f"Error checking if user holds token: {str(e)}", exc_info=True)
            return False

    def _align_time_to_interval(self, time: datetime, interval: Interval) -> datetime:
        """
        Aligns a given time to the nearest interval start time.

        :param time: The time to align.
        :param interval: The interval to align to.
        :return: The aligned time.
        """
        if interval == Interval.H1:
            # 1小时30秒bar：对齐到30秒边界
            second = 0 if time.second < 30 else 30
            return time.replace(second=second, microsecond=0)
        elif interval == Interval.H4:
            # 4小时：对齐到分钟（因为使用1分钟bar）
            return time.replace(second=0, microsecond=0)
        elif interval == Interval.D1:
            # 1天：对齐到5分钟（因为使用5分钟bar）
            minutes = time.minute - (time.minute % 5)
            return time.replace(minute=minutes, second=0, microsecond=0)
        elif interval == Interval.W1:
            # 1周：对齐到小时（因为使用1小时bar）
            return time.replace(minute=0, second=0, microsecond=0)
        elif interval == Interval.M1:
            # 1月：对齐到4小时边界（因为使用4小时bar）
            hour = time.hour - (time.hour % 4)
            return time.replace(hour=hour, minute=0, second=0, microsecond=0)
        elif interval == Interval.Max:
            # Max：对齐到12小时边界（因为使用12小时bar）
            hour = time.hour - (time.hour % 12)
            return time.replace(hour=hour, minute=0, second=0, microsecond=0)
        else:
            raise ValueError(f"Unsupported interval: {interval}")

    async def fetch_24h_volume(self, token_address: str) -> float:
        """
        Fetch 24-hour trading volume for a token.
        First tries to get volume from Redis (real transaction volumes),
        then falls back to kline_1m data if Redis data is not available.
        
        Args:
            token_address: The token contract address
            
        Returns:
            float: 24-hour trading volume sum, or 0 if not found
        """
        try:
            # Try to get volume from Redis first (more accurate for actual trades)
            volume_from_redis = await self._fetch_24h_volume_from_redis(token_address)
            if volume_from_redis > 0:
                return volume_from_redis
                
            # Fallback to kline data
            return await self._fetch_24h_volume_from_kline(token_address)
            
        except Exception as e:
            logger.exception("Error fetching 24h volume for %s: %s", token_address, e)
            return 0.0
    
    async def _fetch_24h_volume_from_redis(self, token_address: str) -> float:
        """
        Fetch 24h volume from Redis transaction volume data
        """
        try:
            from redis.asyncio import Redis
            from src.common.constants import MEMECOIN_REDIS_ENDPOINT
            
            # Calculate 24 hours ago in minutes
            current_time = int(datetime.now(timezone.utc).timestamp())
            start_time = current_time - (24 * 60 * 60)  # 24 hours ago
            
            # Get Redis connection
            redis_client = RedisCli.async_()
            
            try:
                volume_sum = 0.0
                # Iterate through each minute in the last 24 hours
                minute_start = (start_time // 60) * 60
                minute_end = (current_time // 60) * 60
                
                # Use pipeline for efficiency when checking many keys
                pipe = redis_client.pipeline()
                minute_keys = []
                
                for minute_ts in range(minute_start, minute_end + 60, 60):
                    key = f"{MEMECOIN_REDIS_ENDPOINT}:volume_1m:{token_address}:{minute_ts}"
                    minute_keys.append(key)
                    pipe.get(key)
                
                results = await pipe.execute()
                
                for result in results:
                    if result:
                        try:
                            volume_sum += float(result)
                        except (ValueError, TypeError):
                            continue
                            
                return volume_sum
                
            finally:
                await redis_client.close()
                
        except Exception as e:
            logger.debug("Error fetching volume from Redis for %s: %s", token_address, e)
            return 0.0
    
    async def _fetch_24h_volume_from_kline(self, token_address: str) -> float:
        """
        Fetch 24-hour trading volume for a token by summing kline_1m data.
        """
        try:
            from sqlalchemy import func
            
            # Calculate 24 hours ago from now
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=24)
            
            # Query sum of volume from kline_1m for the last 24 hours
            stmt = (
                select(func.sum(Kline1m.volume))
                .where(
                    Kline1m.token_address == token_address,
                    Kline1m.timestamp >= start_time,
                    Kline1m.timestamp <= end_time
                )
            )
            
            result = await self._kline_session.execute(stmt)
            volume_sum = result.scalar()
            
            return float(volume_sum) if volume_sum else 0.0
            
        except Exception as e:
            logger.error(f"Error fetching 24h volume for token {token_address}: {str(e)}")
            return 0.0

    async def fetch_author(self, user_id):
        return await self._session.get(Author, user_id)

    async def fetch_author_by_wallet_address(self, wallet_address):
        stmt = select(Author).join(UserWallet, Author.user_id == UserWallet.user_id).where(UserWallet.pubkey == wallet_address)
        result = await self._session.execute(stmt)
        return result.scalar()

    async def fetch_collection_id_by_token_address(self, token_address):
        stmt = select(Pair.collection_id).where(Pair.base == token_address)
        result = await self._session.execute(stmt)
        return result.scalar()

    async def fetch_collection_post_count(self, collection_id: str) -> int:
        """
        获取集合中的帖子数量

        Args:
            collection_id: 集合ID

        Returns:
            int: 帖子数量
        """
        stmt = select(Collection.contents_count).where(Collection.id == collection_id)
        result = await self._session.execute(stmt)
        return result.scalar() or 0

    @alru_cache(maxsize=256)
    async def fetch_user_info_by_username(
        self, username: str
    ) -> Optional[Dict]:
        # 选择所有需要的列
        stmt = (
            select(
                Author.username.label("username"),
                Author.name.label("name"),
                Author.avatar.label("avatar"),
                UserWallet.pubkey.label("wallet_address")
            )
            .select_from(UserWallet)
            .join(Author, UserWallet.user_id == Author.id)
            .where(
                Author.username_raw == username.lower(),
                UserWallet.type == self.wallet_type,
            )
            .limit(1)
        )

        # 执行并返回
        result = await self._session.execute(stmt)
        row = result.mappings().first()
        return dict(row) if row else None

    async def batch_get_token_follower_counts(self, token_addresses: List[str]) -> Dict[str, int]:
        """批量获取多个token的关注者数量"""
        try:
            if not token_addresses:
                return {}
                
            query = """
                SELECT token_address, COUNT(DISTINCT author_id) as follower_count
                FROM tokens_watchlist 
                WHERE token_address = ANY(:token_addresses)
                GROUP BY token_address
            """
            result = await self._session.execute(text(query), {"token_addresses": token_addresses})
            return {row[0]: row[1] for row in result.fetchall()}
        except Exception as e:
            logger.error(f"Error batch getting token follower counts: {str(e)}", exc_info=True)
            return {}

    async def check_tokens_followed_by_user(self, user_id: str, token_addresses: List[str]) -> Dict[str, bool]:
        """检查用户是否关注了指定的tokens"""
        try:
            if not token_addresses:
                return {}
                
            query = """
                SELECT token_address 
                FROM tokens_watchlist 
                WHERE author_id = :user_id AND token_address = ANY(:token_addresses)
            """
            result = await self._session.execute(text(query), {
                "user_id": user_id, 
                "token_addresses": token_addresses
            })
            followed_tokens = {row[0] for row in result.fetchall()}
            return {token_addr: token_addr in followed_tokens for token_addr in token_addresses}
        except Exception as e:
            logger.error(f"Error checking tokens followed by user: {str(e)}", exc_info=True)
            return {token_addr: False for token_addr in token_addresses}
