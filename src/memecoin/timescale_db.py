import os

from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker, Session
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from src.memecoin.settings import settings

async_engine = create_async_engine(
    settings.TIMESCALE_DB_URL, pool_size=50, max_overflow=25, pool_pre_ping=True
)

SessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


async def get_kline_session() -> AsyncSession:
    async with SessionLocal() as session:
        yield session
