from typing import Optional, List, Dict, Literal
from strenum import StrEnum
from pydantic import BaseModel, Field

from src.common.constants import PairStatus, Network


class SortCriteria(StrEnum):
    DEFAULT = "default"
    POPULAR = "popular"
    PRICE_MAX_24H = "price_max_24h"
    PRICE_MIN_24H = "price_min_24h"
    CHANGE_HIGH_TO_LOW = "24h_change_high_to_low"
    CHANGE_LOW_TO_HIGH = "24h_change_low_to_high"
    NAME_A_TO_Z = "name_a_to_z"
    NAME_Z_TO_A = "name_z_to_a"
    VOLUME_HIGH_TO_LOW = "24h_volume_high_to_low"
    VOLUME_LOW_TO_HIGH = "24h_volume_low_to_high"


class TokenListType(StrEnum):
    top: str = "top"
    new: str = "new"
    completing: str = "completing"
    completed: str = "completed"
    daily: str = "daily"
    weekly: str = "weekly"
    buyback: str = "buyback"


class RankingType(StrEnum):
    daily: str = "daily"
    weekly: str = "weekly"
    buyback: str = "buyback"


class Interval(StrEnum):
    H1: str = "1H"
    H4: str = "4H"
    D1: str = "1D"
    W1: str = "1W"
    M1: str = "1M"
    Max: str = "Max"


class EstimateGasType(StrEnum):
    buy: str = "buy"
    sell: str = "sell"
    transfer_bnb: str = "transfer_bnb"
    transfer_token: str = "transfer_token"
    create: str = "create"


class TokenSchema(BaseModel):
    token_name: str
    token_symbol: str
    token_address: Optional[str]
    description: str
    image_url: str
    collection_id: str
    market_cap: str
    market_cap_increment: Optional[str] = None  # 市值增长量
    price: str
    price_in_quote: str
    price_change_percent: str
    progress: int
    created_at: int = 0
    liquidity: str = ""
    volume_24h: str = ""
    social_links: Optional[Dict[str, str]] = None
    # 新增字段
    follower_count: int = 0  # 关注这个meme的人数
    is_followed_by_current_user: bool = False  # 当前登录用户是否关注了这个meme


class BuybackTokenSchema(BaseModel):
    token_name: str
    token_symbol: str
    token_address: Optional[str]
    description: str
    image_url: str
    collection_id: str
    market_cap: str
    market_cap_increment: Optional[str] = None  # 市值增长量
    price: str
    price_in_quote: str
    price_change_percent: str
    progress: int
    created_at: int = 0
    liquidity: str = ""
    volume_24h: str = ""
    social_links: Optional[Dict[str, str]] = None
    # 回购排行榜专用字段
    buyback_ratio: Optional[str] = None  # 回购比例（固定值），如 "25%" 或 None（仅前10名显示）
    # buyback_value: Optional[str] = None  # 回购价值，如 "$1,234.56" 或 None（根据平台收入计算）


class TokenPriceSchema(BaseModel):
    token_address: str
    open: str
    close: str
    high: str
    low: str
    volume: str
    chg_abs: str
    chg_pct: str
    timestamp: int


class HolderSchema(BaseModel):
    name: str
    username: str
    avatar: str
    user_id: str
    address: str
    balance: str
    ui_amount: str
    decimals: int
    percent: str
    is_live: bool = False  # 表示该持币人是否在进行直播


class CreationResponseSchema(BaseModel):
    txid: str
    detail: str


class ProfileTokenSchema(BaseModel):
    token_name: str
    token_symbol: str
    token_address: Optional[str]
    collection_id: str
    image_url: str
    price: str
    price_change_percent: str
    amount: str
    ui_amount: str
    decimals: int
    usd_amount: str
    market_cap: str


class MemeTokenSchema(BaseModel):
    token_name: str
    token_symbol: str
    token_address: Optional[str]
    image_url: str
    collection_id: str
    price: str
    price_change_percent: str
    market_cap: str
    contents_count: int


class BaseTokenSchema(BaseModel):
    token_name: str
    token_symbol: str
    token_address: Optional[str]
    image_url: str
    price: str
    price_change_percent: str
    market_cap: str


class AssetResponseSchema(BaseModel):
    wallet_address: str
    total_usd_amount: str
    tokens_usd_amount: str
    cash: str
    usd_change: str
    usd_change_percent: str
    tokens: List[ProfileTokenSchema]


class CreateMemeRequest(BaseModel):
    # collection_id: Optional[str] = None  # Can be None if creating new collection
    name: str  # Used as collection_title when creating collection
    symbol: str
    # repo_url: str  # '${Env.envConfig.host}collection/${collectionResponse.id}',
    order_id: Optional[str] = None
    usdt_amount: Optional[str] = None # USDT 买入的数量
    about: Optional[str] = None  # Used as collection_description when creating collection
    avatar: Optional[str] = None  # Used as collection_cover and collection_original_cover when creating collection
    social_links: Optional[Dict[str, str]] = None
    holdview_amount: Optional[str] = ""  # 代表用户需要持有多少token才能加入Meme的群聊中
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0

class EditMemeRequest(BaseModel):
    token_address: str
    about: Optional[str] = None
    social_links: Optional[Dict[str, str]] = None
    avatar: Optional[str] = None


class EditMemeResponse(BaseModel):
    detail: str


class BuyTokenRequest(BaseModel):
    token_address: str
    amount: str
    order_id: Optional[str] = None
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0
    is_with_usdt: Optional[bool] = True


class SellTokenRequest(BaseModel):
    token_address: str
    amount: str
    order_id: Optional[str] = None
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0
    is_with_usdt: Optional[bool] = True



class SendTokenRequest(BaseModel):
    token_address: str
    amount: str
    to_address: str
    order_id: Optional[str] = None
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0


class SendAssetRequest(BaseModel):
    token_address: str
    amount: str
    to_address: Optional[str] = None
    username: Optional[str] = None
    order_id: Optional[str] = None
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0


class SendNativeRequest(BaseModel):
    amount: str
    to_address: str
    order_id: Optional[str] = None
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0


class TransactionHistoryResponseSchema(BaseModel):
    user_id: str
    transaction_id: str
    type: str
    status: str
    timestamp: int
    token_address: str
    image_url: str
    token_name: str
    token_symbol: str
    from_address: str
    to_address: str
    unit_price: str
    order_amount: str
    service_fee: str
    total_amount: str
    payment_source: str
    supply: str
    minting_cost: str
    usd_amount: str
    order_id: Optional[str] = None
    from_user_name: Optional[str] = None
    from_user_avatar: Optional[str] = None
    to_user_name: Optional[str] = None
    to_user_avatar: Optional[str] = None


class TokenCache(BaseModel):
    name: str = ""
    symbol: str = ""
    image_url: str = ""
    token_address: str = ""
    pair: str = ""
    price: float = 0.0
    price_in_quote: float = 0.0
    price_change_percent: float = 0.0
    market_cap: float = 0.0
    liquidity: float = 0.0
    daily_mc: float = 0.0  # 今日00:00的市值
    weekly_mc: float = 0.0  # 本周一00:00的市值
    monthly_mc: float = 0.0  # 本月1号00:00的市值
    total_supply: int = 1_000_000_000
    progress: int = 0
    status: int = PairStatus.NOT_READY.value
    collection_id: str = ""


class BalanceResponseSchema(BaseModel):
    address: str
    amount: str
    usd_amount: str
    ui_amount: str
    ui_amount_10: str
    ui_amount_25: str
    ui_amount_50: str


class EstimateGasResponseSchema(BaseModel):
    gas_used: int
    gas_price: int
    total_gas_cost: int
    ui_amount_total_gas_cost: str
    ui_amount_total_usd_cost: str


class AmountResponseSchema(BaseModel):
    amount: str
    ui_amount: str


class OutTokenAmountResponseSchema(BaseModel):
    amount: str
    ui_amount: str
    max_usd_amount: str = ""


class RecentSendAddressSchema(BaseModel):
    name: str
    avatar: str
    user_id: str
    address: str


class ExportMnemonicResponseSchema(BaseModel):
    pubkey: str
    mnemonic: str


class TokenWatchlistResponse(BaseModel):
    is_subscribed: bool


class TokenWatchlistWithTotalResponse(BaseModel):
    tokens: List[TokenSchema]
    total: int
    target_user_id: Optional[str] = None  # 查询的目标用户ID


class TokenWatchlistSchema(BaseModel):
    token_address: str


class Web3TransactionStatusSchema(BaseModel):
    tx_hash: str
    status: str  # "success", "failed", "pending", "not_found"
    is_success: bool


class EthPriceResponseSchema(BaseModel):
    price: float
    currency: str = "USD"
    timestamp: int


class ConversionResponseSchema(BaseModel):
    token_address: str
    token_amount: str
    usd_amount: str
    conversion_direction: str  # "token_to_usd" or "usd_to_token"

class MoonPaySignatureRequest(BaseModel):
    """MoonPay签名请求"""
    url: str = Field(..., description="需要签名的MoonPay URL")

class MoonPaySignatureResponse(BaseModel):
    """MoonPay签名响应"""
    signature: str = Field(..., description="生成的签名")


class CashAmountResponseSchema(BaseModel):
    wallet_address: str
    cash_amount: str


class WalletAddressResponseSchema(BaseModel):
    username: str
    name: str
    avatar: Optional[str] = None
    wallet_address: str


class WithdrawRequest(BaseModel):
    order_id: Optional[str] = None
    network: Network = Network.LAYER1
    amount: str
    to_address: str
    gas: Optional[int] = 0
    gas_price: Optional[int] = 0
