import inspect
import os
from functools import lru_cache
from typing import Async<PERSON>enerator, Optional, Callable

from fastapi import Depends
from contextlib import asynccontextmanager
from redis.asyncio import Redis
from sqlalchemy import NullPool

from src.database.session import AsyncSession, get_session
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from .logger import logger

from .repos import MemeRepo
from .services import MemeService
from .settings import settings
from .timescale_db import get_kline_session, SessionLocal as KlineSessionLocal
from ..common.blockchain import BlockchainConnectorGetter
from ..common.caching.caching_client import CachingClient
from ..common.redis_cli import RedisCli


async def get_meme_repo(
    session: AsyncSession = Depends(get_session),
    kline_session: AsyncSession = Depends(get_kline_session),
) -> MemeRepo:
    """Builds & returns `MiniApp` repository"""
    return MemeRepo(session, kline_session)


@lru_cache(maxsize=1)
def get_redis_client() -> Redis:
    return RedisCli.async_()


@lru_cache(maxsize=1)
def get_caching_client_singleton() -> CachingClient:
    return CachingClient(get_redis_client(), logger)


def _build_connector_getter(custom_logger=None) -> Callable[[], object]:
    log = custom_logger or logger
    chain = (settings.BLOCKCHAIN_TYPE or "").lower()

    if chain == "sol":
        return BlockchainConnectorGetter(
            logger=log,
            chain_type="sol",
            rpc_url=settings.SOL_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
            },
        )
    elif chain == "eth":
        return BlockchainConnectorGetter(
            logger=log,
            chain_type="eth",
            rpc_url=settings.ETH_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.ETH_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "relayer_abi_path": settings.RELAYER_CONTRACT_ABI_PATH,
                "relayer_contract_address": settings.RELAYER_CONTRACT_ADDRESS,
                "l1_launcher_abi_path": settings.L1_LAUNCHER_ABI_PATH,
                "l1_launcher_contract_address": settings.L1_LAUNCHER_CONTRACT_ADDRESS,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
                "reward_contract_address": settings.REWARD_CONTRACT_ADDRESS,
                "reward_abi_path": settings.REWARD_ABI_PATH,
            },
        )
    elif chain == "bsc":
        return BlockchainConnectorGetter(
            logger=log,
            chain_type="bsc",
            rpc_url=settings.BSC_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.BSC_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
                "relayer_abi_path": settings.RELAYER_CONTRACT_ABI_PATH,
                "relayer_contract_address": settings.RELAYER_CONTRACT_ADDRESS,
                "reward_contract_address": settings.REWARD_CONTRACT_ADDRESS,
                "reward_abi_path": settings.REWARD_ABI_PATH,
            },
        )
    else:
        raise RuntimeError(f"Unsupported BLOCKCHAIN_TYPE: {settings.BLOCKCHAIN_TYPE!r}")


@lru_cache(maxsize=1)
def get_blockchain_getter_singleton() -> Callable[[], object]:
    return _build_connector_getter()


_init_done_flag = {"ok": False}


async def ensure_blockchain_inited(connector) -> None:
    if not _init_done_flag["ok"]:
        await connector.init_connection()
        _init_done_flag["ok"] = True


def get_cached_caching_client() -> CachingClient:
    return get_caching_client_singleton()


async def get_cached_blockchain_connector():
    connector = get_blockchain_getter_singleton()()
    await ensure_blockchain_inited(connector)
    return connector


async def get_meme_service(
    repo: MemeRepo = Depends(get_meme_repo),
    caching_client: CachingClient = Depends(get_cached_caching_client),
    blockchain_connector = Depends(get_cached_blockchain_connector),
) -> MemeService:
    return MemeService(repo, caching_client, blockchain_connector)


class MemeServiceContext:
    def __init__(self, write_session: AsyncSession, kline_session: AsyncSession, blockchain, cache: CachingClient, service: MemeService):
        self.write_session = write_session
        self.kline_session = kline_session
        self.blockchain = blockchain
        self.cache = cache
        self.service = service


@asynccontextmanager
async def meme_service_context(
    custom_logger=None,
    *,
    cache: Optional[CachingClient] = None,
    blockchain: Optional[object] = None,
) -> AsyncGenerator[MemeServiceContext, None]:
    """
    Worker/脚本专用：所有异步资源（Redis/区块链/DB引擎与会话）均在当前事件循环中创建，
    并在退出时于同一事件循环内显式关闭，避免跨循环复用。
    """
    log = custom_logger or logger

    # 1) Redis：本次创建 -> 本次关闭
    redis_created_here = False
    if cache is None:
        redis_created_here = True
        redis = RedisCli.async_()
        cache = CachingClient(redis, log)
    else:
        redis = getattr(cache, "client", None)

    # 2) 区块链连接器：本次创建 -> 本次关闭
    connector_created_here = False
    if blockchain is None:
        connector_created_here = True
        connector = _build_connector_getter(log)()
        await connector.init_connection()
    else:
        connector = blockchain

    # 3) DB 引擎与会话：本次创建 -> 本次关闭（关键改动）
    #    注意：不要再通过全局 SessionLocalWrite()/KlineSessionLocal()！
    write_engine = create_async_engine(
        os.environ["DATABASE_URL_ASYNC"],  # 你的写库 DSN
        pool_pre_ping=True,
        poolclass=NullPool,  # 可选：如果是短生命周期，直接 NullPool 也行
    )
    kline_engine = create_async_engine(
        os.environ["TIMESCALE_DB_URL"],  # 你的时序/只读库 DSN
        pool_pre_ping=True,
        poolclass=NullPool,
    )
    WriteSession = async_sessionmaker(write_engine, expire_on_commit=False, class_=AsyncSession)
    KlineSession = async_sessionmaker(kline_engine, expire_on_commit=False, class_=AsyncSession)

    async with WriteSession() as write_session, KlineSession() as kline_session:
        repo = MemeRepo(write_session, kline_session)
        service = MemeService(repo, cache, connector)
        ctx = MemeServiceContext(write_session, kline_session, connector, cache, service)
        try:
            yield ctx
        finally:
            # 4) 依次清理（在同一事件循环里）
            # 4.1 区块链
            if connector_created_here and connector is not None:
                close_fn = (
                    getattr(connector, "aclose", None)
                    or getattr(connector, "close_connection", None)
                    or getattr(connector, "close", None)
                )
                if close_fn:
                    res = close_fn()
                    if inspect.iscoroutine(res):
                        try:
                            await res
                        except Exception as e:
                            log.warning(f"close blockchain connector failed: {e}")

            # 4.2 Redis
            if redis_created_here and redis is not None:
                try:
                    await redis.close()
                except Exception as e:
                    log.warning(f"close redis client failed: {e}")

            # 4.3 数据库引擎（这一步非常关键，确保断开 asyncpg 连接池）
            try:
                await write_engine.dispose()
            except Exception as e:
                log.warning(f"dispose write engine failed: {e}")
            try:
                await kline_engine.dispose()
            except Exception as e:
                log.warning(f"dispose kline engine failed: {e}")

async def build_meme_service_for_worker_async() -> MemeService:
    """
    Worker 工厂：创建新的异步资源（无单例），用于在 Celery 任务里直接构建 MemeService。
    注意：返回的 MemeService 里包含的异步资源由调用方负责在使用后关闭，
    更推荐使用上面的 meme_service_context() 来自动托管生命周期。
    """
    log = logger
    redis = RedisCli.async_()
    cache = CachingClient(redis, log)

    connector = _build_connector_getter(log)()
    await connector.init_connection()

    from src.database import session as db_session
    assert db_session.SessionWrite is not None, "engines not initialized; call init_engines() first"
    write_session = db_session.SessionWrite()
    kline_session = KlineSessionLocal()
    repo = MemeRepo(write_session, kline_session)
    return MemeService(repo, cache, connector)
