from decimal import Decimal
from typing import Tuple, Union, Optional, List, Dict, Any, AsyncGenerator, cast
import time
import asyncio
import logging
from contextlib import asynccontextmanager
import httpx
from fastapi import FastAPI
from src.infra.logger import get_logger
from fastapi.responses import PlainTextResponse
import uvicorn

from src.common.blockchain.bsc_connector import BSCConnector
from src.common.blockchain.eth_connector import ETHConnector
from redis.asyncio import Redis

from src.common.constants import MEMECOIN_REDIS_ENDPOINT
from src.memecoin.constants import BNB_USD_PRICE, SOL_USD_PRICE, ETH_USD_PRICE
from src.memecoin.settings import settings
from src.common.redis_cli import RedisCli
from src.memecoin.utils import get_bnb_price, get_sol_price, get_eth_price
from src.memeprice.logger import logger

# Global price and connector caches (pool unified in common)
_redis_pool: Dict[str, object] = {}
_connector_cache: Dict[str, Union[BSCConnector, ETHConnector]] = {}
_price_cache: Dict[str, tuple] = {}  # Format: {token_address: (price, timestamp)}
CACHE_EXPIRY = 60  # Price cache expiration time (seconds)

# Price update status
_price_update_task = None
_running = False

logger = get_logger("memeprice", level="INFO", file_path="latest.log")
app = FastAPI(title="Memeprice Health Check")

@app.get("/health", response_class=PlainTextResponse)
async def health_check():
    """Simple health check endpoint for Docker"""
    return "OK"

@asynccontextmanager
async def get_bsc_connector() -> AsyncGenerator[Optional[BSCConnector], None]:
    """
    Context manager for obtaining a BSC connector, using cache to reduce repeated creation

    :yield: BSCConnector instance or None
    """
    cache_key = f"bsc_connector_{settings.BSC_RPC_URL}"

    # Check if there's an available connector in the cache
    if cache_key in _connector_cache:
        connector = _connector_cache[cache_key]
        try:
            # Test if the connection is valid
            await connector.w3.eth.chain_id
            yield connector
            return
        except Exception:
            # Connection invalid, remove from cache
            logger.warning("Cached BSC connector is invalid, creating a new connection")
            del _connector_cache[cache_key]

    # Create a new connector
    connector = BSCConnector(
        logger=logger,
        contract_address=settings.MEME_CONTRACT_ADDRESS,
        chain_id=settings.BSC_CHAIN_ID,
        rpc_url=settings.BSC_RPC_URL,
        meme_abi_path=settings.MEME_CONTRACT_ABI_PATH,
    )

    try:
        await connector.init_connection()
        # Add to cache
        _connector_cache[cache_key] = connector
        yield connector
    except Exception as e:
        logger.error(f"Unable to connect to BSC network: {e}")
        yield None


@asynccontextmanager
async def get_eth_connector() -> AsyncGenerator[Optional[ETHConnector], None]:
    """
    Context manager for obtaining an ETH connector, using cache to reduce repeated creation

    :yield: ETHConnector instance or None
    """
    cache_key = f"eth_connector_{settings.ETH_RPC_URL}"

    # Check if there's an available connector in the cache
    if cache_key in _connector_cache:
        connector = _connector_cache[cache_key]
        try:
            # Test if the connection is valid
            await connector.w3.eth.chain_id
            yield connector
            return
        except Exception:
            # Connection invalid, remove from cache
            logger.warning("Cached ETH connector is invalid, creating a new connection")
            del _connector_cache[cache_key]

    # Create a new connector
    connector = ETHConnector(
        logger=logger,
        contract_address=settings.MEME_CONTRACT_ADDRESS,
        chain_id=settings.ETH_CHAIN_ID,
        rpc_url=settings.ETH_RPC_URL,
        meme_abi_path=settings.MEME_CONTRACT_ABI_PATH,
    )

    try:
        await connector.init_connection()
        # Add to cache
        _connector_cache[cache_key] = connector
        yield connector
    except Exception as e:
        logger.error(f"Unable to connect to ETH network: {e}")
        yield None


@asynccontextmanager
async def get_connector() -> AsyncGenerator[Optional[Union[BSCConnector, ETHConnector]], None]:
    """
    Context manager for obtaining the appropriate connector based on BLOCKCHAIN_TYPE setting

    :yield: BSCConnector or ETHConnector instance or None
    """
    if settings.BLOCKCHAIN_TYPE == "ETH":
        async with get_eth_connector() as connector:
            yield connector
    else:
        async with get_bsc_connector() as connector:
            yield connector


@asynccontextmanager
async def get_redis_client() -> AsyncGenerator[Redis, None]:
    """
    Context manager for obtaining a Redis client, using connection pool to optimize performance

    :param redis_host: Redis host address
    :param redis_port: Redis port
    :param redis_db: Redis database index
    :yield: Redis client instance
    """
    client = RedisCli.async_()
    try:
        yield client
    finally:
        await client.aclose()


async def get_cached_price(token_address: str) -> Optional[float]:
    """
    Get token price from memory cache

    :param token_address: Token address
    :return: Cached price, or None if no cache or cache expired
    """
    if token_address in _price_cache:
        price, timestamp = _price_cache[token_address]
        # Check if cache has expired
        if time.time() - timestamp < CACHE_EXPIRY:
            return price
    return None


def cache_price(token_address: str, price: float) -> None:
    """
    Cache token price in memory

    :param token_address: Token address
    :param price: Token price
    """
    _price_cache[token_address] = (price, time.time())


async def get_token_to_bnb_price(
    token_address: str,
    token_amount: float = 1.0,
    connector: Optional[BSCConnector] = None,
    use_cache: bool = True,
    dex: str = "",
) -> Union[float, None]:
    """
    Get the amount of BNB that can be exchanged for the specified token (excluding fees)
    First try to get from cache, then query from contract, if it fails then query from Pancake

    :param token_address: Token address
    :param token_amount: Amount of token to exchange, default is 1 token
    :param connector: Optional BSCConnector instance
    :param use_cache: Whether to use cache
    :param dex: DEX information for token (e.g. "pancake")
    :return: The amount of BNB that can be exchanged for the token, or None if query fails
    """
    # Check cache
    if use_cache:
        cached_price = await get_cached_price(token_address)
        if cached_price is not None:
            logger.debug(
                f"Got Token {token_address} price from cache: {cached_price} BNB"
            )
            return cached_price

    # Handle connector
    if connector is None:
        # Use context manager to get connector
        async with get_connector() as conn:
            if conn is None:
                return None
            return await _get_token_price_impl(
                conn, token_address, token_amount, dex
            )
    else:
        # Directly use provided connector instance
        return await _get_token_price_impl(
            connector, token_address, token_amount, dex
        )


async def _get_token_price_impl(
    connector: BSCConnector, token_address: str, token_amount: float, dex: str = ""
) -> Optional[float]:
    """
    Internal function that actually performs the price query

    :param connector: BSCConnector instance
    :param token_address: Token address
    :param token_amount: Amount of token to exchange
    :param dex: DEX information for token (e.g. "pancake")
    :return: Amount of BNB that can be exchanged for the token
    """
    try:
        # If dex is specified as "pancake", directly query from Pancake
        if dex and dex.lower() == "pancake":
            logger.debug(f"Token {token_address} has dex info: {dex}, directly querying from Pancake")
            try:
                # Build token path
                token_path = [
                    {
                        "address": token_address,
                        "decimals": 18,
                    },  # Assume token precision is 18
                    {"address": settings.WBNB_ADDRESS, "decimals": 18},  # WBNB address
                ]

                # Query price ratio
                price_ratio = await connector.pancake_query_price(token_path)
                if price_ratio and price_ratio > 0:
                    # Calculate BNB amount, ensure result is float type
                    price_ratio = float(price_ratio)
                    bnb_amount = token_amount * price_ratio
                    logger.debug(
                        f"Got Token {token_address} price from Pancake: {bnb_amount} BNB"
                    )
                    # Cache price
                    cache_price(token_address, bnb_amount)
                    return bnb_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from Pancake: {e}"
                )
                # If Pancake query fails, don't try contract method
                return None
        else:
            # Default path: try contract first, then Pancake
            # 1. First try to query price from contract
            try:
                _, bnb_amount = await connector.get_out_native_amount(
                    token_address, Decimal(token_amount)
                )
                if bnb_amount and bnb_amount > 0:
                    # Ensure result is float type
                    bnb_amount = float(bnb_amount)
                    logger.debug(
                        f"Got Token {token_address} price from contract: {bnb_amount} BNB"
                    )
                    # Cache price
                    cache_price(token_address, bnb_amount)
                    return bnb_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from contract: {e}"
                )

            # 2. If contract query fails, try to query from Pancake
            try:
                # Build token path
                token_path = [
                    {
                        "address": token_address,
                        "decimals": 18,
                    },  # Assume token precision is 18
                    {"address": settings.WBNB_ADDRESS, "decimals": 18},  # WBNB address
                ]

                # Query price ratio
                price_ratio = await connector.pancake_query_price(token_path)
                if price_ratio and price_ratio > 0:
                    # Calculate BNB amount, ensure result is float type
                    price_ratio = float(price_ratio)
                    bnb_amount = token_amount * price_ratio
                    logger.debug(
                        f"Got Token {token_address} price from Pancake: {bnb_amount} BNB"
                    )
                    # Cache price
                    cache_price(token_address, bnb_amount)
                    return bnb_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from Pancake: {e}"
                )

        # 3. If all methods fail, return None
        logger.error(f"Unable to get Token {token_address} price")
        return None

    except Exception as e:
        logger.error(f"Error getting Token {token_address} price: {e}")
        return None


async def get_token_to_eth_price(
    token_address: str,
    token_amount: float = 1.0,
    connector: Optional[ETHConnector] = None,
    use_cache: bool = True,
    dex: str = "",
) -> Union[float, None]:
    """
    Get the amount of ETH that can be exchanged for the specified token (excluding fees)
    First try to get from cache, then query from contract, if it fails then query from MemeSwap

    :param token_address: Token address
    :param token_amount: Amount of token to exchange, default is 1 token
    :param connector: Optional ETHConnector instance
    :param use_cache: Whether to use cache
    :param dex: DEX information for token (e.g. "memeswap")
    :return: The amount of ETH that can be exchanged for the token, or None if query fails
    """
    # Check cache
    if use_cache:
        cached_price = await get_cached_price(token_address)
        if cached_price is not None:
            logger.debug(
                f"Got Token {token_address} price from cache: {cached_price} ETH"
            )
            return cached_price

    # Handle connector
    if connector is None:
        # Use context manager to get connector
        async with get_eth_connector() as conn:
            if conn is None:
                return None
            return await _get_token_price_eth_impl(
                conn, token_address, token_amount, dex
            )
    else:
        # Directly use provided connector instance
        return await _get_token_price_eth_impl(
            connector, token_address, token_amount, dex
        )


async def _get_token_price_eth_impl(
    connector: ETHConnector, token_address: str, token_amount: float, dex: str = ""
) -> Optional[float]:
    """
    Internal function that actually performs the ETH price query

    :param connector: ETHConnector instance
    :param token_address: Token address
    :param token_amount: Amount of token to exchange
    :param dex: DEX information for token (e.g. "memeswap")
    :return: Amount of ETH that can be exchanged for the token
    """
    try:
        # If dex is specified as "memeswap", directly query from MemeSwap
        if dex and dex.lower() == "memeswap":
            logger.debug(f"Token {token_address} has dex info: {dex}, directly querying from MemeSwap")
            try:
                # Query using MemeSwap
                _, eth_amount = await connector.memeswap_get_out_eth_amount(
                    token_address, Decimal(token_amount)
                )
                if eth_amount and eth_amount > 0:
                    # Ensure result is float type
                    eth_amount = float(eth_amount)
                    logger.debug(
                        f"Got Token {token_address} price from MemeSwap: {eth_amount} ETH"
                    )
                    # Cache price
                    cache_price(token_address, eth_amount)
                    return eth_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from MemeSwap: {e}"
                )
                # If MemeSwap query fails, don't try contract method
                return None
        else:
            # Default path: try contract first, then MemeSwap
            # 1. First try to query price from contract
            try:
                _, eth_amount = await connector.get_out_native_amount(
                    token_address, Decimal(token_amount)
                )
                if eth_amount and eth_amount > 0:
                    # Ensure result is float type
                    eth_amount = float(eth_amount)
                    logger.debug(
                        f"Got Token {token_address} price from contract: {eth_amount} ETH"
                    )
                    # Cache price
                    cache_price(token_address, eth_amount)
                    return eth_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from contract: {e}"
                )

            # 2. If contract query fails, try to query from MemeSwap
            try:
                _, eth_amount = await connector.memeswap_get_out_eth_amount(
                    token_address, Decimal(token_amount)
                )
                if eth_amount and eth_amount > 0:
                    # Ensure result is float type
                    eth_amount = float(eth_amount)
                    logger.debug(
                        f"Got Token {token_address} price from MemeSwap: {eth_amount} ETH"
                    )
                    # Cache price
                    cache_price(token_address, eth_amount)
                    return eth_amount
            except Exception as e:
                logger.warning(
                    f"Failed to get Token {token_address} price from MemeSwap: {e}"
                )

        # 3. If all methods fail, return None
        logger.error(f"Unable to get Token {token_address} price")
        return None

    except Exception as e:
        logger.error(f"Error getting Token {token_address} price: {e}")
        return None


async def get_token_to_usdt_price(
    token_address: str,
    token_amount: float = 1.0,
    connector: Optional[ETHConnector] = None,
    use_cache: bool = True,
    dex: str = "",
) -> Union[float, None]:
    """
    获取指定代币可兑换的 USDT 数量（不含手续费）。
    优先从缓存读取，若未命中则通过合约/路由计算。

    :param token_address: 代币地址
    :param token_amount: 兑换代币数量，默认 1 个代币
    :param connector: 可选的 ETHConnector 实例
    :param use_cache: 是否使用内存缓存
    :param dex: DEX 信息（预留，当前实现未区分）
    :return: 可兑换的 USDT 数量，失败返回 None
    """
    # 读取缓存
    if use_cache:
        cached_price = await get_cached_price(token_address)
        if cached_price is not None:
            logger.debug(
                f"Got Token {token_address} price from cache: {cached_price} USDT"
            )
            return cached_price

    # 处理连接器
    if connector is None:
        async with get_eth_connector() as conn:
            if conn is None:
                return None
            return await _get_token_price_usdt_impl(
                conn, token_address, token_amount, dex
            )
    else:
        return await _get_token_price_usdt_impl(
            connector, token_address, token_amount, dex
        )


async def _get_token_price_usdt_impl(
    connector: ETHConnector, token_address: str, token_amount: float, dex: str = ""
) -> Optional[float]:
    """
    实际执行以 USDT 为报价的价格查询。

    :param connector: ETHConnector 实例
    :param token_address: 代币地址
    :param token_amount: 兑换代币数量
    :param dex: DEX 信息（预留，当前实现未区分）
    :return: 可兑换的 USDT 数量
    """
    try:
        # 直接从合约的 bonding curve 价格获取 mid（USDT/Token）
        mid_price = await connector.get_token_price(token_address)
        if mid_price and mid_price > 0:
            # 返回指定 token_amount 的 USDT 数量（默认 1 个代币）
            usdt_amount = float(mid_price) * float(token_amount)
            logger.debug(
                f"Got Token {token_address} price in USDT from contract mid: {usdt_amount} USDT (mid: {mid_price}, amount: {token_amount})"
            )
            cache_price(token_address, usdt_amount)
            return usdt_amount

        logger.error(f"Unable to get Token {token_address} price in USDT from contract mid")
        return None
    except Exception as e:
        logger.error(f"Error getting Token {token_address} price in USDT (mid): {e}")
        return None


async def get_token_to_native_price(
    token_address: str,
    token_amount: float = 1.0,
    connector: Optional[Union[BSCConnector, ETHConnector]] = None,
    use_cache: bool = True,
    dex: str = "",
) -> Union[float, None]:
    """
    Get the amount of native token (BNB/ETH) that can be exchanged for the specified token
    Automatically chooses the appropriate method based on BLOCKCHAIN_TYPE

    :param token_address: Token address
    :param token_amount: Amount of token to exchange, default is 1 token
    :param connector: Optional connector instance
    :param use_cache: Whether to use cache
    :param dex: DEX information for token
    :return: The amount of native token that can be exchanged for the token, or None if query fails
    """
    if settings.BLOCKCHAIN_TYPE == "ETH":
        return await get_token_to_eth_price(token_address, token_amount, connector, use_cache, dex)
    else:
        return await get_token_to_bnb_price(token_address, token_amount, connector, use_cache, dex)


async def get_active_tokens_from_redis(
    redis_client: Redis, batch_size: int = 10
) -> List[str]:
    """
    Get active token list from Redis
    Use lpop to get and remove tokens from the queue, each time get a specified number
    Return value uses Set to deduplicate

    :param redis_client: Redis client
    :param batch_size: Number of tokens to get from the queue at once
    :return: Deduplicated list of active token addresses
    """
    try:
        # Batch get tokens from Redis queue, optimize IO operations
        tokens = await redis_client.lrange(
            "memetracker:active_tokens", 0, batch_size - 1
        )
        # Deduplicate
        unique_tokens = list(set(tokens))
        logger.debug(f"Got {len(unique_tokens)} active Tokens from Redis")
        return unique_tokens

    except Exception as e:
        logger.error(f"Error getting active Tokens from Redis: {e}")
        return []


async def save_token_price_to_redis(
    redis_client: Redis,
    token_address: str,
    token_price: float,
    timestamp: int = None,
    price_in_quote: float = None,
    cache_ttl: int = 3 * 24 * 3600,  # Default Redis cache 3 days
) -> bool:
    """
    Save token price to Redis

    :param redis_client: Redis client
    :param token_address: Token address
    :param token_price: Token price in USD
    :param timestamp: Timestamp, default is current time
    :param price_in_quote: Token price in quote currency (BNB/SOL)
    :param cache_ttl: Redis key expiration time (seconds)
    :return: Whether successfully saved
    """
    if timestamp is None:
        timestamp = int(time.time())

    try:
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"

        # Execute Redis operations
        if price_in_quote is not None:
            await redis_client.hset(key, "price_in_quote", str(price_in_quote))
            
        await redis_client.hset(key, "price", str(token_price))
        # await redis_client.expire(key, cache_ttl)
        await redis_client.zadd(f"{key}:price", {str(token_price): timestamp})
        await redis_client.expire(f"{key}:price", cache_ttl)

        return True
    except Exception as e:
        logger.error(f"Error saving Token {token_address} price to Redis: {e}")
        return False


async def update_token_price_bnb(
    token_address: str,
    connector: BSCConnector,
    redis_client: Redis,
    bnb_price: Optional[float] = None,
    dex: str = ""
) -> bool:
    """
    Update a single token's price

    :param token_address: Token address
    :param connector: BSCConnector instance
    :param redis_client: Redis client
    :param bnb_price: BNB USD price, if None then get from Redis
    :param dex: DEX name for this token (e.g. "pancake")
    :return: Boolean indicating whether the update was successful
    """
    try:
        # Get BNB price
        if bnb_price is None:
            bnb_price = await get_bnb_price(redis_client)
            logger.debug(f"Got BNB price from Redis: {bnb_price} USD")

        # Query price
        bnb_amount = await get_token_to_bnb_price(
            token_address=token_address,
            connector=connector,
            use_cache=False,
            dex=dex,  # Pass dex info to the function
        )

        if bnb_amount is not None:
            # Calculate USD price
            token_price = bnb_amount * bnb_price
            
            # bnb_amount is the price_in_quote (BNB amount for 1 token)
            price_in_quote = bnb_amount

            # Save to Redis
            success = await save_token_price_to_redis(
                redis_client=redis_client,
                token_address=token_address,
                token_price=token_price,
                timestamp=int(time.time()),
                price_in_quote=price_in_quote
            )

            if success:
                logger.info(
                    f"Token {token_address} price updated successfully: {token_price:.10f} USD (price in quote: {price_in_quote})"
                )
                return True
            else:
                logger.warning(f"Failed to save Token {token_address} price")
                return False
        else:
            logger.warning(f"Unable to get Token {token_address} price")
            return False

    except Exception as e:
        logger.error(f"Error processing Token {token_address}: {e}")
        return False


async def update_token_price_eth(
    token_address: str,
    connector: ETHConnector,
    redis_client: Redis,
    eth_price: Optional[float] = None,
    dex: str = ""
) -> bool:
    """
    Update a single token's price on ETH network

    :param token_address: Token address
    :param connector: ETHConnector instance
    :param redis_client: Redis client
    :param eth_price: ETH USD price, if None then get from Redis
    :param dex: DEX name for this token (e.g. "memeswap")
    :return: Boolean indicating whether the update was successful
    """
    try:
        # Get ETH price
        if eth_price is None:
            eth_price = await get_eth_price(redis_client)
            logger.debug(f"Got ETH price from Redis: {eth_price} USD")

        # Query price
        eth_amount = await get_token_to_eth_price(
            token_address=token_address,
            connector=connector,
            use_cache=False,
            dex=dex,  # Pass dex info to the function
        )

        if eth_amount is not None:
            # Calculate USD price
            token_price = eth_amount * eth_price
            
            # eth_amount is the price_in_quote (ETH amount for 1 token)
            price_in_quote = eth_amount

            # Save to Redis
            success = await save_token_price_to_redis(
                redis_client=redis_client,
                token_address=token_address,
                token_price=token_price,
                timestamp=int(time.time()),
                price_in_quote=price_in_quote
            )

            if success:
                logger.info(
                    f"Token {token_address} price updated successfully: {token_price:.10f} USD (price in quote: {price_in_quote})"
                )
                return True
            else:
                logger.warning(f"Failed to save Token {token_address} price")
                return False
        else:
            logger.warning(f"Unable to get Token {token_address} price")
            return False

    except Exception as e:
        logger.error(f"Error processing Token {token_address}: {e}")
        return False


async def update_token_price_usdt(
    token_address: str,
    connector: ETHConnector,
    redis_client: Redis,
    usdt_price: Optional[float] = None,
    dex: str = ""
) -> bool:
    """
    更新以 USDT 为报价的单个代币价格（ETH 网络）。

    :param token_address: 代币地址
    :param connector: ETHConnector 实例
    :param redis_client: Redis 客户端
    :param usdt_price: USDT 对 USD 的价格，若为 None 则固定为 1.0
    :param dex: 该代币的 DEX 名称（如 "memeswap"），当前实现不区分
    :return: 是否更新成功
    """
    try:
        if dex:
            token_prices = await connector.get_dex_out_usdt_amount(token_address, Decimal(1))
            mid_price = float(token_prices[-1])
        else:
            # 直接从合约获取 mid（USDT/Token），按 1 个代币计
            mid_price = await connector.get_token_price(token_address)
        if mid_price and mid_price > 0:
            # 写入 Redis
            success = await save_token_price_to_redis(
                redis_client=redis_client,
                token_address=token_address,
                token_price=mid_price,
                timestamp=int(time.time()),
                price_in_quote=mid_price,
            )

            if success:
                logger.info(
                    f"Token {token_address} price updated successfully: {mid_price:.10f} USD (price in quote: {mid_price})"
                )
                return True
            else:
                logger.warning(f"Failed to save Token {token_address} price")
                return False
        else:
            logger.warning(f"Unable to get Token {token_address} price from contract mid")
            return False

    except Exception as e:
        logger.error(f"Error processing Token {token_address} (mid): {e}")
        return False


async def update_token_price(
    token_address: str,
    connector: Union[BSCConnector, ETHConnector],
    redis_client: Redis,
    quote_price: Optional[float] = None,
    dex: str = ""
) -> bool:
    """
    Update a single token's price - automatically chooses BNB or ETH based on BLOCKCHAIN_TYPE

    :param token_address: Token address
    :param connector: BSCConnector or ETHConnector instance
    :param redis_client: Redis client
    :param quote_price: Quote currency USD price (BNB/ETH), if None then get from Redis
    :param dex: DEX name for this token
    :return: Boolean indicating whether the update was successful
    """
    if settings.BLOCKCHAIN_TYPE == "ETH":
        return await update_token_price_usdt(token_address, connector, redis_client, quote_price, dex)
    else:
        return await update_token_price_bnb(token_address, connector, redis_client, quote_price, dex)


async def update_active_tokens_price(
    connector: Optional[Union[BSCConnector, ETHConnector]] = None,
    interval: int = 300,  # Default update every 5 minutes
    batch_size: int = 20,  # Number of tokens processed per batch
    concurrency: int = 5,  # Number of batches processed concurrently
) -> None:
    """
    Periodically update active Token prices to Redis, using semaphore to control concurrency

    :param connector: Optional BSCConnector or ETHConnector instance
    :param interval: Update interval (seconds)
    :param batch_size: Number of tokens processed each time
    :param concurrency: Number of batches processed concurrently
    """
    # Initialize semaphore to control concurrency
    semaphore = asyncio.Semaphore(concurrency)

    try:
        # Create connector (if not provided)
        active_conn = connector
        if active_conn is None:
            async with get_connector() as conn:
                if conn is None:
                    logger.error("Unable to initialize connector, exiting service")
                    return
                active_conn = conn

        while True:
            try:
                # Use context manager to get Redis client
                async with get_redis_client() as redis_client:
                    # Get active token list
                    active_tokens = await get_active_tokens_from_redis(
                        redis_client,
                        batch_size=batch_size
                        * concurrency,  # Get multiple batch processing amounts at once
                    )

                    if not active_tokens:
                        logger.debug("No active Tokens need to be updated")
                        await asyncio.sleep(interval)
                        continue

                    # Get quote price, only get once
                    if settings.BLOCKCHAIN_TYPE == "ETH":
                        quote_price = await get_eth_price(redis_client)
                        logger.debug(f"Using ETH price for this batch: {quote_price} USD")
                    else:
                        quote_price = await get_bnb_price(redis_client)
                        logger.debug(f"Using BNB price for this batch: {quote_price} USD")

                    # Use semaphore to control number of concurrent tasks
                    async def process_token_with_semaphore(token):
                        async with semaphore:
                            # Skip USDT and other stable coins
                            if hasattr(settings, 'USDT_ADDRESS') and token == settings.USDT_ADDRESS:
                                logger.debug(f"Skipping active price update for USDT: {token}")
                                return (token, True)  # Return success to remove from queue
                                
                            # Get DEX info from Redis if available
                            dex = ""
                            try:
                                key = f"{MEMECOIN_REDIS_ENDPOINT}:{token}"
                                dex = await redis_client.hget(key, "dex")
                                if dex:
                                    logger.debug(f"Token {token} has DEX info: {dex}")
                            except Exception as e:
                                logger.warning(f"Error getting DEX info for token {token}: {e}")
                            
                            return (token, await update_token_price(
                                token_address=token,
                                connector=active_conn,
                                redis_client=redis_client,
                                quote_price=quote_price,  # Pass in already obtained quote price
                                dex=dex,  # Pass the DEX info
                            ))

                    # Create processing tasks for all tokens
                    tasks = [
                        process_token_with_semaphore(token) for token in active_tokens
                    ]

                    # Wait for all tasks to complete, and collect results
                    results = await asyncio.gather(*tasks)
                    
                    # Filter for successfully processed tokens
                    successful_tokens = [token for token, success in results if success]
                    
                    if successful_tokens:
                        # Remove only successfully processed tokens from the queue
                        for token in successful_tokens:
                            await redis_client.lrem("memetracker:active_tokens", 1, token)
                        
                        logger.debug(
                            f"Successfully processed {len(successful_tokens)} out of {len(active_tokens)} tokens"
                        )
                    
                    logger.debug(
                        f"This round of price updates completed, waiting {interval} seconds for the next round"
                    )

                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"Error in price update loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying after error

    except Exception as e:
        logger.error(f"Price update service error: {e}")
    finally:
        # Close all connection pools
        for pool in _redis_pool.values():
            await pool.disconnect()
        
        # Clear connector cache
        _connector_cache.clear()

# Functions migrated from Kafka
async def update_all_tokens_prices(redis_client: Redis, quote_price: float):
    """Update USD prices for all tokens.
    
    Args:
        redis_client: Redis client
        quote_price: Deprecated/unused. ETH 网络下按 USDT 直接计价
    """
    try:
        # Get all token addresses from Redis set
        all_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
        token_addresses = await redis_client.smembers(all_tokens_key)
        
        logger.debug(f"Updating USD prices for {len(token_addresses)} tokens")
        logger.info(f"Starting ETH batch price update (USDT mid) for {len(token_addresses)} tokens")
        
        current_timestamp = int(time.time())
        updated_count = 0
        blockchain_type = settings.BLOCKCHAIN_TYPE
        
        # Create parallel update tasks
        semaphore = asyncio.Semaphore(5)  # Limit concurrency
        
        # 仅 ETH：获取连接器后批量更新
        async with get_eth_connector() as connector:
            if connector is None:
                logger.error("Unable to initialize ETH connector, aborting update_all_tokens_prices")
                return
            logger.debug("ETH connector initialized for update_all_tokens_prices")
            
            # 预取 1 WETH -> USDT 报价，供 dex 分支进行 USD 折算
            eth_usdt_price = 0.0
            try:
                amounts = await connector.memeswap_get_amounts_out(Decimal(1), [connector.weth_address, connector.usdt_address])
                if isinstance(amounts, list) and len(amounts) >= 2 and int(amounts[1]) > 0:
                    eth_usdt_price = float(Decimal(amounts[1]) / Decimal(connector.usdt_decimals))
            except Exception as e:
                logger.warning(f"Failed to prefetch WETH->USDT price for liquidity conversion: {e}")
            
            async def update_token_price_task(token_address):
                nonlocal updated_count
                try:
                    async with semaphore:
                        # 跳过 USDT
                        if hasattr(settings, 'USDT_ADDRESS') and token_address == settings.USDT_ADDRESS:
                            logger.debug(f"Skipping price update for USDT: {token_address}")
                            return
                        
                        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
                        token_data = await redis_client.hgetall(key)
                        
                        # 读取 DEX 信息
                        dex = ""
                        if token_data and 'dex' in token_data and token_data['dex']:
                            dex = token_data['dex']
                        else:
                            try:
                                dex = await redis_client.hget(key, "dex")
                            except Exception:
                                dex = ""
                        logger.debug(f"Token {token_address}: dex='{dex if dex else '-'}'")
                        
                        # 获取 USDT 单价（mid）
                        mid_price: float = 0.0
                        try:
                            if dex:
                                logger.debug(f"Token {token_address}: fetching price via DEX get_dex_out_usdt_amount")
                                prices = await connector.get_dex_out_usdt_amount(token_address, Decimal(1))
                                mid_price = float(prices[-1])
                            else:
                                logger.debug(f"Token {token_address}: fetching price via contract mid get_token_price")
                                mid_price = await connector.get_token_price(token_address)
                        except Exception as ee:
                            logger.warning(f"Failed to fetch price for {token_address}: {ee}")
                            return
                        
                        if not mid_price or mid_price <= 0:
                            logger.info(f"Token {token_address}: non-positive mid price {mid_price}, skipped")
                            return
                        logger.debug(f"Token {token_address}: mid_price_usdt={mid_price}")
                        
                        # 计算市值与进度（保留已有 progress）
                        mc = mid_price * settings.DEFAULT_TOTAL_SUPPLY
                        progress = 0
                        try:
                            progress = int(token_data.get('progress', 0)) if token_data else 0
                        except Exception:
                            progress = 0
                        logger.debug(f"Token {token_address}: market_cap={mc}, progress={progress}")
                        
                        # 计算流动性（USD）：仅当 Redis 中存在 pair 时更新
                        update_data = {
                            "price": str(mid_price),
                            "price_in_quote": str(mid_price),  # USDT 报价与 USD 基本等值
                            "market_cap": str(mc),
                            "progress": str(progress),
                        }
                        liquidity_usd = 0.0
                        try:
                            # 读取 pair（优先从 hgetall，其次 hget）
                            pair_addr = ""
                            if token_data and 'pair' in token_data and token_data['pair']:
                                pair_addr = token_data['pair']
                            else:
                                try:
                                    pair_addr = await redis_client.hget(key, "pair")
                                except Exception:
                                    pair_addr = ""
                            if pair_addr:
                                liquidity_usd = float(await connector.get_pool_liquidity_onchain(pair_addr))
                                update_data["liquidity"] = f"{liquidity_usd:.8f}"
                                logger.debug(f"Token {token_address}: updated liquidity from pair: {liquidity_usd:.8f}")
                            else:
                                try:
                                    liquidity_usd = float(await connector.get_liquidity(token_address))
                                except Exception:
                                    liquidity_usd = 0.0
                                if liquidity_usd > 0:
                                    update_data["liquidity"] = f"{liquidity_usd:.8f}"
                                    logger.debug(f"Token {token_address}: updated liquidity from bonding curve: {liquidity_usd:.8f}")
                                else:
                                    logger.debug(f"Token {token_address}: present but liquidity not available")
                        except Exception as e:
                            logger.warning(f"Token {token_address}: failed to update liquidity from pair: {e}")
                        
                        # 回写 Redis
                        async with redis_client.pipeline() as pipe:
                            await pipe.hset(key, mapping=update_data)
                            await pipe.zadd(f"{key}:price", {str(mid_price): current_timestamp})
                            await pipe.expire(f"{key}:price", 3*24*3600)
                            await pipe.execute()
                        
                        updated_count += 1
                        logger.info(f"Token {token_address} updated: price={mid_price} USD, market_cap={mc}, liquidity={liquidity_usd}")
                except Exception as e:
                    logger.warning(f"Error updating token {token_address} price: {e}")
            
            # Create update tasks for all tokens
            tasks = [update_token_price_task(addr) for addr in token_addresses]
            await asyncio.gather(*tasks)
        
        blockchain_type = settings.BLOCKCHAIN_TYPE
        logger.info(f"Successfully updated prices for {updated_count} tokens on {blockchain_type} using USDT mid")
    except Exception as e:
        logger.exception(f"Error updating all token prices: {e}")

async def update_bnb_price(redis_client: Redis):
    """Update BNB price from Binance API"""
    url = "https://api.binance.com/api/v3/ticker/price?symbol=BNBUSDT"
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get(url)
            if resp.status_code != 200:
                logger.warning(
                    "Failed to get BNB price, status code: %s", resp.status_code
                )
                return None

            data = resp.json()
            bnb_price = data.get("price")
            if bnb_price is None:
                logger.debug("Could not find 'price' field in Binance response")
                return None
                
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{BNB_USD_PRICE}:price"
            current_timestamp = int(time.time())
            expiration_threshold = current_timestamp - 86400  # 24 hours
            await redis_client.zremrangebyscore(
                key, "-inf", expiration_threshold
            )
            ts = int(time.time())
            await redis_client.zadd(key, {str(bnb_price): ts})
            logger.debug(f"BNB price updated: {bnb_price}")
            return float(bnb_price)
    except Exception as e:
        logger.exception(f"Error updating BNB price: {e}")
        return None

async def update_sol_price(redis_client: Redis):
    """Update SOL price from Binance API"""
    url = "https://api.binance.com/api/v3/ticker/price?symbol=SOLUSDT"
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get(url)
            if resp.status_code != 200:
                logger.warning(
                    "Failed to get SOL price, status code: %s", resp.status_code
                )
                return None

            data = resp.json()
            sol_price = data.get("price")
            if sol_price is None:
                logger.warning("Could not find 'price' field in Binance response")
                return None
                
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{SOL_USD_PRICE}:price"
            current_timestamp = int(time.time())
            expiration_threshold = current_timestamp - 86400  # 24 hours
            await redis_client.zremrangebyscore(
                key, "-inf", expiration_threshold
            )
            ts = int(time.time())
            await redis_client.zadd(key, {str(sol_price): ts})
            logger.debug(f"SOL price updated: {sol_price}")
            return float(sol_price)
    except Exception as e:
        logger.exception(f"Error updating SOL price: {e}")
        return None

async def update_eth_price(redis_client: Redis) -> Optional[float]:
    """
    Update ETH price in Redis by fetching from external API.
    """
    url = "https://api.binance.com/api/v3/ticker/price?symbol=ETHUSDT"
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get(url)
            if resp.status_code != 200:
                logger.warning(
                    "Failed to get ETH price, status code: %s", resp.status_code
                )
                return None

            data = resp.json()
            eth_price = data.get("price")
            if eth_price is None:
                logger.debug("Could not find 'price' field in Binance response")
                return None
                
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{ETH_USD_PRICE}:price"
            current_timestamp = int(time.time())
            expiration_threshold = current_timestamp - 86400  # 24 hours
            await redis_client.zremrangebyscore(
                key, "-inf", expiration_threshold
            )
            ts = int(time.time())
            await redis_client.zadd(key, {str(eth_price): ts})
            logger.debug(f"ETH price updated: {eth_price}")
            return float(eth_price)
    except Exception as e:
        logger.exception(f"Error updating ETH price: {e}")
        return None

async def update_quote_price_task():
    """Price update task, runs every minute"""
    global _running
    try:
        logger.info("Starting price update loop")
        while _running:
            try:
                async with get_redis_client() as redis_client:
                    # Determine which base currency price to update based on settings
                    # if settings.BLOCKCHAIN_TYPE == "SOL":
                    #     logger.debug("Updating SOL price")
                    #     quote_price = await update_sol_price(redis_client)
                    # elif settings.BLOCKCHAIN_TYPE == "ETH":
                    #     logger.debug("Updating ETH price")
                    #     quote_price = await update_eth_price(redis_client)
                    # else:
                    #     logger.debug("Updating BNB price")
                    #     quote_price = await update_bnb_price(redis_client)
                    
                    # If price was successfully obtained, update all token prices
                    # if quote_price:
                    await update_all_tokens_prices(redis_client, "1")
            except Exception as e:
                logger.exception(f"Error executing price update: {e}")

            # Wait 10 seconds
            await asyncio.sleep(10)
    except asyncio.CancelledError:
        logger.info("Price update task canceled")
    except Exception as e:
        logger.exception(f"Error in price update loop: {e}")

async def start_price_update_task():
    """Start price update scheduled task"""
    global _price_update_task, _running
    if not _price_update_task or _price_update_task.done():
        _running = True
        _price_update_task = asyncio.create_task(update_quote_price_task())
        logger.info("Price update scheduled task started")
    return _price_update_task

async def stop_price_update_task():
    """Stop price update scheduled task"""
    global _price_update_task, _running
    _running = False
    if _price_update_task and not _price_update_task.done():
        _price_update_task.cancel()
        try:
            await _price_update_task
        except asyncio.CancelledError:
            pass
        _price_update_task = None
        logger.info("Price update scheduled task stopped")

async def init_http_server():
    """Initialize FastAPI server for health checks"""
    config = uvicorn.Config(app, host="0.0.0.0", port=8000, log_level="info")
    server = uvicorn.Server(config)
    await server.serve()

async def run():
    """
    Main function, starts price update service
    """
    try:
        logger.info("Starting price update service...")
        
        # Start price update scheduled task
        await start_price_update_task()
        
        # Start health check server
        # health_server_task = asyncio.create_task(init_http_server())
        
        # Start active token price updates
        price_update_task = asyncio.create_task(update_active_tokens_price(
            interval=300,  # Update active tokens every 5 minutes
            batch_size=10,  # 10 tokens per batch
            concurrency=5,  # Process 5 batches concurrently
        ))
        
        # Wait for both tasks
        await asyncio.gather(price_update_task)
        
    finally:
        # Stop price update scheduled task
        await stop_price_update_task()
        
        # Close all connection pools
        for pool in _redis_pool.values():
            await pool.disconnect()
        
        # Clear connector cache
        _connector_cache.clear()


if __name__ == "__main__":
    asyncio.run(run())
