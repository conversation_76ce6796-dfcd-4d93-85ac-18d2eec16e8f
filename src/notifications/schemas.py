from typing import List, Optional, Dict, Any, Union
from datetime import datetime

from pydantic import BaseModel, model_validator

from src.database.schemas import NotificationBase, NotificationRead


class NotificationsCreateRequest(BaseModel):
    recipients_ids: List[str]
    notification_base: NotificationBase


class NotificationConfigSchema(BaseModel):
    app_updates: bool = True
    new_comments: bool = True
    likes: bool = True
    users_updates: bool = True
    memes_updates: bool = True
    direct_messages: bool = True


class NotificationConfigUpdateRequest(BaseModel):
    app_updates: Optional[bool] = None
    new_comments: Optional[bool] = None
    likes: Optional[bool] = None
    users_updates: Optional[bool] = None
    memes_updates: Optional[bool] = None
    direct_messages: Optional[bool] = None


class NotificationMarkReadRequest(BaseModel):
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class CommitMarkReadRequest(BaseModel):
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


# App版本相关的schema
class AppVersionCreateRequest(BaseModel):
    version: str
    build: int
    target_os: str
    release_date: datetime
    release_notes: Optional[str] = None
    download_url: Optional[str] = None


# 响应模型定义
class HealthResponse(BaseModel):
    status: str


class AppVersionResponse(BaseModel):
    need_update: bool
    force_update: bool
    latest_build: int
    latest_version: str
    release_date: str
    release_notes: str
    download_url: str


class AppVersionCreateResponse(BaseModel):
    status: str
    message: str
    version: str


class NotificationConfigResponse(BaseModel):
    user_id: str
    app_updates: bool
    new_comments: bool
    likes: bool
    users_updates: bool
    memes_updates: bool
    direct_messages: bool


class ConfigStatusResponse(BaseModel):
    status: str
    config: NotificationConfigResponse


class NotificationsCreateResponse(BaseModel):
    pass  # 创建通知成功后不返回内容


class NotificationMarkReadResponse(BaseModel):
    """标记通知为已读的响应模型"""
    status: str
    updated_count: int
    message: str


class CommitMarkReadResponse(BaseModel):
    """标记commit为已读的响应模型"""
    status: str
    updated_count: int
    message: str

