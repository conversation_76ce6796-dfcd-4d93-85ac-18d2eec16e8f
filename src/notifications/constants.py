from sqlalchemy import Enum
from src.database.schemas.Notification import NotificationType


class ContentGenerator:

    @staticmethod
    def author_follow(user: str) -> dict:
        return {
            "en": f"{user} just followed you — don’t keep them waiting, say hi!",
            "ru": f"{user} только что подписался(ась) на вас — не заставляйте ждать, ответьте!",
            "zh": f"{user} 迫不及待地关注了你，快去打个招呼吧！"
        }

    @staticmethod
    def author_new_question() -> dict:
        return {
            "en": "New Question",
            "ru": "Новый вопрос"
        }

    @staticmethod
    def question_subscribe() -> dict:
        return {
            "en": "Your Question has new follower",
            "ru": "На ваш вопрос подписались"
        }

    @staticmethod
    def question_answer() -> dict:
        return {
            "en": "New answer for your Question",
            "ru": "На ваш вопрос ответили"
        }

    @staticmethod
    def question_answer_update() -> dict:
        return {
            "en": "Your Question has new follower",
            "ru": "На ваш вопрос подписались"
        }

    @staticmethod
    def article_update() -> dict:
        return {
            "en": "Article updated",
            "ru": "Автор отредактировал статью"
        }

    @staticmethod
    def post_upvote(user: str) -> dict:
        return {
            "en": f"{user} liked your content — quality recognized!",
            "ru": f"{user} поставил(а) лайк вашему контенту — это признание качества!",
            "zh": f"{user}点赞了你的内容 —— 优质被认可啦！"
        }

    @staticmethod
    def post_comment(user: str, content: str) -> dict:
        return {
            "en": f"{user} commented: \"{content}\". Check it out!",
            "ru": f"{user} оставил(а) комментарий: «{content}». Откройте и посмотрите!",
            "zh": f"{user}评论了你的内容：“{content}”。快去看看吧！"
        }

    @staticmethod
    def collection_subscribe() -> dict:
        return {
            "en": "Your meme has new follower",
            "ru": "У твоего мема появился новый подписчик"
        }

    @staticmethod
    def collection_update() -> dict:
        return {
            "en": "Collection update",
            "ru": "Обновление коллекции"
        }

    @staticmethod
    def post_commit() -> dict:
        return {
            "en": "📝 New commits for your meme — review them now!",
            "ru": "📝 Новые коммиты для вашего мема — проверьте их прямо сейчас!",
            "zh": "📝 你的 Meme 有了新 commit，快去审核吧！"
        }

    @staticmethod
    def translation_created() -> dict:
        return {
            "en": "New translation",
            "ru": "Новый перевод"
        }

    @staticmethod
    def translation_updated() -> dict:
        return {
            "en": "Translation updated",
            "ru": "Перевод обновлен"
        }

    @staticmethod
    def new_message(user: str, content: str) -> dict:
        return {
            "en": f"{user} send you a private message {content}",
            "ru": f"{user} отправил(а) вам личное сообщение: {content}",
            "zh": f"{user} 私信你 {content}"
        }

    @staticmethod
    def follow_meme(user: str) -> dict:
        return {
            "en": f"{user} started following your Meme!",
            "ru": f"{user} начал(а) фолловить ваш Meme!",
            "zh": f"{user} 开始 follow 你的 Meme！"
        }

    @staticmethod
    def post_save(user: str) -> dict:
        return {
            "en": f"{user} saved your content — it’s worth keeping!",
            "ru": f"{user} сохранил(а) ваш контент — стоит оставить себе!",
            "zh": f"{user} 保存了你的内容 —— 值得收藏！"
        }

    @staticmethod
    def token_destructing() -> dict:
        return {
            "en": "Congratulations! Your Meme has entered the Memefans Promotion!",
            "ru": "Поздравляем! Ваш Meme был добавлен в Memefans Promotion!",
            "zh": "恭喜！你持有的Meme进入了Memefans Promotion通道！"
        }

    @staticmethod
    def token_destructed() -> dict:
        return {
            "en": "Your meme was repurchased by Memefans — value up by xx%!",
            "ru": "Ваш мем был выкуплен Memefans — цена выросла на xx%!",
            "zh": "你的 Meme 被 Memefans 官方回购 —— 价值飙升 xx%！"
        }

    @staticmethod
    def commit_submit(meme: str) -> dict:
        return {
            "en": f"Your content has been promoted to {meme}. Click to view.",
            "ru": f"Ваш контент продвинут до «{meme}»! Кликните, чтобы посмотреть.",
            "zh": f"你的内容被推广到 {meme}，可点击进入。"
        }

    @staticmethod
    def commit_reject(user: str) -> dict:
        return {
            "en": f"{user} adjusted the content strategy，your commit was not approve.",
            "ru": f"{user} изменил(а) стратегию контента, поэтому ваша заявка не была одобрена.",
            "zh": f"{user} 调整了内容策略，你的提交未通过。"
        }

    @staticmethod
    def commit_approve(user: str) -> dict:
        return {
            "en": f"{user} approved your commit — it’s live now!",
            "ru": f"{user} одобрил(а) ваш коммит — он уже в эфире!",
            "zh": f"{user} 通过了你的 commit —— 已经上线啦！"
        }

    @staticmethod
    def get_by_type(notification_type: NotificationType, meta: dict) -> dict | None:
        if notification_type == NotificationType.author_follow:
            assert hasattr(meta, 'follower_username'), "Meta must contain 'follower_username' for author_follow notification"
            return ContentGenerator.author_follow(meta.get("follower_username"))
        elif notification_type == NotificationType.post_upvote:
            assert hasattr(meta, 'author_username'), "Meta must contain 'author_username' for post_upvote notification"
            return ContentGenerator.post_upvote(meta.get("author_username"))
        elif notification_type == NotificationType.question_subscribe:
            return ContentGenerator.question_subscribe()
        elif notification_type == NotificationType.question_answer:
            return ContentGenerator.question_answer()
        elif notification_type == NotificationType.article_update:
            return ContentGenerator.article_update()
        elif notification_type == NotificationType.post_comment:
            assert hasattr(meta, 'comment_author_username') and hasattr(meta, 'comment_text'), "Meta must contain 'comment_author_username' and 'comment_text' for post_comment notification"
            return ContentGenerator.post_comment(meta.get("comment_author_username"), meta.get("comment_text"))
        elif notification_type == NotificationType.collection_update:
            return ContentGenerator.collection_update()
        elif notification_type == NotificationType.follow_meme:
            assert hasattr(meta, 'follower_username'), "Meta must contain 'follower_username' for author_follow notification"
            return ContentGenerator.follow_meme(meta.get("follower_username"))
        elif notification_type == NotificationType.post_commit:
            return ContentGenerator.post_commit()
        elif notification_type == NotificationType.translation_created:
            return ContentGenerator.translation_created()
        elif notification_type == NotificationType.new_message:
            assert hasattr(meta, 'user') and hasattr(meta, 'content'), "Meta must contain 'user' and 'content' for new_message notification"
            return ContentGenerator.new_message(meta.get("user"), meta.get("content"))
        elif notification_type == NotificationType.commit_approve:
            return ContentGenerator.commit_approve(meta.get("collection_author_username"))
        elif notification_type == NotificationType.commit_reject:
            return ContentGenerator.commit_reject(meta.get("collection_author_username"))
        elif notification_type == NotificationType.commit_submit:
            return ContentGenerator.commit_submit(meta.get("commit_collection_title"))
        elif notification_type == NotificationType.post_save:
            return ContentGenerator.post_save(meta.get("collection_author_username"))
        return None


class EType(str, Enum):
    author_follow = "author_follow"
    author_new_post = "author_new_post"
    author_new_question = "author_new_question"
    question_subscribe = "question_subscribe"
    question_answer = "question_answer"
    question_answer_update = "question_answer_update"
    article_update = "article_update"
    post_upvote = "post_upvote"
    post_comment = "post_comment"
    post_commit = "post_commit"
    post_save = "post_save"
    collection_subscribe = "collection_subscribe"
    collection_update = "collection_update"
    invitation_accept = "invitation_accept"
    invitation_expiration = "invitation_expiration"
    commit_submit = "commit_submit"
    commit_approve = "commit_approve"
    commit_reject = "commit_reject"
    translation_created = "translation_created"
    translation_updated = "translation_updated"
    new_message = "new_message"
