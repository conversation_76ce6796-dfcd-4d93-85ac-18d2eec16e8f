import logging
from typing import List, Optional

from httpx import AsyncClient
from fastapi import Depends
from sqlalchemy import select

from src.database.session import AsyncSession, get_session
from src.database.models import Device
from src.notifications.settings import settings


class PushService:
    url = "https://onesignal.com/api/v1/notifications"
    client = AsyncClient()
    auth_token: str = settings.ONESIGNAL_AUTH_TOKEN
    app_id: str = settings.ONESIGNAL_APP_ID
    headers = {
        "Accept": "application/json",
        "Authorization": f"Basic {auth_token}",
        "Content-Type": "application/json"
    }

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_user_players(self, author_id: str) -> List[str]:
        stmt = select(Device).where(Device.author_id == author_id)
        result = (await self.session.execute(stmt)).scalars()
        return [device.player_id for device in result]

    async def get_players(self, authors: List[str]) -> List[str]:
        players = []
        for author_id in authors:
            players += await self.get_user_players(author_id)
        return players

    async def send(
            self,
            users: List[str],
            contents: dict,
            headings: Optional[dict] = None,
            custom: Optional[dict] = None
    ):
        if headings is None:
            headings = {"en": "Memefans"}
        players: List[str] = await self.get_players(users)
        if len(players) < 1:
            return None

        payload = {
            "contents": contents,
            "headings": headings,
            "app_id": self.app_id,
            "include_player_ids": players,
            "name": "Memefans"
        }

        if custom is not None:
            payload['data'] = custom

        res = await self.client.post(url=self.url, headers=self.headers, json=payload)
        if res.status_code != 200:
            logging.error(f"Sending a push notification failed:\n{res.json()}")
        return

def get_push_service(
        session: AsyncSession = Depends(get_session)
):
    yield PushService(session)
