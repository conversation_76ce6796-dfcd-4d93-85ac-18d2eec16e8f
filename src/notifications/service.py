from typing import List, Optional
from datetime import datetime, timezone
import json
import os
from pathlib import Path
import re

from httpx import AsyncClient
from fastapi import Depends, HTTPException
from sqlalchemy import select, update, desc
from redis.asyncio import Redis
from starlette.status import HTTP_409_CONFLICT, HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR

from src.database.schemas import NotificationType, NotificationBase
from src.database.session import AsyncSession, get_session
from src.database.models import Notification, NotificationConfig, Commit, AppVersion
from src.notifications.logger import logger
from src.notifications.settings import settings
from src.notifications.constants import ContentGenerator
from src.notifications.push import get_push_service, PushService
from src.notifications.schemas import NotificationsCreateRequest, NotificationConfigSchema, NotificationConfigUpdateRequest, AppVersionCreateRequest
from src.common.notifications.enums import NotificationStatus, CommitReadStatus
from src.common.caching.redis import get_redis_client


class AppVersionService:
    """App版本信息服务"""
    
    def __init__(self, session: AsyncSession, redis: Redis):
        self.session = session
        self.redis = redis
        self.cache_key = "app:latest_version"
        self.cache_ttl = 300  # 5分钟缓存
    
    def _parse_version(self, version: str) -> tuple:
        """
        解析版本号为可比较的元组
        支持格式: x.y.z, x.y.z-alpha, x.y.z-beta.1, x.y.z-rc.1等
        """
        try:
            # 移除版本号前的'v'前缀（如果有的话）
            version = version.lower().lstrip('v')
            
            # 分离主版本号和预发布标识
            if '-' in version:
                main_version, pre_release = version.split('-', 1)
            else:
                main_version, pre_release = version, ''
            
            # 解析主版本号 (major.minor.patch)
            version_parts = main_version.split('.')
            major = int(version_parts[0]) if len(version_parts) > 0 else 0
            minor = int(version_parts[1]) if len(version_parts) > 1 else 0
            patch = int(version_parts[2]) if len(version_parts) > 2 else 0
            
            # 处理预发布版本的优先级
            # 正式版本 > rc > beta > alpha
            pre_release_priority = 1000  # 正式版本的默认优先级
            pre_release_number = 0
            
            if pre_release:
                if pre_release.startswith('alpha'):
                    pre_release_priority = 100
                    # 尝试提取alpha后的数字
                    alpha_match = re.search(r'alpha\.?(\d+)', pre_release)
                    if alpha_match:
                        pre_release_number = int(alpha_match.group(1))
                elif pre_release.startswith('beta'):
                    pre_release_priority = 200
                    # 尝试提取beta后的数字
                    beta_match = re.search(r'beta\.?(\d+)', pre_release)
                    if beta_match:
                        pre_release_number = int(beta_match.group(1))
                elif pre_release.startswith('rc'):
                    pre_release_priority = 300
                    # 尝试提取rc后的数字
                    rc_match = re.search(r'rc\.?(\d+)', pre_release)
                    if rc_match:
                        pre_release_number = int(rc_match.group(1))
            
            return (major, minor, patch, pre_release_priority, pre_release_number)
            
        except (ValueError, AttributeError) as e:
            logger.warning(f"版本号解析失败: {version}, 错误: {e}")
            # 如果解析失败，返回一个最小的版本号
            return (0, 0, 0, 0, 0)
    
    def _compare_versions(self, version1: str, version2: str) -> int:
        """
        比较两个版本号
        返回值: 1 表示version1 > version2, 0 表示相等, -1 表示version1 < version2
        """
        v1_tuple = self._parse_version(version1)
        v2_tuple = self._parse_version(version2)
        
        if v1_tuple > v2_tuple:
            return 1
        elif v1_tuple < v2_tuple:
            return -1
        else:
            return 0
    
    async def get_app_version(self, client_build: int, client_os: str, client_version: Optional[str] = None):
        """获取App版本信息并判断是否需要更新（基于build号和操作系统比较）"""
        try:
            # 获取指定操作系统的最新版本信息（按build号排序）
            query = select(AppVersion).where(
                AppVersion.target_os == client_os
            ).order_by(desc(AppVersion.build)).limit(1)
            
            result = await self.session.execute(query)
            latest_version = result.scalar_one_or_none()
            
            if not latest_version:
                raise HTTPException(
                    status_code=404,
                    detail=f"No app version found for OS: {client_os}"
                )

            utc_release_date = latest_version.release_date.replace(tzinfo=timezone.utc)
            
            # 如果提供了版本号，优先使用版本号比较
            if client_version:
                version_comparison = self._compare_versions(client_version, latest_version.version)
                # 如果客户端版本号更新（>0），则不需要更新
                if version_comparison > 0:
                    need_update = False
                # 如果客户端版本号更旧（<0），则需要更新
                elif version_comparison < 0:
                    need_update = True
                # 如果版本号相同（=0），则基于build号判断
                else:
                    need_update = client_build < latest_version.build
                logger.debug(f"版本号比较: 客户端={client_version} vs 最新={latest_version.version}, 比较结果={version_comparison}, 需要更新={need_update}")
            else:
                # 如果没有提供版本号，仅基于build号判断
                need_update = client_build < latest_version.build
            
            # 强制更新逻辑
            force_update = False
            
            if need_update:
                # 如果需要更新，检查是否需要强制更新
                if client_version:
                    # 基于版本号的强制更新：主版本号差距大于等于1
                    client_major = self._parse_version(client_version)[0]
                    latest_major = self._parse_version(latest_version.version)[0]
                    if latest_major - client_major >= 1:
                        force_update = True
                else:
                    # 基于build号的强制更新：差距超过10个build
                    force_update = (latest_version.build - client_build) > 10
            
            version_data = {
                "need_update": need_update,
                "force_update": force_update,
                "latest_build": latest_version.build,
                "latest_version": latest_version.version,
                "release_date": utc_release_date.isoformat(),
                "release_notes": latest_version.release_notes or "",
                "download_url": latest_version.download_url or ""
            }
            
            logger.debug(f"版本检查: 客户端build={client_build}, version={client_version}, OS={client_os}, 最新build={latest_version.build}, 最新version={latest_version.version}, 需要更新={need_update}")
            return version_data
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get app version: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve app version information"
            )
    
    async def create_app_version(self, version_data: AppVersionCreateRequest):
        """创建新的App版本（管理员接口）"""
        try:
            # 检查build号和target_os的组合是否已存在
            existing_build_query = select(AppVersion).where(
                AppVersion.build == version_data.build,
                AppVersion.target_os == version_data.target_os
            )
            result = await self.session.execute(existing_build_query)
            existing_build = result.scalar_one_or_none()

            if existing_build:
                raise HTTPException(
                    status_code=HTTP_409_CONFLICT,
                    detail=f"Build {version_data.build} for OS {version_data.target_os} already exists"
                )
            
            # 获取当前指定OS的最新版本进行比较（按build号排序）
            current_version_query = select(AppVersion).where(
                AppVersion.target_os == version_data.target_os
            ).order_by(desc(AppVersion.build)).limit(1)
            
            current_result = await self.session.execute(current_version_query)
            current_version = current_result.scalar_one_or_none()
            
            if current_version:
                # 检查新build号必须大于当前最新build号
                if version_data.build <= current_version.build:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail=f"New build {version_data.build} must be greater than current build {current_version.build} for OS {version_data.target_os}"
                    )
                
                # 版本号比较（可选，主要依赖build号）
                comparison = self._compare_versions(version_data.version, current_version.version)
                if comparison < 0:
                    logger.warning(f"新版本号 {version_data.version} 可能小于当前版本 {current_version.version}，但build号更新，允许创建")
                
                logger.debug(f"版本创建: 新build={version_data.build} vs 当前build={current_version.build}, 新版本={version_data.version}, OS={version_data.target_os}")

            release_date = version_data.release_date
            if release_date.tzinfo is not None:
                release_date = release_date.astimezone(timezone.utc).replace(tzinfo=None)
                logger.debug(f"Transform release_date to naive datetime: {version_data.release_date} -> {release_date}")

            new_version = AppVersion(
                version=version_data.version,
                build=version_data.build,
                target_os=version_data.target_os,
                release_date=release_date,
                release_notes=version_data.release_notes,
                download_url=version_data.download_url
            )
            
            self.session.add(new_version)
            await self.session.commit()
            
            await self.redis.delete(self.cache_key)
            
            logger.info(f"新版本创建成功: {version_data.version}, build: {version_data.build}, OS: {version_data.target_os}")
            
            return {
                "status": "success",
                "message": f"App version {version_data.version} (build {version_data.build}) for {version_data.target_os} created successfully",
                "version": version_data.version
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create app version: {e}")
            await self.session.rollback()
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create app version"
            )


class NotificationService:
    def __init__(self, push_service: PushService):
        self.push_service = push_service
        self.session = push_service.session

    async def create_all(self, request_schema: NotificationsCreateRequest):
        if len(request_schema.recipients_ids) < 1:
            return
        push_contents = ContentGenerator.get_by_type(request_schema.notification_base.type, request_schema.notification_base.meta)
        push_coroutine = None
        if push_contents is not None:
            push_coroutine = self.push_service.send(
                request_schema.recipients_ids,
                contents=push_contents,
                custom={
                    "type": request_schema.notification_base.type,
                    "notification": request_schema.notification_base.model_dump()
                }
            )

        notification_data = request_schema.notification_base.model_dump()
        for recipient_id in request_schema.recipients_ids:
            model: Notification = Notification(**notification_data, recipient_id=recipient_id)
            self.session.add(model)
        await self.session.commit()

        if push_coroutine:
            await push_coroutine

    async def create_notification(
            self,
            notification_type: NotificationType,
            recipients_ids: List[str],
            meta: dict
    ):
        try:
            notification_base = NotificationBase(
                type=notification_type,
                meta=meta
            )
            request_schema = NotificationsCreateRequest(
                recipients_ids=recipients_ids,
                notification_base=notification_base
            )
            await self.create_all(request_schema)
            logger.debug(f"Created notifications for recipients: {recipients_ids}")
            return True
        except Exception as e:
            logger.error(f"Creating notifications failed with the following exception:\n{e}")
            return False

    async def create_config(self, user_id: str, config_schema: NotificationConfigUpdateRequest):
        """创建或更新用户的通知配置"""
        try:
            query = select(NotificationConfig).where(NotificationConfig.user_id == user_id)
            result = await self.session.execute(query)
            existing_config = result.scalar_one_or_none()
            
            config_data = config_schema.model_dump(exclude_unset=True, exclude_none=True)
            
            if existing_config:
                for key, value in config_data.items():
                    setattr(existing_config, key, value)
                await self.session.commit()
                
                config_dict = {
                    "user_id": user_id,
                    "app_updates": existing_config.app_updates,
                    "new_comments": existing_config.new_comments,
                    "likes": existing_config.likes,
                    "users_updates": existing_config.users_updates,
                    "memes_updates": existing_config.memes_updates,
                    "direct_messages": existing_config.direct_messages
                }
                return {"status": "updated", "config": config_dict}
            else:
                config = NotificationConfig(
                    user_id=user_id,
                    **config_data
                )
                self.session.add(config)
                await self.session.commit()
                
                config_dict = {
                    "user_id": user_id,
                    "app_updates": config.app_updates,
                    "new_comments": config.new_comments,
                    "likes": config.likes,
                    "users_updates": config.users_updates,
                    "memes_updates": config.memes_updates,
                    "direct_messages": config.direct_messages
                }
                return {"status": "created", "config": config_dict}
        except Exception as e:
            logger.error(f"创建通知配置失败，错误：{e}")
            await self.session.rollback()
            raise e
            
    async def update_config(self, user_id: str, update_schema: NotificationConfigUpdateRequest):
        """更新用户的通知配置"""
        try:
            query = select(NotificationConfig).where(NotificationConfig.user_id == user_id)
            result = await self.session.execute(query)
            config = result.scalar_one_or_none()
            
            if not config:
                config = NotificationConfig(user_id=user_id)
                self.session.add(config)
            
            update_data = update_schema.model_dump(exclude_unset=True, exclude_none=True)
            for key, value in update_data.items():
                setattr(config, key, value)
                
            await self.session.commit()
            
            query = select(NotificationConfig).where(NotificationConfig.user_id == user_id)
            result = await self.session.execute(query)
            updated_config = result.scalar_one()
            
            return {
                "status": "updated",
                "config": {
                    "user_id": updated_config.user_id,
                    "app_updates": updated_config.app_updates,
                    "new_comments": updated_config.new_comments,
                    "likes": updated_config.likes,
                    "users_updates": updated_config.users_updates,
                    "memes_updates": updated_config.memes_updates,
                    "direct_messages": updated_config.direct_messages
                }
            }
        except Exception as e:
            logger.error(f"更新通知配置失败，错误：{e}")
            await self.session.rollback()
            raise e
            
    async def get_config(self, user_id: str):
        """获取用户的通知配置"""
        try:
            query = select(NotificationConfig).where(NotificationConfig.user_id == user_id)
            result = await self.session.execute(query)
            config = result.scalar_one_or_none()
            
            if not config:
                return {
                    "user_id": user_id,
                    "app_updates": True,
                    "new_comments": True,
                    "likes": True,
                    "users_updates": True,
                    "memes_updates": True,
                    "direct_messages": True
                }
                
            return {
                "user_id": config.user_id,
                "app_updates": config.app_updates,
                "new_comments": config.new_comments,
                "likes": config.likes,
                "users_updates": config.users_updates,
                "memes_updates": config.memes_updates,
                "direct_messages": config.direct_messages
            }
        except Exception as e:
            logger.error(f"Failed to get notification config: {e}")
            raise e

    async def mark_notifications_as_read(self, user_id: str, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None):
        """根据时间范围标记用户的通知为已读"""
        try:
            query = update(Notification).where(
                Notification.recipient_id == user_id,
                Notification.status != NotificationStatus.read
            )
            
            if start_time:
                # 转换为不带时区的UTC时间
                if start_time.tzinfo is not None:
                    start_time = start_time.utctimetuple()
                    start_time = datetime(*start_time[:6])
                query = query.where(Notification.created_at >= start_time)
            if end_time:
                if end_time.tzinfo is not None:
                    end_time = end_time.utctimetuple()
                    end_time = datetime(*end_time[:6])
                query = query.where(Notification.created_at <= end_time)
            
            query = query.values(status=NotificationStatus.read)
            
            result = await self.session.execute(query)
            await self.session.commit()
            
            updated_count = result.rowcount
            
            logger.info(f"Marked {updated_count} notifications as read for user {user_id}")
            
            return {
                "status": "success",
                "updated_count": updated_count,
                "message": f"Successfully marked {updated_count} notifications as read"
            }
            
        except Exception as e:
            logger.error(f"Failed to mark notifications as read: {e}")
            await self.session.rollback()
            raise e

    async def mark_commits_as_read(self, user_id: str, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None):
        """根据时间范围标记用户的commit为已读"""
        try:
            query = update(Commit).where(
                Commit.author_id == user_id,
                Commit.read_status != CommitReadStatus.read
            )
            if start_time:
                if start_time.tzinfo is not None:
                    start_time = start_time.utctimetuple()
                    start_time = datetime(*start_time[:6])
                query = query.where(Commit.created_at >= start_time)
            if end_time:
                if end_time.tzinfo is not None:
                    end_time = end_time.utctimetuple()
                    end_time = datetime(*end_time[:6])
                query = query.where(Commit.created_at <= end_time)
            
            query = query.values(read_status=CommitReadStatus.read)
            
            result = await self.session.execute(query)
            await self.session.commit()
            
            updated_count = result.rowcount
            
            logger.info(f"Marked {updated_count} commits as read for user {user_id}")
            
            return {
                "status": "success",
                "updated_count": updated_count,
                "message": f"Successfully marked {updated_count} commits as read"
            }
            
        except Exception as e:
            logger.error(f"Failed to mark commits as read: {e}")
            await self.session.rollback()
            raise e

def get_notification_service(
        push_service: PushService = Depends(get_push_service)
):
    yield NotificationService(push_service)

def get_app_version_service(
    session: AsyncSession = Depends(get_session),
    redis: Redis = Depends(get_redis_client)
):
    """获取App版本服务实例"""
    return AppVersionService(session, redis)
