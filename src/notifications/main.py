import uuid
from typing import Optional, List

from fastapi import FastAPI, Depends, status, HTTPException, Path
from fastapi.openapi.utils import get_openapi
from src.infra.logger import get_logger
from src.infra.app import create_app

from src.database.session import AsyncSession, get_session
from src.database.models import Notification, User
from src.notifications.schemas import (
    NotificationsCreateRequest, NotificationConfigSchema, NotificationConfigUpdateRequest,
    HealthResponse, NotificationConfigResponse, ConfigStatusResponse, NotificationsCreateResponse,
    NotificationMarkReadRequest, NotificationMarkReadResponse,
    CommitMarkReadRequest, CommitMarkReadResponse, AppVersionResponse, AppVersionCreateRequest, AppVersionCreateResponse
)
from src.notifications.service import NotificationService, get_notification_service, AppVersionService, get_app_version_service
from src.auth.dependecies import current_user


logger = get_logger("notifications", level="INFO", file_path="latest.log")
app = create_app(title="Notification service", version="1.0.0", description="Notification service API", request_logger=logger)



 


@app.get("/health", status_code=status.HTTP_200_OK, response_model=HealthResponse)
async def health():
    return {"status": "ok"}

@app.post(
    "/",
    status_code=status.HTTP_200_OK,
    response_model=NotificationsCreateResponse
)
async def create_notifications(
        request_schema: NotificationsCreateRequest,
        notification_service: NotificationService = Depends(get_notification_service)
):
    return await notification_service.create_all(request_schema)


@app.post(
    "/settings/config",
    tags=["settings"],
    status_code=status.HTTP_200_OK,
    response_model=ConfigStatusResponse
)
async def create_config(
        config_schema: NotificationConfigSchema,
        notification_service: NotificationService = Depends(get_notification_service),
        user: User = Depends(current_user)
):
    """创建或更新当前用户的通知配置"""
    return await notification_service.create_config(user.id, config_schema)

@app.get(
    "/settings/config",
    tags=["settings"],
    status_code=status.HTTP_200_OK,
    response_model=NotificationConfigResponse
)
async def get_my_config(
        notification_service: NotificationService = Depends(get_notification_service),
        user: User = Depends(current_user)
):
    """获取当前用户的通知配置"""
    return await notification_service.get_config(user.id)

@app.put(
    "/settings/config",
    tags=["settings"],
    status_code=status.HTTP_200_OK,
    response_model=ConfigStatusResponse
)
async def update_my_config(
        update_schema: NotificationConfigUpdateRequest,
        notification_service: NotificationService = Depends(get_notification_service),
        user: User = Depends(current_user)
):
    """更新当前用户的通知配置"""
    return await notification_service.update_config(user.id, update_schema)

@app.put(
    "/mark_read",
    status_code=status.HTTP_200_OK,
    response_model=NotificationMarkReadResponse
)
async def mark_notifications_as_read(
        request_schema: NotificationMarkReadRequest,
        notification_service: NotificationService = Depends(get_notification_service),
        user: User = Depends(current_user)
):
    """根据时间范围标记当前用户的通知为已读"""
    return await notification_service.mark_notifications_as_read(
        user.id, 
        request_schema.start_time, 
        request_schema.end_time
    )

@app.put(
    "/commits/mark_read",
    status_code=status.HTTP_200_OK,
    response_model=CommitMarkReadResponse
)
async def mark_commits_as_read(
        request_schema: CommitMarkReadRequest,
        notification_service: NotificationService = Depends(get_notification_service),
        user: User = Depends(current_user)
):
    """根据时间范围标记当前用户的commit为已读"""
    return await notification_service.mark_commits_as_read(
        user.id, 
        request_schema.start_time, 
        request_schema.end_time
    )

@app.get(
    "/app/version",
    status_code=status.HTTP_200_OK,
    response_model=AppVersionResponse,
    tags=["app"]
)
async def get_app_version(
        build: int,
        os: str,
        version: Optional[str] = None,
        app_version_service: AppVersionService = Depends(get_app_version_service)
):
    """获取最新App版本信息并判断是否需要更新"""
    return await app_version_service.get_app_version(build, os, version)

@app.post(
    "/app/update_version",
    status_code=status.HTTP_201_CREATED,
    response_model=AppVersionCreateResponse,
    tags=["app"]
)
async def create_app_version(
        version_data: AppVersionCreateRequest,
        app_version_service: AppVersionService = Depends(get_app_version_service),
        user: User = Depends(current_user)
):
    """创建新的App版本（管理员接口，用于CI/CD Pipeline）"""
    if not user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")
    return await app_version_service.create_app_version(version_data)
