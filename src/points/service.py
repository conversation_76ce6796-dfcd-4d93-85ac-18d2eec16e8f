from typing import Optional
from decimal import Decimal

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session


from .constants import PointsAction, PointsStatus
from ..database.models import Point


class PointsService:

    def __init__(self, session: AsyncSession):
        self.session = session

    def add_points(
            self,
            author_id: str,
            amount: float,
            action: PointsAction,
            meta: Optional[dict] = None
    ):
        status = PointsStatus.ISSUED if amount <= 0 else PointsStatus.PENDING
        amount = Decimal(amount).quantize(Decimal("1.00"))
        model: Point = Point(
            amount=amount,
            action=action,
            status=status,
            meta=meta if meta else {}
        )
        self.session.add(model)
        self.session.commit()
        self.session.refresh(model)
        return model

    @classmethod
    def issue_pending(cls, session: AsyncSession):
        stmt = f"""
            UPDATE points
            SET status='{PointsStatus.ISSUED}'
            WHERE status='{PointsStatus.PENDING}' AND NOW() > created_at + INTERVAL '60 seconds'
        """
        session.execute(stmt)
        session.commit()

    def lock_points(self, author_id: str) -> None:
        stmt = f"""
            UPDATE points
            SET status='{PointsStatus.LOCKED}'
            WHERE status='{PointsStatus.ISSUED}' AND author_id='{author_id}'
        """
        self.session.execute(stmt)
        self.session.commit()

    def unlock_points(self, author_id: str) -> None:
        stmt = f"""
            UPDATE points
            SET status='{PointsStatus.ISSUED}'
            WHERE status='{PointsStatus.LOCKED}' AND author_id='{author_id}'
        """
        self.session.execute(stmt)
        self.session.commit()

    def spend_locked_points(self, author_id: str) -> None:
        stmt = f"""
            UPDATE points
            SET status='{PointsStatus.SPENT}'
            WHERE status='{PointsStatus.LOCKED}' AND author_id='{author_id}'
        """
        self.session.execute(stmt)
        self.session.commit()

    def get_amount(self, author_id: str) -> int:
        stmt = f"""
            SELECT SUM(amount)
            FROM points
            WHERE status='{PointsStatus.ISSUED}' AND author_id = '{author_id}'
        """
        points: int = self.session.execute(stmt).scalar() or 0
        return points


def get_points_service(
        session: AsyncSession = Depends(get_session)
):
    yield PointsService(session)
