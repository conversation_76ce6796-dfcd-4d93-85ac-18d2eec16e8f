# 数据分析服务

这个模块提供了帖子分析数据的上传和存储功能。

## 功能特性

- 📊 **数据上传**: 支持批量上传帖子分析数据
- 💾 **数据存储**: 自动保存到 PostgreSQL 数据库
- 🛡️ **错误处理**: 完善的异常处理和日志记录

## 数据库表结构

### post_analytics 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | SERIAL | 主键，自增 |
| post_id | VARCHAR(255) | 帖子ID |
| click_count | INTEGER | 点击次数 |
| exposure_duration | FLOAT | 曝光时长（秒） |
| author_id | VARCHAR(255) | 作者ID |
| timestamp | TIMESTAMP | 数据时间戳 |
| metadata | TEXT | 额外元数据（JSON格式） |
| created_at | TIMESTAMP | 记录创建时间 |
| updated_at | TIMESTAMP | 记录更新时间 |

## API 接口

### 上传分析数据

**POST** `/analytics/upload`

上传帖子分析数据到数据库。

**请求体示例:**
```json
{
  "data": [
    {
      "post_id": "post_123",
      "click_count": 15,
      "exposure_duration": 120.5,
      "author_id": "author_456",
      "timestamp": "2024-01-15T10:30:00Z"
    },
    {
      "post_id": "post_124",
      "click_count": 8,
      "exposure_duration": 85.2,
      "author_id": "author_789"
    }
  ]
}
```

**响应示例:**
```json
{
  "status": "success",
  "message": "成功保存 2 条数据到数据库",
  "processed_count": 2,
  "saved_ids": [1, 2],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 安装和设置

### 1. 创建数据库表

运行以下命令创建 `post_analytics` 表：

```bash
# 创建表
python -m src.analytics.scripts.create_analytics_table

# 删除表（用于测试）
python -m src.analytics.scripts.create_analytics_table --drop
```

### 2. 启动服务

```bash
# 启动分析服务
python src/analytics/main.py
```

服务将在 `http://localhost:8000` 启动。

## 使用示例

### Python 客户端示例

```python
import requests
import json

# 上传分析数据
data = {
    "data": [
        {
            "post_id": "post_123",
            "click_count": 15,
            "exposure_duration": 120.5,
            "author_id": "author_456"
        }
    ]
}

response = requests.post(
    "http://localhost:8000/analytics/upload",
    json=data,
    headers={"Content-Type": "application/json"}
)

print(response.json())
```

### cURL 示例

```bash
# 上传数据
curl -X POST "http://localhost:8000/analytics/upload" \
  -H "Content-Type: application/json" \
  -d '{
    "data": [
      {
        "post_id": "post_123",
        "click_count": 15,
        "exposure_duration": 120.5,
        "author_id": "author_456"
      }
    ]
  }'
```

## 错误处理

所有接口都包含完善的错误处理：

- **400 Bad Request**: 请求数据格式错误
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 日志记录

服务会自动记录以下日志：

- 数据接收和处理过程
- 数据库操作结果
- 错误和异常信息

日志文件位置：`latest.log`

## 扩展功能

### 添加新的分析指标

1. 在 `PostAnalytics` 模型中添加新字段
2. 更新数据模型 `PostAnalyticsData`
3. 修改服务类中的保存逻辑
4. 运行数据库迁移脚本

### 添加数据聚合功能

可以在 `AnalyticsService` 中添加更多聚合方法：

- 按时间范围聚合
- 按作者聚合
- 按帖子类型聚合

### 添加缓存功能

对于频繁查询的数据，可以添加 Redis 缓存来提高性能。

## 注意事项

1. **数据验证**: 所有输入数据都会进行验证
2. **事务安全**: 使用数据库事务确保数据一致性
3. **性能优化**: 表已创建适当的索引以提高查询性能
4. **日志记录**: 所有操作都有详细的日志记录
