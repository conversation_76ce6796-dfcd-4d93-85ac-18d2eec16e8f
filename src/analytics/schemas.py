from datetime import datetime
from typing import List
from pydantic import BaseModel, Field


def get_utc_now() -> datetime:
    """获取当前UTC时间（无时区）"""
    return datetime.utcnow()


# 数据分析数据模型
class PostAnalyticsData(BaseModel):
    post_id: str = Field(..., description="帖子ID")
    click_count: int = Field(..., ge=0, description="点击次数")
    exposure_duration: float = Field(..., ge=0, description="曝光时长（秒）")
    user_id: str = Field(..., description="用户ID")
    timestamp: datetime = Field(default_factory=get_utc_now, description="数据时间戳")


class AnalyticsUploadRequest(BaseModel):
    data: List[PostAnalyticsData] = Field(..., description="分析数据列表")
