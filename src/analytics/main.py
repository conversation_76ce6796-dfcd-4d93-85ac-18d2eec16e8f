from fastapi import FastAPI, status, HTTPException, Depends, BackgroundTasks
from datetime import datetime

from src.infra.logger import get_logger
from src.infra.app import create_app
from .dependencies import get_analytics_service
from .service import AnalyticsService
from .schemas import PostAnalyticsData, AnalyticsUploadRequest

logger = get_logger("analytics", level="INFO", file_path="latest.log")
app = create_app(title="Analytics", description="Analytics API", version="1.0", request_logger=logger)


@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}


@app.post("/analytics/upload", status_code=status.HTTP_200_OK)
async def upload_analytics_data(
    request: AnalyticsUploadRequest,
    background_tasks: BackgroundTasks,
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """上传帖子分析数据"""
    # 验证数据
    if not request.data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="分析数据不能为空"
        )
    
    # 准备数据用于存储
    analytics_data_list = []
    for item in request.data:
        # 处理时区问题：确保timestamp是无时区的datetime
        timestamp = item.timestamp
        if timestamp and timestamp.tzinfo is not None:
            # 如果有时区信息，转换为UTC时间并移除时区信息
            timestamp = timestamp.astimezone().replace(tzinfo=None)
        elif timestamp is None:
            # 如果没有提供timestamp，使用当前UTC时间
            timestamp = datetime.utcnow()
        
        data_dict = {
            "post_id": item.post_id,
            "click_count": item.click_count,
            "exposure_duration": item.exposure_duration,
            "user_id": item.user_id,
            "timestamp": timestamp,
            "extra_data": None
        }
        analytics_data_list.append(data_dict)
    
    # 保存到数据库
    background_tasks.add_task(analytics_service.save_analytics_data, analytics_data_list)
    
    return {
        "message": "数据保存任务已提交到后台执行",
        "processed_count": len(analytics_data_list)
    }


