from typing import List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from src.database.models.PostAnalytics import PostAnalytics
from src.infra.logger import get_logger

logger = get_logger("analytics_service", level="INFO", file_path="latest.log")


class AnalyticsService:
    """数据分析服务类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    def _normalize_timestamp(self, timestamp) -> datetime:
        """标准化时间戳，确保是无时区的datetime"""
        if timestamp is None:
            return datetime.utcnow()
        
        if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo is not None:
            # 如果有时区信息，转换为UTC时间并移除时区信息
            return timestamp.astimezone().replace(tzinfo=None)
        
        return timestamp
    
    async def save_analytics_data(self, analytics_data_list: List[dict]) -> List[PostAnalytics]:
        """
        保存分析数据到数据库
        
        Args:
            analytics_data_list: 分析数据列表，每个元素包含 post_id, click_count, exposure_duration, user_id, timestamp
            
        Returns:
            List[PostAnalytics]: 保存的数据记录列表
        """
        try:
            saved_records = []
            
            for data in analytics_data_list:
                # 标准化时间戳
                normalized_timestamp = self._normalize_timestamp(data.get("timestamp"))
                
                # 创建新的分析记录
                analytics_record = PostAnalytics(
                    post_id=data["post_id"],
                    click_count=data["click_count"],
                    exposure_duration=data["exposure_duration"],
                    user_id=data["user_id"],
                    timestamp=normalized_timestamp,
                    extra_data=data.get("extra_data")
                )
                
                # 添加到会话
                self.session.add(analytics_record)
                saved_records.append(analytics_record)
                
                logger.info(f"准备保存分析数据: post_id={data['post_id']}, "
                           f"clicks={data['click_count']}, duration={data['exposure_duration']}s")
            
            # 提交事务
            await self.session.commit()
            
            logger.info(f"成功保存 {len(saved_records)} 条分析数据")
            return saved_records
            
        except Exception as e:
            # 回滚事务
            await self.session.rollback()
            logger.error(f"保存分析数据时发生错误: {str(e)}")
            raise
