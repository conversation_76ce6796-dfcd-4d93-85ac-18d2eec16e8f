#!/usr/bin/env python3
"""
创建 post_analytics 表的脚本

使用方法:
python -m src.analytics.scripts.create_analytics_table
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from sqlalchemy import text
from src.database.session import get_session_context


async def create_analytics_table():
    """创建 post_analytics 表"""
    
    # SQL 创建表的语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS post_analytics (
        id SERIAL PRIMARY KEY,
        post_id VARCHAR(255) NOT NULL,
        click_count INTEGER NOT NULL DEFAULT 0,
        exposure_duration FLOAT NOT NULL DEFAULT 0.0,
        user_id VARCHAR(255) NOT NULL,
        timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
        extra_data TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
    """
    
    # 创建索引的 SQL 语句
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_post_analytics_post_id ON post_analytics(post_id);",
        "CREATE INDEX IF NOT EXISTS idx_post_analytics_user_id ON post_analytics(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_post_analytics_timestamp ON post_analytics(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_post_analytics_post_user ON post_analytics(post_id, user_id);",
    ]
    
    try:
        async with get_session_context() as session:
            # 创建表
            await session.execute(text(create_table_sql))
            await session.commit()
            print("✅ 成功创建 post_analytics 表")
            
            # 创建索引
            for index_sql in create_indexes_sql:
                await session.execute(text(index_sql))
            await session.commit()
            print("✅ 成功创建所有索引")
            
            # 验证表是否存在
            result = await session.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'post_analytics'
                );
            """))
            table_exists = result.scalar()
            
            if table_exists:
                print("✅ 表创建验证成功")
            else:
                print("❌ 表创建验证失败")
                
    except Exception as e:
        print(f"❌ 创建表时发生错误: {str(e)}")
        raise


async def drop_analytics_table():
    """删除 post_analytics 表（用于测试）"""
    
    drop_table_sql = "DROP TABLE IF EXISTS post_analytics CASCADE;"
    
    try:
        async with get_session_context() as session:
            await session.execute(text(drop_table_sql))
            await session.commit()
            print("✅ 成功删除 post_analytics 表")
            
    except Exception as e:
        print(f"❌ 删除表时发生错误: {str(e)}")
        raise


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="管理 post_analytics 表")
    parser.add_argument("--drop", action="store_true", help="删除表而不是创建")
    
    args = parser.parse_args()
    
    if args.drop:
        await drop_analytics_table()
    else:
        await create_analytics_table()


if __name__ == "__main__":
    asyncio.run(main())
