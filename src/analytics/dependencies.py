from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from .service import AnalyticsService


async def get_analytics_service(
    session: AsyncSession = Depends(get_session)
) -> AnalyticsService:
    """
    获取 AnalyticsService 实例
    
    Args:
        session: 数据库会话（自动注入）
        
    Returns:
        AnalyticsService: 配置好的服务实例
    """
    return AnalyticsService(session)
