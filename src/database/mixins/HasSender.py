from sqlalchemy import Column, String
from sqlalchemy.orm import declared_attr, declarative_mixin, relationship
from sqlalchemy.sql.schema import Foreign<PERSON>ey


@declarative_mixin
class HasSender:
    @declared_attr
    def sender_id(cls):
        return Column(String(), ForeignKey("authors.id", ondelete="CASCADE"), nullable=False)

    # @declared_attr
    # def sender(cls):
    #     return relationship("Author", foreign_keys=[cls.sender_id], lazy="selectin")
