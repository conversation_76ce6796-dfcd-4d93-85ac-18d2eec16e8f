from sqlalchemy import Column, String
from sqlalchemy.orm import declared_attr, declarative_mixin, relationship
from sqlalchemy.sql.schema import ForeignKey


@declarative_mixin
class HasTargetPost:
    @declared_attr
    def target_post_id(cls):
        return Column(String(), ForeignKey("posts.id", ondelete="CASCADE"), nullable=False)

    # @declared_attr
    # def target_post(cls):
    #     return relationship("Post", foreign_keys=[cls.target_post_id], lazy="selectin")
