from sqlalchemy import Column, String
from sqlalchemy.orm import declared_attr
from sqlalchemy.orm import declarative_mixin
from sqlalchemy.sql.schema import ForeignKey
from src.database.mixins.utils import generate_shortid


@declarative_mixin
class HasPostID:
    @declared_attr
    def id(cls):
        return Column(
            String(),
            ForeignKey("posts.id", ondelete="CASCADE"),
            primary_key=True,
            default=generate_shortid,
        )
