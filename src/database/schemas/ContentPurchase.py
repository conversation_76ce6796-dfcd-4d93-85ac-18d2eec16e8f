from pydantic import BaseModel
from datetime import datetime
from typing import Optional

from src.common.constants import ContentPurchaseStatus


class ContentPurchaseBase(BaseModel):
    """Base schema for ContentPurchase"""
    user_id: str
    post_id: str
    usd_amount_cents: int
    user_transaction_id: int
    status: str = ContentPurchaseStatus.CONFIRMED


class ContentPurchaseCreate(ContentPurchaseBase):
    """Schema for creating ContentPurchase"""
    pass


class ContentPurchaseRead(ContentPurchaseBase):
    """Schema for reading ContentPurchase"""
    id: str
    purchased_at: datetime
    
    class Config:
        from_attributes = True


class ContentPurchaseWithTransaction(ContentPurchaseRead):
    """ContentPurchase with transaction details"""
    transaction_details: Optional[dict] = None