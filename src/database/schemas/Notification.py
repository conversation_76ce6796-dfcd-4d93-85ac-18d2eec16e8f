from enum import Enum
from uuid import UUID
from datetime import datetime

from pydantic import BaseModel, ConfigDict


class NotificationStatus(str, Enum):
    CREATED = "created"
    DELIVERED = "delivered"
    READ = "read"


class NotificationType(str, Enum):
    author_follow = "author_follow"
    author_new_post = "author_new_post"
    author_new_question = "author_new_question"
    question_subscribe = "question_subscribe"
    question_answer = "question_answer"
    question_answer_update = "question_answer_update"
    article_update = "article_update"
    post_upvote = "post_upvote"
    post_comment = "post_comment"
    post_commit = "post_commit"
    post_save = "post_save"
    collection_subscribe = "collection_subscribe"
    collection_update = "collection_update"
    invitation_accept = "invitation_accept"
    invitation_expiration = "invitation_expiration"
    commit_submit = "commit_submit"
    commit_approve = "commit_approve"
    commit_reject = "commit_reject"
    translation_created = "translation_created"
    translation_updated = "translation_updated"
    new_message = "new_message"
    follow_meme = "follow_meme"


class NotificationBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    type: NotificationType
    meta: dict


class NotificationCreate(NotificationBase):
    model_config = ConfigDict(from_attributes=True)

    recipient_id: str


class NotificationRead(NotificationBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    status: NotificationStatus
    recipient_id: str
    created_at: datetime

