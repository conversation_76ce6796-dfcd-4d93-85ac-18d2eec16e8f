from typing import Optional

from strenum import StrEnum

from src.database.schemas import Post


class UrlType(StrEnum):
    EMBEDDED = "embedded"
    CLOUDFLARE = "cloudflare"


class ProcessingStatus(StrEnum):
    PROCESSING = "processing"
    COMPLETED = "completed"


class VideoRead(Post):
    title: Optional[str]
    cover: Optional[str]
    description: Optional[str]
    width: Optional[int]
    height: Optional[int]

    url: str
    url_type: Optional[UrlType]
    processing_status: Optional[ProcessingStatus]
