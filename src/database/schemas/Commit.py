from strenum import StrE<PERSON>
from pydantic import BaseModel, ConfigDict
from typing import Optional
from datetime import datetime

from .Author import AuthorRead
from .Post import Post


class CommitStatus(StrEnum):
    NEW = "new"
    APPROVED = "accepted"
    REJECTED = "rejected"


class CommitReadStatus(StrEnum):
    READ = "read"
    UNREAD = "unread"


class CommitRead(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime
    updated_at: datetime

    collection_id: str
    post_id: str

    status: CommitStatus
    read_status: CommitReadStatus
    reason: Optional[str]
    post_id: Optional[str]
    post: Optional[Post]

    author_id: str
    author: AuthorRead

    resolved_by: Optional[str]
    resolved_at: Optional[datetime]
