from typing import Optional
from datetime import datetime

from pydantic import BaseModel, ConfigDict, Extra

from src.database.constants import PostStatus
from src.common.constants import Language


class Post(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra=Extra.ignore)

    id: str
    type: str
    status: Optional[PostStatus]

    created_at: datetime
    updated_at: datetime

    comments_count: int
    likes_count: int
    collections_count: int
    view_count: int

    region: str
    language: Language
    author_id: str

    tags: list[str]
