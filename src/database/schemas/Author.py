from typing import Optional
from datetime import date, datetime

from pydantic import BaseModel, ConfigDict, field_validator, field_serializer

from src.common.constants import UserStatus, PermissionTypes
from src.database.constants import Gender


class AuthorRead(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    username: str
    avatar: Optional[str]
    region: str
    created_at: datetime
    role: Optional[str]
    status: str
    invitation_id: Optional[str] = None

    @field_serializer("role")
    def modify_role(self, role: str) -> str:
        if self.role is not None:
            return self.role
        return PermissionTypes.user

    @field_serializer("name")
    def modify_name(self, name: str) -> str:
        if self.status == UserStatus.DELETED:
            return "Deleted"
        return name

    @field_serializer("avatar")
    def modify_avatar(self, avatar: str) -> str:
        if self.status == UserStatus.DELETED:
            return "https://images.to.ci/b5cc6681-829f-48c6-ab1d-e7a997d331e8.png"
        return avatar


class AuthorReadExtended(AuthorRead):
    email: str
    dedication: Optional[str]
    description: Optional[str]
    location: Optional[str]
    country: Optional[str]
    language: Optional[str]
    education: Optional[str]
    birthday: Optional[date]
    phone_number: Optional[str]
    likes_count: Optional[int]
    citations_count: int
    email: Optional[str]
    gender: Optional[Gender]
    original_avatar: Optional[str]
    invitation_id_owned: Optional[str] = None


class RecommenderAuthor(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    region: str
    dedication: str | None = None
    description: str | None = None
    gender: str

    @field_validator("gender", mode="before")
    @classmethod  # type: ignore
    def default_gender(cls, v: str | None):
        if not v:
            return "n"
        return v
