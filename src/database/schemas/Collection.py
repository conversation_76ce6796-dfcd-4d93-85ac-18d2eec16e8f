from typing import Optional, List, Literal
from datetime import datetime

from pydantic import ConfigDict, Extra, BaseModel

from src.database.constants import CollectionContentType
from src.database.constants import PostStatus, PostType


class CollectionRead(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra=Extra.ignore)

    id: str
    type: Literal[PostType.COLLECTION]
    status: Optional[PostStatus]

    created_at: datetime
    updated_at: datetime

    region: str
    author_id: str
    title: str
    description: Optional[str]
    cover: Optional[str]
    original_cover: str | None

    subscriber_count: int
    contributor_count: int

    content_type: CollectionContentType
    contents_count: int
    contents: List[str]
    carnival_status: str
    carnival_start_time: Optional[datetime]
    is_subscribed: Optional[bool] = False
