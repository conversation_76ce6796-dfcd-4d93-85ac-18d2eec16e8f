from typing import Optional, List
from pydantic import BaseModel, Field, field_validator

from src.database.schemas import Post


class ImageDataStored(BaseModel):
    """Image data stored in database (without upload_url)"""
    url: str
    width: int
    height: int
    name: str
    content_type: str


class ImageRead(Post):
    title: Optional[str]
    cover: str
    description: Optional[str]
    width: int
    height: int
    images_data: List[ImageDataStored] = Field(default=[], description="Multiple images data (without upload_url)")
    
    @field_validator('images_data', mode='before')
    @classmethod
    def validate_images_data(cls, v):
        """Handle None values from database and convert to empty list"""
        if v is None:
            return []
        return v
