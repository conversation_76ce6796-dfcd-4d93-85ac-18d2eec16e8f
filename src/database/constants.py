from strenum import StrEnum


class PostType(StrEnum):
    ARTICLE = "Article"
    QUESTION = "Question"
    ANSWER = "Answer"
    COLLECTION = "Collection"
    IMAGE = "Image"
    VIDEO = "Video"
    TEXT = "Text"
    COMMENT = "Comment"
    FILE = "File"

    @classmethod
    def all(cls):
        return list(map(lambda c: c.value, cls))


class PostStatus(StrEnum):
    POSTED = "posted"
    DRAFTED = "drafted"
    SCHEDULED = "scheduled"
    DELETED = "deleted"
    SUSPENDED = "suspended"


class FeedReason(StrEnum):
    AUTHORS_LIKES = "authors_likes"
    AUTHOR_CREATE = "author_create"
    QUESTION_ANSWER = "question_answer"
    COLLECTIONS_POSTS = "collections_posts"


class CollectionContentType(StrEnum):
    MIXED = "Mixed"
    VIDEOS = "Video"
    IMAGES = "Image"
    TEXTS = "Text"
    FILES = "File"
    ARTICLES = "Article"
    DISCUSSIONS = "Discussion"


class Gender(StrEnum):
    MALE = "m"
    FEMALE = "f"
    UNDEFINED = "n"


class Region(StrEnum):
    ENGLISH = "en"
    CHINESE = "zh"
    RUSSIAN = "ru"
    OTHER = "oth"
