from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.sql.sqltypes import DateTime
from datetime import datetime

from src.database.session import Base

from src.database.mixins.HasID import HasID


class View(HasID, Base):
    __tablename__ = "views"

    post_id = Column(String, ForeignKey("posts.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(String, ForeignKey("authors.id", ondelete="CASCADE"), nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    token = Column(String, nullable=True)
