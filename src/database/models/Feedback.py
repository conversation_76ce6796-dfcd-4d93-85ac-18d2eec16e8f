import uuid
import json
from datetime import datetime
from typing import List

from sqlalchemy import Column, String, Text, DateTime, JSON

from src.database import Base


class Feedback(Base):
    __tablename__ = "feedbacks"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, comment="User name")
    email = Column(String, nullable=False, comment="User email")
    description = Column(Text, nullable=False, comment="Problem description")
    category = Column(String, nullable=False, comment="Feedback category")
    sub_category = Column(String, nullable=True, comment="Feedback sub-category")
    status = Column(String, nullable=False, default="Pending", comment="Processing status")
    admin_response = Column(Text, nullable=True, comment="Admin response")
    
    # Image URLs JSON array
    images = Column(JSON, nullable=True, comment="List of image URLs")

    user_id = Column(String, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<Feedback(id={self.id}, name={self.name}, category={self.category}, status={self.status})>"
    
    def set_images(self, image_urls: List[str]):
        """Set image URL list"""
        self.images = json.dumps(image_urls)
    
    def get_images(self) -> List[str]:
        """Get image URL list"""
        if not self.images:
            return []
        return json.loads(self.images) 