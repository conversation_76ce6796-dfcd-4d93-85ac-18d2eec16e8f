from sqlalchemy import Column, ForeignKey, String, DateTime, func, text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID

from src.database.session import Base
from src.common.notifications.enums import NotificationStatus


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(UUID, server_default=text("gen_random_uuid()"), primary_key=True)
    type = Column(String, nullable=False)
    status = Column(String, nullable=False, server_default=NotificationStatus.created)
    recipient_id = Column(String, ForeignKey("authors.id", ondelete="CASCADE"), nullable=False)
    meta = Column(JSONB, nullable=False, server_default="{}")
    created_at = Column(DateTime, server_default=func.now())
