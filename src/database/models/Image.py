from sqlalchemy import Index, Column, Computed, Integer
from sqlalchemy.dialects.postgresql import JSONB

from src.database.mixins.HasPostID import HasPostID
from src.database.models.Post import Post
from src.database.mixins.HasTitle import HasTitle
from src.database.mixins.HasDescription import HasDescription
from src.database.mixins.HasCover import HasCover


class Image(HasPostID, HasTitle, HasDescription, HasCover, Post):
    __tablename__ = "images"
    __mapper_args__ = {
        "polymorphic_identity": "Image",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(title,'') || ' ' || coalesce(description,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_images___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    width = Column(Integer, nullable=False, server_default="0", comment="cover width")
    height = Column(Integer, nullable=False, server_default="0", comment="cover height")
    
    images_data = Column(JSONB, nullable=True, comment="Multiple images data including URLs and metadata")
