from sqlalchemy.sql.schema import Column, Foreign<PERSON>ey, PrimaryKeyConstraint
from sqlalchemy.sql.sqltypes import String

from src.database.session import Base


class SavedPost(Base):
    __tablename__ = "saved_posts"

    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True)
    post_id = Column(String, ForeignKey("posts.id", ondelete="CASCADE"), primary_key=True)
    collection_id = Column(String, ForeignKey("collections.id", ondelete="CASCADE"), primary_key=True)

    __table_args__ = (PrimaryKeyConstraint("user_id", "post_id", "collection_id"),)
