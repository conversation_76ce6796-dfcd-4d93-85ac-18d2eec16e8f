from sqlalchemy import Column, Foreign<PERSON>ey, String, DateTime, func, text, Boolean, Integer, Index
from sqlalchemy.dialects.postgresql import UUID

from src.database.session import Base


class NotificationConfig(Base):
    __tablename__ = "notification_configs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, ForeignKey("authors.id", ondelete="CASCADE"), nullable=False, unique=True)
    app_updates = Column(Boolean, nullable=False, default=True)
    new_comments = Column(Boolean, nullable=False, default=True)
    likes = Column(Boolean, nullable=False, default=True)
    users_updates = Column(Boolean, nullable=False, default=True)
    memes_updates = Column(Boolean, nullable=False, default=True)
    direct_messages = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_notification_config_user_id', user_id),
    ) 