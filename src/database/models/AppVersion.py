from sqlalchemy import Column, DateTime, Boolean, Text, Index, Integer
from datetime import datetime

from sqlalchemy.dialects.postgresql import JSONB

from src.database.session import Base


class AppVersion(Base):
    __tablename__ = "app_versions"

    build = Column(Integer, primary_key=True)
    target_os = Column(Text, nullable=False, index=True)
    version = Column(Text, nullable=False, index=True)
    release_date = Column(DateTime, nullable=False)
    release_notes = Column(Text, nullable=True)
    download_url = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    extra = Column(JSONB, nullable=True)

    __table_args__ = (
        Index('idx_app_version_release', 'release_date'),
    )
