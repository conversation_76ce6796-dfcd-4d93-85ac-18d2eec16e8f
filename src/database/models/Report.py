from datetime import datetime

from sqlalchemy import DateTime, Boolean, Text, Column, Integer
from sqlalchemy.dialects.postgresql import JSONB

from src.database import Base
from src.database.mixins.HasID import HasID


class Report(HasID, Base):
    __tablename__ = "reports"

    creator_id = Column(
        Text,
        nullable=True,
        index=True,
        comment="举报人ID",
    )

    target_id = Column(
        Text,
        nullable=True,
        index=True,
        comment="被举报者ID",
    )

    handler_id = Column(
        Text,
        nullable=True,
        index=True,
        comment="处理人ID",
    )

    is_handled = Column(
        Boolean,
        default=False,
        comment="是否已处理",
    )

    handled_at = Column(
        DateTime,
        nullable=True,
        comment="处理时间",
    )

    reason = Column(
        Text,
        nullable=False,
        comment="举报原因",
    )

    comment = Column(
        Text,
        nullable=True,
        comment="举报说明",
    )

    status = Column(
        Text,
        nullable=False,
        index=True,
        default="pending",
        comment="举报状态",
    )

    ex = Column(
        JSONB,
        nullable=True,
        comment="扩展信息",
    )

    created_at = Column(
        DateTime, 
        default=datetime.now,
        comment="创建时间",
    )

    updated_at = Column(
        DateTime,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )
    