from datetime import datetime
from typing import Optional

from sqlalchemy import (
    <PERSON>umn,
    BigInteger,
    Integer,
    Text,
    text,
    Numeric,
    SmallInteger,
    DateTime,
    UniqueConstraint,
    func,
)
from src.database.session import Base


class TokenHolder(Base):
    __tablename__ = 'token_holder'
    __table_args__ = (
        UniqueConstraint("address", "token_address", name="uq_token_address_holder"),
    )

    id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="Primary key, auto-increment."
    )
    chain = Column(
        Integer,
        nullable=True,
        comment="Chain ID, can be null."
    )
    user_id = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="User ID, not null, default empty string."
    )
    username = Column(
        Text,
        nullable=True,
        server_default=text("''"),
        comment="User name, not null, default empty string."
    )
    avatar = Column(
        Text,
        nullable=True,
        server_default=text("''"),
        comment="Holder avatar, not null, default empty string."
    )
    address = Column(
        Text,
        nullable=False,
        index=True,
        server_default=text("''"),
        comment="Holder address in text, default empty string."
    )
    token_address = Column(
        Text,
        nullable=False,
        index=True,
        server_default=text("''"),
        comment="Token contract address in text, default empty string."
    )
    amount = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Amount (int256), default 0."
    )
    decimals = Column(
        SmallInteger,
        nullable=False,
        server_default=text('18')
    )
    updated_at = Column(
        DateTime,
        nullable=True,
        onupdate=func.now(),
        comment="Record update time, can be null."
    )
    creator_id: str = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="Creator id in text, default empty string."
    )
