from sqlalchemy import Index, Column, Computed
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql.sqltypes import String, Text, Integer

from src.database.models.Post import Post
from src.database.mixins import HasPostID
from src.database.mixins import <PERSON>SVector
from src.database.mixins import <PERSON><PERSON><PERSON><PERSON>
from src.database.mixins.HasDescription import HasDescription
from src.database.mixins import HasCover


class Video(HasPostID, HasTitle, HasDescription, HasCover, Post):
    __tablename__ = "videos"
    __mapper_args__ = {
        "polymorphic_identity": "Video",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(title,'') || ' ' || coalesce(description,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_videos___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )
    uid = Column(Text, unique=True, nullable=True, comment="The unique id on cloudflare")
    url = Column(String(), nullable=False)
    url_type = Column(String(), nullable=True)
    processing_status = Column(String(), nullable=True)
    metainfo = Column(JSONB, nullable=True, comment="Metadata of the video")
    width = Column(Integer, nullable=False, server_default="0")
    height = Column(Integer, nullable=False, server_default="0")
    
    free_seconds = Column(Integer, nullable=True, server_default="0", comment="Free viewing seconds for this video")
