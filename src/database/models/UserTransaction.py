from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Numeric, SmallInteger, text, UniqueConstraint, Index
from sqlalchemy.sql.schema import Column, ForeignKey
from sqlalchemy.sql.sqltypes import Text, Float

from src.database.session import Base
from src.database.mixins.HasTimestamp import TimestampMixin


class UserTransaction(Base, TimestampMixin):
    __tablename__ = "user_transactions"
    
    # Define table constraints and indexes
    __table_args__ = (
        # Composite unique constraint allowing multiple transaction types for the same tx_hash
        UniqueConstraint('token_address', 'type', 'tx_hash', 
                        name='uq_user_transactions_token_type_txhash'),
        # Indexes for performance
        Index('idx_user_transactions_tx_hash', 'tx_hash'),
        Index('idx_user_transactions_type_txhash', 'type', 'tx_hash'),
        Index('idx_user_transactions_token_txhash', 'token_address', 'tx_hash'),
    )

    id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True
    )
    user_id = Column(
        Text,
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    chain = Column(
        Text,
        nullable=False,
    )
    # CREATE MEME
    # SEND TOKEN
    # RECEIVE TOKEN
    # BUY TOKEN
    # SELL TOKEN
    # PAY - SUBSCRIBE
    # BUY TOKEN FROM PANCAKE - 6
    # SELL TOKEN TO PANCAKE - 7
    # GIFT_BUY_TOKEN - 8
    # WITHDRAW_CASH - 9
    # DEPOSIT_CASH - 10
    # PAY_CONTENT - 11
    type = Column(
        SmallInteger,
        nullable=False,
        index=True,
        comment="0 CREATE MEME, 1 SEND TOKEN, 2 RECEIVE TOKEN, 3 BUY TOKEN, 4 SELL TOKEN, 5 PAY - SUBSCRIBE, 6 BUY_TOKEN_DEX, 7 SELL_TOKEN_DEX, 8 GIFT_BUY_TOKEN, 9 WITHDRAW_CASH, 10 DEPOSIT_CASH, 11 PAY_CONTENT"
    )
    order_amount = Column(
        Numeric(78, 0),
        nullable=True
    )
    base_amount = Column(
        Numeric(78, 0),
        nullable=True
    )
    quote_amount = Column(
        Numeric(78, 0),
        nullable=True
    )
    unit_price = Column(
        Float,
        nullable=True
    )
    service_fee = Column(
        Numeric(96,18),
        nullable=True
    )
    value = Column(
        Float,
        nullable=True
    )
    from_address = Column(
        Text,
        nullable=False,
        index=True
    )
    to_address = Column(
        Text,
        nullable=True,
        index=True
    )
    gas_limit = Column(
        Numeric(78, 0),
        nullable=True
    )
    gas_used = Column(
        Numeric(78, 0),
        nullable=True
    )
    token_address = Column(
        Text,
        nullable=True
    )
    tx_hash = Column(
        Text,
        nullable=True,
        # Removed unique=True since we now use composite unique constraint
    )
    # 0 AWAITING CONFIRMATION
    # 1 CONFIRMED
    # 2 FAILED
    status = Column(
        SmallInteger,
        nullable=False,
        index=True,
        comment="0 AWAITING CONFIRMATION, 1 CONFIRMED, 2 FAILED"
    )
    reason = Column(
        Text,
        nullable=False,
        server_default=text("''")
    )
    payment_source = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="payment source, maybe credit card number or wallet address"
    )
    order_id = Column(
        Text,
        nullable=True,
        comment="order id for tracking transaction orders"
    )
