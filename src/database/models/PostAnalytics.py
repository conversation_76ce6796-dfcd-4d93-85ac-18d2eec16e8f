from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, DateTime, Index, Text
from sqlalchemy.sql import func

from src.database.session import Base
from src.database.mixins.HasTimestamp import TimestampMixin


class PostAnalytics(Base, TimestampMixin):
    """
    帖子分析数据模型
    
    存储帖子的点击次数、曝光时长等分析数据
    """
    __tablename__ = "post_analytics"
    
    # 定义索引以提高查询性能
    __table_args__ = (
        Index("idx_post_analytics_post_id", "post_id"),
        Index("idx_post_analytics_user_id", "user_id"),
        Index("idx_post_analytics_timestamp", "timestamp"),
        Index("idx_post_analytics_post_user", "post_id", "user_id"),
    )
    
    id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="主键，自增"
    )
    
    post_id = Column(
        String(255),
        nullable=False,
        index=True,
        comment="帖子ID"
    )
    
    click_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="点击次数"
    )
    
    exposure_duration = Column(
        Float,
        nullable=False,
        default=0.0,
        comment="曝光时长（秒）"
    )
    
    user_id = Column(
        String(255),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    timestamp = Column(
        DateTime,
        nullable=False,
        default=func.now(),
        comment="数据时间戳"
    )
    
    # 可选：添加额外元数据字段
    extra_data = Column(
        Text,
        nullable=True,
        comment="额外的元数据（JSON格式）"
    )
    
    def __repr__(self):
        return f"<PostAnalytics(post_id='{self.post_id}', clicks={self.click_count}, duration={self.exposure_duration}s)>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "post_id": self.post_id,
            "click_count": self.click_count,
            "exposure_duration": self.exposure_duration,
            "user_id": self.user_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "extra_data": self.extra_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
