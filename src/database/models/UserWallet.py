from sqlalchemy import String, Date, Integer, text, SmallInteger, Text
from sqlalchemy.orm import relationship, query_expression
from sqlalchemy.sql.schema import Table, Column, ForeignKey

from src.database.session import Base
from src.database.mixins import HasID
from src.database.mixins import TimestampMixin
from src.database.mixins.HasPins import HasPins


class UserWallet(HasID, TimestampMixin, Base):
    __tablename__ = "user_wallet"

    user_id = Column(
        Text,
        ForeignKey("users.id"),
        nullable=False,
        index=True,
    )
    type = Column(
        Text,
        comment="User's wallet type, sol or bsc",
        nullable=False,
        server_default=text("''")
    )
    pubkey = Column(
        Text,
        comment="User's public key",
        nullable=False,
        server_default=text("''")
    )
    secret = Column(
        Text,
        comment="Token for request KMS service",
        nullable=False,
        server_default=text("''"),
    )
    status = Column(
        Text,
        comment="User's wallet status, 0 - inactive, 1 - active",
        nullable=False,
        default=0
    )
