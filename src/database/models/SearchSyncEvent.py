"""
搜索同步事件表
用于追踪PostgreSQL到Elasticsearch的数据同步事件
"""
from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, Text, Index, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy import Enum as SAEnum

from src.database import Base
from enum import Enum


class SyncOperation(str, Enum):
    """同步操作类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


class SyncStatus(str, Enum):
    """同步状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


class SearchSyncEvent(Base):
    """搜索同步事件表"""
    __tablename__ = "search_sync_events"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 实体信息
    entity_type = Column(String(50), nullable=False, index=True)  # Video, Image, Article, Author
    entity_id = Column(String(255), nullable=False, index=True)  # 实体的主键ID
    
    # 操作信息
    operation = Column(SAEnum(SyncOperation), nullable=False, index=True)
    status = Column(SAEnum(SyncStatus), default=SyncStatus.PENDING, nullable=False, index=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    
    # 额外数据（可选）
    extra_data = Column(JSONB, nullable=True, comment="额外的同步数据，如字段变更信息")
    
    # 优先级（数字越小优先级越高）
    priority = Column(Integer, default=100, nullable=False, index=True)
    
    # 批次ID（用于批量处理）
    batch_id = Column(String(36), nullable=True, index=True)

    # 创建复合索引优化查询性能
    __table_args__ = (
        # 待处理事件查询索引：按状态+优先级+创建时间排序
        Index('idx_sync_events_pending', status, priority, created_at),
        
        # 实体查询索引：按实体类型+实体ID查询
        Index('idx_sync_events_entity', entity_type, entity_id),
        
        # 失败重试查询索引
        Index('idx_sync_events_retry', status, retry_count, created_at),
        
        # 批次处理索引
        Index('idx_sync_events_batch', batch_id, status),
        
        # 清理任务索引：按状态+处理时间
        Index('idx_sync_events_cleanup', status, processed_at),
    )
    
    def __repr__(self):
        return f"<SearchSyncEvent(id={self.id}, entity_type={self.entity_type}, entity_id={self.entity_id}, operation={self.operation}, status={self.status})>"
    
    def can_retry(self) -> bool:
        """判断是否可以重试"""
        return (self.status in [SyncStatus.FAILED, SyncStatus.RETRY] 
                and self.retry_count < self.max_retries)
    
    def mark_processing(self):
        """标记为处理中"""
        self.status = SyncStatus.PROCESSING
        self.processed_at = datetime.utcnow()
    
    def mark_success(self):
        """标记为成功"""
        self.status = SyncStatus.SUCCESS
        self.processed_at = datetime.utcnow()
        self.error_message = None
    
    def mark_failed(self, error_message: str):
        """标记为失败"""
        self.status = SyncStatus.FAILED
        self.processed_at = datetime.utcnow()
        self.error_message = error_message
        self.retry_count += 1
    
    def mark_retry(self):
        """标记为等待重试"""
        self.status = SyncStatus.RETRY
        self.retry_count += 1 