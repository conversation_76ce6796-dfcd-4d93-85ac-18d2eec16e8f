from datetime import datetime
from copy import copy

from sqlalchemy import DateTime, <PERSON><PERSON><PERSON>
from sqlalchemy import String
from sqlalchemy import ARRAY
from sqlalchemy.sql.schema import Column
from fastapi_users.db import SQLAlchemyBaseUserTable


from src.database.session import Base
from src.database.mixins.HasID import HasID
from src.database.mixins.HasRegion import HasRegion
from src.common.constants import UserStatus


class User(SQLAlchemyBaseUserTable[str], HasRegion, HasID, Base):
    __tablename__ = "users"

    email: str = Column(String(length=320), unique=True, index=True, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    status = Column(String, default=UserStatus.ACTIVE, nullable=False)
    phone_number = Column(String, nullable=True, unique=True, index=True)
    preferred_languages = Column(ARRAY(String), server_default='{}', nullable=False)
    
    # OAuth fields with improved indexing and tracking
    google_id = Column(String, nullable=True, unique=True, index=True)
    apple_id = Column(String, nullable=True, unique=True, index=True)
    avatar_url = Column(String, nullable=True)
    
    # OAuth account linking timestamps
    google_linked_at = Column(DateTime, nullable=True)
    apple_linked_at = Column(DateTime, nullable=True)
    
    # Email verification method tracking
    email_verified_via = Column(String, nullable=True)  # 'code', 'oauth_google', 'oauth_apple'
    
    # Last login tracking for different methods
    last_login_at = Column(DateTime, nullable=True)
    last_login_method = Column(String, nullable=True)  # 'email', 'phone', 'google', 'apple'

    # 是否是虚拟用户
    is_virtual = Column(Boolean, nullable=True, default=False)

    def dict(self) -> dict:
        user_dict = copy(self.__dict__)
        if user_dict.get("_sa_instance_state"):
            user_dict.pop("_sa_instance_state")
        user_dict["created_at"] = user_dict["created_at"].isoformat()
        if user_dict.get("google_linked_at"):
            user_dict["google_linked_at"] = user_dict["google_linked_at"].isoformat()
        if user_dict.get("apple_linked_at"):
            user_dict["apple_linked_at"] = user_dict["apple_linked_at"].isoformat()
        if user_dict.get("last_login_at"):
            user_dict["last_login_at"] = user_dict["last_login_at"].isoformat()
        return user_dict

    @classmethod
    def from_dict(cls, data: dict):
        user_dict = copy(data)
        user_dict["created_at"] = datetime.fromisoformat(user_dict["created_at"])
        if user_dict.get("google_linked_at"):
            user_dict["google_linked_at"] = datetime.fromisoformat(user_dict["google_linked_at"])
        if user_dict.get("apple_linked_at"):
            user_dict["apple_linked_at"] = datetime.fromisoformat(user_dict["apple_linked_at"])
        if user_dict.get("last_login_at"):
            user_dict["last_login_at"] = datetime.fromisoformat(user_dict["last_login_at"])
        return cls(**user_dict)  # type: ignore
