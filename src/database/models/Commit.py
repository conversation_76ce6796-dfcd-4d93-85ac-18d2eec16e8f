from sqlalchemy.orm import relationship
from sqlalchemy.sql.schema import <PERSON>um<PERSON>, ForeignKey
from sqlalchemy.sql.sqltypes import DateTime, String

from src.database.session import Base
from src.database.mixins.HasID import HasID
from src.database.mixins.HasAuthor import <PERSON><PERSON><PERSON><PERSON>
from src.database.mixins.HasTimestamp import TimestampMixin
from src.database.mixins.HasDescription import HasDescription


class Commit(Base, HasID, HasAuthor, TimestampMixin, HasDescription):
    __tablename__ = "commits"

    collection_id = Column(String(), ForeignKey("collections.id", ondelete="CASCADE"), nullable=False)
    # collection = relationship(
    #     "Collection",
    #     foreign_keys=[collection_id],
    #     back_populates="commits",
    #     cascade="all,delete"
    # )

    post_id = Column(String(), ForeignKey("posts.id"), nullable=True)
    post = relationship("Post", backref="committed_in")

    status = Column(String(), nullable=False, default="new")
    reason = Column(String(), nullable=True)
    read_status = Column(String(), nullable=False, default="unread")

    resolved_by = Column(String(), nullable=True)
    resolved_at = Column(DateTime, nullable=True)
