from sqlalchemy import (
    Column,
    Text,
    DateTime,
    Index, Float, Boolean, PrimaryKeyConstraint,
)
from src.database.session import Base


class KlineMixin:
    timestamp = Column(DateTime(timezone=True), nullable=False)
    token_address = Column(Text, nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False, default=0)
    is_filled = Column(Boolean, nullable=False, default=False)


def make_table_args(suffix: str):
    return (
        PrimaryKeyConstraint('timestamp', 'token_address', name=f'kline_{suffix}_pkey'),
        Index(f"idx_token_address_symbol_{suffix}", "token_address"),
    )


class Kline1m(KlineMixin, Base):
    __tablename__ = 'kline_1m'
    __table_args__ = make_table_args("1m")


class Kline5m(KlineMixin, Base):
    __tablename__ = 'kline_5m'
    __table_args__ = make_table_args("5m")


class Kline30s(KlineMixin, Base):
    __tablename__ = 'kline_30s'
    __table_args__ = make_table_args("30s")


class Kline30m(KlineMixin, Base):
    __tablename__ = 'kline_30m'
    __table_args__ = make_table_args("30m")


class Kline1h(KlineMixin, Base):
    __tablename__ = 'kline_1h'
    __table_args__ = make_table_args("1h")


class Kline4h(KlineMixin, Base):
    __tablename__ = 'kline_4h'
    __table_args__ = make_table_args("4h")


class Kline12h(KlineMixin, Base):
    __tablename__ = 'kline_12h'
    __table_args__ = make_table_args("12h")


class Kline1d(KlineMixin, Base):
    __tablename__ = 'kline_1d'
    __table_args__ = make_table_args("1d")
