from sqlalchemy import Index, Column, Computed

# from src.database.mixins import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HasContentZone
from src.database.models.Post import Post
from src.database.mixins.TSVector import TSVector
from src.database.mixins.HasCover import HasCover
from src.database.mixins.HasDescription import HasDescription
from src.database.mixins.HasPostID import HasPostID
from src.database.mixins.HasText import HasText
from src.database.mixins.HasTitle import <PERSON>Title
from src.database.mixins.HasOriginal import HasOriginal
from src.database.mixins.HasContentRating import HasContentRating
from src.database.mixins.HasContentZone import HasContentZone


class Article(
        HasPostID,
        HasTitle,
        HasCover,
        HasText,
        HasDescription,
        HasOriginal,
        HasContentRating,
        HasContentZone,
        # HasLanguage,
        Post
):
    __tablename__ = "articles"
    __mapper_args__ = {
        "polymorphic_identity": "Article",
        "polymorphic_load": "inline",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(title,'') || ' ' || coalesce(text,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_articles___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )
