from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text
from sqlalchemy.sql import func

from src.database.session import Base
from src.database.mixins import HasID


class VipCode(Base):
    """
    VIP码表
    """
    __tablename__ = "vip_codes"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, index=True, nullable=False, comment="VIP码")
    description = Column(Text, nullable=True, comment="VIP码描述")
    max_uses = Column(Integer, default=1, nullable=False, comment="最大使用次数")
    current_uses = Column(Integer, default=0, nullable=False, comment="当前使用次数")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    device_brand = Column(String(100), nullable=True, comment="绑定的设备品牌")
    device_id = Column(String(255), nullable=True, comment="绑定的设备ID")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
