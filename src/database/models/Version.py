from sqlalchemy import Column, String, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import JSONB

from datetime import datetime
from src.database.session import Base
from src.database.mixins.HasID import HasID


class Version(HasID, Base):
    __tablename__ = "versions"

    post_id = Column(String, ForeignKey("posts.id", ondelete="CASCADE"), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    snapshot = Column(JSONB, nullable=False)

    prev_version_id = Column(String, ForeignKey("versions.id", ondelete='CASCADE'))
