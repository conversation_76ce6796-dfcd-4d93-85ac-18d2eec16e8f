from sqlalchemy import String
from sqlalchemy.orm import relationship, backref
from sqlalchemy.sql.schema import Column, ForeignKey

from src.database.models.Post import Post
from src.database.mixins.HasText import HasText
from src.database.mixins.utils import generate_shortid

# https://stackoverflow.com/questions/4864935/sqlalchemy-inheritance-and-relationships


class Comment(HasText, Post):
    id = Column(
        String(),
        ForeignKey("posts.id", ondelete="CASCADE"),
        primary_key=True,
        default=generate_shortid,
    )
    __tablename__ = "comments"
    __mapper_args__ = {
        "polymorphic_identity": "Comment",
        "inherit_condition": id == Post.id,
    }

    parent_id = Column(
        String(), ForeignKey("posts.id", ondelete="CASCADE"), nullable=False
    )

    parent = relationship("Post", foreign_keys=[parent_id], backref=backref("comments", cascade="all,delete"))
