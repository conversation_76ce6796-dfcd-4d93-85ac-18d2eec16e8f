from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, BigInteger

from src.database.session import Base


class BuyBackMCHistory(Base):
    """存储 buyback_marketcap 的计算结果"""
    __tablename__ = "buyback_mc_history"

    id = Column(Integer, primary_key=True, autoincrement=True)
    pair_id = Column(BigInteger, nullable=False, index=True)
    base = Column(String, nullable=False, index=True)
    base_symbol = Column(String, nullable=False)
    base_created_at = Column(DateTime, nullable=True)
    mc_update_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    marketcap_usd = Column(Float, nullable=False)
    bnb_quote_price = Column(Float, nullable=False)
    usd_quote_price = Column(Float, nullable=False)

    def __repr__(self):
        return f"<BuyBackMCHistory(id={self.id}, base={self.base}, base_symbol={self.base_symbol}, marketcap_usd={self.marketcap_usd})>"