from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Column,
    BigInteger,
    Integer,
    Text,
    Numeric,
    DateTime,
    SmallInteger,
    UniqueConstraint,
    Index,
    text,
)
from sqlalchemy.dialects.postgresql import JSONB

from src.database.session import Base


class Pair(Base):
    __tablename__ = 'pair'
    __table_args__ = (
        UniqueConstraint("base", "quote", name="uq_base_quote"),
        Index("ix_pair_base", "address", "base"),
        Index("ix_pair_quote", "address", "quote"),
        Index("ix_pair_creator", "address", "creator"),
        Index("ix_pair_liq", "address", "liq"),
        Index("ix_pair_mc", "address", "mc"),
    )

    id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="Primary key, auto-increment."
    )
    chain = Column(
        Integer,
        nullable=False,
        index=True,
        server_default=text('0'),
        comment="Chain ID, default 0."
    )
    address = Column(
        Text,
        nullable=True,
        unique=True,
        comment="Pair address as text, default empty string."
    )
    dex = Column(
        Text,
        nullable=False,
        index=True,
        server_default=text("''"),
        comment="DEX as text, default empty string."
    )
    base = Column(
        Text,
        nullable=True,
        index=True,
        comment="Base token address as text, default empty string."
    )
    base_decimals = Column(
        SmallInteger,
        nullable=False,
        server_default=text('0'),
        comment="Decimal places of the base token, default 0."
    )
    base_name = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        index=True,
        comment="Base token name, not null, default empty string."
    )
    base_symbol = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        index=True,
        comment="Base token symbol, not null, default empty string."
    )
    base_image_url = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="Base token image URL as text, default empty string."
    )
    base_description: str = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="Base token description as text, default empty string."
    )
    base_total_supply: int = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Base total supply of the token, default 0."
    )
    base_created_at = Column(
        DateTime,
        nullable=True,
        comment="Base token creation time, can be null."
    )
    quote = Column(
        Text,
        nullable=True,
        index=True,
        comment="Quote token address as text"
    )
    quote_decimals = Column(
        SmallInteger,
        nullable=False,
        server_default=text('0'),
        comment="Decimal places of the quote token, default 0."
    )
    base_amount = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Base amount, default 0."
    )
    quote_amount = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Quote amount, default 0."
    )
    creator = Column(
        Text,
        nullable=False,
        index=True,
        server_default=text("''"),
        comment="Creator as text, default empty string."
    )
    created_at = Column(
        DateTime,
        nullable=True,
        comment="Pair creation time, can be null."
    )
    open_at = Column(
        DateTime,
        nullable=True,
        comment="Open time, can be null."
    )
    price_usd = Column(
        Numeric(38, 18),
        nullable=False,
        server_default=text('0'),
        comment="Price in USD (double precision), default 0."
    )
    liq = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Liquidity (bigint), default 0."
    )
    mc = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Market cap (bigint), default 0."
    )
    bonding_curve = Column(
        SmallInteger,
        nullable=False,
        server_default=text('0'),
        comment="Bonding curve (smallint) from 0 to 100, default 0."
    )
    status = Column(
        SmallInteger,
        nullable=False,
        index=True,
        server_default=text('0'),
        comment="0 - Data is not displayed, 1 - Data is ready, default 0."
    )
    creator_id: str = Column(
        Text,
        nullable=True,
        index=True,
        comment="Creator ID -> user id / author id, not null."
    )
    collection_id: str = Column(
        Text,
        nullable=True,
        index=True,
        comment="Collection ID, not null."
    )
    creation_txid: str = Column(
        Text,
        nullable=True,
        index=True,
        comment="Creation transaction ID, not null."
    )
    social_links = Column(
        JSONB,
        nullable=True,
        server_default=text("'{}'::jsonb"),
        comment="Social media links in JSON format."
    )