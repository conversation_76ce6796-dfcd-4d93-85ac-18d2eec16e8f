from sqlalchemy import Column, Integer, BigInteger, Text, DateTime, Index, SmallInteger
from datetime import datetime, timezone

from sqlalchemy.dialects.postgresql import JSONB

from src.database.session import Base
from src.agora.constants import ChannelStatus, RecordingStatus


def utc_now_naive():
    """返回无时区的 UTC 时间（用于数据库存储）"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class LiveChannel(Base):
    """直播间模型"""
    __tablename__ = "live_channels"

    id = Column(BigInteger, primary_key=True, index=True)
    sid = Column(Text, index=True, unique=True)
    channel_name = Column(Text, index=True, nullable=False)
    broadcaster_uid = Column(BigInteger, nullable=False, index=True, comment="Agora UID")  # 主播的 UID
    user_id = Column(Text, nullable=False, index=True)
    token_address = Column(Text, nullable=True, index=True)  # 代币地址
    status = Column(Text, default=ChannelStatus.CREATED, index=True)  # created/live/ended
    created_at = Column(DateTime, default=utc_now_naive)
    started_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)
    current_audience = Column(BigInteger, default=0)
    duration = Column(Integer, default=0)


class ChannelAudience(Base):
    """直播间观众记录模型"""
    __tablename__ = "channel_audience"

    id = Column(BigInteger, primary_key=True, index=True)
    sid = Column(Text, index=True)  # Session ID
    channel_id = Column(BigInteger, index=True, comment="ID of LiveChannel")
    channel_name = Column(Text, index=True, nullable=False)
    user_id = Column(Text, nullable=False, index=True)
    platform = Column(SmallInteger, index=True, nullable=False) # 1: Android, 2: iOS, 5: Windows, 6: Linux, 7: Web, 8: macOS, 0: Other platforms
    # 1: The viewer left the channel normally.
    # 2: The connection between the client and the Agora business server has timed out. Agora SD-RTN™ has not received any data packets from the host for more than 10 seconds, or the connection to a single server has timed out for 4 seconds and has not completed reconnection within 1 second.
    # 3: Permissions issue. For example, the operator can make someone leave the channel through the RESTful API.
    # 4: Internal reason of the Agora business server. The server is adjusting the load, temporarily disconnecting from the client and then reconnecting.
    # 5: The host switches to a new device, forcing the old device to go offline.
    # 9: Since the client has multiple IP addresses, the SDK actively disconnects and reconnects with the Agora business server. This process is invisible to the user. Please check whether the user has multiple IP addresses or uses a VPN.
    # 10: Due to network connection problems, such as the SDK not receiving any data packets from the Agora business server for more than 4 seconds or a socket connection error, the SDK actively disconnects and reconnects with the Agora business server. This process is invisible to the user. Please check the network connection status.
    # 999: Abnormal user. For example, a user who frequently logs in and out of the channel in a short period of time is considered an abnormal user. Your App server should call the kick API to kick the user out of the channel 60 seconds after receiving the 104 event with reason set to 999. Otherwise, the user may not receive relevant event notifications after joining the channel again.
    # 0: Other reasons.
    leave_reason = Column(SmallInteger, index=True)
    role = Column(Text, default="audience")  # audience/host
    client_seq = Column(BigInteger, default=0)
    joined_at = Column(DateTime, default=utc_now_naive)
    left_at = Column(DateTime, nullable=True)
    duration = Column(Integer, default=0)

    __table_args__ = (
        Index('idx_audience_sid_channel', 'sid', 'channel_name'),
    )


class RecordingSession(Base):
    """录制会话模型"""
    __tablename__ = "recording_sessions"

    id = Column(BigInteger, primary_key=True, index=True)
    channel_id = Column(BigInteger, index=True, comment="ID of LiveChannel")
    channel_name = Column(Text, nullable=False, index=True)  # 直接使用 channel_name 而不是外键
    resource_id = Column(Text, nullable=False)
    sid = Column(Text, nullable=False)
    status = Column(Text, default=RecordingStatus.STARTED)  # started/stopped/failed
    started_at = Column(DateTime, default=utc_now_naive)
    stopped_at = Column(DateTime, nullable=True)

    # 唯一约束
    __table_args__ = (
        Index('idx_recording_resource_sid', 'resource_id', 'sid', unique=True),
    )


class ChannelComment(Base):
    """直播间评论模型"""
    __tablename__ = "channel_comments"

    id = Column(BigInteger, primary_key=True, index=True)
    channel_id = Column(BigInteger, index=True, comment="ID of LiveChannel")
    channel_name = Column(Text, index=True, nullable=False)
    user_id = Column(Text, nullable=False, index=True)
    content = Column(Text, nullable=True)  # 评论内容
    reply_to_id = Column(BigInteger, nullable=True, index=True)  # 回复的评论ID，支持评论回复
    is_deleted = Column(SmallInteger, default=0, index=True)  # 0: 正常, 1: 用户删除, 2: 管理员删除
    like_count = Column(BigInteger, default=0)  # 点赞数
    created_at = Column(DateTime, default=utc_now_naive)
    updated_at = Column(DateTime, default=utc_now_naive, onupdate=utc_now_naive)

    # 索引优化
    __table_args__ = (
        Index('idx_comment_channel_id_time', 'channel_id', 'created_at'),  # 按频道和时间查询
        Index('idx_comment_user_time', 'user_id', 'created_at'),  # 按用户查询评论历史
        Index('idx_comment_reply', 'reply_to_id'),  # 查询回复
    )


class ChannelGiftHistory(Base):
    """直播间礼物打赏历史模型"""
    __tablename__ = "channel_gift_history"

    id = Column(BigInteger, primary_key=True, index=True)
    channel_id = Column(BigInteger, index=True, comment="ID of LiveChannel")
    channel_name = Column(Text, index=True, nullable=False)
    tipper = Column(Text, nullable=False, index=True, comment="the user id of tipper")  # 打赏者user_id
    gift_id = Column(Integer, index=True)  # 礼物ID/类型
    gift_count = Column(BigInteger, default=1)  # 礼物数量
    gift_value = Column(Integer, nullable=False, default=0, index=True, comment="总打赏价值（USD分）")  # 总价值 = 单价 * 数量
    message = Column(Text, nullable=True)  # 打赏留言
    operation_id = Column(Text, nullable=True, index=True, comment="客户端生成的唯一操作ID")  # 操作追踪ID
    txid = Column(Text)  # 交易哈希
    created_at = Column(DateTime, default=utc_now_naive)

    # 索引优化
    __table_args__ = (
        Index('idx_gift_history_channel_id_time', 'channel_id', 'created_at'),  # 按频道和时间查询
        Index('idx_gift_history_tipper_time', 'tipper', 'created_at'),  # 按打赏者查询
        Index('idx_gift_history_type', 'gift_id'),  # 按礼物类型统计
        Index('idx_gift_history_operation_id', 'operation_id'),  # 按操作ID查询（去重和追踪）
        Index('idx_gift_history_value', 'gift_value'),  # 按打赏价值查询和排序
        Index('idx_gift_history_channel_value', 'channel_name', 'gift_value'),  # 频道打赏价值复合索引
    )


class GiftList(Base):
    """礼物列表模型 - 存储可用的礼物类型"""
    __tablename__ = "gift_list"

    id = Column(Integer, primary_key=True, index=True)
    meta = Column(JSONB)  # 礼物名称，描述
    price = Column(Integer, nullable=False)  # 美元价格
    currency = Column(Text, default="USD")  # 货币类型
    cover = Column(Text, nullable=True)  # 礼物图标URL
    animation = Column(Text, nullable=True)  # 动画效果URL
    category = Column(Text, default="normal")  # 礼物分类（normal/special/premium/supreme等）
    # 稀有度等级 1-5
    # 5 - $128+
    # 3 - 25$ - $50
    # 1 - $1 - $10
    rarity = Column(SmallInteger, default=1)
    is_active = Column(SmallInteger, default=1, index=True)  # 0: 下架, 1: 上架
    sort_order = Column(Integer, default=0)  # 排序权重，数字越大越靠前
    created_at = Column(DateTime, default=utc_now_naive)
    updated_at = Column(DateTime, default=utc_now_naive, onupdate=utc_now_naive)

    # 索引优化
    __table_args__ = (
        Index('idx_gift_list_category_active', 'category', 'is_active'),  # 按分类和状态查询
        Index('idx_gift_list_price_active', 'price', 'is_active'),  # 按价格和状态查询
        Index('idx_gift_list_sort_active', 'sort_order', 'is_active'),  # 按排序和状态查询
    )


class ChannelStats(Base):
    """直播间统计数据模型 - 存储每次直播的统计信息"""
    __tablename__ = "channel_stats"

    id = Column(BigInteger, primary_key=True, index=True)
    channel_id = Column(BigInteger, index=True, comment="ID of LiveChannel")
    channel_name = Column(Text, index=True, nullable=False)
    user_id = Column(Text, nullable=False, index=True, comment="主播用户ID")
    
    # 观众统计
    peak_audience = Column(BigInteger, default=0, comment="峰值观众数")
    total_audience = Column(BigInteger, default=0, comment="累计观众数（去重）")
    average_audience = Column(BigInteger, default=0, comment="平均观众数")
    
    # 时长统计
    live_duration = Column(Integer, default=0, comment="直播时长（秒）")
    started_at = Column(DateTime, nullable=True, comment="开播时间")
    ended_at = Column(DateTime, nullable=True, comment="结束时间")
    
    # 交易统计
    total_gift_value = Column(BigInteger, default=0, comment="礼物总价值（USD分）")
    total_gift_count = Column(BigInteger, default=0, comment="礼物总数量")
    unique_gifters = Column(BigInteger, default=0, comment="打赏人数（去重）")
    
    # 互动统计
    total_comments = Column(BigInteger, default=0, comment="评论总数")
    total_likes = Column(BigInteger, default=0, comment="点赞总数")
    
    # 记录时间
    created_at = Column(DateTime, default=utc_now_naive)
    updated_at = Column(DateTime, default=utc_now_naive, onupdate=utc_now_naive)
    
    # 索引优化
    __table_args__ = (
        Index('idx_channel_stats_channel_name', 'channel_name'),  # 按频道名查询
        Index('idx_channel_stats_user_id', 'user_id'),  # 按主播查询
        Index('idx_channel_stats_time', 'started_at', 'ended_at'),  # 按时间查询
        Index('idx_channel_stats_value', 'total_gift_value'),  # 按交易额查询
    )
    