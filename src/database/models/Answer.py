from sqlalchemy import Index, Computed, String
from sqlalchemy.orm import relationship, synonym
from sqlalchemy.sql.schema import <PERSON>um<PERSON>, ForeignKey

from src.database.models.Post import Post
from src.database.mixins import TSVector
from src.database.mixins import HasPostID
from src.database.mixins import <PERSON><PERSON>ex<PERSON>
from src.database.mixins.HasDescription import HasD<PERSON>cription
from src.database.mixins import Has<PERSON><PERSON>
from src.database.mixins import HasOriginal
from src.database.mixins import HasLanguage
from src.database.mixins import HasContentRating
# from src.database.mixins.HasContentZone import HasContentZone


class Answer(
    HasPostID,
    HasText,
    HasDescription,
    HasCover,
    HasOriginal,
    HasContentRating,
    # HasContentZone,
    Post
):
    __tablename__ = "answers"
    __mapper_args__ = {
        "polymorphic_identity": "Answer",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', text)", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_answers___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    parent_id = Column(String(), ForeignKey("questions.id", ondelete="CASCADE"), nullable=False)

    parent = relationship(
        "Question", foreign_keys=[parent_id], back_populates="answers"
    )

    question_id = synonym("parent_id")

    question = relationship(
        "Question", foreign_keys=[parent_id], back_populates="answers"
    )
