from sqlalchemy import Column, String, Integer

from src.database.models.Post import Post
from src.database.mixins import HasPostID
from src.database.mixins import <PERSON><PERSON><PERSON><PERSON>
from src.database.mixins.HasDescription import HasDescription


class File(HasPostID, HasTitle, HasDescription, Post):
    __tablename__ = "files"
    __mapper_args__ = {
        "polymorphic_identity": "File",
    }

    url = Column(String, nullable=False)
    format = Column(String, nullable=False, server_default="")
    size = Column(Integer, nullable=False, server_default="0")
    downloads_count = Column(Integer, nullable=False, server_default="0")
