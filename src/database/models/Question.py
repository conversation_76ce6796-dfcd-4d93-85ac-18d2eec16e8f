from sqlalchemy import Index, <PERSON><PERSON><PERSON>, Comp<PERSON>, Integer, Table, String, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from src.database import Base
from src.database.models.Post import Post
from src.database.models.Answer import Answer
from src.database.mixins import TSVector
from src.database.mixins.HasDescription import HasDescription
from src.database.mixins import Has<PERSON>ost<PERSON>
from src.database.mixins import Has<PERSON>ex<PERSON>
from src.database.mixins import <PERSON>Title
from src.database.mixins import Has<PERSON><PERSON>
from src.database.mixins import HasOriginal
from src.database.mixins import HasLanguage
from src.database.mixins import HasContentRating
# from src.database.mixins.HasContentZone import HasContentZone


questions_subscriptions = Table(
    "questions_subscriptions",
    Base.metadata,
    Column("author_id", String(), ForeignKey("authors.id"), primary_key=True),
    <PERSON>umn("question_id", String(), ForeignKey("questions.id", ondelete="CASCADE"), primary_key=True),
)


class Question(
    <PERSON><PERSON>ost<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    HasDescription,
    <PERSON><PERSON><PERSON>,
    Has<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ontentR<PERSON>,
    # <PERSON><PERSON><PERSON>nt<PERSON><PERSON>,
    Post
):
    __tablename__ = "questions"
    __mapper_args__ = {
        "polymorphic_identity": "Question",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(title,'') || ' ' || coalesce(text,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_questions___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    subscribers = relationship(
        "Author",
        secondary="questions_subscriptions",
        back_populates="questions_subscribed"
    )

    answers = relationship(
        "Answer",
        back_populates="question",
        foreign_keys=[Answer.question_id],
        cascade="all, delete"
    )

    answers_count = Column(Integer, nullable=False, server_default="0")
