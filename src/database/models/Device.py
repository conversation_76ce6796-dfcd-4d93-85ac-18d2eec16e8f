from sqlalchemy import Column
from sqlalchemy.types import String

from src.database.session import Base
from src.database.mixins import <PERSON><PERSON><PERSON><PERSON>
from src.database.mixins import HasID


class Device(HasID, HasAuthor, Base):
    __tablename__ = "devices"

    device_id = Column(String, nullable=False)
    player_id = Column(String, nullable=False)
    device_info = Column(String, nullable=True)
