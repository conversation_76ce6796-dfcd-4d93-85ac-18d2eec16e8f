from sqlalchemy import Index, Column, Computed, String

from src.database.models.Post import Post
from src.database.mixins import TSVector
from src.database.mixins import HasPostID


class Text(HasPostID, Post):
    __tablename__ = "texts"

    __mapper_args__ = {
        "polymorphic_identity": "Text",
    }

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(text,''))", persisted=True),
    # )
    #
    # __table_args__ = (
    #     Index("ix_texts___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    text = Column(String(), nullable=False, server_default="")
