from sqlalchemy import Column, Text, Integer, BigInteger, DateTime, Index, UniqueConstraint
from sqlalchemy.sql.schema import ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from uuid import uuid4

from src.database.session import Base
from src.common.constants import ContentPurchaseStatus


class ContentPurchase(Base):
    """内容购买记录表 - 专注于访问控制"""
    __tablename__ = 'content_purchases'
    
    # Define table constraints and indexes for optimal performance
    __table_args__ = (
        # 唯一约束：每个用户每个帖子只能有一条成功购买记录
        UniqueConstraint('user_id', 'post_id', name='uq_user_post_purchase'),
        # 性能索引
        Index('idx_content_purchase_user', 'user_id'),
        Index('idx_content_purchase_post', 'post_id'), 
        Index('idx_content_purchase_status', 'status', 'purchased_at'),
        Index('idx_content_purchase_user_status', 'user_id', 'status'),
    )
    
    id = Column(Text, primary_key=True, default=lambda: str(uuid4()))
    user_id = Column(Text, ForeignKey('users.id'), nullable=False)
    post_id = Column(Text, ForeignKey('posts.id'), nullable=False)
    
    # 购买时的USD价格（用于显示购买历史）
    usd_amount_cents = Column(Integer, nullable=False)
    
    # 关联到具体的支付交易（token详情在UserTransaction中查询）
    user_transaction_id = Column(BigInteger, ForeignKey('user_transactions.id'), nullable=True)
    
    # 购买状态和时间
    status = Column(Text, default=ContentPurchaseStatus.PENDING, nullable=False)  # pending -> processing -> confirmed/refunded
    purchased_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # 关系
    user = relationship("User")
    post = relationship("Post")
    user_transaction = relationship("UserTransaction")  # 通过这个获取token详细信息