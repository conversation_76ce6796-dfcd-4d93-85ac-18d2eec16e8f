from datetime import datetime
from sqlalchemy import (
    Column,
    BigInteger,
    Text,
    Numeric,
    DateTime,
    UniqueConstraint,
    Index,
    text,
)
from src.database.session import Base


class TokenPrice(Base):
    __tablename__ = 'token_price'
    __table_args__ = (
        UniqueConstraint("token_address", "pricetime", name="uq_token_address_pricetime"),
    )

    id = Column(
        BigInteger,
        autoincrement=True,
        primary_key=True,
        comment="Primary key, auto-increment."
    )
    pricetime = Column(
        DateTime,
        nullable=False,
        index=True,
        comment="Price time, not null."
    )
    token_address = Column(
        Text,
        nullable=False,
        index=True,
        server_default=text("''"),
        comment="Token contract address as text, default empty string."
    )
    price = Column(
        Numeric(38, 18),
        nullable=False,
        comment="Price (NUMERIC(38,18)), not null."
    )
