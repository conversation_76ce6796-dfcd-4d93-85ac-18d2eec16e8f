from src.database.session import Base
from sqlalchemy import <PERSON>umn, Foreign<PERSON><PERSON>, String
from fastapi_users_db_sqlalchemy.access_token import SQLAlchemyBaseAccessTokenTable
from sqlalchemy.orm import declared_attr

from src.database.models.User import User


class AccessToken(SQLAlchemyBaseAccessTokenTable[str], Base):
    @declared_attr
    def user_id(cls):
        return Column(String(), ForeignKey(User.id, ondelete="cascade"), nullable=False)
