from sqlalchemy import String, Date, Integer
from sqlalchemy.orm import relationship, query_expression
from sqlalchemy.sql.schema import Table, Column, ForeignKey
from sqlalchemy.dialects.postgresql import CITEXT

from src.database.session import Base
from src.database.mixins import HasID
from src.database.mixins import TimestampMixin
from src.database.mixins.HasPins import HasPins


class Author(HasID, TimestampMixin, HasPins, Base):
    __tablename__ = "authors"

    name = Column(String(255), nullable=False)
    username = Column(String(255), nullable=False, unique=True)
    username_raw = Column(CITEXT, nullable=False, unique=True)
    avatar = Column(String(255), nullable=True)
    original_avatar = Column(String(255), nullable=True)
    dedication = Column(String(255), nullable=True)
    description = Column(String(300), nullable=True)
    location = Column(String(255), nullable=True)
    country = Column(String(255), nullable=True)
    language = Column(String(255), nullable=True)
    region = Column(String, nullable=False)
    education = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    phone_number = Column(String(255), nullable=True)
    birthday = Column(Date, nullable=True)
    likes_count = Column(Integer, nullable=False, default=0)
    citations_count = Column(Integer, nullable=False, server_default="0")
    gender = Column(String, nullable=True)
    posts_count = Column(Integer, nullable=False, server_default="0")
    user_id = Column(
        String(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    invitation_id = Column(
        String(), ForeignKey("invitations.id", ondelete="CASCADE"), nullable=True
    )

    invitation_id_owned = Column(
        String(), nullable=True
    )
    # 内测码字段
    vip_code = Column(String(20), nullable=True, comment="内测码")

    group_size = Column(Integer(), nullable=False, server_default="0")
    group_grade = Column(Integer(), nullable=False, server_default="0")
    direct_invited_count = Column(Integer(), nullable=False, server_default="0")
    follower_count = query_expression()
    following_count = query_expression()

    permission = relationship("Permission", uselist=False, lazy="joined")

    @property
    def role(self):
        return self.permission.name if self.permission else None

    user = relationship("User", uselist=False, lazy="joined")

    @property
    def status(self):
        return self.user.status

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(name,'') || ' ' || coalesce(username,'') || ' ' || coalesce(dedication,'') || ' ' || coalesce(description,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_authors___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    followers = relationship(
        "Author",
        lambda: authors_followers,
        primaryjoin=lambda: Author.id == authors_followers.columns.author_id,
        secondaryjoin=lambda: Author.id == authors_followers.columns.follower_id,
        back_populates="following",
    )

    following = relationship(
        "Author",
        lambda: authors_followers,
        primaryjoin=lambda: Author.id == authors_followers.columns.follower_id,
        secondaryjoin=lambda: Author.id == authors_followers.columns.author_id,
        back_populates="followers",
    )

    liked = relationship("Post", secondary="authors_likes")
    posts = relationship("Post", back_populates="author")

    tags_subscribed = relationship("Tag", secondary="tags_subscriptions", lazy="noload")

    collections_subscribed = relationship(
        "Collection",
        secondary="collections_subscriptions",
        back_populates="subscribers",
    )
    questions_subscribed = relationship(
        "Question", secondary="questions_subscriptions", back_populates="subscribers"
    )
    collections_contributed = relationship(
        "Collection",
        secondary="collections_contributors",
        back_populates="contributors",
    )

    is_following = query_expression()
    is_blocked = query_expression()

    blockers = relationship(
        "Author",
        lambda: authors_blocks,
        primaryjoin=lambda: Author.id == authors_blocks.columns.blocked_id,
        secondaryjoin=lambda: Author.id == authors_blocks.columns.blocker_id,
        back_populates="blocked",
    )

    blocked = relationship(
        "Author",
        lambda: authors_blocks,
        primaryjoin=lambda: Author.id == authors_blocks.columns.blocker_id,
        secondaryjoin=lambda: Author.id == authors_blocks.columns.blocked_id,
        back_populates="blockers",
    )

    tokens_watchlist = relationship(
        "Pair",
        secondary="tokens_watchlist",
        primaryjoin="Author.id == TokensWatchlist.author_id",
        secondaryjoin="Pair.base == TokensWatchlist.token_address",
        lazy="noload",
        backref="watchlisted_by"
    )


authors_followers = Table(
    "authors_followers",
    Base.metadata,
    Column("author_id", String(), ForeignKey(Author.id), primary_key=True),
    Column("follower_id", String(), ForeignKey(Author.id), primary_key=True),
)

authors_blocks = Table(
    "authors_blocks",
    Base.metadata,
    Column("blocker_id", String(), ForeignKey(Author.id), primary_key=True),
    Column("blocked_id", String(), ForeignKey(Author.id), primary_key=True),
)
