from sqlalchemy.orm import relationship, query_expression
from sqlalchemy.sql.schema import <PERSON>umn, Foreign<PERSON>ey, Table
from sqlalchemy.sql.sqltypes import String, ARRA<PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.dialects.postgresql.base import INTEGER

from src.database import Base
from src.database.mixins.HasInteractionID import HasInteractionRating
from src.database.models.Author import Author
from src.database.mixins.HasAuthor import <PERSON><PERSON><PERSON><PERSON>
from src.database.mixins.HasID import HasID
from src.database.mixins.HasTimestamp import TimestampMixin
from src.database.mixins.HasViewCount import HasViewCount
from src.common.constants import Language


authors_likes = Table(
    "authors_likes",
    Base.metadata,
    Column("post_id", String(), ForeignKey("posts.id"), primary_key=True),
    Column("author_id", String(), ForeignKey("authors.id"), primary_key=True),
)


class Post(HasInteractionRating, Has<PERSON>, <PERSON><PERSON><PERSON>or, TimestampMixin, <PERSON><PERSON><PERSON><PERSON>Count, Base):
    __tablename__ = "posts"
    __mapper_args__ = {
        "polymorphic_on": "type",
        "polymorphic_identity": "Post",
    }

    type = Column(String)

    status = Column(String, nullable=False)
    likes = relationship(Author, secondary="authors_likes", back_populates="liked")
    likes_count = Column(INTEGER, nullable=False, server_default="0")

    comments_count = Column(INTEGER, nullable=False, server_default="0")

    current_version_id = Column(String, ForeignKey("versions.id"), nullable=True)

    tags_list = Column(MutableList.as_mutable(ARRAY(String)), nullable=False, server_default='{}')

    region = Column(String, nullable=False)
    language = Column(String, nullable=False, server_default=Language.GLOBAL.value)

    collections_count = Column(INTEGER, nullable=False, server_default="0")
    complaint_count = Column(INTEGER, nullable=False, server_default="0")
    
    # USD-based paid content: price in USD cents for viewing this post (e.g., 150 = $1.50)
    # If 0, content is free to view; otherwise users must purchase access
    holdview_amount = Column(INTEGER, nullable=False, server_default="0")

    # Payment token: token address required for purchasing content access
    # Users must pay the USD equivalent in this token to unlock content
    binding_token = Column(String, nullable=True)

    feed_reason = query_expression()
    is_liked = query_expression()
    is_in_collection = query_expression()

    source = Column(Text, nullable=True, server_default="user")

    @property
    def tags(self):
        """Return list of tag objects associated with this post."""
        tags = getattr(self, 'tags_list', [])
        return list(tags)
