from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from src.database.session import Base

class DeviceLock(Base):
    """
    设备锁定表
    """
    __tablename__ = "device_locks"

    id = Column(String(100), primary_key=True, index=True, comment="主键ID")
    device_id = Column(String(100), nullable=False, index=True, comment="设备ID")
    is_locked = Column(Boolean, default=True, nullable=False, comment="是否被锁定")
    lock_reason = Column(String(100), default="vip_code_failed", nullable=False, comment="锁定原因")
    locked_at = Column(DateTime, default=func.now(), nullable=False, comment="锁定时间")
    lock_until_midnight = Column(Boolean, default=True, nullable=False, comment="是否锁定到0点")
    unlocked_at = Column(DateTime, nullable=True, comment="解锁时间")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
