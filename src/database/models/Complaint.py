from datetime import datetime

from sqlalchemy import Column, ForeignKey
from sqlalchemy.types import String, DateTime

from src.database.session import Base
from src.database.mixins.HasID import HasID
from src.common.constants import ComplaintStatus


class Complaint(HasID, Base):
    __tablename__ = "complaints"

    reason = Column(String())
    post_id = Column(String(), nullable=True, default=None)
    comment_id = Column(String(), nullable=True, default=None)
    reportee_id = Column(String(), nullable=True, default=None)
    author_id = Column(String(), ForeignKey("authors.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    status = Column(String, default=ComplaintStatus.OPEN, nullable=False)
