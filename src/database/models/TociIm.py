from sqlalchemy import String, ForeignKey
from sqlalchemy.sql.schema import Column

from src.database.session import Base


class TociIm(Base):
    __tablename__ = "toci_im"

    toci_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True)
    im_id = Column(String, unique=True, nullable=False)
    im_login = Column(String, nullable=False)
    im_password = Column(String, nullable=False)