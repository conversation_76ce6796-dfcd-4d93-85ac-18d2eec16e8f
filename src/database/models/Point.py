from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import NUMERIC, JSONB

from src.database.session import Base
from src.database.mixins import TimestampMixin, HasID, HasAuthor


class Point(HasID, TimestampMixin, HasAuthor, Base):
    __tablename__ = "points"

    status = Column(String, nullable=False, default="pending")
    amount = Column(NUMERIC, nullable=False)
    action = Column(String, nullable=False)
    meta = Column(JSONB, nullable=False, server_default="{}")
