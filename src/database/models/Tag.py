from sqlalchemy.orm import relationship
from sqlalchemy.sql.sqltypes import String, Integer
from sqlalchemy.sql.schema import Column, ForeignKey, Table

from src.database.session import Base
from src.database.mixins.HasID import HasID
from src.database.mixins.HasTimestamp import TimestampMixin
from src.database.mixins.HasTitle import HasTitle
from src.database.mixins.HasDescription import HasDescription
from src.database.mixins.HasCover import HasCover


tags_subscriptions = Table(
    "tags_subscriptions",
    Base.metadata,
    Column("author_id", String(), ForeignKey("authors.id"), primary_key=True),
    Column("tag_title", String(), ForeignKey("tags.title"), primary_key=True),
)


class Tag(TimestampMixin, HasTitle, HasDescription, HasCover, Base):
    __tablename__ = "tags"
    __mapper_args__ = {"eager_defaults": True}

    title = Column(String(255), unique=True, primary_key=True)

    subscribers = relationship(
        "Author", secondary="tags_subscriptions", back_populates="tags_subscribed"
    )

    posts_count = Column(Integer, nullable=False, server_default="0")
