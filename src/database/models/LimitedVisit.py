from sqlalchemy import Column, String, DateTime, ForeignKey

from src.database.mixins.HasID import HasID
from datetime import datetime
from src.database.session import Base


class LimitedVisit(HasID, Base):
    __tablename__ = "limited_visits"

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    endpoint = Column(String, nullable=False)
    user_id = Column(String, ForeignKey("authors.id", ondelete="CASCADE"), nullable=False)
