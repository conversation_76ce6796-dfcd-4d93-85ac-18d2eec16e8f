from typing import List

from sqlalchemy import String, TEXT, <PERSON><PERSON>an, Foreign<PERSON>ey, ARRAY
from sqlalchemy.orm import Mapped, mapped_column

from src.database import Base
from src.database.mixins import HasPostID
from src.database.models import Post

from src.common.mini_apps import enums


class MiniApp(HasPostID, Post):
    __tablename__ = "mini_apps"
    __mapper_args__ = {
        "polymorphic_identity": "MiniApp",
    }

    name: Mapped[str] = mapped_column(String(length=255), nullable=False)
    description: Mapped[str] = mapped_column(TEXT)
    moderation_status: Mapped[enums.MiniAppModerationStatus] = mapped_column(
        String(),
        default=enums.MiniAppModerationStatus.ON_APPROVAL,
    )
    app_status: Mapped[enums.MiniAppStatus] = mapped_column(
        String(),
        default=enums.MiniAppStatus.ACTIVE,
    )
    icon: Mapped[str] = mapped_column(String(length=512), nullable=False)
    cover: Mapped[str] = mapped_column(
        String(length=512),
        nullable=False,
    )
    is_web: Mapped[bool] = mapped_column(
        <PERSON><PERSON>an,
        nullable=False,
    )
    is_mobile: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
    )
    version: Mapped[str] = mapped_column(
        String(length=512),
        nullable=False,
    )
    url: Mapped[str] = mapped_column(
        String(length=255),
        nullable=False,
    )


class AuthorAppInteraction(Base):
    __tablename__ = "user_app_interactions"

    author_id: Mapped[str] = mapped_column(
        String(),
        ForeignKey("authors.id", ondelete="CASCADE"),
        unique=True,
        primary_key=True,
    )
    apps: Mapped[List[str]] = mapped_column(
        ARRAY(String, zero_indexes=True),
        default=[],
    )
