from sqlalchemy import Column, String, ForeignKey, PrimaryKeyConstraint
from sqlalchemy.orm import query_expression

from src.database.session import Base


class Translation(Base):
    __tablename__ = "translations"

    original_id = Column(String, ForeignKey("posts.id", ondelete="CASCADE"), nullable=False)
    post_id = Column(String, ForeignKey("posts.id", ondelete="CASCADE"), nullable=False)
    language = Column(String, nullable=False)

    __table_args__ = (PrimaryKeyConstraint("post_id"),)

    original = query_expression(default_expr=original_id == post_id)
