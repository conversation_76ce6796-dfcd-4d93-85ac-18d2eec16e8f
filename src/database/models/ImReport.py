from datetime import datetime
from enum import Enum

from sqlalchemy import String, ForeignKey, DateTime, Boolean, Text, Integer, Column
from sqlalchemy.orm import relationship

from src.database import Base
from src.database.mixins import generate_shortid
from src.reports.enums import ReportReason


class ChatType(str, Enum):
    PRIVATE = "private"  # 私聊
    GROUP = "group"      # 群聊


class ImReport(Base):
    __tablename__ = "im_reports"

    id = Column(
        String(),
        primary_key=True,
        default=generate_shortid,
    )

    # 举报者ID
    reporter_id = Column(
        Text,
        nullable=False,
    )

    # 被举报者ID
    reported_user_id = Column(
        Text,
        nullable=False,
    )

    # 会话ID
    conversation_id = Column(
        Text,
        nullable=False,
    )

    # 聊天类型（私聊/群聊）
    chat_type = Column(
        Text,
        nullable=False,
    )

    # 举报的消息内容
    message_content = Column(
        Text,
        nullable=True,
    )

    # 举报原因
    reason = Column(
        Text,
        nullable=False,
    )

    # 举报者备注
    comment = Column(
        Text,
        nullable=True,
    )

    # 处理状态
    is_handled = Column(
        Boolean,
        default=False,
    )

    # 处理人ID
    handler_id = Column(
        Text,
        nullable=True,
    )

    # 处理时间
    handled_at = Column(
        DateTime,
        nullable=True,
    )

    # 创建时间
    created_at = Column(
        DateTime, 
        default=datetime.now
    )
