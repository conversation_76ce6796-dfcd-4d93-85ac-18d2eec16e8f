from datetime import datetime

from sqlalchemy import Index, Computed, text, select, func
from sqlalchemy.orm import relationship, query_expression, column_property
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.sql.sqltypes import ARRAY, String, Integer, DateTime
from sqlalchemy.sql.schema import Column, ForeignKey, Table
from sqlalchemy.ext.hybrid import hybrid_property

from src.database import Base
from src.database.models.Post import Post
from src.database.mixins import (
    TSVector, HasCover, HasPostID, HasTitle, HasDescription, HasPins
)


collections_subscriptions = Table(
    "collections_subscriptions",
    Base.metadata,
    Column("author_id", String(), Foreign<PERSON>ey("authors.id"), primary_key=True),
    Column("collection_id", String(), ForeignKey("collections.id", ondelete="CASCADE"), primary_key=True),
    Column("timestamp", DateTime, default=datetime.utcnow, nullable=False)
)

collections_contributors = Table(
    "collections_contributors",
    Base.metadata,
    Column("author_id", String(), <PERSON><PERSON>ey("authors.id"), primary_key=True),
    Column("collection_id", String(), Foreign<PERSON>ey("collections.id", ondelete="CASCADE"), primary_key=True),
    Column("timestamp", DateTime, default=datetime.utcnow, nullable=False)
)


class Collection(HasPostID, HasCover, HasTitle, HasDescription, HasPins, Post):
    __tablename__ = "collections"
    __mapper_args__ = {
        "polymorphic_identity": "Collection",
    }
    
    def fix_id_from_state(self):
        """修复 ID 值从 SQLAlchemy 状态"""
        if (hasattr(self, '_sa_instance_state') and
            self._sa_instance_state.identity and
            self.id is None):
            # 直接设置 __dict__ 中的 id 值
            self.__dict__['id'] = self._sa_instance_state.identity[0]

    # __ts_vector__ = Column(
    #     TSVector(),
    #     Computed("to_tsvector('english', coalesce(title,'') || ' ' || coalesce(description,''))", persisted=True),
    # )
    # __table_args__ = (
    #     Index("ix_collections___ts_vector__", __ts_vector__, postgresql_using="gin"),
    # )

    content_type = Column(String, nullable=False, server_default="Mixed")
    contents = Column(MutableList.as_mutable(ARRAY(String)), nullable=False, default=[])
    contents_count = Column(Integer, nullable=False, server_default="0")
    carnival_status = Column(String(), nullable=False, server_default="new")
    carnival_start_time = Column(DateTime(), nullable=True)
    original_cover = Column(String(255), nullable=True)
    subscriber_count = Column(Integer, nullable=False, server_default="0")
    contributor_count = Column(Integer, nullable=False, server_default="0")

    # commits = relationship("Commit", cascade="all,delete")

    subscribers = relationship(
        "Author", secondary="collections_subscriptions", back_populates="collections_subscribed"
    )
    contributors = relationship(
        "Author", secondary="collections_contributors", back_populates="collections_contributed"
    )

    is_subscribed = query_expression()
    is_contributor = query_expression()
    can_commit = query_expression()
    is_post_in_collection = query_expression()

    @hybrid_property
    def posts_count(self):
        return len(self.contents)  # type: ignore