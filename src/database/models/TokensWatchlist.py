from datetime import datetime
from sqlalchemy import Column, String, ForeignKey, DateTime, func, Index

from src.database.session import Base

class TokensWatchlist(Base):
    __tablename__ = 'tokens_watchlist'
    __table_args__ = (
        Index("idx_author_id", "author_id"),
        Index("idx_author_id_token_address", "author_id", "token_address"),
    )

    author_id = Column(String, ForeignKey("authors.id"), primary_key=True)
    token_address = Column(String, primary_key=True)
    timestamp = Column(DateTime, default=datetime.utcnow, onupdate=func.now())