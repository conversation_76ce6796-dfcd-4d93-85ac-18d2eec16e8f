from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Column,
    BigInteger,
    Integer,
    Text,
    Numeric,
    DateTime,
    func,
    text,
    Index,
)
from src.database.session import Base


class Token(Base):
    __tablename__ = 'token'
    __table_args__ = (
        Index("idx_token_address_creator", "token_address", "creator"),
    )

    id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="Primary key, auto-increment."
    )
    chain = Column(
        Integer,
        nullable=True,
        comment="Chain ID, can be null."
    )
    token_address = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        unique=True,
        comment="Token contract address as TEXT. Not null, default empty string."
    )
    name = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        index=True,
        comment="Token name, not null, default empty string."
    )
    symbol = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        index=True,
        comment="Token symbol, not null, default empty string."
    )
    decimals = Column(
        Integer,
        nullable=False,
        server_default=text('0'),
        comment="Decimal places of the token, default 0."
    )
    total_supply = Column(
        Numeric(78, 0),
        nullable=False,
        server_default=text('0'),
        comment="Total supply of the token, default 0."
    )
    creator = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="Creator address as text, default empty string."
    )
    created_at = Column(
        DateTime,
        nullable=True,
        comment="Record creation time, can be null."
    )
    logo = Column(
        Text,
        nullable=False,
        server_default=text("''"),
        comment="Logo URL of the token, not null, default empty string."
    )
    updated_at = Column(
        DateTime,
        nullable=True,
        onupdate=func.now(),
        comment="Record update time, can be null."
    )
    creator_id: str = Column(
        Text,
        nullable=True,
        comment="Creator ID -> , not null."
    )
    collection_id: str = Column(
        Text,
        nullable=True,
        comment="Collection ID, not null."
    )