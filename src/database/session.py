"""
数据库会话与引擎（PgBouncer 事务池友好）

使用约定：
- 仅在应用生命周期（FastAPI lifespan/startup）里调用 init_engines(echo=...) 一次；
- 模块导入期（import）禁止做任何初始化操作；
- 通过依赖工厂 session_dep()/tx_session_dep() 向路由分发会话；
- 每个请求一个会话，短生命周期，不要跨请求缓存 AsyncSession。
"""

import os
import logging
import time
import threading
from contextlib import asynccontextmanager, contextmanager
from typing import AsyncGenerator, Optional, Literal, Generator, Any

from sqlalchemy import event, text, create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker, AsyncEngine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool
from sqlalchemy.orm import declarative_base

logger = logging.getLogger(__name__)

# --------------------------
# 声明基类 + PgBouncer（事务池）友好：延迟初始化 + NullPool
# --------------------------

Base = declarative_base()

async_write_engine: Optional[AsyncEngine] = None
async_read_engine: Optional[AsyncEngine] = None
SessionWrite: Optional[async_sessionmaker[AsyncSession]] = None
SessionRead: Optional[async_sessionmaker[AsyncSession]] = None
# 兼容别名：指向写库的会话工厂，运行时在 init_engines() 中赋值
_init_lock = threading.Lock()

# 同步引擎/会话（仅供 Celery/同步路径使用）
sync_engine = None
SessionSync: Optional[sessionmaker] = None

__all__ = [
    "init_engines",
    "dispose_engines",
    "get_session",
    "get_session_context",
    "get_tx_session",
    "get_read_session",
    "get_transactional_session",
    "get_sync_context",
    "SessionWrite",
    "SessionRead",
    "Base",
    "session_dep",
    "tx_session_dep",
]


def _build_async_engine_pgbouncer(dsn: str, *, application_name: str, read_only: bool,
                                  echo: bool = False) -> AsyncEngine:
    connect_args = {
        "server_settings": {
            "application_name": application_name,
        },
        # 客户端侧超时；建议配合 PG 侧 statement_timeout
        "command_timeout": 30 if read_only else 60,
        "timeout": 30,
        # 事务池模式下必须关闭，避免跨连接失配
        "statement_cache_size": 0,
        "prepared_statement_cache_size": 0,
    }
    return create_async_engine(
        dsn,
        poolclass=NullPool,
        pool_pre_ping=False,  # 交给 PgBouncer 处理
        echo=echo,
        connect_args=connect_args,
    )


def _install_slow_query_logger(engine: AsyncEngine, threshold_ms: int = 200) -> None:
    @event.listens_for(engine.sync_engine, "before_cursor_execute")
    def _before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        context._query_start_time = time.perf_counter()
        try:
            context._params_preview = str(parameters)[:300] if parameters is not None else ""
        except Exception:
            context._params_preview = "<unrepr-params>"

    @event.listens_for(engine.sync_engine, "after_cursor_execute")
    def _after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        started = getattr(context, "_query_start_time", None)
        if started is not None:
            elapsed_ms = (time.perf_counter() - started) * 1000.0
            if elapsed_ms >= threshold_ms:
                rowcount = getattr(cursor, "rowcount", -1)
                logger.warning(
                    "Slow SQL %.1f ms rows=%s | %s | params=%s",
                    elapsed_ms,
                    rowcount,
                    statement[:500],
                    getattr(context, "_params_preview", ""),
                )


def init_engines(*, echo: bool = False) -> None:
    """在应用启动时调用，按 PgBouncer 事务池模式初始化异步读写引擎。

    注意：不要在任何模块 import 期间调用，仅在 FastAPI lifespan/startup 中调用一次。
    """
    global async_write_engine, async_read_engine, SessionWrite, SessionRead

    with _init_lock:
        if async_write_engine or async_read_engine:
            # 避免重复初始化（热重载/并发启动钩子）
            logger.info("Database engines already initialized; skip re-init")
            return

        dsn_write = os.environ["DATABASE_URL_ASYNC"]
        dsn_read = os.environ.get("DATABASE_URL_ASYNC_READ", dsn_write)

        async_write_engine = _build_async_engine_pgbouncer(
            dsn_write, application_name="memefans_write", read_only=False, echo=echo
        )
        async_read_engine = _build_async_engine_pgbouncer(
            dsn_read, application_name="memefans_read", read_only=True, echo=echo
        )

        SessionWrite = async_sessionmaker(bind=async_write_engine, expire_on_commit=False, autoflush=False)
        SessionRead = async_sessionmaker(bind=async_read_engine, expire_on_commit=False, autoflush=False)

        _install_slow_query_logger(async_write_engine)
        _install_slow_query_logger(async_read_engine)
        logger.info("Database async engines initialized (PgBouncer/NullPool)")


async def dispose_engines() -> None:
    """在应用停止时调用，释放底层资源。"""
    global async_write_engine, async_read_engine, SessionWrite, SessionRead
    try:
        if async_write_engine:
            await async_write_engine.dispose()
        if async_read_engine:
            await async_read_engine.dispose()
        # 同步引擎（若已初始化）
        global sync_engine, SessionSync
        if sync_engine is not None:
            try:
                sync_engine.dispose()
            except Exception:
                pass
    finally:
        async_write_engine = None
        async_read_engine = None
        SessionWrite = None
        SessionRead = None
        sync_engine = None
        SessionSync = None
        logger.info("Database async engines disposed")


async def get_session(role: Literal["read", "write"] = "write") -> AsyncGenerator[AsyncSession, None]:
    """FastAPI 依赖：返回 AsyncSession；不要用于 async with。"""
    assert SessionWrite and SessionRead, "engines not initialized; call init_engines() at startup"
    factory = SessionRead if role == "read" else SessionWrite
    async with factory() as session:
        yield session


@asynccontextmanager
async def get_session_context(role: Literal["read", "write"] = "write") -> AsyncGenerator[AsyncSession, None]:
    """上下文管理用法：async with get_session_context(...)."""
    assert SessionWrite and SessionRead, "engines not initialized; call init_engines() at startup"
    factory = SessionRead if role == "read" else SessionWrite
    async with factory() as session:
        yield session


@asynccontextmanager
async def get_tx_session(role: Literal["read", "write"] = "write", statement_timeout_ms: Optional[int] = None) -> \
AsyncGenerator[AsyncSession, None]:
    assert SessionWrite and SessionRead, "engines not initialized; call init_engines() at startup"
    factory = SessionRead if role == "read" else SessionWrite
    effective_timeout = statement_timeout_ms if statement_timeout_ms is not None else (3000 if role == "read" else 5000)
    async with factory() as session, session.begin():
        # 针对读事务，在事务级别强制只读，适配 PgBouncer 事务池
        if role == "read":
            await session.execute(text("SET TRANSACTION READ ONLY"))
        if effective_timeout is not None:
            await session.execute(text(f"SET LOCAL statement_timeout = {int(effective_timeout)}"))
        yield session


# 兼容别名（保留关键常用别名，供 "async with" 使用）
@asynccontextmanager
async def get_read_session() -> AsyncGenerator[AsyncSession, None]:
    async with get_session_context("read") as session:
        yield session


@asynccontextmanager
async def get_transactional_session() -> AsyncGenerator[AsyncSession, None]:
    async with get_tx_session("write") as session:
        yield session


@contextmanager
def get_sync_context() -> Generator[Session, Any, None]:
    """提供同步 SQLAlchemy Session（PgBouncer 事务池友好）。

    - 延迟初始化同步 Engine，使用 NullPool，避免长连接/状态跨复用
    - with 语义：退出时自动提交；异常时回滚
    """
    global sync_engine, SessionSync
    if SessionSync is None or sync_engine is None:
        with _init_lock:
            if SessionSync is None or sync_engine is None:
                dsn = os.environ["DATABASE_URL"]
                sync_engine = create_engine(
                    dsn,
                    poolclass=NullPool,
                    pool_pre_ping=False,
                    echo=False,
                )
                SessionSync = sessionmaker(bind=sync_engine, expire_on_commit=False)

    session: Session = SessionSync()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


# --------------------------
# FastAPI 依赖工厂（推荐）：在路由中直接声明读/写与事务
# --------------------------

def session_dep(role: Literal["read", "write"]):
    async def _dep() -> AsyncGenerator[AsyncSession, None]:
        async with get_session_context(role) as s:
            yield s

    return _dep


def tx_session_dep(role: Literal["read", "write"], timeout_ms: Optional[int] = None):
    async def _dep() -> AsyncGenerator[AsyncSession, None]:
        async with get_tx_session(role, statement_timeout_ms=timeout_ms) as s:
            yield s

    return _dep
