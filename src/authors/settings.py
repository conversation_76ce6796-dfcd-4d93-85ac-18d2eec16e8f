from decimal import Decimal

from pydantic_settings import BaseSettings, SettingsConfigDict

from src.common.constants import Environment


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")

    ENVIRONMENT: Environment = Environment.PRODUCTION
    DATABASE_URL: str

    GRADE_E1_DIRECTS_COUNT: int = 3
    GRADE_E1_NETWORK_SIZE: int = 20
    GRADE_E1_MULTIPLIER: Decimal = Decimal("0.2")

    GRADE_E2_DIRECTS_COUNT: int = 5
    GRADE_E2_NETWORK_SIZE: int = 50
    GRADE_E2_MULTIPLIER: Decimal = Decimal("0.3")

    GRADE_E3_DIRECTS_COUNT: int = 10
    GRADE_E3_NETWORK_SIZE: int = 200
    GRADE_E3_MULTIPLIER: Decimal = Decimal("0.4")

    GRADE_E4_DIRECTS_COUNT: int = 20
    GRADE_E4_NETWORK_SIZE: int = 500
    GRADE_E4_MULTIPLIER: Decimal = Decimal("0.5")

    GRADE_E5_DIRECTS_COUNT: int = 40
    GRADE_E5_NETWORK_SIZE: int = 2000
    GRADE_E5_MULTIPLIER: Decimal = Decimal("0.6")

    GRADE_E6_DIRECTS_COUNT: int = 80
    GRADE_E6_NETWORK_SIZE: int = 5000
    GRADE_E6_MULTIPLIER: Decimal = Decimal("0.7")

    GRADE_E7_DIRECTS_COUNT: int = 160
    GRADE_E7_NETWORK_SIZE: int = 20_000
    GRADE_E7_MULTIPLIER: Decimal = Decimal("0.75")

    GRADE_E8_DIRECTS_COUNT: int = 320
    GRADE_E8_NETWORK_SIZE: int = 50_000
    GRADE_E8_MULTIPLIER: Decimal = Decimal("0.8")

    GRADE_E9_DIRECTS_COUNT: int = 640
    GRADE_E9_NETWORK_SIZE: int = 200_000
    GRADE_E9_MULTIPLIER: Decimal = Decimal("0.85")

    GRADE_E10_DIRECTS_COUNT: int = 1280
    GRADE_E10_NETWORK_SIZE: int = 500_000
    GRADE_E10_MULTIPLIER: Decimal = Decimal("0.9")


settings = Settings()
