from .settings import settings


def get_grade_number(directs_count: int, network_size: int) -> int:
    if (
        directs_count >= settings.GRADE_E10_DIRECTS_COUNT
        and network_size >= settings.GRADE_E10_NETWORK_SIZE
    ):
        return 10
    if (
        directs_count >= settings.GRADE_E9_DIRECTS_COUNT
        and network_size >= settings.GRADE_E9_NETWORK_SIZE
    ):
        return 9
    if (
        directs_count >= settings.GRADE_E8_DIRECTS_COUNT
        and network_size >= settings.GRADE_E8_NETWORK_SIZE
    ):
        return 8
    if (
        directs_count >= settings.GRADE_E7_DIRECTS_COUNT
        and network_size >= settings.GRADE_E7_NETWORK_SIZE
    ):
        return 7
    if (
        directs_count >= settings.GRADE_E6_DIRECTS_COUNT
        and network_size >= settings.GRADE_E6_NETWORK_SIZE
    ):
        return 6
    if (
        directs_count >= settings.GRADE_E5_DIRECTS_COUNT
        and network_size >= settings.GRA<PERSON>_E5_NETWORK_SIZE
    ):
        return 5
    if (
        directs_count >= settings.GRADE_E4_DIRECTS_COUNT
        and network_size >= settings.GRADE_E4_NETWORK_SIZE
    ):
        return 4
    if (
        directs_count >= settings.GRADE_E3_DIRECTS_COUNT
        and network_size >= settings.GRADE_E3_NETWORK_SIZE
    ):
        return 3
    if (
        directs_count >= settings.GRADE_E2_DIRECTS_COUNT
        and network_size >= settings.GRADE_E2_NETWORK_SIZE
    ):
        return 2
    if (
        directs_count >= settings.GRADE_E1_DIRECTS_COUNT
        and network_size >= settings.GRADE_E1_NETWORK_SIZE
    ):
        return 1
    return 0
