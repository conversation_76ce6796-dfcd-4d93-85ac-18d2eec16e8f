from typing import Optional, Annotated, List, Literal
from fastapi.openapi.utils import get_openapi
from httpx import AsyncClient
from fastapi import FastAPI, Depends, status, HTTPException, Query
from fastapi_pagination import Page, Params, add_pagination, paginate
from fastapi_pagination.ext.sqlalchemy import paginate as sa_paginate
from fastapi_pagination.utils import disable_installed_extensions_check
from sqlalchemy import select, text, func, or_, update
from sqlalchemy.orm import selectinload, with_loader_criteria

from src.common.notifications.enums import NotificationType
from src.database.session import AsyncSession, get_session
from src.database.models.Collection import collections_subscriptions, Collection
from src.database.models.Author import authors_followers, authors_blocks
from src.database.models import Author, Invitation, Notification, Point, User, Permission
from src.database.models.TociIm import TociIm
from src.database.schemas.Notification import NotificationRead
from src.database.schemas.Collection import CollectionRead
from src.auth import current_user, optional_user

from src.common.dependencies import admin, super_admin
from src.common.points.enums import PointStatus, PointsStatus, PointsAction
from src.common.constants import CollectionContentType, UserStatus, PermissionTypes

from src.authors.service import AuthorService, get_author_service
from src.authors.dependencies import valid_collection_id
from src.infra.logger import get_logger
from src.infra.app import create_app
from src.authors.schemas import (
    AuthorRead,
    AuthorWithWallet,
    AuthorReadExtended,
    AuthorExtendedWithCounters,
    AuthorWithGrade,
    AuthorWithFlags,
    Activity,
    PointStat,
    PointPassiveAnalytics,
    PointAnalytics,
    UpdateNameRequest,
    UpdateDescriptionRequest,
    AuthorUpdate,
)
from src.notifications.service import NotificationService, get_notification_service
from src.posts.collections.service import CollectionService, get_collection_service
from src.common.middleware import RequestTimingMiddleware
from src.auth.settings import settings as auth_settings
from src.common.elasticsearch.sync_client import create_es_sync_client

logger = get_logger("authors", level="INFO", file_path="latest.log")
app = create_app(title="Authors", description="Authors API", version="1.0", request_logger=logger)
add_pagination(app)
disable_installed_extensions_check()
http_client = AsyncClient()


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="Author Service",
        version="1.0.1",
        description="Author Service API",
        routes=app.routes,
    )

    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }

    openapi_schema["security"] = [{"BearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get(
    "/",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorReadExtended],
    dependencies=[Depends(admin)],
    tags=['Admin'],
)
async def get_all(
    status: UserStatus = Query(None),
    role: PermissionTypes = Query(None),
    search: str = Query(None),
    params: Params = Depends(),
    session: AsyncSession = Depends(get_session),
):
    stmt = select(Author).join(Author.user)

    if status is not None:
        stmt = stmt.where(User.status == status)

    if role is not None:
        if PermissionTypes.user == role:
            stmt = stmt.outerjoin(Author.permission).where(
                or_(
                    Permission.name == role,
                    Author.permission == None,
                )
            )
        else:
            stmt = stmt.join(Author.permission).where(Permission.name == role)

    if search is not None:
        search_term = f"%{search}%"
        stmt = stmt.where(
            or_(
                Author.username_raw.ilike(search_term.lower()),
                Author.name.ilike(search_term),
                Author.email.ilike(search_term),
                Author.phone_number.ilike(search_term),
            )
        )

    stmt = stmt.order_by(Author.created_at.desc())

    return await sa_paginate(session, stmt, params)


@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get("/batch", status_code=status.HTTP_200_OK, response_model=List[AuthorRead])
async def get_batch(
    ids: Annotated[list[str] | None, Query()] = None,
    session: AsyncSession = Depends(get_session),
):
    if ids is None:
        return []
    stmt = select(Author).where(Author.id.in_(ids))
    return (await session.execute(stmt)).scalars()


@app.get("/me", status_code=status.HTTP_200_OK, response_model=AuthorReadExtended)
async def get_me(
    user: User = Depends(current_user), 
    session: AsyncSession = Depends(get_session),
):
    stmt = select(Author).where(Author.id == user.id)
    author = (await session.execute(stmt)).scalar_one()
    return author


@app.put("/me", status_code=status.HTTP_200_OK, response_model=AuthorReadExtended)
async def update_me(
    update_data: AuthorUpdate,
    user: User = Depends(current_user),
    author_service: AuthorService = Depends(get_author_service)
):
    """
    Update current user's author information.
    Validates that username and email are unique before updating.
    """
    return await author_service.update(user.id, update_data)


@app.get(
    "/me/invited",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorWithGrade],
)
async def get_invited(
    sort: Optional[str] = None,
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
    params: Params = Depends(),
):
    subq = select(Invitation.id).where(Invitation.author_id == user.id)
    stmt = select(Author).where(Author.invitation_id == subq)
    if sort:
        if sort == "created_at":
            stmt = stmt.order_by(Author.created_at.desc())
        elif sort == "group_grade":
            stmt = stmt.order_by(Author.group_grade.desc())
        elif sort == "direct_invited_count":
            stmt = stmt.order_by(Author.direct_invited_count.desc())
        elif sort == "group_size":
            stmt = stmt.order_by(Author.group_size.desc())
    return await sa_paginate(session, stmt, params)


@app.get("/me/grade", status_code=status.HTTP_200_OK, response_model=AuthorWithGrade)
async def get_grade(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    subq = select(Invitation.id).where(Invitation.author_id == user.id)
    stmt = select(Author).where(Author.id == user.id)
    model: Author = (await session.execute(stmt)).scalar_one()

    stmt = select(func.count(Author.id)).where(Author.invitation_id == subq)
    model.directs_count = (await session.execute(stmt)).scalar()

    return model


@app.get("/me/wallet", status_code=status.HTTP_200_OK, response_model=AuthorWithWallet)
async def get_my_wallet(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = select(Author).where(Author.id == user.id)
    model: Author = (await session.execute(stmt)).scalar_one()

    stmt = text(
        f"""
        SELECT SUM(amount)
        FROM points
        WHERE status='issued' AND author_id = '{user.id}'
    """
    )
    points = (await session.execute(stmt)).scalar() or 0
    model.points = str(points)

    stmt = text(
        f"""
        SELECT SUM(amount)
        FROM points
        WHERE status='pending' AND author_id = '{user.id}'
    """
    )
    pending_points = (await session.execute(stmt)).scalar() or 0
    model.pending_points = str(pending_points)

    model.potential_points = str(points + pending_points)

    stmt = text(
        f"""
        SELECT SUM(amount)
        FROM points
        WHERE author_id = '{user.id}'
            AND status = 'issued'
            AND DATE(created_at) = CURRENT_DATE
    """
    )
    model.today_points = str((await session.execute(stmt)).scalar() or 0)

    address = (
        await session.execute(
            text(f"SELECT address FROM wallets WHERE author_id = '{user.id}'")
        )
    ).scalar()
    # if address:
    #     try:
    #         model.epi = str(
    #             (
    #                 await http_client.get(f"http://toci-dev-01.aurora:8099/users?user_address={address}")
    #             ).json()["user_balance"]
    #         )
    #     except KeyError:
    model.epi = None
    model.wallet_address = str(address)
    return model


@app.get(
    "/me/notifications/count",
    status_code=status.HTTP_200_OK,
)
async def get_notifications_count(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = text(
        f"""
        SELECT COUNT(*)
        FROM notifications
        WHERE status = 'created'
            AND recipient_id = '{user.id}'
    """
    )
    return {"unread_count": (await session.execute(stmt)).scalar()}


@app.get(
    "/me/notifications",
    status_code=status.HTTP_200_OK,
    response_model=Page[NotificationRead],
)
async def get_notifications(
    type: Optional[str] = None,
    params: Params = Depends(),
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
):
    allowed_types = [
        NotificationType.author_follow,
        NotificationType.post_comment,
        NotificationType.post_upvote,
        NotificationType.collection_subscribe,
        NotificationType.follow_meme,
    ]
    
    stmt = select(Notification).where(
        Notification.recipient_id == user.id,
        Notification.type.in_(allowed_types)
    )
    
    if type is not None:
        stmt = stmt.where(Notification.type == type)
    
    return await sa_paginate(
        session, stmt.order_by(Notification.created_at.desc()), params
    )


@app.get(
    "/me/assets",
    summary="Get history of assets-obtaining",
    response_model=Page[Activity],
)
async def get_assets(
    params: Params = Depends(),
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
):
    statement = (
        select(Point)
        .where(Point.author_id == user.id)
        .order_by(Point.created_at.desc())
    )

    return await sa_paginate(session, statement, params)


@app.get("/me/points_series", response_model=List[PointStat])
async def points_series(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = text(
        """
        SELECT
            DATE(dates.date)::TEXT AS "date",
            COALESCE(p.amount, 0) AS "amount"
        FROM generate_series(CURRENT_DATE - INTERVAL '9 days', CURRENT_DATE, '1 day') dates(date)
        LEFT JOIN (
            SELECT
                DATE(created_at) AS "date",
                SUM(amount) AS "amount"
            FROM points
            WHERE author_id = :author_id
                AND (
                    status = :status_issued
                    OR status = :status_spent
                )
                AND DATE(created_at) <= CURRENT_DATE
                AND DATE(created_at) >= CURRENT_DATE - INTERVAL '10 days'
            GROUP BY DATE(created_at)
        ) p ON dates.date = p.date
        ORDER BY dates.date ASC;
    """
    )

    params = {
        "author_id": user.id,
        "status_issued": PointStatus.ISSUED,
        "status_spent": PointStatus.SPENT,
    }

    results = await session.execute(stmt, params)

    stats: List[PointStat] = []
    for date, amount in results.all():
        stats.append(PointStat(date=date, amount=amount))

    return stats


@app.get("/me/points_passive_analytics", response_model=PointPassiveAnalytics)
async def points_passive_analytics(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = text(
        """
        SELECT
            SUM(amount) AS "count",
            SUM((points.meta->>'likes_count')::FLOAT) AS "likes_count",
            SUM((points.meta->>'likes_points')::FLOAT) AS "likes_points",
            SUM((points.meta->>'comments_count')::FLOAT) AS "comments_count",
            SUM((points.meta->>'comments_points')::FLOAT) AS "comments_point",
            SUM((points.meta->>'commits_count')::FLOAT) AS "commits_count",
            SUM((points.meta->>'commits_points')::FLOAT) AS "commits_points",
            SUM((points.meta->>'saves_count')::FLOAT) AS "saves_count",
            SUM((points.meta->>'saves_points')::FLOAT) AS "saves_points"
        FROM points
        WHERE author_id = :author_id
            AND (
                status = :status_issued
                OR status = :status_spent
            )
            AND action = :action_daily_active
            AND DATE(created_at) = CURRENT_DATE
        GROUP BY action
        """
    )

    params = {
        "author_id": user.id,
        "status_issued": PointsStatus.ISSUED,
        "status_spent": PointsStatus.SPENT,
        "action_daily_active": PointsAction.DAILY_ACTIVE,
    }

    analytics = PointPassiveAnalytics()
    row = await session.execute(stmt, params)
    row = row.first()

    if row:
        analytics.count = row[0]
        analytics.likes_count = row[1]
        analytics.likes_points = row[2]
        analytics.comments_count = row[3]
        analytics.comments_points = row[4]
        analytics.commits_count = row[5]
        analytics.commits_points = row[6]
        analytics.saves_count = row[7]
        analytics.saves_points = row[8]

    return analytics


@app.get("/me/points_analytics", response_model=PointAnalytics)
async def points_analytics(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = text(
        """
        SELECT
            action AS "action",
            COUNT(id) AS "amount",
            SUM(amount) AS "points"
        FROM points
        WHERE author_id = :author_id
            AND (
                status = :status_issued
                OR status = :status_spent
            )
            AND action != :action_daily_active
            AND DATE(created_at) = CURRENT_DATE
        GROUP BY action
        """
    )

    params = {
        "author_id": user.id,
        "status_issued": PointsStatus.ISSUED,
        "status_spent": PointsStatus.SPENT,
        "action_daily_active": PointsAction.DAILY_ACTIVE,
    }

    results = await session.execute(stmt, params)

    analytics = PointAnalytics()

    for action, amount, points in results.all():
        match action:
            case PointsAction.LIKE:
                analytics.like_amount = amount
                analytics.like_points = points
            case PointsAction.COMMENT:
                analytics.comment_amount = amount
                analytics.comment_points = points
            case PointsAction.COMMIT:
                analytics.commit_amount = amount
                analytics.commit_points = points
            case PointsAction.SAVE:
                analytics.save_amount = amount
                analytics.save_points = points
            case PointsAction.COMMIT:
                analytics.commit_approve_amount = amount
                analytics.commit_approve_points = points
    return analytics


@app.patch(
    "/me/notifications",
    status_code=status.HTTP_200_OK,
)
async def read_notifications(
    user: User = Depends(current_user), session: AsyncSession = Depends(get_session)
):
    stmt = text(
        f"""
        UPDATE notifications
        SET status = 'read'
        WHERE recipient_id = '{user.id}'
    """
    )
    await session.execute(stmt)
    await session.commit()


@app.patch(
    "/me/display_name",
    status_code=status.HTTP_200_OK,
    response_model=AuthorRead
)
async def update_display_name(
    request: UpdateNameRequest,
    user: User = Depends(current_user),
    author_service: AuthorService = Depends(get_author_service)
):
    """更新用户名称，只有当name字段为空时才允许更新"""
    return await author_service.update_name(user.id, request.display_name)


@app.patch(
    "/me/bio",
    status_code=status.HTTP_200_OK,
    response_model=AuthorRead
)
async def update_description(
    request: UpdateDescriptionRequest,
    user: User = Depends(current_user),
    author_service: AuthorService = Depends(get_author_service)
):
    """更新用户描述，只有当description字段为空时才允许更新"""
    return await author_service.update_description(user.id, request.description)


@app.get(
    "/subscribed_to",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorWithFlags]
)
async def get_subscribers(
        collection_id: str = Depends(valid_collection_id),
        sort: Literal["new", "old"] = "new",
        user: User | None = Depends(optional_user),
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
):
    stmt = (
        select(Author)
        .join(collections_subscriptions)
        .where(collections_subscriptions.c.collection_id == collection_id)
        .options(
            AuthorService.is_following_expression(getattr(user, "id", None)),
            AuthorService.follower_count_expression()
        )
    )
    if sort == "new":
        stmt.order_by(collections_subscriptions.c.timestamp.desc())
    if sort == "old":
        stmt.order_by(collections_subscriptions.c.timestamp.asc())
    return await sa_paginate(session, stmt, params)


@app.get(
    "/{id_or_username}",
    status_code=status.HTTP_200_OK,
    response_model=AuthorExtendedWithCounters,
)
async def get_one(
        id_or_username: str,
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
):
    stmt = select(Author).where(or_(Author.id == id_or_username, Author.username_raw == id_or_username.lower()))
    stmt = stmt.options(
        AuthorService.follower_count_expression(),
        AuthorService.following_count_expression(),
        AuthorService.is_following_expression(getattr(user, 'id', None)),
        AuthorService.is_blocked_expression(getattr(user, 'id', None))
    )
    author = (await session.execute(stmt)).first()
    if not author:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Author does not exist")
    
    # 查询im_id
    author_obj = author[0]
    im_stmt = select(TociIm.im_id).where(TociIm.toci_id == author_obj.id)
    im_result = await session.execute(im_stmt)
    im_id = im_result.scalar_one_or_none()
    
    # 手动设置im_id字段
    author_obj.im_id = im_id
    
    # 查询用户关注的meme数量
    from src.database.models.TokensWatchlist import TokensWatchlist
    meme_count_stmt = select(func.count(TokensWatchlist.token_address)).where(TokensWatchlist.author_id == author_obj.id)
    meme_count_result = await session.execute(meme_count_stmt)
    followed_memes_count = meme_count_result.scalar() or 0
    
    # 手动设置followed_memes_count字段
    author_obj.followed_memes_count = followed_memes_count
    
    
    return author_obj


@app.get(
    "/{author_id}/followers",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorWithFlags],
)
async def get_followers(
    author_id: str,
    filter: Optional[str] = Query(None, title="Filter by username or name"),
    params: Params = Depends(),
    user: User = Depends(optional_user),
    session: AsyncSession = Depends(get_session),
):
    stmt = (
        select(Author)
        .join(authors_followers, onclause=Author.id == authors_followers.c.follower_id)
        .where(authors_followers.c.author_id == author_id)
        .options(
            AuthorService.is_following_expression(getattr(user, 'id', None)),
            AuthorService.follower_count_expression()
        )
    )
    
    # 添加查询关键字过滤
    if filter:
        stmt = stmt.where(
            or_(
                Author.username.ilike(f"%{filter}%"),
                Author.name.ilike(f"%{filter}%")
            )
        )
    
    return await sa_paginate(session, stmt, params)


@app.get(
    "/{author_id}/following",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorWithFlags],
)
async def get_following(
    author_id: str,
    filter: Optional[str] = Query(None, title="Filter by username or name"),
    params: Params = Depends(),
    user: User = Depends(optional_user),
    session: AsyncSession = Depends(get_session),
):
    stmt = (
        select(Author)
        .join(authors_followers, onclause=Author.id == authors_followers.c.author_id)
        .where(authors_followers.c.follower_id == author_id)
        .options(
            AuthorService.is_following_expression(getattr(user, 'id', None)),
            AuthorService.follower_count_expression()
        )
    )
    
    # 添加查询关键字过滤
    if filter:
        stmt = stmt.where(
            or_(
                Author.username.ilike(f"%{filter}%"),
                Author.name.ilike(f"%{filter}%")
            )
        )
    
    return await sa_paginate(session, stmt, params)


@app.get(
    "/{author_id}/subscribed",
    status_code=status.HTTP_200_OK,
    response_model=Page[CollectionRead],
)
async def get_subscribed_collections(
    author_id: str,
    params: Params = Depends(),
    session: AsyncSession = Depends(get_session),
    collection_service: CollectionService = Depends(get_collection_service),
    user: User | None = Depends(optional_user),
):
    stmt = (
        select(Author)
        .where(Author.id == author_id)
        .options(
            selectinload(Author.collections_subscribed),
            with_loader_criteria(
                Collection,
                Collection.content_type.in_([CollectionContentType.MIXED]),
            )
        )
    )
    author: Author = (await session.execute(stmt)).first()[0]
    
    collection_ids = [collection.id for collection in author.collections_subscribed]
    
    if not collection_ids:
        return paginate([], params)
    
    collections_stmt = (
        select(Collection)
        .where(Collection.id.in_(collection_ids))
        .options(
            collection_service.subscribed_expression(user_id=getattr(user, "id", None))
        )
    )
    collections = (await session.execute(collections_stmt)).scalars().all()
    return paginate(collections, params)


@app.get(
    "/phone_exists/{phone_number}",
    status_code=status.HTTP_200_OK,
)
async def exists_by_phone(
    phone_number: str,
    session: AsyncSession = Depends(get_session),
):
    stmt = select(User).where(User.phone_number == phone_number)
    return (await session.execute(stmt)).first() is not None


@app.delete(
    "/",
    status_code=status.HTTP_200_OK,
    response_model=dict
)
async def delete_account(
        user: User = Depends(current_user),
        author_service: AuthorService = Depends(get_author_service)
):
    resp = await author_service.delete_account(user.id)

    # 异步同步到 Elasticsearch
    es_sync_client = create_es_sync_client("authors_service")
    es_sync_client.sync_author(str(user.id), "delete", priority=5)
    return resp


@app.post(
    "/me/blocks/{author_id}",
    status_code=status.HTTP_200_OK,
)
async def block_user(
    author_id: str,
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
):
    """屏蔽用户"""
    # 检查目标用户是否存在
    stmt = select(Author).where(Author.id == author_id)
    blocked_author = (await session.execute(stmt)).scalar_one_or_none()
    if not blocked_author:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Author does not exist"
        )
    
    # 不能屏蔽自己
    if user.id == author_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot block yourself"
        )
    
    # 检查是否已经屏蔽
    stmt = select(authors_blocks).where(
        authors_blocks.c.blocker_id == user.id,
        authors_blocks.c.blocked_id == author_id
    )
    existing_block = (await session.execute(stmt)).first()
    if existing_block:
        return {"message": "User is already blocked"}
    
    # 添加屏蔽关系
    stmt = authors_blocks.insert().values(blocker_id=user.id, blocked_id=author_id)
    await session.execute(stmt)
    await session.commit()
    
    return {"message": "User blocked successfully"}


@app.post(
    "/me/unblocks/{author_id}",
    status_code=status.HTTP_200_OK,
)
async def unblock_user(
    author_id: str,
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
):
    """解除屏蔽用户"""
    # 检查是否存在屏蔽关系
    stmt = select(authors_blocks).where(
        authors_blocks.c.blocker_id == user.id,
        authors_blocks.c.blocked_id == author_id
    )
    existing_block = (await session.execute(stmt)).first()
    if not existing_block:
        return {"message": "User is not blocked"}
    
    # 删除屏蔽关系
    stmt = authors_blocks.delete().where(
        authors_blocks.c.blocker_id == user.id,
        authors_blocks.c.blocked_id == author_id
    )
    await session.execute(stmt)
    await session.commit()
    
    return {"message": "User unblocked successfully"}

@app.get(
    "/me/blocklist",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorWithFlags],
)
async def get_blocked_users(
    params: Params = Depends(),
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session),
):
    """获取已屏蔽用户列表"""
    stmt = (
        select(Author)
        .join(authors_blocks, onclause=Author.id == authors_blocks.c.blocked_id)
        .where(authors_blocks.c.blocker_id == user.id)
        .options(
            AuthorService.is_following_expression(user.id),
            AuthorService.follower_count_expression(),
            AuthorService.is_blocked_expression(user.id)
        )
    )
    
    # Get the paginated result
    result = await sa_paginate(session, stmt, params)
    
    # Fetch im_id for each author in the results
    if result.items:
        author_ids = [author.id for author in result.items]
        im_stmt = select(TociIm.toci_id, TociIm.im_id).where(TociIm.toci_id.in_(author_ids))
        im_results = await session.execute(im_stmt)
        im_mapping = {toci_id: im_id for toci_id, im_id in im_results.all()}
        
        # Set im_id for each author
        for author in result.items:
            author.im_id = im_mapping.get(author.id)
    
    return result


@app.get("/email_exists/{email}", status_code=status.HTTP_200_OK)
async def email_exists(email: str, db: AsyncSession = Depends(get_session)):
    email = email.lower()
    # 使用异步查询检查数据库中是否存在该email
    query = select(Author).where(Author.email == email)
    result = await db.execute(query)
    author = result.scalar_one_or_none()
    
    if author:
        return True
    else:
        if auth_settings.FSERVER_URL:
            fserver_response = await http_client.get(f"{auth_settings.FSERVER_URL}/author/foreign_email_exists/{email}")
            try:
                fserver_check = fserver_response.json() == "true" 
                return fserver_check or False
            except:
                return False
        return False

@app.get("/username_exists/{username}", status_code=status.HTTP_200_OK)
async def username_exists(username: str, db: AsyncSession = Depends(get_session)):
    query = select(Author).where(Author.username_raw == username.lower())
    result = await db.execute(query)
    author = result.scalar_one_or_none()
    if author:
        return True
    else:
        if auth_settings.FSERVER_URL:
            fserver_response = await http_client.get(f"{auth_settings.FSERVER_URL}/author/foreign_username_exists/{username}")
            try:
                fserver_check = fserver_response.json() == "true" 
                return fserver_check or False
            except:
                return False
        return False
    
@app.get("/foreign_email_exists/{email}", status_code=status.HTTP_200_OK)
async def foreign_email_exists(email: str, db: AsyncSession = Depends(get_session)):
    query = select(Author).where(Author.email == email)
    result = await db.execute(query)
    author = result.scalar_one_or_none()
    return author is not None
    
@app.get("/foreign_username_exists/{username}", status_code=status.HTTP_200_OK)
async def foreign_username_exists(username: str, db: AsyncSession = Depends(get_session)):
    query = select(Author).where(Author.username_raw == username.lower())
    result = await db.execute(query)
    author = result.scalar_one_or_none()
    return author is not None

@app.post(
    "/{author_id}/follow",
    response_model=AuthorRead,
    status_code=status.HTTP_200_OK
)
async def follow(
    author_id: str,
    user: User = Depends(current_user),
    author_service: AuthorService = Depends(get_author_service),
    notification_service: NotificationService = Depends(get_notification_service)
):
    author = await author_service.follow(author_id=author_id, follower_id=user.id)
    follower = await author_service.get(user.id)
    await notification_service.create_notification(
        notification_type=NotificationType.author_follow,
        recipients_ids=[author_id],
        meta={
            "follower_id": follower.id,
            "follower_avatar": follower.avatar,
            "follower_username": follower.username,
            "follower_name": follower.name
        }
    )
    return author

@app.post(
    "/{author_id}/unfollow",
    response_model=AuthorRead,
    status_code=status.HTTP_200_OK
)
async def unfollow(
    author_id: str,
    author_service: AuthorService = Depends(get_author_service),
    user: User = Depends(current_user)
):
    """
    Unfollow author with author_id.
    """
    return await author_service.unfollow(author_id=author_id, user_id=user.id)


@app.put(
    "/{author_id}",
    status_code=status.HTTP_200_OK,
    response_model=AuthorReadExtended,
    dependencies=[Depends(super_admin)],
    tags=['Admin'],
)
async def update_user(
    author_id: str,
    update_data: AuthorUpdate,
    author_service: AuthorService = Depends(get_author_service)
):
    return await author_service.update(author_id, update_data)



