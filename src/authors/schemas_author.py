from typing import List, Optional
from src.topics.schemas import Tag
from src.authors.schemas import AuthorRead

# Import CollectionRead at the top level
from src.posts.collections.schemas import CollectionRead

class Author(AuthorRead):
    tags_subscribed: Optional[List[Tag]]
    collections_subscribed: Optional[List[str]]  # Store collection IDs instead of CollectionRead objects

Author.update_forward_refs()