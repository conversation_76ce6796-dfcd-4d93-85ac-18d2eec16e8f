import json
from sqlalchemy import select, func, exists, literal, update, or_
from sqlalchemy.orm import with_expression, aliased
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status
from fastapi_pagination import Params

from src.database.models.Author import authors_followers, authors_blocks, Author
from src.database.models.User import User
from src.database.models.TociIm import TociIm
from src.database.session import get_session
from src.database.schemas.Author import RecommenderAuthor
from src.common.recommender.client import RecommenderClient
from src.common.celery_client import create_celery_client
from src.common.im_service import ImService
from src.common.settings import settings
from src.authors.logger import logger
from src.common.constants import UserStatus

class AuthorService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.recommender_client = RecommenderClient(settings.RECOMMENDER_TOKEN)
        self._celery_client = None
        self._im_service = None
    
    def _get_celery_client(self):
        if self._celery_client is None:
            self._celery_client = create_celery_client("authors_service")
        return self._celery_client

    def _get_im_service(self):
        if self._im_service is None:
            self._im_service = ImService(self.session)
        return self._im_service

    async def get(self, id: str):
        return await self.session.get(Author, id)
    
    async def follow(self, author_id: str, follower_id: str):
        author = await self.get(author_id)
        follower = await self.get(follower_id)
        
        if not author or not follower:
            return None
            
        if author_id != follower_id and follower not in author.followers:
            author.followers.append(follower)
            self.session.add(author)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            return author
        return author

    async def unfollow(self, author_id: str, user_id: str):
        author = await self.get(author_id)
        follower = await self.get(user_id)
        
        if not author or not follower:
            return None
            
        if author_id != user_id and follower in author.followers:
            author.followers.remove(follower)
            self.session.add(author)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            return author
        return author
        
    async def citations_inc(self, author_id: str):
        author = await self.get(author_id)
        if author:
            author.citations_count += 1
            self.session.add(author)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            return author
        return None

    async def citations_dec(self, author_id: str):
        """
        Decrement author's citations count.
        Do nothing, if citations count less or equal zero.
        """
        author = await self.get(author_id)
        if author and author.citations_count > 0:
            author.citations_count -= 1
            self.session.add(author)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            return author
        return None

    @staticmethod
    def follower_count_expression():
        stmt = select(func.count(authors_followers.c.follower_id)).where(
            authors_followers.c.author_id == Author.id
        ).correlate(Author)
        expression = with_expression(Author.follower_count, stmt.scalar_subquery())
        return expression

    @staticmethod
    def following_count_expression():
        stmt = select(func.count(authors_followers.c.author_id)).where(
            authors_followers.c.follower_id == Author.id
        ).correlate(Author)
        expression = with_expression(Author.following_count, stmt.scalar_subquery())
        return expression

    @staticmethod
    def is_following_expression(user_id: str | None = None):
        if not user_id:
            return with_expression(Author.is_following, literal(False))
        
        followers_table = aliased(authors_followers, name='followers_table')
        stmt = exists(select(followers_table).where(
            followers_table.c.follower_id == user_id,
            followers_table.c.author_id == Author.id
        ))
        expression = with_expression(Author.is_following, stmt)
        return expression

    @staticmethod
    def is_blocked_expression(user_id: str | None = None):
        if not user_id:
            return with_expression(Author.is_blocked, literal(False))
        
        blocks_table = aliased(authors_blocks, name='blocks_table')
        stmt = exists(select(blocks_table).where(
            blocks_table.c.blocker_id == user_id,
            blocks_table.c.blocked_id == Author.id
        ))
        expression = with_expression(Author.is_blocked, stmt)
        return expression

    async def likes_count_inc(self, author_id: str):
        model = await self.get(author_id)
        model.likes_count += 1
        self.session.add(model)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    async def likes_count_dec(self, author_id: str):
        """
        Decrement author's likes count.
        Do nothing, if likes count less or equal zero.
        """
        model = await self.get(author_id)
        if model.likes_count <= 0:
            return
        model.likes_count -= 1
        self.session.add(model)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    async def update_name(self, author_id: str, name: str):
        """
        更新Author的name字段，只有当name字段为空时才允许更新
        """
        # 获取当前Author记录
        author = await self.get(author_id)
        
        if not author:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Author not found"
            )
        
        # 检查name字段是否为空
        if author.name and author.name.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name already exists and cannot be modified"
            )
        
        # 直接更新author对象的name字段
        author.name = name
        self.session.add(author)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
        
        # 同步到 IM 系统
        try:
            im_service = self._get_im_service()
            await im_service.sync_author_to_im(author)
        except Exception as e:
            # IM 同步失败不应该影响主流程，只记录错误
            logger.warning(f"Failed to sync author {author_id} to IM system after name update: {e}")
        
        # 返回更新后的Author对象
        return author

    async def update_description(self, author_id: str, description: str):
        """
        更新Author的description字段，只有当description字段为空时才允许更新
        """
        # 获取当前Author记录
        author = await self.get(author_id)
        
        if not author:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Author not found"
            )
        
        # 检查description字段是否为空
        if author.description and author.description.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Description already exists and cannot be modified"
            )
        
        # 直接更新author对象的description字段
        author.description = description
        self.session.add(author)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
        
        # 同步到 IM 系统
        try:
            im_service = self._get_im_service()
            await im_service.sync_author_to_im(author)
        except Exception as e:
            # IM 同步失败不应该影响主流程，只记录错误
            logger.warning(f"Failed to sync author {author_id} to IM system after description update: {e}")
        
        # 返回更新后的Author对象
        return author

    async def update(self, author_id: str, update_data):
        """
        Update author model by author_id.
        Raise exception if updated field contains username or email that already exists in database.
        """
        try:
            model: Author = await self.get(author_id)
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Author not found"
                )
            
            user_needs_update: bool = (
                (update_data.email is not None and update_data.email != model.email)
                or (update_data.phone_number is not None and update_data.phone_number != model.phone_number)
            )
            
            if user_needs_update:
                values = {"email": update_data.email, "phone_number": update_data.phone_number}
                values = {k: v for k, v in values.items() if v is not None}
                
                # Check if email or phone_number already exists for another user
                stmt = select(User).where(
                    User.id != model.user_id,
                    *[getattr(User, k) == v for k, v in values.items()]
                )
                user = (await self.session.execute(stmt)).first()
                if user:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="User with this email or phone number already exists"
                    )
                
                # Update user table
                stmt = update(User).where(User.id == model.user_id).values(**values)
                await self.session.execute(stmt)
            
            # Check username uniqueness
            if update_data.username and model.username != update_data.username:
                existing_author = await self.session.execute(
                    select(Author).filter(Author.username_raw == update_data.username.lower())
                )
                if existing_author.scalar():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, 
                        detail="Username already exists"
                    )
            
            # Check email uniqueness in Author table
            if update_data.email and model.email != update_data.email:
                existing_author = await self.session.execute(
                    select(Author).filter(Author.email == update_data.email)
                )
                if existing_author.scalar():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, 
                        detail="Email already exists"
                    )
            
            # Update author model
            for field, value in update_data.model_dump(exclude_unset=True).items():
                if hasattr(model, field):
                    setattr(model, field, value)
            
            self.session.add(model)
            
            # Prepare external service operations before committing
            schema = RecommenderAuthor.model_validate(model).model_dump()
            
            # Send Celery task before commit - if this fails, we can rollback
            celery_client = self._get_celery_client()
            celery_client.send_task(
                "src.worker.tasks.elasticsearch_sync.queue_sync_author_to_elasticsearch",
                args=[author_id],
                kwargs={"priority": 5},
                queue="elasticsearch_queue",
                priority=5
            )
            
            # Update recommender before commit - if this fails, we can rollback
            await self.recommender_client.update_user(schema)

            # Sync to IM system
            try:
                im_service = self._get_im_service()
                await im_service.sync_author_to_im(model)
            except Exception as e:
                # IM 同步失败不应该影响主流程，只记录错误
                logger.warning(f"Failed to sync author {author_id} to IM system: {e}")
            
            # Only commit if all external services succeeded
            await self.session.commit()
            await self.session.refresh(model)
            
            return model
            
        except HTTPException:
            # Re-raise HTTP exceptions as they are
            await self.session.rollback()
            raise
        except Exception as e:
            # Handle any other database or validation errors
            await self.session.rollback()
            logger.error(f"Error updating author {author_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update author"
            )



    async def delete_account(self, user_id: str) -> dict:
        try:
            # Update user table, including OAuth field cleanup
            stmt_user = (
                update(User)
                .where(User.id == user_id)
                .values(
                    status=UserStatus.DELETED,
                    email=User.email + " " + User.id,
                    # Clean up OAuth related fields
                    google_id=None,
                    apple_id=None,
                    google_linked_at=None,
                    apple_linked_at=None,
                    avatar_url=None,
                    email_verified_via=None,
                    last_login_method=None,
                    phone_number=None,
                )
            )

            # Update author table
            stmt_author = (
                update(Author)
                .where(Author.id == user_id)
                .values(email=Author.email + " " + Author.id)
            )

            await self.session.execute(stmt_user)
            await self.session.execute(stmt_author)
            await self.session.commit()
            
            # 同步删除到 IM 系统
            try:
                im_service = self._get_im_service()
                # 获取用户的 IM ID
                stmt = select(TociIm).where(TociIm.toci_id == user_id)
                result = await self.session.execute(stmt)
                toci_im = result.scalar_one_or_none()
                
                if toci_im:
                    # 更新 IM 用户信息，将昵称设置为 "已删除用户"
                    await im_service.update_user_info(
                        im_id=toci_im.im_id,
                        nickname="已删除用户",
                        ex={"user_id": user_id, "deleted": True}
                    )
            except Exception as e:
                # IM 同步失败不应该影响主流程，只记录错误
                logger.warning(f"Failed to sync account deletion {user_id} to IM system: {e}")
            
            await self.recommender_client.delete_user(user_id=user_id)
            
            return {"success": True, "message": "Account successfully deleted"}
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete account {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete account: {str(e)}"
            )




async def get_author_service(session: AsyncSession = Depends(get_session)):
    return AuthorService(session)