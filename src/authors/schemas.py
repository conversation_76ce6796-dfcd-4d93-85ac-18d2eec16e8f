from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Literal, Optional, List

from pydantic import BaseModel, <PERSON>, model_validator
from datetime import date, datetime
from src.database.schemas.Author import AuthorRead, AuthorReadExtended



class AuthorWithFlags(AuthorRead):
    is_following: bool
    follower_count: int = 0
    im_id: Optional[str] = None


class AuthorWithBlockFlags(AuthorWithFlags):
    is_blocked: bool


class AuthorWithGrade(AuthorReadExtended):
    group_grade: int
    direct_invited_count: int
    group_size: int


class AuthorWithWallet(AuthorReadExtended):
    points: str
    pending_points: str
    potential_points: str
    today_points: str
    epi: Optional[str] = None
    wallet_address: Optional[str] = None


class AuthorExtendedWithCounters(AuthorReadExtended):
    follower_count: int
    following_count: int
    posts_count: int
    is_following: bool
    is_blocked: bool = False
    im_id: Optional[str] = None
    followed_memes_count: int = 0


class Activity(BaseModel):
    amount: Decimal
    action: str
    created_at: datetime
    meta: dict

    class Config:
        from_attributes = True


class PointStat(BaseModel):
    date: str
    amount: int


class PointPassiveAnalytics(BaseModel):
    count: int = 0
    likes_count: int = 0
    likes_points: float = 0
    comments_count: int = 0
    comments_points: float = 0
    commits_count: int = 0
    commits_points: float = 0
    saves_count: int = 0
    saves_points: float = 0


class PointAnalytics(BaseModel):
    like_amount: int = 0
    like_points: int = 0
    comment_amount: int = 0
    comment_points: int = 0
    commit_amount: int = 0
    commit_points: int = 0
    save_amount: int = 0
    save_points: int = 0
    commit_approve_amount: int = 0
    commit_approve_points: int = 0

class Gender(str, Enum):
    MALE = "m"
    FEMALE = "f"
    UNDEFINED = "n"

class AuthorBase(BaseModel):
    type: Literal["Author"] = "Author"
    id: str
    name: str
    username: str
    email: str
    avatar: Optional[str]
    original_avatar: Optional[str]
    region: str
    user_id: str
    invitation_id: Optional[str] = None

    class Config:
        from_attributes = True

class AuthorRead(AuthorBase):
    created_at: datetime
    updated_at: datetime

    dedication: Optional[str]
    description: Optional[str]
    location: Optional[str]
    country: Optional[str]
    language: Optional[str]
    education: Optional[str]
    birthday: Optional[date]
    phone_number: Optional[str]
    likes_count: Optional[int]
    citations_count: int
    email: Optional[str]
    posts_count: int
    gender: Optional[Gender]
    role: Optional[str]


class UpdateNameRequest(BaseModel):
    display_name: str = Field(..., min_length=1, strip_whitespace=True, description="显示名称不能为空")
    
    class Config:
        from_attributes = True


class UpdateDescriptionRequest(BaseModel):
    description: str = Field(..., min_length=1, strip_whitespace=True, description="描述不能为空")
    
    class Config:
        from_attributes = True


class AuthorUpdate(BaseModel):
    dedication: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    name: Optional[str] = None
    avatar: Optional[str] = None
    original_avatar: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    education: Optional[str] = None
    birthday: Optional[date] = None
    username: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    gender: Optional[Gender] = None

    @model_validator(mode='after')
    def validate_at_least_one_field(self):
        fields_dict = self.model_dump(exclude_unset=True)
        if not fields_dict:
            raise ValueError("At least one field must be provided for update")
        return self

    class Config:
        from_attributes = True





