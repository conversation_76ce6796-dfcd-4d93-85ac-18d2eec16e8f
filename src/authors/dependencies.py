from fastapi import Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.models import Collection
from src.database.session import get_session


async def valid_collection_id(collection_id: str, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(Collection).where(Collection.id == collection_id)
    collection = (await session.execute(stmt)).first()
    if not collection:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Collection does not exist")
    return collection_id
