from bytewax.operators.windowing import fold_window

from src.memekline.constants import KlineData
from src.memekline.logger import logger
from src.memekline.parser import ProcessedData
from src.memekline.windower import (
    kline_30s_windower,
    kline_1m_windower,
    kline_5m_windower,
    kline_30m_windower,
    kline_1h_windower,
    kline_4h_windower,
    kline_12h_windower,
    kline_1d_windower,
)


def _builder():
    return {}


def _folder_30s(state, item):
    return _folder(state, item, interval=30)


def _folder_1m(state, item):
    return _folder(state, item, interval=60)


def _folder_5m(state, item):
    return _folder(state, item, interval=300)


def _folder_30m(state, item):
    return _folder(state, item, interval=1800)


def _folder_1h(state, item):
    return _folder(state, item, interval=3600)


def _folder_4h(state, item):
    return _folder(state, item, interval=14400)


def _folder_12h(state, item):
    return _folder(state, item, interval=43200)


def _folder_24h(state, item):
    return _folder(state, item, interval=86400)


def _folder(state, item: ProcessedData, interval):
    token_address = item.token_address
    current_price = item.token_price
    timestamp = item.timestamp
    volume = item.token_amount

    if token_address not in state:
        state[token_address] = KlineData(token_address=token_address)

    kline = state[token_address]

    if kline.opentime is None:
        aligned_timestamp = (timestamp // interval) * interval
        kline.opentime = aligned_timestamp
        kline.closetime = aligned_timestamp + interval

    open_time = kline.opentime
    close_time = kline.closetime
    if timestamp < open_time or timestamp > close_time:
        logger.error(
            f"opentime: {open_time}, timestamp: {timestamp}, closetime: {close_time}"
        )

    if kline.open is None:
        kline.open = current_price

    if current_price > kline.high:
        kline.high = current_price
    if current_price < kline.low:
        kline.low = current_price

    kline.close = current_price
    kline.volume += volume

    return state


def _merge_acc(state1, state2):
    state1.extend(state2)
    return state1


def kline_foldwindow_30s(up, clock):
    kline_30s_windowed = fold_window(
        step_id="kline_30s",
        up=up,
        clock=clock,
        windower=kline_30s_windower,
        builder=_builder,
        folder=_folder_30s,
        merger=_merge_acc,
    )
    return kline_30s_windowed


def kline_foldwindow_1m(up, clock):
    kline_1m_windowed = fold_window(
        step_id="kline_1m",
        up=up,
        clock=clock,
        windower=kline_1m_windower,
        builder=_builder,
        folder=_folder_1m,
        merger=_merge_acc,
    )
    return kline_1m_windowed


def kline_foldwindow_5m(up, clock):
    kline_5m_windowed = fold_window(
        step_id="kline_5m",
        up=up,
        clock=clock,
        windower=kline_5m_windower,
        builder=_builder,
        folder=_folder_5m,
        merger=_merge_acc,
    )
    return kline_5m_windowed


def kline_foldwindow_30m(up, clock):
    kline_30m_windowed = fold_window(
        step_id="kline_30m",
        up=up,
        clock=clock,
        windower=kline_30m_windower,
        builder=_builder,
        folder=_folder_30m,
        merger=_merge_acc,
    )
    return kline_30m_windowed


def kline_foldwindow_1h(up, clock):
    kline_1h_windowed = fold_window(
        step_id="kline_1h",
        up=up,
        clock=clock,
        windower=kline_1h_windower,
        builder=_builder,
        folder=_folder_1h,
        merger=_merge_acc,
    )
    return kline_1h_windowed


def kline_foldwindow_4h(up, clock):
    kline_4h_windowed = fold_window(
        step_id="kline_4h",
        up=up,
        clock=clock,
        windower=kline_4h_windower,
        builder=_builder,
        folder=_folder_4h,
        merger=_merge_acc,
    )
    return kline_4h_windowed


def kline_foldwindow_12h(up, clock):
    kline_12h_windowed = fold_window(
        step_id="kline_12h",
        up=up,
        clock=clock,
        windower=kline_12h_windower,
        builder=_builder,
        folder=_folder_12h,
        merger=_merge_acc,
    )
    return kline_12h_windowed


def kline_foldwindow_1d(up, clock):
    kline_24h_windowed = fold_window(
        step_id="kline_1d",
        up=up,
        clock=clock,
        windower=kline_1d_windower,
        builder=_builder,
        folder=_folder_24h,
        merger=_merge_acc,
    )
    return kline_24h_windowed


def kline_output(interval: int, item):
    if not item:
        return
    try:
        kline_dict: dict = item[1][1]
        if not kline_dict:
            return
        for token_address, kline in kline_dict.items():
            yield kline

    except Exception as e:
        logger.error(str(e))
        return
