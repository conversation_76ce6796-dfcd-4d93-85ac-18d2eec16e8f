from datetime import timedelta, timezone, datetime

from bytewax.operators.windowing import Event<PERSON><PERSON>, TumblingWindower

from src.memekline.parser import extract_timestamp

ALIGN_TO = datetime(2025, 1, 31, 0, 0, 0, tzinfo=timezone.utc)

clock_30s = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(seconds=5)
)

clock_1m = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(seconds=15)
)

clock_5m = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(seconds=30)
)

clock_30m = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(minutes=3)
)

clock_1h = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(minutes=6)
)

clock_1d = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(minutes=30)
)

# New longer-interval clocks
clock_4h = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(minutes=20)
)

clock_12h = EventClock(
    ts_getter=extract_timestamp, wait_for_system_duration=timedelta(minutes=60)
)

kline_1m_windower = TumblingWindower(
    length=timedelta(minutes=1),
    align_to=ALIGN_TO,
)

kline_5m_windower = TumblingWindower(
    length=timedelta(minutes=5),
    align_to=ALIGN_TO,
)

kline_30s_windower = TumblingWindower(
    length=timedelta(seconds=30),
    align_to=ALIGN_TO,
)

kline_30m_windower = TumblingWindower(
    length=timedelta(minutes=30),
    align_to=ALIGN_TO,
)

kline_1h_windower = TumblingWindower(
    length=timedelta(hours=1),
    align_to=ALIGN_TO,
)

kline_1d_windower = TumblingWindower(
    length=timedelta(days=1),
    align_to=ALIGN_TO,
)

# New longer-interval windowers
kline_4h_windower = TumblingWindower(
    length=timedelta(hours=4),
    align_to=ALIGN_TO,
)

kline_12h_windower = TumblingWindower(
    length=timedelta(hours=12),
    align_to=ALIGN_TO,
)
