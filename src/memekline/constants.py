import attr

from src.memekline.settings import settings


ACTIVE_TOKENS_REDIS_KEY = "memetracker:active_tokens"

BROKERS = [settings.KAFKA_BROKERS]
IN_TOPICS = [settings.KAFKA_BSC_TOPIC]

@attr.s
class ProcessedData:
    timestamp = attr.ib()
    token_address = attr.ib()
    token_price = attr.ib()
    token_amount = attr.ib()
    bnb_amount = attr.ib()


@attr.s
class KlineData:
    token_address = attr.ib(default=None)
    opentime = attr.ib(default=None)
    closetime = attr.ib(default=None)
    open = attr.ib(default=None)
    high = attr.ib(default=float("-inf"))
    low = attr.ib(default=float("inf"))
    close = attr.ib(default=None)
    volume = attr.ib(default=0)


@attr.s
class PriceTick:
    timestamp = attr.ib()
    token_address = attr.ib()
    price = attr.ib()
    volume = attr.ib(default=0)
