from functools import partial

from bytewax._bytewax import run_main
from bytewax.dataflow import Dataflow
import bytewax.operators as op
from bytewax.connectors.kafka import operators as kop

from src.memekline.constants import BROKERS, IN_TOPICS
from src.memekline.kline_generator import (
    kline_foldwindow_30s,
    kline_foldwindow_1m,
    kline_foldwindow_5m,
    kline_foldwindow_30m,
    kline_foldwindow_1h,
    kline_foldwindow_4h,
    kline_foldwindow_12h,
    kline_output,
    kline_foldwindow_1d,
)
from src.memekline.parser import parse_message, filter_message, preprocess
from src.memekline.redis_source import RedisPriceSource
from src.memekline.sink import DatabaseSinkKline
from src.memekline.windower import clock_30s, clock_1m, clock_5m, clock_30m, clock_1h, clock_1d, clock_4h, clock_12h

flow = Dataflow("kline_processing")

redis_source = RedisPriceSource(
    interval=1,
)

kafka_input = kop.input(
    "inp", flow, brokers=BROKERS, topics=IN_TOPICS, tail=True, batch_size=100
)

redis_input = op.input("redis_input", flow, redis_source)

parsed = op.map("parse_message", kafka_input.oks, parse_message)

filtered = op.filter("filter_message", parsed, filter_message)

preprocessed = op.map("preprocess_message", filtered, preprocess)

merged_stream = op.merge("merge_streams", redis_input, preprocessed)

# op.inspect("inspect merged_stream", merged_stream)

kline_30s = kline_foldwindow_30s(merged_stream, clock_30s)
kline_1m = kline_foldwindow_1m(merged_stream, clock_1m)
kline_5m = kline_foldwindow_5m(merged_stream, clock_5m)
# kline_30m = kline_foldwindow_30m(merged_stream, clock_30m)
kline_1h = kline_foldwindow_1h(merged_stream, clock_1h)
kline_4h = kline_foldwindow_4h(merged_stream, clock_4h)
kline_12h = kline_foldwindow_12h(merged_stream, clock_12h)
# kline_1d = kline_foldwindow_1d(merged_stream, clock_1d)

#
# kline
output_kline_30s = op.flat_map(
    "output_kline_30s", kline_30s.down, partial(kline_output, 30)
)
output_kline_1m = op.flat_map(
    "output_kline_1m", kline_1m.down, partial(kline_output, 60)
)
output_kline_5m = op.flat_map(
    "output_kline_5m", kline_5m.down, partial(kline_output, 300)
)
# output_kline_30m = op.flat_map(
#     "output_kline_30m", kline_30m.down, partial(kline_output, 1800)
# )
output_kline_1h = op.flat_map(
    "output_kline_1h", kline_1h.down, partial(kline_output, 3600)
)
output_kline_4h = op.flat_map(
    "output_kline_4h", kline_4h.down, partial(kline_output, 14400)
)
output_kline_12h = op.flat_map(
    "output_kline_12h", kline_12h.down, partial(kline_output, 43200)
)
# output_kline_1d = op.flat_map(
#     "output_kline_1d", kline_1d.down, partial(kline_output, 86400)
# )

op.output("sink_kline_30s", output_kline_30s, DatabaseSinkKline(30))
op.output("sink_kline_1m", output_kline_1m, DatabaseSinkKline(60))
op.output("sink_kline_5m", output_kline_5m, DatabaseSinkKline(300))
# op.output("sink_kline_30m", output_kline_30m, DatabaseSinkKline(1800))
op.output("sink_kline_1h", output_kline_1h, DatabaseSinkKline(3600))
op.output("sink_kline_4h", output_kline_4h, DatabaseSinkKline(14400))
op.output("sink_kline_12h", output_kline_12h, DatabaseSinkKline(43200))
# op.output("sink_kline_1d", output_kline_1d, DatabaseSinkKline(86400))

if __name__ == "__main__":
    run_main(flow)
