from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

    TIMESCALE_DSN: str = (
        "postgresql://kratos:<EMAIL>:6432/memefans"
    )
    
    # 数据库连接池配置
    DB_POOL_MIN_CONN: int = 1
    DB_POOL_MAX_CONN: int = 10
    
    # 价格数据写入配置
    PRICE_BUFFER_SIZE: int = 20  # 缓冲区大小
    PRICE_BUFFER_FLUSH_INTERVAL: int = 1  # 缓冲区刷新间隔(秒)
    PRICE_SAMPLING_INTERVAL: int = 0  # 价格采样间隔(秒)，0表示全采样

    DEFAULT_GAS_LIMIT: int = 1_000_000
    DEFAULT_GAS_PRICE: int = 3
    DEFAULT_DECIMALS: int = 1_000_000_000_000_000_000
    DEFAULT_TOTAL_SUPPLY: int = 1_000_000_000

    DEFAULT_BNB_PRICE: float = 606.35

    # Kafka
    KAFKA_BROKERS: str = "meme-kafka-01.aurora:9092"
    KAFKA_BSC_TOPIC: str = "kafka_bsc_events"


settings = Settings()
