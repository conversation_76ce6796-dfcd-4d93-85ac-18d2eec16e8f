import asyncio
import json

from datetime import datetime, timezone
from typing import Union

from bytewax.connectors.kafka import KafkaSourceMessage

from src.memecoin.utils import get_bnb_price
from src.memekafka.constants import KafkaEvent
from src.memekline.cache import redis_client
from src.memekline.constants import ProcessedData


def parse_message(msg: Union[KafkaSourceMessage, dict]):
    """解析 Kafka 的原始bytes，返回一个dict."""
    if isinstance(msg, KafkaSourceMessage):
        if msg.value is None:
            return {}
        event = json.loads(msg.value)
    else:
        event = msg
    return (event.get("event"), event.get("data"))


def filter_message(x):
    if x:
        if x[0] == KafkaEvent.BUY or x[0] == KafkaEvent.SELL:
            token_amount = x[1].get("TokenAmountOut") or x[1].get("TokenAmountIn")
            if token_amount == 0:
                return False
            return True
    return False


def extract_timestamp(x: ProcessedData):
    ts = x.timestamp
    if isinstance(ts, datetime):
        return ts
    return datetime.fromtimestamp(ts, tz=timezone.utc)


def preprocess(filtered_item):
    data = filtered_item[1]
    timestamp = data.get("Timestamp")
    token_address = data.get("TokenAddress")
    bnb_amount = data.get("BnbAmountIn") or data.get("BnbAmountOut")
    token_amount = data.get("TokenAmountOut") or data.get("TokenAmountIn")
    bnb_price = asyncio.run(get_bnb_price(redis_client))
    token_price = (bnb_amount / token_amount) * bnb_price
    return token_address, ProcessedData(
        timestamp, token_address, token_price, token_amount, bnb_amount
    )
