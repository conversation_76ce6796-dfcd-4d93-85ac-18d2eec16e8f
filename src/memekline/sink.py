from datetime import datetime, timezone
from typing import Any, List
import psycopg2
from psycopg2.extras import execute_values
from typing_extensions import override

from src.memekline.constants import KlineData
from src.memekline.logger import logger
from src.memekline.settings import settings
from bytewax.outputs import DynamicSink, StatelessSinkPartition


class _DatabaseSinkKlinePartition(StatelessSinkPartition[Any]):
    def __init__(self, interval: int):
        self._conn = psycopg2.connect(settings.TIMESCALE_DSN)
        self.interval = interval
        self.table_name = self._determine_table_name(interval)

    def _determine_table_name(self, interval: int) -> str:
        """
        根据 interval 返回不同的目标表名。
        根据提供的表：
          - 30秒   -> kline_30s
          - 60秒   -> kline_1m
          - 300秒  -> kline_5m
          - 1800秒 -> kline_30m
          - 3600秒 -> kline_1h
          - 14400秒 -> kline_4h
          - 43200秒 -> kline_12h
          - 86400秒-> kline_1d
        如果 interval 不匹配上述规则，则抛出异常。
        """
        match interval:
            case 30:
                return "kline_30s"
            case 60:
                return "kline_1m"
            case 300:
                return "kline_5m"
            case 1800:
                return "kline_30m"
            case 3600:
                return "kline_1h"
            case 14400:
                return "kline_4h"
            case 43200:
                return "kline_12h"
            case 86400:
                return "kline_1d"
            case _:
                raise ValueError(f"Unsupported interval: {interval}")

    def write_batch(self, items: List[KlineData]) -> None:
        if not items:
            return

        data = [
            (
                datetime.fromtimestamp(item.opentime, timezone.utc),
                item.token_address,
                item.open,
                item.high,
                item.low,
                item.close,
                item.volume,
            )
            for item in items
        ]

        try:
            with self._conn.cursor() as cur:
                insert_query = f"""
                    INSERT INTO {self.table_name} (
                        timestamp,
                        token_address,
                        open,
                        high,
                        low,
                        close,
                        volume
                    )
                    VALUES %s
                    ON CONFLICT (timestamp, token_address)
                    DO UPDATE SET
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume
                """
                execute_values(cur, insert_query, data)
            self._conn.commit()
            logger.info(
                f"Successfully inserted {self.table_name} {len(data)} items from {data[0][0]}."
            )
        except Exception as e:
            self._conn.rollback()
            logger.exception("Error writing batch to table %s", self.table_name)


class DatabaseSinkKline(DynamicSink[Any]):
    def __init__(self, interval: int):
        self.interval = interval

    @override
    def build(
        self, _step_id: str, _worker_index: int, _worker_count: int
    ) -> _DatabaseSinkKlinePartition:
        return _DatabaseSinkKlinePartition(self.interval)
