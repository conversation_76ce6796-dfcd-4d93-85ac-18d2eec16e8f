"""
Elasticsearch 客户端连接池管理器
提供全局单例ES客户端，避免重复初始化
"""
import asyncio
from typing import Optional
from src.common.elasticsearch import get_elasticsearch_client, UnifiedLanguageIndexManager
from src.search.logger import logger


class ESClientPool:
    """ES客户端连接池单例管理器"""
    
    _instance: Optional['ESClientPool'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.es_client = None
        self.unified_manager = None
        self._init_lock = asyncio.Lock()
    
    @classmethod
    async def get_instance(cls) -> 'ESClientPool':
        """获取单例实例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    logger.info("ES客户端连接池初始化")
        return cls._instance
    
    async def get_client(self):
        """获取ES客户端，带连接检查和自动重连"""
        async with self._init_lock:
            # 如果客户端未初始化，进行初始化
            if self.es_client is None or self.unified_manager is None:
                try:
                    self.es_client = await get_elasticsearch_client()
                    self.unified_manager = UnifiedLanguageIndexManager(self.es_client.client)
                    logger.info("ES客户端池连接建立成功")
                except Exception as e:
                    logger.error(f"ES客户端池初始化失败: {str(e)}")
                    self.es_client = None
                    self.unified_manager = None
                    raise
            
            # 检查连接状态
            try:
                if not await self.es_client.ping():
                    logger.warning("ES连接失效，重新建立连接")
                    self.es_client = await get_elasticsearch_client()
                    self.unified_manager = UnifiedLanguageIndexManager(self.es_client.client)
                    logger.info("ES客户端池重连成功")
            except Exception as e:
                logger.warning(f"ES连接检查失败: {str(e)}，重新建立连接")
                try:
                    self.es_client = await get_elasticsearch_client()
                    self.unified_manager = UnifiedLanguageIndexManager(self.es_client.client)
                    logger.info("ES客户端池重连成功")
                except Exception as init_error:
                    logger.error(f"ES客户端池重连失败: {str(init_error)}")
                    self.es_client = None
                    self.unified_manager = None
                    raise
                    
        return self.es_client
    
    async def get_unified_manager(self):
        """获取统一语言索引管理器"""
        await self.get_client()  # 确保客户端已初始化
        return self.unified_manager
    
    async def close(self):
        """关闭连接池"""
        async with self._init_lock:
            if self.es_client:
                try:
                    await self.es_client.close()
                    logger.info("ES客户端池连接已关闭")
                except Exception as e:
                    logger.warning(f"关闭ES客户端池时出错: {str(e)}")
                finally:
                    self.es_client = None
                    self.unified_manager = None


# 全局函数接口
async def get_es_client_from_pool():
    """从连接池获取ES客户端"""
    pool = await ESClientPool.get_instance()
    return await pool.get_client()


async def get_unified_manager_from_pool():
    """从连接池获取统一语言索引管理器"""
    pool = await ESClientPool.get_instance()
    return await pool.get_unified_manager()