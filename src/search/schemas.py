from pydantic import BaseModel, ConfigDict, Field
from typing import Literal, Optional, List, Union, ClassVar
from datetime import datetime, timedelta
from strenum import StrEnum

from src.memecoin.schemas import TokenSchema
from src.feed.schemas import PostFeed
from src.authors.schemas import AuthorWithFlags


class SearchType(StrEnum):

    TAG = "tag"
    AUTHOR = "author"
    IMAGE = "image"
    VIDEO = "video"
    MEME_COIN = "memecoin"
    ALL = "all"


class FollowRelation(StrEnum):
    FOLLOWERS = "followers"
    FOLLOWING = "following"


class TopSearchItem(BaseModel):
    """热门搜索项"""
    query: str
    count: int
    search_type: Optional[SearchType] = None
    
    model_config = ConfigDict(from_attributes=True)


class SearchItem(BaseModel):
    id: str
    type: str
    created_at: Optional[datetime] = None
    headline_title: str = ""
    headline_description: str = ""
    
    model_config = ConfigDict(arbitrary_types_allowed=True, from_attributes=True)


class VideoSearchItem(SearchItem):
    type: Literal["VideoSearchItem"] = "VideoSearchItem"
    video: PostFeed


class MemeCoinSearchItem(SearchItem): 
    type: Literal["MemeCoinSearchItem"] = "MemeCoinSearchItem"
    memecoin: TokenSchema


class ImageSearchItem(SearchItem):
    type: Literal["ImageSearchItem"] = "ImageSearchItem"
    image: PostFeed


class AuthorSearchItem(SearchItem):
    type: Literal["AuthorSearchItem"] = "AuthorSearchItem"
    author: AuthorWithFlags


Search = Union[
    AuthorSearchItem,
    ImageSearchItem,
    VideoSearchItem,
    MemeCoinSearchItem,
]
