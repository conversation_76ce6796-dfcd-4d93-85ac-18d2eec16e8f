from fastapi import FastAPI, Query, Depends, HTTPException
from fastapi.openapi.utils import get_openapi
from typing import Optional, Union, List
from starlette import status

from src.infra.app import create_app
from src.infra.logger import get_logger
from fastapi_pagination import Page, Params
logger = get_logger("search", level="INFO", file_path="latest.log")
from src.search.schemas import Search, SearchType, TopSearchItem, FollowRelation
from src.search.service import SearchService
from src.search.dependencies import get_search_service
from src.auth import optional_user
from src.database.models import User

app = create_app(title="SearchService", description="A microservice that handles the search requests", version="1.0", request_logger=logger)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="Search Service API",
        version="1.0.0",
        description="Search Service API",
        routes=app.routes,
    )
    # 在 components 下添加安全认证方案
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }
    # 所有接口默认需要 BearerAuth 验证（可根据需要调整）
    openapi_schema["security"] = [{"BearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get(
    "/search",
    tags=["Search Info"],
    summary="Search for Infos",
    description="搜索信息。支持以下功能：\n"
                "- 普通搜索：直接输入关键词\n"
                "- Tag搜索：使用 # 前缀（如：#音乐）搜索包含相关标签的视频和图片\n"
                "- 空字符串搜索：不传入query或传入空字符串时返回默认排序（按创建时间倒序的最新内容）\n"
                "- type=all：搜索所有类型内容\n"
                "- type=tag：专门搜索标签相关内容\n\n"
                "🔓 该接口支持匿名访问，无需登录即可使用。登录用户会自动过滤黑名单作者的内容。",
    status_code=status.HTTP_200_OK,
    response_model=Page[Search]
)
async def search(
    query: Optional[str] = Query("", title="Search query", description="搜索关键词。使用 # 前缀进行标签搜索（如：#音乐）。传入空字符串或不传入时返回默认排序内容"),
    type: Optional[SearchType] = Query(SearchType.ALL, description="搜索类型。注意：如果使用 # 前缀，会自动切换为标签搜索模式"),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(20, ge=1, le=50, description="每页数量，默认20条，最大50条"),
    relation: Optional[FollowRelation] = Query(None, description="仅 AUTHOR 类型生效：followers 或 following 过滤作者范围"),
    relation_owner_id: Optional[str] = Query(None, description="关系所有者ID，省略则默认当前登录用户。仅 AUTHOR 类型生效"),
    service: SearchService = Depends(get_search_service),
    user: Optional[User] = Depends(optional_user)
):
    # 仅当传入 relation 时进行最基本的参数校验（只针对 AUTHOR）
    if relation is not None and type != SearchType.AUTHOR:
        # 非 AUTHOR 类型忽略 relation 参数
        relation = None
        relation_owner_id = None

    if relation is not None and relation_owner_id is None and user is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="relation 需要登录或提供 relation_owner_id")
    # 创建分页参数对象
    params = Params(page=page, size=size)
    return await service.search(query, params, type, user, relation=relation, relation_owner_id=relation_owner_id)

@app.get(
    "/top_searches",
    tags=["Search Info"],
    summary="Get Top Searches",
    description="获取最近3天的热门搜索列表",
    status_code=status.HTTP_200_OK,
    response_model=List[TopSearchItem]
)
async def top_searches(
    limit: int = Query(10, ge=1, le=50, description="Number of results to return"),
    service: SearchService = Depends(get_search_service)
):
    return await service.get_top_searches(limit)
