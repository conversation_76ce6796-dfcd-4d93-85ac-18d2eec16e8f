from sqlalchemy.ext.asyncio import AsyncSession
from src.database.session import get_session
from src.search.service import SearchService
from fastapi import Depends
from src.memecoin.dependencies import get_meme_service
from src.memecoin.services import MemeService
from src.common.caching.caching_client import CachingClient, CachingClientGetter
from src.search.logger import logger
from src.worker.celery_app import celery_app

# 创建缓存客户端获取器
get_search_cache_client = CachingClientGetter(logger=logger)

async def get_search_service(
    db: AsyncSession = Depends(get_session),
    meme_service: MemeService = Depends(get_meme_service),
    cache_client: CachingClient = Depends(get_search_cache_client),
):
    """
    依赖注入获取SearchService实例
    
    Args:
        db: 数据库会话
        meme_service: MemeService实例
        cache_client: 缓存客户端
        
    Returns:
        SearchService实例
    """
    return SearchService(db, meme_service, cache_client, celery_app)
