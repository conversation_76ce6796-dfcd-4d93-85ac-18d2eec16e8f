import asyncio
import re
from typing import Optional, Union, List, Type, Any, Dict, Sequence, Tuple
from difflib import SequenceMatcher

from celery import Celery
from sqlalchemy import and_, exists, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models import User
from src.database.models.Author import authors_blocks, authors_followers
from fastapi_pagination import Page, Params
from src.search.logger import logger
from src.search.schemas import (
    SearchType, TopSearchItem, FollowRelation
)
from datetime import datetime, timedelta
from src.memecoin.services import MemeService
from src.common.constants import MEMECOIN_REDIS_ENDPOINT
from src.common.caching.caching_client import CachingClient
from src.common.elasticsearch.mappings import SUPPORTED_LANGUAGES, get_prefixed_index_name
from src.search.es_client_pool import get_es_client_from_pool, get_unified_manager_from_pool
import base64

# 常量定义
_SEARCH_REDIS_ENDPOINT = "search"
_DEFAULT_CHAIN_ID = 5918  # 默认chain id，用于memecoin搜索过滤


class SearchService:
    """语言路由搜索服务 - 支持语言独立索引架构"""
    
    def __init__(self, db: AsyncSession, meme_service: MemeService, cache_client: CachingClient, celery_app: Celery):
        self.db = db
        self.meme_service = meme_service
        self.cache_client = cache_client
        self.celery_app = celery_app
        # 🎯 移除实例级别的ES客户端，改用连接池
        
        # 初始化 common celery_client 用于提交任务
        from src.common.celery_client import create_celery_client
        self.common_celery_client = create_celery_client("search_service")

    async def _get_es_client(self):
        """从连接池获取Elasticsearch客户端"""
        return await get_es_client_from_pool()
    
    async def _get_unified_manager(self):
        """从连接池获取统一语言索引管理器"""
        return await get_unified_manager_from_pool()

    def _normalize_search_query(self, query: str) -> str:
        """
        标准化搜索词，用于热门搜索的相似词折叠
        
        Args:
            query: 原始搜索词
            
        Returns:
            str: 标准化后的搜索词
        """
        if not query:
            return ""
            
        # 1. 去除前后空格
        normalized = query.strip()
        
        # 2. 统一大小写（转小写）
        normalized = normalized.lower()
        
        # 3. 去除多余的空格，将多个空格替换为单个空格
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # 4. 处理特殊字符 - 去除标点符号但保留中文、英文、数字和基本符号
        # 保留中文字符、英文字母、数字、空格、#号（标签搜索）
        normalized = re.sub(r'[^\w\s#\u4e00-\u9fff]', '', normalized)
        
        # 5. 再次去除前后空格
        normalized = normalized.strip()
        
        return normalized

    def _is_valid_search_query(self, query: str) -> bool:
        """
        判断搜索词是否有效，过滤掉无意义的搜索词
        
        Args:
            query: 搜索词
            
        Returns:
            bool: 是否有效
        """
        if not query:
            return False
            
        # 过滤条件
        # 1. 长度过短（单字符，但允许中文单字符）
        if len(query) == 1:
            # 允许中文单字符
            if not re.match(r'[\u4e00-\u9fff]', query):
                return False
        
        # 2. 过滤明显的测试词汇
        test_patterns = [
            r'^[a-zA-Z]\d*$',  # 单字母或字母+数字，如 "a", "a1", "b2"
            r'^\d+$',          # 纯数字，如 "1", "123"
            r'^[a-zA-Z]+\d+$', # 字母+数字，如 "test123"
            r'^(.)\1{2,}$',    # 重复字符，如 "aaa", "111", "ddddd"
            r'^(test|demo|sample|example)$',  # 常见测试词
        ]
        
        for pattern in test_patterns:
            if re.match(pattern, query, re.IGNORECASE):
                return False
        
        # 3. 过滤过长的搜索词（可能是垃圾数据）
        if len(query) > 50:
            return False
            
        return True

    def _calculate_query_similarity(self, query1: str, query2: str) -> float:
        """
        计算两个搜索词的相似度
        
        Args:
            query1: 第一个搜索词
            query2: 第二个搜索词
            
        Returns:
            float: 相似度分数 (0-1)
        """
        if not query1 or not query2:
            return 0.0
            
        # 先进行标准化
        norm1 = self._normalize_search_query(query1)
        norm2 = self._normalize_search_query(query2)
        
        if norm1 == norm2:
            return 1.0
        
        # 使用SequenceMatcher计算相似度
        return SequenceMatcher(None, norm1, norm2).ratio()

    def _group_similar_queries(self, search_items: List[TopSearchItem], similarity_threshold: float = 0.8) -> List[TopSearchItem]:
        """
        将相似的搜索词进行分组合并
        
        Args:
            search_items: 搜索项列表
            similarity_threshold: 相似度阈值
            
        Returns:
            List[TopSearchItem]: 合并后的搜索项列表
        """
        if not search_items:
            return []
            
        # 按搜索次数排序，确保高频词优先
        sorted_items = sorted(search_items, key=lambda x: x.count, reverse=True)
        grouped_items = []
        processed_indices = set()
        
        for i, item in enumerate(sorted_items):
            if i in processed_indices:
                continue
                
            # 创建当前组的代表项
            group_item = TopSearchItem(
                query=item.query,
                count=item.count,
                search_type=item.search_type
            )
            
            # 查找相似的搜索词
            for j, other_item in enumerate(sorted_items[i+1:], i+1):
                if j in processed_indices:
                    continue
                    
                similarity = self._calculate_query_similarity(item.query, other_item.query)
                
                if similarity >= similarity_threshold:
                    # 合并搜索次数
                    group_item.count += other_item.count
                    processed_indices.add(j)
                    
                    # 如果其他项的搜索词更短且有效，使用更短的作为代表
                    if (len(other_item.query) < len(group_item.query) and 
                        self._is_valid_search_query(other_item.query)):
                        group_item.query = other_item.query
            
            grouped_items.append(group_item)
        
        # 按合并后的搜索次数重新排序
        return sorted(grouped_items, key=lambda x: x.count, reverse=True)

    def _calculate_query_quality_score(self, query: str, count: int, search_type: SearchType) -> float:
        """
        计算搜索词的质量分数
        
        Args:
            query: 搜索词
            count: 搜索次数
            search_type: 搜索类型
            
        Returns:
            float: 质量分数
        """
        if not self._is_valid_search_query(query):
            return 0.0
            
        score = 0.0
        
        # 1. 基础分数 - 搜索次数
        score += count * 0.3
        
        # 2. 长度分数 - 适中的长度得分更高
        length = len(query)
        if 2 <= length <= 10:
            score += 10.0
        elif 11 <= length <= 20:
            score += 5.0
        elif length > 20:
            score -= 5.0
            
        # 3. 搜索类型分数
        if search_type == SearchType.AUTHOR:
            score += 3.0  # 作者搜索通常更有价值
        elif search_type == SearchType.TAG:
            score += 2.0  # 标签搜索也很有价值
        elif search_type == SearchType.MEME_COIN:
            score += 1.0
            
        # 4. 语言内容分数
        if re.search(r'[\u4e00-\u9fff]', query):
            score += 1.0  # 中文内容
        
        return score

    def _detect_query_language(self, query: str) -> str:
        """
        检测查询语言 - 简化版本，因为索引已按语言分离
        
        Args:
            query: 搜索查询
            
        Returns:
            str: 检测到的语言代码
        """
        try:
            from src.common.utils import detect_language
            from src.common.constants import Language
            
            # 使用现有的语言检测功能
            detected = detect_language(query)
            
            # 转换为标准语言代码
            if detected == Language.CHINESE:
                return 'zh'
            elif detected == Language.ENGLISH:
                return 'en'
            elif detected == Language.RUSSIAN:
                return 'ru'
            else:
                # 对于OTHER类型，进行更细致的检测
                return self._fallback_language_detection(query)
                
        except Exception as e:
            logger.warning(f"语言检测失败，使用fallback: {e}")
            return self._fallback_language_detection(query)
    
    def _fallback_language_detection(self, query: str) -> str:
        """备用语言检测方法 - 仅支持中英俄三种语言"""
        import re
        
        # 检测中文字符（汉字）
        if re.search(r'[\u4e00-\u9fff]', query):
            return 'zh'
        
        # 检测俄文字符（西里尔字母）
        if re.search(r'[\u0400-\u04ff]', query):
            return 'ru'
        
        # 其他所有情况默认为英文
        return 'en'

    def _get_target_indices(self, search_type: SearchType, query_language: str = None, user_language: str = None) -> List[str]:
        """
        获取目标搜索索引列表
        
        Args:
            search_type: 搜索类型
            query_language: 查询语言
            user_language: 用户偏好语言
            
        Returns:
            List[str]: 目标索引列表
        """
        index_type = self._get_index_type(search_type)
        
        # 🎯 全语言搜索策略：总是搜索所有语言索引
        # 这样可以解决跨语言搜索问题，提高召回率：
        # - "Panda"在中文索引但用英语搜索找不到的问题
        # - "lei"作者在中文索引但被检测为英文搜索的问题
        # - "video title"在其他语言索引但语言检测错误的问题
        logger.debug(f"{search_type.name}搜索：使用全语言搜索策略")
        return [get_prefixed_index_name(index_type, lang) for lang in SUPPORTED_LANGUAGES]
    
    def _get_index_type(self, search_type: SearchType) -> str:
        """根据搜索类型获取索引类型"""
        if search_type in [SearchType.VIDEO, SearchType.IMAGE]:
            return "posts"
        elif search_type == SearchType.AUTHOR:
            return "authors"
        elif search_type == SearchType.MEME_COIN:
            return "memecoins"
        else:
            return "posts"  # 默认

    def _build_simple_query(self, query: str, search_type: SearchType) -> Dict[str, Any]:
        """
        构建简化的搜索查询 - 不需要多语言字段，每个索引已经是语言特定的
        
        Args:
            query: 搜索查询（已经过清理处理）
            search_type: 搜索类型
            
        Returns:
            Dict: ES查询DSL
        """
        # 🎯 不再重复清理查询，因为调用方已经处理过了
        # 检测是否为标签搜索
        is_tag_search, clean_query = self._detect_tag_search(query)
        
        # 对于SearchType.TAG，强制使用标签搜索逻辑
        if search_type == SearchType.TAG:
            is_tag_search = True
            clean_query = query  # TAG类型的查询已经移除了#前缀
        
        # 根据搜索类型构建查询
        if search_type in [SearchType.VIDEO, SearchType.IMAGE, SearchType.TAG]:
            # Posts搜索
            if is_tag_search:
                must_clauses = [{"term": {"tags.keyword": clean_query}}]
            else:
                must_clauses = [{
                    "multi_match": {
                        "query": clean_query,
                        "fields": [
                            "description^3",  # 描述权重最高
                            "author.name^2"   # 作者名称权重中等
                        ],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                }]
            
            # 添加类型过滤 (TAG搜索不需要类型过滤，因为会同时搜索VIDEO和IMAGE)
            if search_type == SearchType.VIDEO:
                must_clauses.append({"term": {"type": "Video"}})
            elif search_type == SearchType.IMAGE:
                must_clauses.append({"term": {"type": "Image"}})
                
        elif search_type == SearchType.AUTHOR:
            # Authors搜索 - 优化相关性评分，针对不同字段类型使用适当的查询
            # 🎯 性能优化：使用小写查询 + keyword字段，避免case_insensitive带来的性能损耗
            clean_query_lower = clean_query.lower().strip()
            
            must_clauses = [{
                "bool": {
                    "should": [
                        # 精确匹配（最高权重） - name字段用phrase，username使用text字段进行大小写不敏感匹配
                        {
                            "multi_match": {
                                "query": clean_query,
                                "fields": ["name^10"],
                                "type": "phrase",
                                "boost": 10.0
                            }
                        },
                        # username精确匹配：优先使用keyword字段（高性能）+ text字段（大小写不敏感）
                        {
                            "bool": {
                                "should": [
                                    {
                                        "term": {
                                            "username": {
                                                "value": clean_query_lower,
                                                "boost": 8.0
                                            }
                                        }
                                    },
                                    {
                                        "match": {
                                            "username.text": {
                                                "query": clean_query,
                                                "boost": 7.0
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        # 前缀匹配（高权重） - name字段用phrase_prefix，username字段优化查询
                        {
                            "multi_match": {
                                "query": clean_query,
                                "fields": ["name^5"],
                                "type": "phrase_prefix",
                                "boost": 5.0
                            }
                        },
                        # username前缀匹配：组合keyword和text字段
                        {
                            "bool": {
                                "should": [
                                    {
                                        "wildcard": {
                                            "username": {
                                                "value": f"{clean_query_lower}*",
                                                "boost": 3.0
                                            }
                                        }
                                    },
                                    {
                                        "match_phrase_prefix": {
                                            "username.text": {
                                                "query": clean_query,
                                                "boost": 2.5
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        # 包含匹配（中等权重） - username字段优化查询
                        {
                            "bool": {
                                "should": [
                                    {
                                        "wildcard": {
                                            "username": {
                                                "value": f"*{clean_query_lower}*",
                                                "boost": 2.0
                                            }
                                        }
                                    },
                                    {
                                        "match": {
                                            "username.text": {
                                                "query": clean_query,
                                                "boost": 1.5
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        # 模糊匹配（低权重） - 只对text字段使用
                        {
                            "multi_match": {
                                "query": clean_query,
                                "fields": [
                                    "name^3",
                                    "description^1",
                                    "dedication^1"
                                ],
                                "type": "best_fields",
                                "fuzziness": "AUTO",
                                "boost": 1.0
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }]
            
        elif search_type == SearchType.MEME_COIN:
            # MemeCoins搜索
            must_clauses = [{
                "bool": {
                    "should": [
                        # 符号精确匹配（最高优先级）- 使用.keyword字段确保精确匹配
                        {
                            "term": {
                                "symbol.keyword": {
                                    "value": clean_query.upper(),
                                    "boost": 5.0
                                }
                            }
                        },
                        # 符号文本匹配（备用）
                        {
                            "match": {
                                "symbol": {
                                    "query": clean_query.upper(),
                                    "boost": 4.0
                                }
                            }
                        },
                        # 名称精确匹配
                        {
                            "match": {
                                "name": {
                                    "query": clean_query,
                                    "boost": 3.0
                                }
                            }
                        },
                        # 文本搜索（描述和地址）
                        {
                            "multi_match": {
                                "query": clean_query,
                                "fields": [
                                    "description^2",
                                    "address^1"
                                ],
                                "type": "best_fields",
                                "fuzziness": "AUTO",
                                "boost": 1.0
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }]
            
            # 添加chain过滤条件，使用默认CHAIN_ID
            must_clauses.append({"term": {"chain": _DEFAULT_CHAIN_ID}})
            
        else:
            # 默认Posts搜索 (不应该到达这里，因为TAG已经在上面处理了)
            if is_tag_search:
                must_clauses = [{"term": {"tags.keyword": clean_query}}]
            else:
                must_clauses = [{
                    "multi_match": {
                        "query": clean_query,
                        "fields": [
                            "description^3",  # 描述权重最高
                            "author.name^2"   # 作者名称权重中等
                        ],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                }]
        
        # 添加状态过滤
        if search_type in [SearchType.VIDEO, SearchType.IMAGE, SearchType.TAG]:
            # Posts使用"posted"状态
            must_clauses.append({"term": {"status": "posted"}})
        # Author和Memecoin不检查status
        
        return {
            "bool": {
                "must": must_clauses
            }
        }

    async def _get_relation_author_ids(self, relation: FollowRelation, owner_id: str, limit: int = 5000) -> List[str]:
        """根据关系类型获取允许的作者ID集合（仅用于AUTHOR过滤）。
        followers: 取 follower_id where author_id = owner_id
        following: 取 author_id where follower_id = owner_id
        """
        try:
            if relation == FollowRelation.FOLLOWERS:
                stmt = select(authors_followers.c.follower_id).where(
                    authors_followers.c.author_id == owner_id
                )
            else:
                stmt = select(authors_followers.c.author_id).where(
                    authors_followers.c.follower_id == owner_id
                )

            result = await self.db.execute(stmt)
            ids = [str(row[0]) for row in result.all()]
            if len(ids) > limit:
                logger.warning(f"关系ID数量超限 owner_id={owner_id} relation={relation} count={len(ids)} > limit={limit}，将截断")
                ids = ids[:limit]
            return ids
        except Exception as e:
            logger.error(f"获取关系作者ID失败 owner_id={owner_id} relation={relation}: {e}")
            return []

    async def _search_multiple_indices(
        self, 
        query: str, 
        search_type: SearchType,
        target_indices: List[str],
        page: int = 1, 
        page_size: int = 20,
        blocked_author_ids: List[str] = None,
        allowed_author_ids: List[str] | None = None
    ) -> tuple[List[Dict], int]:
        """
        在多个语言索引中搜索
        
        Args:
            query: 搜索查询
            search_type: 搜索类型
            target_indices: 目标索引列表
            page: 页码
            page_size: 页面大小
            blocked_author_ids: 被屏蔽的作者ID列表
            
        Returns:
            tuple: (搜索结果列表, 总数)
        """
        es_client = None
        max_retries = 2
        
        for retry_count in range(max_retries + 1):
            try:
                es_client = await self._get_es_client()
                if not es_client:
                    logger.warning("ES客户端不可用")
                    return [], 0
                
                # 构建查询
                es_query = self._build_simple_query(query, search_type)

                # 对AUTHOR类型应用允许作者集合过滤
                if search_type == SearchType.AUTHOR and allowed_author_ids is not None:
                    if len(allowed_author_ids) == 0:
                        return [], 0
                    if "bool" not in es_query:
                        es_query = {"bool": {"must": [es_query]}}
                    es_query["bool"].setdefault("filter", []).append({
                        "terms": {"id": allowed_author_ids}
                    })
                
                # 🎯 添加调试日志：输出实际的ES查询
                if search_type == SearchType.MEME_COIN:
                    logger.debug(f"MEME_COIN ES查询构建完成: {es_query}")
                
                # 添加被屏蔽作者过滤（仅对posts类型）
                if search_type in [SearchType.VIDEO, SearchType.IMAGE, SearchType.TAG] and blocked_author_ids:
                    if "bool" not in es_query:
                        es_query = {"bool": {"must": [es_query]}}
                    es_query["bool"]["must_not"] = [
                        {"terms": {"author.id": blocked_author_ids}}
                    ]
                
                # 计算分页参数
                from_param = (page - 1) * page_size
                
                # 过滤存在的索引
                existing_indices = []
                for index_name in target_indices:
                    try:
                        if await es_client.client.indices.exists(index=index_name):
                            existing_indices.append(index_name)
                    except Exception as e:
                        logger.warning(f"检查索引存在性失败: {index_name}, error={str(e)}")
                        continue
                
                if not existing_indices:
                    logger.warning(f"没有找到可用的索引: {target_indices}")
                    return [], 0
                
                # 多索引搜索
                index_pattern = ",".join(existing_indices)
                
                # 🎯 添加调试日志：输出搜索参数
                if search_type == SearchType.MEME_COIN:
                    logger.debug(f"执行ES搜索: indices={index_pattern}, query={query}, chain_filter={_DEFAULT_CHAIN_ID}")
                
                response = await es_client.search_documents(
                    index=index_pattern,
                    query=es_query,
                    size=page_size,
                    from_=from_param,
                    sort=[{"_score": {"order": "desc"}}, {"created_at": {"order": "desc"}}]
                )
                
                # 🎯 添加调试日志：输出ES响应概要
                if search_type == SearchType.MEME_COIN:
                    hits_total = response.get("hits", {}).get("total", {}).get("value", 0)
                    logger.debug(f"ES响应: total={hits_total}, returned={len(response.get('hits', {}).get('hits', []))}")
                    
                    # 如果没有结果，输出更多调试信息
                    if hits_total == 0:
                        logger.warning(f"未找到匹配的memecoin: query='{query}', indices={existing_indices}")
                        # 尝试简单的match_all查询看看索引中是否有数据
                        try:
                            test_response = await es_client.search_documents(
                                index=index_pattern,
                                query={"match_all": {}},
                                size=1
                            )
                            test_total = test_response.get("hits", {}).get("total", {}).get("value", 0)
                            logger.debug(f"索引中总文档数: {test_total}")
                        except Exception as e:
                            logger.warning(f"测试查询失败: {e}")
                
                # 解析结果
                hits = response.get("hits", {}).get("hits", [])
                total = response.get("hits", {}).get("total", {}).get("value", 0)
                
                # 转换为标准格式
                results = []
                for hit in hits:
                    try:
                        source = hit["_source"]
                        
                        # 根据类型构建结果对象
                        # 🎯 修复MEME_COIN类型名称生成
                        type_name = search_type.name.title().replace("_", "")  # MEME_COIN -> MemeCoin
                        result_item = {
                            "type": f"{type_name}SearchItem",
                            "id": source["id"],
                            "created_at": source.get("created_at"),
                            "headline_title": source.get("title" if search_type in [SearchType.VIDEO, SearchType.IMAGE] else "name", ""),
                            "headline_description": source.get("description", ""),
                            "language": source.get("language", "unknown"),
                            "score": hit.get("_score", 0)
                        }
                        
                        # 构建具体的对象数据
                        if search_type == SearchType.VIDEO:
                            result_item["video"] = await self._build_video_from_es(source)
                        elif search_type == SearchType.IMAGE:
                            result_item["image"] = await self._build_image_from_es(source)
                        elif search_type == SearchType.AUTHOR:
                            result_item["author"] = await self._build_author_from_es(source)
                        elif search_type == SearchType.MEME_COIN:
                            result_item["memecoin"] = await self._build_memecoin_from_es(source)
                        
                        results.append(result_item)
                    except Exception as e:
                        logger.warning(f"解析搜索结果失败: {str(e)}")
                        continue
                
                # 🎯 对结果进行相关性重新评分（解决跨索引分数不可比较问题）
                results = self._re_score_results(results, query, search_type)
                
                logger.debug(f"多索引搜索完成: indices={existing_indices}, query={query}, results={len(results)}, total={total}")
                return results, total
                
            except Exception as e:
                logger.error(f"多索引搜索失败 (尝试 {retry_count + 1}/{max_retries + 1}): {str(e)}")
                
                # 如果不是最后一次重试，等待一下再试
                if retry_count < max_retries:
                    await asyncio.sleep(0.1 * (retry_count + 1))  # 递增等待时间
                    # 清理可能有问题的客户端
                    if es_client:
                        try:
                            await es_client.close()
                        except:
                            pass
                    continue
                else:
                    # 最后一次重试也失败了，返回空结果
                    return [], 0
        
        return [], 0

    def _re_score_results(self, results: List[Dict], query: str, search_type: SearchType) -> List[Dict]:
        """
        重新评分搜索结果，解决跨索引分数不可比较的问题
        
        Args:
            results: 原始搜索结果
            query: 搜索查询
            search_type: 搜索类型
            
        Returns:
            List[Dict]: 重新评分并排序后的结果
        """
        if not results:
            return results
        
        query_lower = query.lower().strip()
        
        for result in results:
            if search_type == SearchType.AUTHOR:
                # Author相关性评分
                author = result.get("author", {})
                name = (author.get("name", "") or "").lower().strip()
                username = (author.get("username", "") or "").lower().strip()
                description = (author.get("description", "") or "").lower().strip()
                
                relevance_score = 0
                
                # 精确匹配（最高分）
                if name == query_lower:
                    relevance_score += 1000
                elif username == query_lower:
                    relevance_score += 800
                
                # 完全包含（高分）
                elif query_lower in name:
                    relevance_score += 500
                elif query_lower in username:
                    relevance_score += 300
                
                # 前缀匹配（中等分）
                elif name.startswith(query_lower):
                    relevance_score += 200
                elif username.startswith(query_lower):
                    relevance_score += 150
                
                # 包含在description中（低分）
                elif query_lower in description:
                    relevance_score += 50
                
                # 模糊匹配（最低分）
                else:
                    # 计算编辑距离相似度
                    name_similarity = self._calculate_similarity(query_lower, name)
                    username_similarity = self._calculate_similarity(query_lower, username)
                    relevance_score += max(name_similarity, username_similarity) * 10
                
                # 更新结果的分数
                result["relevance_score"] = relevance_score
                result["original_score"] = result.get("score", 0)
            
            else:
                # 其他类型暂时保持原分数
                result["relevance_score"] = result.get("score", 0)
                result["original_score"] = result.get("score", 0)
        
        # 按相关性分数重新排序
        results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        return results
    
    def _calculate_similarity(self, s1: str, s2: str) -> float:
        """计算两个字符串的相似度（简化版Levenshtein距离）"""
        if not s1 or not s2:
            return 0.0
        
        # 如果其中一个字符串包含另一个，给高分
        if s1 in s2 or s2 in s1:
            return 0.8
        
        # 简化的相似度计算
        longer = s2 if len(s2) > len(s1) else s1
        shorter = s1 if len(s1) <= len(s2) else s2
        
        if len(longer) == 0:
            return 1.0
        
        # 计算相同字符的比例
        matches = sum(1 for a, b in zip(shorter, longer) if a == b)
        return matches / len(longer)

    async def _search_all_indices(
        self, 
        query: str, 
        page: int = 1, 
        page_size: int = 20,
        blocked_author_ids: List[str] = None,
        user_language: str = None
    ) -> tuple[List[Dict], int]:
        """
        搜索所有类型的索引并合并结果
        
        Args:
            query: 搜索查询
            page: 页码
            page_size: 页面大小
            blocked_author_ids: 被屏蔽的作者ID列表
            user_language: 用户偏好语言
            
        Returns:
            tuple: (搜索结果列表, 总数)
        """
        try:
            query_language = self._detect_query_language(query)
            
            # 🎯 修复分页逻辑：先获取足够多的结果，然后统一分页
            # 计算需要获取的总数：当前页之前的所有记录 + 当前页记录
            total_needed = page * page_size
            # 为每种类型分配合理的数量，确保能获取到足够的结果进行排序
            type_fetch_size = max(total_needed // 3, page_size)  # 至少获取page_size数量
            
            # 并发搜索所有类型 - 注意：这里传递page=1，因为我们要在合并后统一分页
            search_tasks = []
            
            for search_type in [SearchType.VIDEO, SearchType.IMAGE, SearchType.AUTHOR, SearchType.MEME_COIN]:
                target_indices = self._get_target_indices(search_type, query_language, user_language)
                task = self._search_multiple_indices(
                    query, search_type, target_indices, 1, type_fetch_size, blocked_author_ids
                )
                search_tasks.append(task)
            
            # 等待所有搜索完成
            results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # 合并结果
            all_results = []
            total_count = 0
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"搜索任务失败: {result}")
                    continue
                    
                search_results, count = result
                all_results.extend(search_results)
                total_count += count
            
            # 按分数和创建时间排序
            all_results.sort(
                key=lambda x: (x.get("score", 0), x.get("created_at") or datetime.min),
                reverse=True
            )
            
            # 🎯 应用正确的分页逻辑
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_results = all_results[start_index:end_index]
            
            logger.debug(f"全索引搜索完成: query={query}, total_fetched={len(all_results)}, paginated={len(paginated_results)}, total={total_count}")
            return paginated_results, total_count
            
        except Exception as e:
            logger.error(f"全索引搜索失败: {str(e)}")
            return [], 0

    async def _search_tag_indices(
        self, 
        query: str, 
        page: int = 1, 
        page_size: int = 20,
        blocked_author_ids: List[str] = None,
        user_language: str = None
    ) -> tuple[List[Dict], int]:
        """
        专门用于标签搜索的方法 - 只搜索VIDEO和IMAGE类型，使用优化的多层次标签匹配
        
        Args:
            query: 搜索查询（已去除#前缀）
            page: 页码
            page_size: 页面大小
            blocked_author_ids: 被屏蔽的作者ID列表
            user_language: 用户偏好语言
            
        Returns:
            tuple: (搜索结果列表, 总数)
        """
        es_client = None
        max_retries = 2
        
        for retry_count in range(max_retries + 1):
            try:
                es_client = await self._get_es_client()
                if not es_client:
                    logger.warning("ES客户端不可用")
                    return [], 0
                
                query_language = self._detect_query_language(query)
                
                # 获取posts索引（包含VIDEO和IMAGE）
                target_indices = self._get_target_indices(SearchType.VIDEO, query_language, user_language)
                
                # 🎯 构建优化的标签查询 - 使用可靠的tags.keyword字段
                tag_query = {
                    "bool": {
                        "must": [
                            {
                                "bool": {
                                    "should": [
                                        {"term": {"tags.keyword": query}},               # 精确匹配（最高优先级
                                        {"wildcard": {"tags.keyword": f"*{query}*"}},  # 包含匹配（*photomaker*）
                                        {"wildcard": {"tags.keyword": f"{query}*"}},  # 前缀匹配（photomaker*）
                                        {"wildcard": {"tags.keyword": f"*{query}"}}  # 后缀匹配（*photomaker）
                                    ],
                                    "minimum_should_match": 1
                                }
                            },
                            {"term": {"status": "posted"}}  # 只搜索已发布内容
                        ]
                    }
                }
                
                # 添加被屏蔽作者过滤
                if blocked_author_ids:
                    tag_query["bool"]["must_not"] = [
                        {"terms": {"author.id": blocked_author_ids}}
                    ]
                
                # 计算分页参数
                from_param = (page - 1) * page_size
                
                # 过滤存在的索引
                existing_indices = []
                for index_name in target_indices:
                    try:
                        exists = await es_client.client.indices.exists(index=index_name)
                        if exists:
                            existing_indices.append(index_name)
                    except Exception as e:
                        logger.warning(f"检查索引存在性失败 {index_name}: {e}")
                
                if not existing_indices:
                    logger.warning("没有可用的索引进行搜索")
                    return [], 0
                
                # 执行搜索
                search_body = {
                    "query": tag_query,
                    "from": from_param,
                    "size": page_size,
                    "sort": [
                        {"_score": {"order": "desc"}},
                        {"created_at": {"order": "desc"}}
                    ]
                }
                
                logger.debug(f"标签优化搜索查询: {tag_query}")
                
                response = await es_client.client.search(
                    index=",".join(existing_indices),
                    body=search_body
                )
                
                total_count = response['hits']['total']['value']
                logger.debug(f"多索引搜索完成: indices={existing_indices}, query={query}, results={len(response['hits']['hits'])}, total={total_count}")
                
                # 构建结果
                results = []
                for hit in response['hits']['hits']:
                    source = hit['_source']
                    source['score'] = hit['_score']
                    
                    # 根据类型构建结果对象
                    if source.get('type') == 'Video':
                        video_result = await self._build_video_from_es(source)
                        results.append({
                            "id": source['id'],
                            "type": "VideoSearchItem",
                            "created_at": video_result.get('created_at'),
                            "headline_title": "",
                            "headline_description": source.get('description', ''),
                            "video": video_result,
                            "score": hit['_score']
                        })
                    elif source.get('type') == 'Image':
                        image_result = await self._build_image_from_es(source)
                        results.append({
                            "id": source['id'],
                            "type": "ImageSearchItem", 
                            "created_at": image_result.get('created_at'),
                            "headline_title": "",
                            "headline_description": source.get('description', ''),
                            "image": image_result,
                            "score": hit['_score']
                        })
                
                return results, total_count
                
            except Exception as e:
                logger.error(f"标签搜索失败 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
                
                if retry_count < max_retries:
                    await asyncio.sleep(0.1 * (retry_count + 1))
                    if es_client:
                        try:
                            await es_client.close()
                        except:
                            pass
                    continue
                else:
                    return [], 0
        
        return [], 0

    async def _build_video_from_es(self, source: Dict) -> Dict:
        """从ES源构建符合PostFeed的Video对象"""
        from datetime import datetime
        from src.common.constants import Language, PostStatus
        
        # 处理created_at时间
        created_at = source.get("created_at")
        if isinstance(created_at, str):
            try:
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            except:
                created_at = datetime.utcnow()
        elif not isinstance(created_at, datetime):
            created_at = datetime.utcnow()
        
        # 语言映射：ES中的'other'对应Language.OTHER='oth'
        language_mapping = {
            'zh': Language.CHINESE,
            'en': Language.ENGLISH, 
            'ru': Language.RUSSIAN,
            'other': Language.OTHER,
            'global': Language.GLOBAL
        }
        language = language_mapping.get(source.get("language", "other"), Language.OTHER)
        
        # 构建author对象
        author_data = source.get("author", {})
        author = {
            "id": author_data.get("id", ""),
            "name": author_data.get("name", "Unknown"),
            "username": author_data.get("username", ""),
            "avatar": author_data.get("avatar"),
            "region": author_data.get("region", "oth"),
            "created_at": created_at,
            "role": author_data.get("role"),
            "status": "active"
        }
        
        return {
            # PostFeed基础字段
            "id": source["id"],
            "type": "Video",
            "status": source.get("status", "posted"),
            "created_at": created_at,
            "updated_at": created_at,  # 默认使用created_at
            "comments_count": 0,  # 默认值，后续可以从数据库补充
            "likes_count": 0,
            "collections_count": 0,
            "view_count": 0,
            "region": source.get("region") or "oth",
            "language": language,
            "author_id": author_data.get("id", ""),
            "tags": source.get("tags", []),
            
            # PostFeed扩展字段
            "title": source.get("title"),
            "text": source.get("text"),
            "description": source.get("description"),
            "cover": source.get("cover"),
            "url": source.get("url"),
            "url_type": None,
            "content_type": None,
            "contents_count": None,
            "original_cover": None,
            "is_in_collection": None,
            "is_liked": None,
            "width": source.get("width"),
            "height": source.get("height"),
            "images_data": source.get("images_data", []),
            
            # Author信息
            "author": author
        }

    async def _build_image_from_es(self, source: Dict) -> Dict:
        """从ES源构建符合PostFeed的Image对象"""
        from datetime import datetime
        from src.common.constants import Language, PostStatus
        
        # 处理created_at时间
        created_at = source.get("created_at")
        if isinstance(created_at, str):
            try:
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            except:
                created_at = datetime.utcnow()
        elif not isinstance(created_at, datetime):
            created_at = datetime.utcnow()
        
        # 语言映射：ES中的'other'对应Language.OTHER='oth'
        language_mapping = {
            'zh': Language.CHINESE,
            'en': Language.ENGLISH, 
            'ru': Language.RUSSIAN,
            'other': Language.OTHER,
            'global': Language.GLOBAL
        }
        language = language_mapping.get(source.get("language", "other"), Language.OTHER)
        
        # 构建author对象
        author_data = source.get("author", {})
        author = {
            "id": author_data.get("id", ""),
            "name": author_data.get("name", "Unknown"),
            "username": author_data.get("username", ""),
            "avatar": author_data.get("avatar"),
            "region": author_data.get("region", "oth"),
            "created_at": created_at,
            "role": author_data.get("role"),
            "status": "active"
        }
        
        return {
            # PostFeed基础字段
            "id": source["id"],
            "type": "Image",
            "status": source.get("status", "posted"),
            "created_at": created_at,
            "updated_at": created_at,  # 默认使用created_at
            "comments_count": 0,  # 默认值，后续可以从数据库补充
            "likes_count": 0,
            "collections_count": 0,
            "view_count": 0,
            "region": source.get("region") or "oth",
            "language": language,
            "author_id": author_data.get("id", ""),
            "tags": source.get("tags", []),
            
            # PostFeed扩展字段
            "title": source.get("title"),
            "text": source.get("text"),
            "description": source.get("description"),
            "cover": source.get("cover"),
            "url": source.get("url"),
            "url_type": None,
            "content_type": None,
            "contents_count": None,
            "original_cover": None,
            "is_in_collection": None,
            "is_liked": None,
            "width": source.get("width"),
            "height": source.get("height"),
            "images_data": source.get("images_data", []),
            
            # Author信息
            "author": author
        }

    async def _build_author_from_es(self, source: Dict) -> Dict:
        """从ES源构建符合AuthorWithFlags的Author对象"""
        # 解析created_at时间
        created_at = source.get("created_at")
        if isinstance(created_at, str):
            try:
                # 尝试解析ISO格式时间戳
                parsed_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            except:
                parsed_time = datetime.utcnow()
        else:
            parsed_time = datetime.utcnow()
        
        # 🎯 从数据库获取关注者数量
        author_id = source["id"]
        follower_count = 0
        
        try:
            # 查询关注者数量
            from src.database.models.Author import authors_followers
            follower_count_query = select(func.count(authors_followers.c.follower_id)).where(
                authors_followers.c.author_id == author_id
            )
            result = await self.db.execute(follower_count_query)
            follower_count = result.scalar() or 0
            
            logger.debug(f"从数据库获取author {author_id} 关注者数量: {follower_count}")
            
        except Exception as e:
            logger.warning(f"获取author {author_id} 关注者数量失败: {e}")
            follower_count = 0
        
        return {
            # AuthorRead必需字段
            "id": source["id"],
            "name": source.get("name", ""),
            "username": source.get("username", ""),
            "avatar": source.get("avatar"),  # 可以为None
            "region": source.get("region", ""),
            "created_at": parsed_time,
            "role": source.get("role"),  # 可以为None  
            "status": source.get("status", "active"),
            
            # AuthorWithFlags额外字段
            "is_following": False,  # 默认未关注，后续可通过数据库查询更新
            "follower_count": follower_count,  # 🎯 从数据库获取的实时关注者数量
        }

    async def _build_memecoin_from_es(self, source: Dict) -> Dict:
        """从ES源构建符合TokenSchema的MemeCoin对象"""
        from datetime import datetime
        import time
        
        # 处理created_at时间戳转换
        created_at_timestamp = 0
        if source.get("created_at"):
            try:
                if isinstance(source["created_at"], str):
                    # 如果是字符串，尝试解析为datetime然后转为时间戳
                    dt = datetime.fromisoformat(source["created_at"].replace('Z', '+00:00'))
                    created_at_timestamp = int(dt.timestamp())
                else:
                    # 如果已经是数字，直接使用
                    created_at_timestamp = int(source["created_at"])
            except Exception as e:
                logger.warning(f"解析created_at失败: {e}")
                created_at_timestamp = int(time.time())
        
        # 🎯 从Redis获取token的实时价格数据
        token_address = source.get("address", "")
        market_cap = "0"
        price = "0"
        price_in_quote = "0"
        price_change_percent = "0"
        progress = 0
        image_url = ""
        collection_id = ""
        
        if token_address:
            try:
                # 构建Redis key
                redis_key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
                
                # 从Redis获取token数据
                token_data = await self.cache_client.client.hgetall(redis_key)
                
                if token_data:
                    # 提取实时价格数据
                    market_cap = token_data.get("market_cap", "0")
                    price = token_data.get("price", "0")
                    price_in_quote = token_data.get("price_in_quote", "0")
                    price_change_percent = token_data.get("price_change_percent", "0")
                    progress = int(token_data.get("progress", 0))
                    
                    # 🎯 获取图片URL和collection_id
                    image_url = token_data.get("image_url", "")
                    collection_id = token_data.get("collection_id", "")
                    
                    logger.debug(f"从Redis获取token {token_address} 数据: market_cap={market_cap}, price_change={price_change_percent}%, image_url={image_url}")
                else:
                    logger.warning(f"Redis中未找到token {token_address} 的数据，使用默认值")
                    
            except Exception as e:
                logger.warning(f"从Redis获取token {token_address} 数据失败: {e}")
        
        # 🎯 如果Redis中没有image_url，使用ES中的image_url作为fallback
        if not image_url:
            image_url = source.get("image_url", "")
        
        # 🎯 返回符合TokenSchema的数据结构
        return {
            # 必需字段 - 从ES源映射
            "token_name": source.get("name", ""),
            "token_symbol": source.get("symbol", ""),
            "token_address": token_address,
            "description": source.get("description", ""),
            "created_at": created_at_timestamp,
            
            # 必需字段 - 从Redis获取实时数据或使用默认值
            "image_url": image_url,
            "collection_id": collection_id,
            "market_cap": market_cap,
            "price": price,
            "price_in_quote": price_in_quote,
            "price_change_percent": price_change_percent,
            "progress": progress,
            
            # 可选字段
            "liquidity": "",
            "volume_24h": "",
            "social_links": None
        }

    def _record_search_history_async(
        self, 
        query: str, 
        search_type: str, 
        user_id: Optional[str], 
        results_count: int, 
        duration: float
    ) -> None:
        """
        异步记录搜索历史到 Elasticsearch
        
        Args:
            query: 搜索查询
            search_type: 搜索类型
            user_id: 用户ID（字符串类型）
            results_count: 搜索结果数量
            duration: 搜索耗时（秒）
        """
        try:
            # 使用 common celery_client 异步提交搜索历史记录任务
            task_name = "src.worker.tasks.search.record_search_task"
            
            # 通过 common celery_client 提交任务
            result = self.common_celery_client.send_task(
                task_name=task_name,
                args=[query, search_type, user_id, results_count],
                kwargs={},
                countdown=0  # 立即执行
            )
            
            logger.debug(f"已提交搜索历史记录任务: task_id={result.id}, query='{query}', type={search_type}, user_id={user_id}, results={results_count}, duration={duration:.3f}s")
            
        except Exception as e:
            # 记录搜索历史失败不应该影响搜索功能，只记录警告日志
            logger.warning(f"提交搜索历史记录任务失败: {str(e)}")

    def _safe_sanitize(self, query: str) -> str:
        """
        安全清理查询但保留关键信息
        只移除明显的特殊字符，保留字母数字和基本符号
        适用于memecoin搜索等需要保留短词的场景
        
        Args:
            query: 原始搜索查询
            
        Returns:
            str: 安全清理后的查询
        """
        import re
        # 只移除明显的特殊字符，保留字母数字、空格、点、破折号
        cleaned = re.sub(r'[^\w\s.-]', ' ', query)
        # 标准化空白字符，移除多余空格
        return ' '.join(cleaned.split())

    def _detect_tag_search(self, query: str) -> tuple[bool, str]:
        """检测是否为 tag 搜索（以 # 开头）"""
        if query.strip().startswith('#'):
            # 移除 # 号并清理查询
            cleaned_query = query.strip()[1:].strip()
            return True, cleaned_query
        return False, query

    async def _search_default_content(
            self,
            params: Params,
            type: SearchType,
            user: Optional[User] = None,
            relation: Optional[FollowRelation] = None,
            relation_owner_id: Optional[str] = None,
    ) -> Page[dict]:
        """
        返回默认排序的内容（按创建时间倒序）
        
        Args:
            params: 分页参数
            type: 搜索类型
            user: 用户对象（可选）
            
        Returns:
            Page[dict]: 分页的搜索结果
        """
        try:
            # 获取被屏蔽的作者ID列表（仅登录用户）
            blocked_author_ids: List[str] = []
            if user is not None:
                blocked_author_ids = [
                    str(aid)
                    for (aid,) in await self.db.execute(
                        select(authors_blocks.c.blocked_id).where(
                            authors_blocks.c.blocker_id == user.id
                        )
                    )
                ]
                logger.debug(f"已屏蔽作者数量: {len(blocked_author_ids)}")
            
            # 分页参数
            page = params.page
            page_size = min(params.size, 20)
            
            # 获取用户语言偏好（匿名用户默认英文）
            user_language = getattr(user, 'language', None) or 'en'
            
            # 根据搜索类型获取默认内容
            if type == SearchType.ALL:
                return await self._search_default_all_content(params, blocked_author_ids, user_language)
            else:
                allowed_author_ids: List[str] | None = None
                if type == SearchType.AUTHOR and relation is not None:
                    owner_id = relation_owner_id or (getattr(user, 'id', None))
                    if owner_id is None:
                        return Page.create(items=[], params=params, total=0)
                    allowed_author_ids = await self._get_relation_author_ids(relation, owner_id)
                    if len(allowed_author_ids) == 0:
                        return Page.create(items=[], params=params, total=0)
                return await self._search_default_by_type(
                    params, type, blocked_author_ids, user_language, allowed_author_ids
                )
                
        except Exception as e:
            logger.error(f"默认内容搜索失败: {str(e)}")
            return Page.create(items=[], params=params, total=0)

    async def _search_default_all_content(
            self,
            params: Params,
            blocked_author_ids: List[str],
            user_language: str
    ) -> Page[dict]:
        """获取所有类型的默认内容"""
        try:
            page = params.page
            page_size = min(params.size, 20)
            
            # 为每种类型分配合理的数量
            type_fetch_size = max(page_size // 3, 5)  # 至少每种类型获取5条
            
            # 并发搜索所有类型的默认内容
            search_tasks = []
            for search_type in [SearchType.VIDEO, SearchType.IMAGE, SearchType.AUTHOR, SearchType.MEME_COIN]:
                task = self._search_default_by_type(
                    Params(page=1, size=type_fetch_size), 
                    search_type, 
                    blocked_author_ids, 
                    user_language
                )
                search_tasks.append(task)
            
            # 等待所有搜索完成
            results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # 合并结果
            all_results = []
            total_count = 0
            
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"默认内容搜索任务失败: {result}")
                    continue
                
                if hasattr(result, 'items') and hasattr(result, 'total'):
                    all_results.extend(result.items)
                    total_count += result.total
            
            # 按创建时间倒序排序
            def get_sort_key(x):
                """安全获取排序键，处理不同类型的created_at值"""
                created_at = x.get("created_at")
                
                if created_at is None:
                    return datetime.min
                
                # 如果已经是datetime对象，直接返回
                if isinstance(created_at, datetime):
                    return created_at
                
                # 如果是字符串，尝试解析ISO格式
                if isinstance(created_at, str):
                    try:
                        # 移除时区信息并截取前19个字符 (YYYY-MM-DDTHH:MM:SS)
                        clean_date = created_at.replace('Z', '').split('+')[0][:19]
                        return datetime.fromisoformat(clean_date.replace('T', ' '))
                    except:
                        try:
                            # 备用解析方法
                            return datetime.strptime(clean_date, '%Y-%m-%d %H:%M:%S')
                        except:
                            # 解析失败，返回最小值
                            return datetime.min
                
                # 其他类型都返回最小值
                return datetime.min
            
            all_results.sort(key=get_sort_key, reverse=True)
            
            # 应用分页
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_results = all_results[start_index:end_index]
            
            logger.info(f"默认全内容搜索完成: total_fetched={len(all_results)}, paginated={len(paginated_results)}")
            return Page.create(items=paginated_results, params=params, total=len(all_results))
            
        except Exception as e:
            logger.error(f"默认全内容搜索失败: {str(e)}")
            return Page.create(items=[], params=params, total=0)

    async def _search_default_by_type(
            self,
            params: Params,
            search_type: SearchType,
            blocked_author_ids: List[str],
            user_language: str,
            allowed_author_ids: List[str] | None = None
    ) -> Page[dict]:
        """根据类型获取默认内容"""
        try:
            es_client = await self._get_es_client()
            if not es_client:
                logger.warning("ES客户端不可用")
                return Page.create(items=[], params=params, total=0)
            
            # 获取目标索引
            target_indices = self._get_target_indices(search_type, user_language, user_language)
            
            # 过滤存在的索引
            existing_indices = []
            for index_name in target_indices:
                try:
                    exists = await es_client.client.indices.exists(index=index_name)
                    if exists:
                        existing_indices.append(index_name)
                except Exception as e:
                    logger.warning(f"检查索引存在性失败 {index_name}: {e}")
            
            if not existing_indices:
                logger.warning(f"没有可用的{search_type}索引")
                return Page.create(items=[], params=params, total=0)
            
            # 构建默认查询（按创建时间倒序）
            page = params.page
            page_size = min(params.size, 20)
            from_param = (page - 1) * page_size
            
            # 构建默认查询（根据类型使用不同的过滤条件）
            if search_type == SearchType.AUTHOR:
                # Author不检查status，搜索所有author
                default_query = {
                    "bool": {
                        "must": [
                            {"match_all": {}}  # 搜索所有author，不过滤status
                        ]
                    }
                }
                if allowed_author_ids is not None:
                    if len(allowed_author_ids) == 0:
                        return Page.create(items=[], params=params, total=0)
                    default_query["bool"].setdefault("filter", []).append({
                        "terms": {"id": allowed_author_ids}
                    })
            elif search_type == SearchType.MEME_COIN:
                # Memecoin不检查status，只添加chain过滤
                default_query = {
                    "bool": {
                        "must": [
                            {"term": {"chain": _DEFAULT_CHAIN_ID}}  # 只过滤默认chain
                        ]
                    }
                }
            else:
                # Video和Image使用 "posted" 状态
                default_query = {
                    "bool": {
                        "must": [
                            {"term": {"status": "posted"}}  # Video/Image使用posted状态
                        ]
                    }
                }
                
                # 添加类型过滤
                if search_type == SearchType.VIDEO:
                    default_query["bool"]["must"].append({"term": {"type": "Video"}})
                elif search_type == SearchType.IMAGE:
                    default_query["bool"]["must"].append({"term": {"type": "Image"}})
            
            # 添加被屏蔽作者过滤
            if blocked_author_ids:
                default_query["bool"]["must_not"] = [
                    {"terms": {"author.id": blocked_author_ids}}
                ]
            
            # 构建搜索请求
            search_body = {
                "query": default_query,
                "from": from_param,
                "size": page_size,
                "sort": [
                    {"created_at": {"order": "desc"}}  # 按创建时间倒序
                ]
            }
            
            # 执行搜索
            response = await es_client.client.search(
                index=",".join(existing_indices),
                body=search_body
            )
            
            total_count = response['hits']['total']['value']
            logger.debug(f"默认{search_type}内容搜索完成: results={len(response['hits']['hits'])}, total={total_count}")
            
            # 构建结果
            results = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                source['score'] = 1.0  # 默认评分
                
                # 根据类型构建结果对象
                if search_type == SearchType.VIDEO or source.get('type') == 'Video':
                    video_result = await self._build_video_from_es(source)
                    results.append({
                        "id": source['id'],
                        "type": "VideoSearchItem",
                        "created_at": video_result.get('created_at'),
                        "headline_title": "",
                        "headline_description": source.get('description', ''),
                        "video": video_result,
                        "score": 1.0
                    })
                elif search_type == SearchType.IMAGE or source.get('type') == 'Image':
                    image_result = await self._build_image_from_es(source)
                    results.append({
                        "id": source['id'],
                        "type": "ImageSearchItem",
                        "created_at": image_result.get('created_at'),
                        "headline_title": "",
                        "headline_description": source.get('description', ''),
                        "image": image_result,
                        "score": 1.0
                    })
                elif search_type == SearchType.AUTHOR:
                    author_result = await self._build_author_from_es(source)
                    results.append({
                        "id": source['id'],
                        "type": "AuthorSearchItem",
                        "created_at": author_result.get('created_at'),
                        "headline_title": author_result.get('name', ''),
                        "headline_description": source.get('description', ''),
                        "author": author_result,
                        "score": 1.0
                    })
                elif search_type == SearchType.MEME_COIN:
                    memecoin_result = await self._build_memecoin_from_es(source)
                    results.append({
                        "id": source['id'],
                        "type": "MemeCoinSearchItem",
                        "created_at": memecoin_result.get('created_at'),
                        "headline_title": memecoin_result.get('name', ''),
                        "headline_description": source.get('description', ''),
                        "memecoin": memecoin_result,
                        "score": 1.0
                    })
            
            return Page.create(items=results, params=params, total=total_count)
            
        except Exception as e:
            logger.error(f"默认{search_type}内容搜索失败: {str(e)}")
            return Page.create(items=[], params=params, total=0)

    async def search(
            self,
            query: Optional[str],
            params: Params,
            type: SearchType,
            user: Optional[User] = None,
            relation: Optional[FollowRelation] = None,
            relation_owner_id: Optional[str] = None,
    ) -> Page[dict]:
        """
        使用语言路由的Elasticsearch搜索
        
        Args:
            query: 搜索查询（可选，空字符串或None时返回默认排序）
            params: 分页参数
            type: 搜索类型
            user: 用户对象（可选，如果为None则为匿名搜索）
        """
        # 预处理作者关系过滤（仅 AUTHOR）
        allowed_author_ids: List[str] | None = None
        if type == SearchType.AUTHOR and relation is not None:
            owner_id = relation_owner_id or (getattr(user, 'id', None))
            if owner_id is None:
                return Page.create(items=[], params=params, total=0)
            allowed_author_ids = await self._get_relation_author_ids(relation, owner_id)
            if len(allowed_author_ids) == 0:
                return Page.create(items=[], params=params, total=0)

        # 处理空查询情况
        if not query or query.strip() == "":
            logger.info(f"执行默认内容搜索: type={type}, user_id={user.id if user else 'Anonymous'}")
            return await self._search_default_content(params, type, user, relation, relation_owner_id)
        
        logger.info(f"执行语言路由搜索: query='{query}', type={type}, user_id={user.id if user else 'Anonymous'}")
        start_time = datetime.now()
        
        # 🎯 只有当type=SearchType.ALL时，才检测#前缀
        is_tag_search = False
        cleaned_query = query
        
        if type == SearchType.ALL:
            # 检测是否为 tag 搜索
            is_tag_search, cleaned_query = self._detect_tag_search(query)
        
        # 如果检测到 # 前缀，记录为 tag 搜索
        search_type_for_log = "tag" if is_tag_search else (type.name if type else None)
        
        # 记录搜索历史到ES（会在搜索完成后异步记录结果数量）
        search_start_time = datetime.now()

        sanitized_query = self._safe_sanitize(cleaned_query)
        if sanitized_query == "":
            return await self._search_default_content(params, type, user)
        
        # 🎯 只有当用户登录时才获取黑名单列表
        blocked_author_ids: Sequence[int] = []
        if user is not None:
            blocked_author_ids = [
                aid
                for (aid,) in await self.db.execute(
                    select(authors_blocks.c.blocked_id).where(
                        authors_blocks.c.blocker_id == user.id
                    )
                )
            ]
            logger.debug(f"已屏蔽作者数量: {len(blocked_author_ids)}")
        else:
            logger.debug("匿名用户搜索，跳过黑名单过滤")

        # 分页参数
        page = params.page
        page_size = min(params.size, 20)  # 限制最大页面大小，防止性能问题
        
        # 获取用户语言偏好（匿名用户默认英文）
        user_language = getattr(user, 'language', None) or 'en'
        
        try:
            # 🎯 调整标签搜索路由：只有当type=SearchType.ALL且检测到#前缀时，才使用标签搜索
            if type == SearchType.TAG or (type == SearchType.ALL and is_tag_search):
                # TAG搜索：只搜索VIDEO和IMAGE类型
                search_results, total_count = await self._search_tag_indices(
                    sanitized_query, page, page_size, 
                    [str(aid) for aid in blocked_author_ids],
                    user_language
                )
            elif type == SearchType.ALL:
                search_results, total_count = await self._search_all_indices(
                    sanitized_query, page, page_size, 
                    [str(aid) for aid in blocked_author_ids],
                    user_language
                )
            else:
                # 单类型搜索
                query_language = self._detect_query_language(sanitized_query)
                target_indices = self._get_target_indices(type, query_language, user_language)
                search_results, total_count = await self._search_multiple_indices(
                    sanitized_query, type, target_indices, page, page_size,
                    [str(aid) for aid in blocked_author_ids],
                    allowed_author_ids if type == SearchType.AUTHOR else None
                )
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"语言路由搜索完成: query='{sanitized_query}', 结果数量={len(search_results)}, 总数={total_count}, 耗时={duration:.3f}s")
            
            # 异步记录搜索历史到ES
            search_duration = (datetime.now() - search_start_time).total_seconds()
            user_id = user.id if user else None
            self._record_search_history_async(cleaned_query, search_type_for_log, user_id, len(search_results), search_duration)
            
            return Page.create(items=search_results, params=params, total=total_count)
            
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            # 记录失败的搜索历史
            search_duration = (datetime.now() - search_start_time).total_seconds()
            user_id = user.id if user else None
            self._record_search_history_async(cleaned_query, search_type_for_log, user_id, 0, search_duration)
            
            return Page.create(items=[], params=params, total=0)

    async def get_top_searches(
            self,
            limit: int = 10,
            language: str | None = None,
            days: int = 3
    ) -> List[TopSearchItem]:
        """获取热门搜索（从ES聚合查询）- 优化版本，支持相似词折叠和质量过滤"""
        logger.info("获取热门搜索: limit=%s language=%s days=%s", limit, language or "all", days)
        start_wallclock = datetime.now()

        # ---------- 1. 先查缓存 ----------
        cache_key = f"top_searches_optimized:{days}d:{limit}:{language or 'all'}"
        cached = await self.cache_client.get(_SEARCH_REDIS_ENDPOINT, cache_key)
        if cached:
            logger.debug("缓存命中")
            return [TopSearchItem(**item) for item in cached]

        # ---------- 2. ES聚合查询 ----------
        es_client = None
        max_retries = 2
        
        for retry_count in range(max_retries + 1):
            try:
                es_client = await self._get_es_client()
                unified_manager = await self._get_unified_manager()
                if not es_client or not unified_manager:
                    logger.warning("ES客户端或管理器未初始化，回退到空结果")
                    return []

                # 计算时间范围
                from_date = datetime.utcnow() - timedelta(days=days)
                
                # 构建ES聚合查询 - 获取更多原始数据用于后续优化处理
                query = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "range": {
                                        "created_at": {
                                            "gte": from_date.isoformat()
                                        }
                                    }
                                },
                                {
                                    "exists": {
                                        "field": "query.keyword"
                                    }
                                }
                            ]
                        }
                    },
                    "aggs": {
                        "top_queries": {
                            "terms": {
                                "field": "query.keyword",
                                "size": limit * 3,  # 获取3倍数量的原始数据用于后续处理
                                "min_doc_count": 2,  # 至少被搜索2次
                                "order": {"_count": "desc"}
                            },
                            "aggs": {
                                "latest_search": {
                                    "max": {
                                        "field": "created_at"
                                    }
                                },
                                "most_common_type": {
                                    "terms": {
                                        "field": "search_type",
                                        "size": 1
                                    }
                                }
                            }
                        }
                    },
                    "size": 0  # 不返回文档，只返回聚合结果
                }
                
                # 添加语言过滤
                if language:
                    query["query"]["bool"]["must"].append({
                        "term": {
                            "language": language
                        }
                    })
                
                # 执行聚合查询
                pattern = unified_manager.search_history_manager.get_search_pattern()
                response = await es_client.client.search(
                    index=pattern,
                    body=query
                )
                
                # 解析聚合结果
                raw_searches = []
                if "aggregations" in response:
                    buckets = response["aggregations"]["top_queries"]["buckets"]
                    
                    for bucket in buckets:
                        try:
                            query_text = bucket["key"]
                            count = bucket["doc_count"]
                            
                            # 获取最常见的搜索类型
                            search_type = "all"
                            if bucket.get("most_common_type", {}).get("buckets"):
                                search_type = bucket["most_common_type"]["buckets"][0]["key"]
                            
                            raw_searches.append(TopSearchItem(
                                query=query_text,
                                count=count,
                                search_type=SearchType[search_type.upper()] if search_type.upper() in SearchType.__members__ else SearchType.ALL,
                            ))
                        except Exception as e:
                            logger.warning(f"解析热门搜索项失败: {str(e)}")
                            continue
                
                # 成功获取结果，退出重试循环
                break
                
            except Exception as e:
                logger.error(f"ES热门搜索查询失败 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
                
                # 如果不是最后一次重试，等待一下再试
                if retry_count < max_retries:
                    await asyncio.sleep(0.1 * (retry_count + 1))  # 递增等待时间
                    # 清理可能有问题的客户端
                    if es_client:
                        try:
                            await es_client.close()
                        except:
                            pass
                    continue
                else:
                    # 最后一次重试也失败了，返回空结果
                    return []

        # ---------- 3. 优化处理 ----------
        if raw_searches:
            logger.debug(f"获取到 {len(raw_searches)} 个原始搜索项，开始优化处理")
            
            # 3.1 过滤无效搜索词
            valid_searches = [
                item for item in raw_searches 
                if self._is_valid_search_query(item.query)
            ]
            logger.debug(f"过滤后剩余 {len(valid_searches)} 个有效搜索项")
            
            # 3.2 相似词分组合并
            if valid_searches:
                grouped_searches = self._group_similar_queries(valid_searches, similarity_threshold=0.8)
                logger.debug(f"相似词分组后剩余 {len(grouped_searches)} 个搜索项")
            else:
                grouped_searches = []
            
            # 3.3 质量评分和排序
            if grouped_searches:
                scored_searches = []
                for item in grouped_searches:
                    score = self._calculate_query_quality_score(item.query, item.count, item.search_type)
                    if score > 0:  # 只保留有正分的搜索项
                        scored_searches.append((item, score))
                
                # 按质量分数排序
                scored_searches.sort(key=lambda x: x[1], reverse=True)
                
                # 提取最终结果
                top_searches = [item[0] for item in scored_searches[:limit]]
                logger.debug(f"质量评分后最终返回 {len(top_searches)} 个搜索项")
            else:
                top_searches = []
        else:
            top_searches = []

        # ---------- 4. 写缓存 ----------
        if top_searches:
            await self.cache_client.set(
                _SEARCH_REDIS_ENDPOINT,
                cache_key,
                [item.model_dump() for item in top_searches],
                expiration_time=timedelta(minutes=5)
            )

        logger.info(
            "ES热门搜索查询完成 rows=%s 耗时=%.3fs",
            len(top_searches),
            (datetime.now() - start_wallclock).total_seconds()
        )
        return top_searches

    async def close(self):
        """清理资源（连接池中的ES客户端由连接池统一管理）"""
        # 🎯 由于使用连接池，这里不需要手动关闭ES客户端
        # 连接池会统一管理ES客户端的生命周期
        logger.debug("SearchService清理完成（ES客户端由连接池管理）")


