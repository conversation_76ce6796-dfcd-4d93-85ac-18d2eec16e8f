"""
Reports任务客户端
使用common中的celery client，专注于任务提交和状态查询
"""
from typing import Dict, Any
from src.common.celery_client import create_celery_client


class ReportsTaskClient:
    """Reports服务的任务客户端"""
    
    def __init__(self):
        self.client = create_celery_client("reports")
    
    def create_report(self, report_data: Dict[str, Any]):
        """提交创建举报任务到后台队列"""
        return self.client.send_task(
            "src.worker.tasks.reports.create_report",  # 使用reports域的任务名
            args=[report_data],
            queue="reports"
        )
    
    def get_task_status(self, task_id: str):
        """获取任务状态"""
        return self.client.get_task_status(task_id)


# 创建全局实例
reports_task_client = ReportsTaskClient() 