class ReportNotFound(Exception):
    def __init__(self, report_id: str):
        self.report_id = report_id
        super().__init__(f"Report with ID '{report_id}' not found")


class ReportAlreadyHandled(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(message)


class CantCreateReport(Exception):
    def __init__(self, message: str = "Cannot create report"):
        self.message = message
        super().__init__(message)


class InvalidReportType(Exception):
    def __init__(self, report_type: str):
        self.report_type = report_type
        super().__init__(f"Invalid report type: {report_type}")


class DuplicateReport(Exception):
    def __init__(self, message: str = "Report already exists"):
        self.message = message
        super().__init__(message)


class InvalidTargetId(Exception):
    def __init__(self, target_type: str, target_id: str):
        self.target_type = target_type
        self.target_id = target_id
        super().__init__(f"Invalid {target_type} ID: {target_id}")


class SelfReportError(Exception):
    def __init__(self, message: str = "Cannot report yourself"):
        self.message = message
        super().__init__(message)
