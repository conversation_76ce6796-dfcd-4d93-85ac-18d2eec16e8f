from datetime import datetime
from typing import Union

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, text

from src.database.models import User, <PERSON>, Comment, Author, Complaint, ImReport
from src.database.session import get_session
from src.common.constants import ComplaintStatus, ReportStatus
from src.reports.dto import ReportCreateDTO, ReportDTO, ReportFetchParamsDTO
from src.reports.exceptions import (
    ReportNotFound,
    ReportAlreadyHandled,
    CantCreateReport
)
from src.reports.repos import ReportRepo


class ReportService:
    """
    Business logic of `Report`
    """

    def __init__(self, report_repo: ReportRepo):
        """
        Initializes `self`
        :param report_repo: Initialized `ReportRepo`
        """
        self._repo = report_repo

    async def handle_report(self, report: ReportDTO, current_user: User) -> ReportDTO:
        """
        Handles some `Report` (marks it as handled by current user, issues partial update)
        :param report: A report
        :param current_user: A current user
        :return: Updated `ReportDTO` object
        """

        if report.is_handled:
            raise ReportAlreadyHandled(f"Report {report.id} already handled.")

        return await self._repo.update(
            report_id=report.id,
            handled_at=datetime.now(),
            is_handled=True,
            handled_by=current_user.id,
            status=ReportStatus.COMPLETED,
        )

    async def create_report(self, data: ReportCreateDTO, session: AsyncSession = None):
        """
        统一的举报创建方法，根据类型路由到不同的处理逻辑
        :param data: 举报数据
        :param session: 数据库会话
        :return: 创建的举报对象
        """
        
        # 根据类型分发到不同的处理方法
        if data.type == "user":
            return await self._create_user_report(data)
        elif data.type == "post":
            return await self._create_post_complaint(data, session)
        elif data.type == "comment":
            return await self._create_comment_complaint(data, session)
        elif data.type == "group":
            return await self._create_group_report(data, session)
        elif data.type == "private":
            return await self._create_private_report(data, session)
        else:
            raise CantCreateReport(f"Unsupported report type: {data.type}")

    async def _create_user_report(self, data: ReportCreateDTO):
        """创建用户举报"""
        if data.target_id == data.current_user_id:
            raise CantCreateReport("Cannot report yourself")

        return await self._repo.create(
            target_id=data.target_id,
            reason=data.reason,
            creator_id=data.current_user_id,
            comment=data.comment,
            ex=data.ex,
        )

    async def _create_post_complaint(self, data: ReportCreateDTO, session: AsyncSession):
        """创建帖子投诉"""
        if session is None:
            raise CantCreateReport("Database session is required")
        
        # 验证帖子是否存在
        stmt = select(Post).where(Post.id == data.target_id)
        post = (await session.execute(stmt)).first()
        if not post:
            raise CantCreateReport("Invalid post_id")

        # 创建投诉记录
        stmt = insert(Complaint).values(
            author_id=data.current_user_id,
            post_id=data.target_id,
            reason=data.reason.value,
            status=ComplaintStatus.OPEN
        ).returning(Complaint)
        complaint = (await session.execute(stmt)).fetchone()
        
        # 更新帖子投诉数量
        stmt = text(f"""
            UPDATE posts
            SET complaint_count = complaint_count + 1
            WHERE id = '{data.target_id}'
        """)
        await session.execute(stmt)
        await session.commit()
        
        return complaint[0]

    async def _create_comment_complaint(self, data: ReportCreateDTO, session: AsyncSession):
        """创建评论投诉"""
        if session is None:
            raise CantCreateReport("Database session is required")
        
        # 验证评论是否存在
        stmt = select(Comment).where(Comment.id == data.target_id)
        comment = (await session.execute(stmt)).first()
        if not comment:
            raise CantCreateReport("Invalid comment_id")

        # 创建投诉记录
        stmt = insert(Complaint).values(
            author_id=data.current_user_id,
            comment_id=data.target_id,
            reason=data.reason.value,
            status=ComplaintStatus.OPEN
        ).returning(Complaint)
        complaint = (await session.execute(stmt)).fetchone()
        await session.commit()
        
        return complaint[0]

    async def _create_group_report(self, data: ReportCreateDTO, session: AsyncSession):
        """创建群组举报"""
        if session is None:
            raise CantCreateReport("Database session is required")
        
        # 创建IM举报记录，群组举报使用group类型
        stmt = insert(ImReport).values(
            reporter_id=data.current_user_id,
            reported_user_id=data.target_id,  # 群组ID作为目标
            conversation_id=data.target_id,   # 群组ID作为会话ID
            chat_type="group",
            message_content=data.ex.get("message_content") if data.ex else None,
            reason=data.reason.value,
            comment=data.comment
        ).returning(ImReport)
        im_report = (await session.execute(stmt)).fetchone()
        await session.commit()
        
        return im_report[0]

    async def _create_private_report(self, data: ReportCreateDTO, session: AsyncSession):
        """创建私聊举报"""
        if session is None:
            raise CantCreateReport("Database session is required")
        
        # 获取额外信息
        ex_data = data.ex or {}
        conversation_id = ex_data.get("conversation_id", data.target_id)
        message_content = ex_data.get("message_content")
        
        # 检查是否已经举报过相同内容
        stmt = select(ImReport).where(
            ImReport.reporter_id == data.current_user_id,
            ImReport.reported_user_id == data.target_id,
            ImReport.conversation_id == conversation_id,
            ImReport.message_content == message_content
        )
        existing_report = (await session.execute(stmt)).first()
        if existing_report:
            raise CantCreateReport("You have already reported this content")

        # 创建IM举报记录
        stmt = insert(ImReport).values(
            reporter_id=data.current_user_id,
            reported_user_id=data.target_id,
            conversation_id=conversation_id,
            chat_type="private",
            message_content=message_content,
            reason=data.reason.value,
            comment=data.comment
        ).returning(ImReport)
        im_report = (await session.execute(stmt)).fetchone()
        await session.commit()
        
        return im_report[0]

    async def get_report(self, report_id: str) -> ReportDTO:  # 修改参数类型为str
        """
        Gets `Report` object
        :param report_id: A report id
        :return: `ReportDTO` object
        """
        report = await self._repo.get_report_by_id_or_none(report_id)

        if report is None:
            raise ReportNotFound(report_id)

        return report

    async def fetch_reports(self, params: ReportFetchParamsDTO):
        """
        Fetches all reports
        :param params: A fetching parameters
        :return:
        """
        return await self._repo.fetch_all(
            page_size=params.page_size,
            page=params.page,
            handler_id=params.handled_by,
            target_id=params.target_id,
            report_type=params.report_type,
            creator_id=params.creator_id,
            reason=params.reason,
            from_date=params.from_date,
            to_date=params.to_date,
            comment=params.comment,
            is_handled=params.is_handled,
            status=params.status,
        )
