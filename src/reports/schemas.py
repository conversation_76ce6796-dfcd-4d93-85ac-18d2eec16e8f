from datetime import datetime
from typing import Optional, Literal, Dict, Any

from src.common.constants import ReportStatus
from src.common.schemas import BaseModel
from src.reports.enums import ReportReason


class ReportCreateSchema(BaseModel):
    """统一的举报创建Schema"""
    # 举报类型
    type: Literal["user", "post", "comment", "group", "private"]
    
    # 通用字段
    reason: ReportReason
    comment: Optional[str] = None
    
    # 统一的目标ID字段
    # user: 用户ID
    # post: 帖子ID  
    # comment: 评论ID
    # group: 群组ID
    # private: 私聊用户ID
    target_id: str
    
    # 额外信息字段，用于保存JSON格式的扩展数据
    ex: Optional[Dict[str, Any]] = None

    class Config:
        use_enum_values = True


class ReportSchema(BaseModel):
    id: str  # 修改为str类型以匹配text主键
    target_id: str  # ID of the target entity
    creator_id: str  # 举报人ID
    handler_id: str | None = None  # 处理人ID
    reason: ReportReason
    comment: str | None = None
    status: ReportStatus  # 举报状态
    ex: Dict[str, Any] | None = None  # 扩展信息
    is_handled: bool = False
    handled_at: datetime | None = None
    created_at: datetime
    updated_at: datetime
