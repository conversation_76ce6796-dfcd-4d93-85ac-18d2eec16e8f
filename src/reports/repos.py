import logging
from datetime import datetime, date

from sqlalchemy import select, update

from src.common.repos import BaseRepository
from src.reports.dto import ReportDTO
from src.reports.enums import ReportReason

from src.database.models.Report import Report


def report_to_dto(report: Report) -> ReportDTO:
    return ReportDTO(
        id=report.id,
        target_id=report.target_id,
        creator_id=report.creator_id,
        handler_id=report.handler_id,
        reason=report.reason,
        comment=report.comment,
        status=report.status,
        ex=report.ex,
        is_handled=report.is_handled,
        handled_at=report.handled_at,
        created_at=report.created_at,
        updated_at=report.updated_at,
    )


class ReportRepo(BaseRepository):
    async def create(
        self,
        target_id: str,
        creator_id: str,
        reason: ReportReason,
        comment: str | None = None,
        ex: dict | None = None,
        status: str = "pending",
    ) -> ReportDTO:
        obj = Report(
            target_id=target_id,
            creator_id=creator_id,
            reason=reason,
            comment=comment,
            ex=ex,
            status=status,
        )

        self._session.add(obj)
        await self._session.flush()
        await self._session.commit()
        await self._session.refresh(obj)

        return report_to_dto(
            report=obj,
        )

    async def fetch_all(
        self,
        page: int,
        page_size: int,
        target_id: str | None = None,
        report_type: str | None = None,
        creator_id: str | None = None,
        reason: ReportReason | None = None,
        from_date: date | None = None,
        to_date: date | None = None,
        comment: str | None = None,
        is_handled: bool | None = None,
        handler_id: str | None = None,
        status: str | None = None,
    ):
        stmt = select(Report)

        if target_id is not None:
            stmt = stmt.where(
                Report.target_id == target_id,
            )

        if report_type is not None:
            stmt = stmt.where(
                Report.ex.op("->>")("report_type") == report_type,
            )

        if handler_id is not None:
            stmt = stmt.where(
                Report.handler_id == handler_id,
            )

        if creator_id is not None:
            stmt = stmt.where(
                Report.creator_id == creator_id,
            )

        if reason is not None:
            stmt = stmt.where(
                Report.reason == reason,
            )

        if from_date is not None:
            stmt = stmt.where(
                Report.created_at >= from_date,
            )

        if to_date is not None:
            stmt = stmt.where(
                Report.created_at <= to_date,
            )

        if comment is not None:
            stmt = stmt.where(
                Report.comment.like(f"%{comment}%"),
            )

        if is_handled is not None:
            stmt = stmt.where(
                Report.is_handled == is_handled,
            )

        if status is not None:
            stmt = stmt.where(
                Report.status == status,
            )

        # Order by created_at
        stmt = stmt.order_by(
            Report.created_at.desc(),
        )

        return await self._paginate(
            stmt,
            page_size=page_size,
            page=page,
            mapper=report_to_dto,
        )

    async def get_report_by_id_or_none(self, report_id: str) -> ReportDTO | None:  # 修改参数类型为str
        results = await self._session.execute(
            select(Report).where(Report.id == report_id)
        )

        first_one = results.scalars().first()

        if first_one is not None:
            return report_to_dto(first_one)

        return None

    async def update(
        self,
        report_id: str,  # 修改参数类型为str
        comment: str | None = None,
        handled_at: datetime | None = None,
        handled_by: str | None = None,
        is_handled: bool | None = None,
        status: str | None = None,
        ex: dict | None = None,
    ) -> ReportDTO:
        """Partial update for `Report`"""
        stmt = update(Report).where(Report.id == report_id)

        if comment is not None:
            stmt = stmt.values(comment=comment)

        if handled_at is not None:
            stmt = stmt.values(handled_at=handled_at)

        if handled_by is not None:
            stmt = stmt.values(handler_id=handled_by)

        if is_handled is not None:
            stmt = stmt.values(is_handled=is_handled)

        if status is not None:
            stmt = stmt.values(status=status)

        if ex is not None:
            stmt = stmt.values(ex=ex)

        # Always update updated_at
        stmt = stmt.values(updated_at=datetime.now())

        await self._session.execute(stmt)

        report = await self.get_report_by_id_or_none(
            report_id=report_id,
        )

        await self._session.commit()

        # Return refreshed `Report`
        return report
