[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
py-modules = ["dto", "schemas", "enums", "dependencies", "main", "services", "repos"]


[project]
requires-python = ">=3.11"
name = "reports"
version = "0.1.0"
description = "Report system of to.ci"
authors = [{ name = "Ilyas Qalandarzoda", email = "<EMAIL>" }]
dependencies = [
    "httpx==0.27.2",
    "uvicorn==0.32.0",
    "fastapi==0.115.6",
    "fastapi-pagination==0.12.31",
    "fastapi-users[sqlalchemy]==14.0.0",
    "pydantic==2.9.2",
    "pydantic-settings==2.6.1",
    "sqlalchemy==2.0.35",
    "psycopg2-binary==2.9.10",
    "asyncpg==0.30.0",
]