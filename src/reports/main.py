from datetime import date

from fastapi import Depends, HTTPException, Path, Query
from starlette import status

from src.auth import current_user
from src.common.constants import ReportStatus
from src.common.dependencies import admin
from src.common.schemas import PaginationParams, Page
from src.database.models import User
from src.reports import enums
from src.reports.dependencies import get_report_service, get_report
from src.reports.dto import ReportDTO, ReportFetchParamsDTO
from src.reports.exceptions import (
    ReportNotFound,
    ReportAlreadyHandled,
)
from src.reports.schemas import ReportCreateSchema, ReportSchema
from src.reports.services import ReportService
from src.reports.task_client import reports_task_client
from src.infra.app import create_app

app = create_app(
    title="Reports",
    version="1.0",
    description="Reports API",
)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}


@app.post(
    "/",
    summary="Create unified report",
    response_model=dict,
    description="统一的举报接口，支持用户、帖子、评论、群组、私聊举报。替代旧的 `/posts/complaints` 和 `/im/report` 接口",
    status_code=status.HTTP_201_CREATED,
)
async def create_report(
    data: ReportCreateSchema,
    user: User = Depends(current_user),
):
    """
    统一的举报接口
    
    🆕 **新的统一举报接口** - 替代以下已弃用的接口：
    - `POST /posts/complaints` (帖子投诉)
    - `POST /im/report` (IM举报)
    
    支持的举报类型：
    - **user**: 用户举报 (target_id为用户ID)
    - **post**: 帖子举报 (target_id为帖子ID) - 替代 `/posts/complaints`
    - **comment**: 评论举报 (target_id为评论ID) - 替代 `/posts/complaints`
    - **group**: 群组举报 (target_id为群组ID) - 替代 `/im/report`
    - **private**: 私聊举报 (target_id为用户ID，ex中可包含额外信息) - 替代 `/im/report`
    
    📋 **使用示例**：
    
    **帖子举报**：
    ```json
    {
        "type": "post",
        "target_id": "post_123",
        "reason": "SPAM",
        "comment": "垃圾信息"
    }
    ```
    
    **私聊举报**：
    ```json
    {
        "type": "private",
        "target_id": "user_456",
        "reason": "HARASSMENT",
        "comment": "骚扰行为",
        "ex": {
            "conversation_id": "conv_789",
            "message_content": "举报的消息内容"
        }
    }
    ```
    
    **群组举报**：
    ```json
    {
        "type": "group",
        "target_id": "group_123",
        "reason": "INAPPROPRIATE_CONTENT",
        "comment": "不当内容",
        "ex": {
            "message_content": "举报的消息内容"
        }
    }
    ```
    
    所有举报都会存储在统一的 `reports` 表中，便于管理员集中处理。
    """
    
    # 准备后台任务数据
    report_data = {
        "type": data.type,
        "current_user_id": user.id,
        "reason": data.reason,
        "comment": data.comment,
        "target_id": data.target_id,
        "ex": data.ex,
    }

    try:
        # 提交后台任务
        task = reports_task_client.create_report(report_data)
        
        return {
            "success": True,
            "type": data.type,
            "message": f"{data.type.capitalize()} report is being processed",
            "status": "accepted",
            "description": f"Report has been submitted and is being processed in the background. Please save the task_id to query the processing status."
        }
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit report task, please try again later"
        )


@app.get(
    "/task/{task_id}",
    summary="Get report task status",
    description="查询举报任务的处理状态",
    response_model=dict,
)
async def get_report_task_status(
    task_id: str = Path(title="Task ID"),
):
    """
    查询举报任务状态
    """
    try:
        task_status = reports_task_client.get_task_status(task_id)
        
        state = task_status.get("status", "unknown")
        
        if state == 'PENDING':
            return {
                "task_id": task_id,
                "status": "pending",
                "message": "Task is waiting to be processed"
            }
        elif state == 'SUCCESS':
            return {
                "task_id": task_id,
                "status": "success",
                "message": "Report created successfully",
                "result": task_status.get("result")
            }
        elif state == 'FAILURE':
            return {
                "task_id": task_id,
                "status": "failure",
                "message": "Report creation failed",
                "error": task_status.get("error", "Unknown error")
            }
        else:
            return {
                "task_id": task_id,
                "status": state.lower(),
                "message": f"Task is {state.lower()}"
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}"
        )


@app.get(
    "/{report_id}",
    response_model=ReportSchema,
    summary="Get report",
    dependencies=[Depends(admin)],
)
async def get_report(
    report_id: str = Path(title="Report ID"),  # 修改参数类型为str
    report_service: ReportService = Depends(
        get_report_service,
    ),
):
    try:
        return await report_service.get_report(report_id)
    except ReportNotFound:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found",
        )


@app.post(
    "/{report_id}/handle",
    summary="Handle report",
    response_model=ReportSchema,
    responses={
        200: {"description": "Report handled."},
        409: {"description": "Report is already handled."},
    },
)
async def handle_report(
    report: ReportDTO = Depends(get_report),
    report_service: ReportService = Depends(
        get_report_service,
    ),
    user: User = Depends(admin),
):
    try:
        return await report_service.handle_report(
            report,
            user,
        )
    except ReportAlreadyHandled:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Report already handled",
        )


@app.get(
    "/",
    summary="Get reports",
    response_model=Page[ReportSchema],
    dependencies=[Depends(admin)],
    responses={
        401: {"description": "Only admins allowed."},
    },
)
async def get_reports(
    handled_by: str | None = Query(
        title="Handled By",
        description="For filtering by `Handler ID`",
        default=None,
    ),
    creator_id: str | None = Query(
        title="Creator ID", description="For filtering by `Creator ID`", default=None
    ),
    target_id: str | None = Query(
        title="Target ID", description="For filtering by `ID` of the target entity", default=None
    ),
    report_type: str | None = Query(
        title="Report type", description="For filtering by `Report type`", default=None
    ),
    from_date: date | None = Query(
        title="From date", description="From date (filtering)", default=None
    ),
    to_date: date | None = Query(title="To date", default=None),
    reason: enums.ReportReason = Query(
        title="Reason",
        description="Reason for filtering",
        default=None,
    ),
    is_handled: bool | None = Query(
        title="Is handled",
        description="Is handled?",
        default=None,
    ),
    comment: str = Query(
        title="Comment",
        description="For searching by `comment`, issues `like` expr.",
        default=None,
    ),
    status: ReportStatus = Query(
        title="Status",
        description="For filtering by report status",
        default=None,
    ),
    pagination_params: PaginationParams = Depends(),
    report_service: ReportService = Depends(get_report_service),
):
    params = ReportFetchParamsDTO(
        page=pagination_params.page,
        page_size=pagination_params.page_size,
        handled_by=handled_by,
        from_date=from_date,
        to_date=to_date,
        creator_id=creator_id,
        target_id=target_id,
        report_type=report_type,
        reason=reason,
        is_handled=is_handled,
        comment=comment,
        status=status,
    )
    reports = await report_service.fetch_reports(
        params,
    )

    return reports
