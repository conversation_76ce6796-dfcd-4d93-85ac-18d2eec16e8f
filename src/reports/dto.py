from dataclasses import dataclass
from datetime import date, datetime
from typing import Optional, Literal, Dict, Any

from src.reports.enums import ReportReason
from src.common.constants import ReportStatus


@dataclass
class ReportCreateDTO:
    """统一的举报创建DTO"""
    # 举报类型
    type: Literal["user", "post", "comment", "group", "private"]
    
    # 通用字段
    current_user_id: str
    reason: ReportReason
    
    # 统一的目标ID字段
    target_id: str
    
    # 可选字段（有默认值的字段必须在后面）
    comment: Optional[str] = None
    ex: Optional[Dict[str, Any]] = None


@dataclass
class ReportDTO:
    id: str  # 修改为str类型以匹配text主键
    target_id: str  # ID of the target entity
    creator_id: str  # 举报人ID
    reason: ReportReason
    created_at: datetime
    updated_at: datetime
    
    # 可选字段（有默认值的字段必须在后面）
    handler_id: str | None = None  # 处理人ID
    comment: str | None = None
    status: ReportStatus = ReportStatus.PENDING  # 举报状态
    ex: Dict[str, Any] | None = None  # 扩展信息
    is_handled: bool = False
    handled_at: datetime | None = None


@dataclass
class ReportFetchParamsDTO:
    page: int
    page_size: int
    target_id: str | None = None
    report_type: str | None = None
    creator_id: str | None = None
    handled_by: str | None = None
    from_date: date | None = None
    to_date: date | None = None
    reason: ReportReason | None = None
    is_handled: bool | None = None
    comment: str | None = None
    status: ReportStatus | None = None  # 添加状态过滤
