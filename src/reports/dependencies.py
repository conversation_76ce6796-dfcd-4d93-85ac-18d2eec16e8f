from fastapi import Depends, HTTPException, Path
from sqlalchemy.ext.asyncio import AsyncSession
from starlette import status

from src.database.session import get_session
from src.reports.repos import ReportRepo
from src.reports.services import ReportService
from src.reports.exceptions import ReportNotFound


def get_reports_repo(session: AsyncSession = Depends(get_session)):
    return ReportRepo(
        session,
    )


def get_report_service(
    repo: ReportRepo = Depends(get_reports_repo),
) -> ReportService:
    return ReportService(
        repo,
    )


async def get_report(
    report_id: str = Path(title="Report ID"),
    report_service: ReportService = Depends(get_report_service),
):
    """获取报告的依赖项"""
    try:
        return await report_service.get_report(report_id)
    except ReportNotFound:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found",
        )
