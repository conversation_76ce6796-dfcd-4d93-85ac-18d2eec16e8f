import time
from typing import Optional, Callable, Awaitable

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response


class RequestTimingMiddleware(BaseHTTPMiddleware):
	"""记录请求处理耗时的中间件。

	依赖外部传入的 logger（例如各服务自己的 logger），避免在中间件内创建全局日志器。
	"""

	def __init__(self, app, *, logger):
		super().__init__(app)
		self._logger = logger

	async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
		start = time.perf_counter()
		status_code: int = 500
		try:
			response = await call_next(request)
			status_code = response.status_code
			return response
		finally:
			elapsed_ms = (time.perf_counter() - start) * 1000.0
			self._logger.info(
				"request %s %s -> %s in %.1f ms",
				request.method,
				request.url.path,
				status_code,
				elapsed_ms,
			)


__all__ = [
	"RequestTimingMiddleware",
]


