from contextlib import asynccontextmanager
from typing import Optional, Callable, Awaitable
import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from sqlalchemy import text

from src.database.session import init_engines, dispose_engines, get_session_context
from src.infra.middlewares import RequestTimingMiddleware

__all__ = [
	"build_lifespan",
	"create_app",
	"install_custom_openapi",
]


def build_lifespan(*, db_echo: bool = False):
	@asynccontextmanager
	async def lifespan(app: FastAPI):
		# 仅在应用生命周期中初始化数据库引擎
		init_engines(echo=db_echo)
		try:
			# 早期健康检查，尽快暴露 DSN/鉴权问题
			async with get_session_context("read") as s:
				await s.execute(text("SELECT 1"))
			async with get_session_context("write") as s:
				await s.execute(text("SELECT 1"))
			yield
		finally:
			await dispose_engines()

	return lifespan



def create_app(
	*,
	title: str,
	version: str,
	description: Optional[str] = None,
	db_echo: bool = False,
	cors_allow_origin_regex: str = ".*",
    request_logger=None,
    root_path: Optional[str] = None,
    startup_hook: Optional[Callable[[FastAPI], Awaitable[None]]] = None,
    shutdown_hook: Optional[Callable[[FastAPI], Awaitable[None]]] = None,
) -> FastAPI:
	base_lifespan = build_lifespan(db_echo=db_echo)

	log = request_logger if request_logger is not None else logging.getLogger("infra.lifespan")

	@asynccontextmanager
	async def composed_lifespan(app: FastAPI):
		async with base_lifespan(app):
			try:
				if startup_hook is not None:
					await startup_hook(app)
			except Exception:
				try:
					log.exception("startup_hook failed during application startup")
				except Exception:
					pass
				raise
			try:
				yield
			except Exception:
				try:
					log.exception("Unhandled exception during application lifespan")
				except Exception:
					pass
				raise
			finally:
				if shutdown_hook is not None:
					try:
						await shutdown_hook(app)
					except Exception:
						try:
							log.exception("shutdown_hook failed during application shutdown")
						except Exception:
							pass

	app = FastAPI(
		title=title,
		version=version,
		description=description or "",
		lifespan=composed_lifespan,
		root_path=root_path or "",
	)

	app.add_middleware(
		CORSMiddleware,
		allow_origin_regex=cors_allow_origin_regex,
		allow_credentials=True,
		allow_methods=["*"],
		allow_headers=["*"],
	)

	if request_logger is not None:
		app.add_middleware(RequestTimingMiddleware, logger=request_logger)

	# 默认直接安装自定义 OpenAPI（含 BearerAuth 安全方案）
	install_custom_openapi(app, title=title, version=version, description=description or "")

	return app


def install_custom_openapi(app: FastAPI, *, title: str, version: str, description: str = "") -> None:
	def custom_openapi():
		if app.openapi_schema:
			return app.openapi_schema
		openapi_schema = get_openapi(
			title=title,
			version=version,
			description=description,
			routes=app.routes,
		)
		openapi_schema["components"]["securitySchemes"] = {
			"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
		}
		openapi_schema["security"] = [{"BearerAuth": []}]
		app.openapi_schema = openapi_schema
		return app.openapi_schema

	app.openapi = custom_openapi


