import logging
from typing import Optional

__all__ = ["get_logger"]


def get_logger(name: str, level: str = "INFO", file_path: Optional[str] = None) -> logging.Logger:
	"""Create or get a configured logger.

	- name: logger name
	- level: logging level name (e.g., "INF<PERSON>", "DEBUG")
	- file_path: optional file path; if provided, add a FileHandler

	Idempotent: repeated calls with the same name won't duplicate handlers.
	"""
	logger = logging.getLogger(name)
	logger.setLevel(level.upper())

	# Avoid duplicate handlers on re-import/hot-reload
	if not any(isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler) for h in logger.handlers):
		stream_handler = logging.StreamHandler()
		stream_handler.setFormatter(logging.Formatter("[%(asctime)s][%(name)s][%(levelname)s]: %(message)s"))
		logger.addHandler(stream_handler)

	if file_path and not any(isinstance(h, logging.FileHandler) and getattr(h, "baseFilename", None) == file_path for h in logger.handlers):
		file_handler = logging.FileHandler(file_path)
		file_handler.setFormatter(logging.Formatter("[%(asctime)s][%(name)s][%(levelname)s]: %(message)s"))
		logger.addHandler(file_handler)

	return logger


