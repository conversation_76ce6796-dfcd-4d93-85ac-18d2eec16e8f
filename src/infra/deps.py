from typing import Optional, AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import session_dep as _session_dep
from src.database.session import tx_session_dep as _tx_session_dep

__all__ = [
	"session_dep",
	"tx_session_dep",
]


def session_dep(role: str):
	return _session_dep(role)  # 返回 FastAPI 依赖工厂


def tx_session_dep(role: str, timeout_ms: Optional[int] = None):
	return _tx_session_dep(role, timeout_ms)


