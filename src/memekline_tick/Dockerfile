FROM python:3.11.10-slim-bookworm as streaming_builder

RUN apt update && \
     apt install -y vim python3-distutils git

RUN pip install --upgrade setuptools

RUN git clone https://github.com/bytewax/bytewax-rolling.git

RUN pip install uv

WORKDIR bytewax-rolling

RUN uv pip compile --generate-hashes -p 3.11 --all-extras pyproject.toml -o requirements/lib-py3.11.txt

RUN uv pip compile --generate-hashes -p 3.11 requirements/build.in requirements/lib-py3.11.txt -o requirements/build-py3.11.txt

RUN uv pip install -e . --system

FROM streaming_builder

USER root

RUN apt-get update && \
    apt clean && \
    rm -rf /var/cache/apt/*

RUN apt-get install -y procps

COPY src/database /src/database
COPY src/common /src/common
COPY src/auth /src/auth
COPY src/memekafka /src/memekafka
COPY src/memecoin /src/memecoin
COPY src/memekline /src/memekline
COPY src/memekline_tick /src/memekline_tick

ENV PATH "$PATH:/src/memekline_tick/scripts"
ENV PYTHONPATH "$PYTHONPATH:/"

WORKDIR /src

RUN pip install -r /src/common/requirements.txt
RUN pip install -r /src/auth/requirements.txt
RUN pip install -r /src/memekafka/requirements.txt
RUN pip install -r /src/memecoin/requirements.txt
RUN pip install -r /src/memekline/requirements.txt
RUN chmod +x /src/memekline_tick/scripts/healthcheck.sh
