from __future__ import annotations

import threading, time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

from psycopg2 import pool
from psycopg2.extras import execute_values
from typing_extensions import override

from bytewax.outputs import FixedPartitionedSink, StatefulSinkPartition

from src.memekline_tick.logger import logger
from src.memekline.settings import settings
from src.memekline.constants import PriceTick


# ════════════════════  Buffer  ════════════════════
class _PriceBuffer:
    """线程安全缓冲区；容量 / 时间 双阈值触发 flush"""

    def __init__(
        self,
        flush_cb,
        max_size=settings.PRICE_BUFFER_SIZE,
        flush_interval=settings.PRICE_BUFFER_FLUSH_INTERVAL,
    ):
        self._flush_cb = flush_cb
        self._max = max_size
        self._intv = flush_interval

        self._buf: list[Tuple[datetime, str, float]] = []
        self._lock = threading.RLock()

        self._stop = threading.Event()
        if flush_interval > 0:
            th = threading.Thread(target=self._loop, daemon=True)
            th.start()
            self._th = th

    # -- public -------------------------------------------------
    def add(self, token: str, tick: PriceTick):
        ts = (
            datetime.fromtimestamp(tick.timestamp, timezone.utc)
            if isinstance(tick.timestamp, int)
            else tick.timestamp
        )

        with self._lock:
            self._buf.append((ts, token, tick.price))
            need = len(self._buf) >= self._max

        if need:
            self.flush()

    def flush(self):
        with self._lock:
            if not self._buf:
                return
            out, self._buf = self._buf, []
        self._flush_cb(out)

    def shutdown(self):
        self._stop.set()
        if hasattr(self, "_th") and self._th.is_alive():
            self._th.join(timeout=2)
        self.flush()

    def _loop(self):
        try:
            while not self._stop.is_set():
                time.sleep(self._intv)
                self.flush()
        except Exception as e:
            logger.exception("buffer auto-flush error: %s", e)


# ════════════════════  Partition  ════════════════════
class _DBPricePart(StatefulSinkPartition[PriceTick, None]):
    """每个 worker 一个分区；可恢复（此处返回 None）"""

    def __init__(self):
        self._pool = pool.SimpleConnectionPool(
            settings.DB_POOL_MIN_CONN,
            settings.DB_POOL_MAX_CONN,
            settings.TIMESCALE_DSN,
        )
        self._table = "token_price_ticks"
        self._buf = _PriceBuffer(self._write)
        logger.info("DB sink partition ready")

    def write_batch(self, items: List[PriceTick]) -> None:
        for tick in items:
            self._buf.add(tick.token_address, tick)

    def snapshot(self) -> None:
        # 不需要恢复位点，直接返回 None
        return None

    def close(self):
        logger.info("DB sink closing")
        self._buf.shutdown()
        self._pool.closeall()

    def _write(self, rows: List[Tuple]):
        if not rows:
            return
        conn = None
        try:
            conn = self._pool.getconn()
            with conn.cursor() as cur:
                sql = f"""
                    INSERT INTO {self._table} (timestamp, token_address, price)
                    VALUES %s
                    ON CONFLICT (timestamp, token_address) DO NOTHING
                """
                execute_values(cur, sql, rows)
            conn.commit()
            logger.debug("wrote %d rows", len(rows))
        except Exception as e:
            if conn:
                conn.rollback()
            logger.exception("DB write failed: %s", e)
        finally:
            if conn:
                self._pool.putconn(conn)


# ════════════════════  Sink (factory)  ════════════════════
class DatabaseSinkPriceTick(FixedPartitionedSink[PriceTick, None]):
    """一个固定分区（'db'），持续写入 TimescaleDB"""

    def list_parts(self) -> List[str]:
        return ["db"]

    @override
    def build_part(
        self,
        step_id: str,
        for_part: str,
        resume_state: Optional[None],
    ) -> _DBPricePart:
        logger.info("build sink part %s for step %s", for_part, step_id)
        return _DBPricePart()
