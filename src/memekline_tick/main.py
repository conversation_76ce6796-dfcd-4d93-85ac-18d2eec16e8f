from bytewax._bytewax import run_main
from bytewax.dataflow import Dataflow
import bytewax.operators as op

from src.memekline_tick.price_sink import DatabaseSinkPriceTick
from src.memekline_tick.redis_source import RedisPriceSource

flow = Dataflow("kline_tick_processing")

redis_source = RedisPriceSource(
    interval=1,
)

redis_data = op.input("redis_input", flow, redis_source)

op.output("sink_redis_price_ticks", redis_data, DatabaseSinkPriceTick())


if __name__ == "__main__":
    run_main(flow)
