import asyncio, time
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Tuple, Optional

from bytewax.inputs import StatefulSourcePartition, FixedPartitionedSource
from redis.asyncio import Redis

from src.common.constants import MEMECOIN_REDIS_ENDPOINT
from src.memekline_tick.logger import logger
from src.memekline.constants import PriceTick
from src.common.redis_cli import RedisCli

@dataclass(frozen=True)
class _Clock:
    next_awake: datetime       # 必须 timezone.utc


# ───────────────── Partition ─────────────────
class RedisPricePart(StatefulSourcePartition[_Clock, Tuple[str, PriceTick]]):
    def __init__(self, part_id: str, interval: float = 5.0, state: Optional[_Clock] = None):
        self.part_id   = part_id
        self.interval  = interval
        self._clock    = state or _Clock(datetime.now(timezone.utc) + timedelta(seconds=interval))
        self._redis_cfg = dict(connection_pool=RedisCli.async_().connection_pool)

    # ---------- pull from Redis ----------
    async def _pull(self) -> Dict[str, float]:
        redis = Redis(**self._redis_cfg)
        try:
            tokens = list(await redis.smembers(f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"))
            if not tokens:
                return {}

            pipe = redis.pipeline()
            for t in tokens:
                pipe.hget(f"{MEMECOIN_REDIS_ENDPOINT}:{t}", "price")
            raw = await pipe.execute()

            out: Dict[str, float] = {}
            for t, p in zip(tokens, raw):
                if p is None:
                    continue
                try:
                    out[t] = float(p)
                except ValueError:
                    logger.warning("bad price %s for %s", p, t)
            return out
        finally:
            await redis.close()

    def next_batch(self) -> List[Tuple[str, PriceTick]]:
        start = time.time()
        try:
            price_map = asyncio.run(self._pull())
        except Exception as e:
            logger.exception("Redis pull failed: %s", e)
            price_map = {}

        ts = int(time.time())
        batch = [(tok, PriceTick(ts, tok, prc, 0)) for tok, prc in price_map.items()]

        logger.info("emit %d ticks in %.2fs", len(batch), time.time() - start)

        self._clock = _Clock(datetime.now(timezone.utc) + timedelta(seconds=self.interval))
        return batch

    def next_awake(self) -> datetime:
        logger.debug("next_awake -> %s", self._clock.next_awake.isoformat())
        return self._clock.next_awake

    def snapshot(self) -> _Clock:
        return self._clock

    def close(self):
        pass


# ───────────────── Source ─────────────────
class RedisPriceSource(FixedPartitionedSource[str, Tuple[str, PriceTick]]):
    def __init__(self, interval: float = 5.0):
        self.interval = interval

    def list_parts(self) -> List[str]:
        return ["redis_query"]

    def build_part(
        self,
        step_id: str,
        for_part: str,
        resume_state: Optional[_Clock] = None,
    ) -> RedisPricePart:
        logger.info("build partition %s for step %s", for_part, step_id)
        return RedisPricePart(for_part, self.interval, resume_state)
