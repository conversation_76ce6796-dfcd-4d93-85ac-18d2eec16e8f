from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.models.Feedback import Feedback
from src.feedback.schemas import FeedbackCreate, FeedbackUpdate
from src.feedback.logger import logger


class FeedbackRepo:
    """Feedback data repository"""
    
    def __init__(self, session: AsyncSession):
        self._session = session
    
    async def create_feedback(
        self, 
        feedback_data: FeedbackCreate, 
        user_id: Optional[str] = None
    ) -> Feedback:
        """Create new user feedback"""
        feedback = Feedback(
            name=feedback_data.name,
            email=str(feedback_data.email),
            description=feedback_data.description,
            category=feedback_data.category,
            sub_category=feedback_data.sub_category,
            user_id=user_id,
            status="Pending"
        )

        if feedback_data.images:
            feedback.set_images(feedback_data.images)
        
        self._session.add(feedback)
        await self._session.commit()
        await self._session.refresh(feedback)
        return feedback
    
    async def get_feedback(self, feedback_id: str) -> Optional[Feedback]:
        """Get specific feedback"""
        result = await self._session.execute(
            select(Feedback).where(Feedback.id == feedback_id)
        )
        return result.scalars().first()
    
    async def get_all_feedback(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Feedback]:
        """Get all feedback, can filter by status"""
        query = select(Feedback)
        
        if status:
            query = query.where(Feedback.status == status)
            
        query = query.offset(skip).limit(limit)
        result = await self._session.execute(query)
        return result.scalars().all()
    
    async def get_user_feedback(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Feedback]:
        """Get all feedback from a specific user"""
        result = await self._session.execute(
            select(Feedback)
            .where(Feedback.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def update_feedback(
        self,
        feedback_id: str,
        feedback_data: FeedbackUpdate
    ) -> Optional[Feedback]:
        """Update feedback status and admin response"""
        update_data = feedback_data.dict(exclude_unset=True)
        
        if not update_data:
            return None
            
        await self._session.execute(
            update(Feedback)
            .where(Feedback.id == feedback_id)
            .values(**update_data)
        )
        
        await self._session.commit()
        
        # Get updated feedback
        return await self.get_feedback(feedback_id)
    
    async def delete_feedback(self, feedback_id: str) -> bool:
        """Delete specific feedback"""
        feedback = await self.get_feedback(feedback_id)
        
        if not feedback:
            return False
            
        await self._session.delete(feedback)
        await self._session.commit()
        return True 