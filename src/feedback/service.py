from typing import List, Optional
import base64

from src.database.models.Feedback import Feedback
from src.feedback.schemas import FeedbackC<PERSON>, FeedbackUpdate
from src.feedback.repos import FeedbackRepo
from src.feedback.logger import logger


class FeedbackService:
    """Service class for handling user feedback"""
    
    def __init__(self, repo: FeedbackRepo):
        self._repo = repo
    
    async def create_feedback(
        self, 
        feedback_data: FeedbackCreate, 
        user_id: Optional[str] = None
    ) -> Feedback:
        """Create new user feedback"""
        return await self._repo.create_feedback(feedback_data, user_id)
    
    async def get_feedback(
        self, 
        feedback_id: str
    ) -> Optional[Feedback]:
        """Get specific feedback"""
        return await self._repo.get_feedback(feedback_id)
    
    async def get_all_feedback(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Feedback]:
        """Get all feedback, can filter by status"""
        feedbacks = await self._repo.get_all_feedback(skip, limit, status)
        return feedbacks
    
    async def get_user_feedback(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Feedback]:
        """Get all feedback from a specific user"""
        feedbacks = await self._repo.get_user_feedback(user_id, skip, limit)
        return feedbacks
    
    async def update_feedback(
        self,
        feedback_id: str,
        feedback_data: FeedbackUpdate
    ) -> Optional[Feedback]:
        """Update feedback status and admin response"""
        logger.info(f"Updating feedback: {feedback_id}, data: {feedback_data}")
        return await self._repo.update_feedback(feedback_id, feedback_data)
    
    async def delete_feedback(
        self,
        feedback_id: str
    ) -> bool:
        """Delete specific feedback"""
        logger.info(f"Deleting feedback: {feedback_id}")
        return await self._repo.delete_feedback(feedback_id) 