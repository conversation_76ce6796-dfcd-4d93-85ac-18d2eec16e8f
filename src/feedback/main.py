from typing import List, Optional

from fastapi import Depends, Query, HTTPException, status
from fastapi_pagination import Page, Params, add_pagination

from src.database.models.User import User
from src.auth import optional_user
from src.feedback.logger import logger
from src.feedback.schemas import <PERSON><PERSON><PERSON><PERSON><PERSON>, FeedbackRead
from src.feedback.service import FeedbackService
from src.feedback.dependencies import (
    get_feedback_service,
)
from src.infra.app import create_app


app = create_app(title="Feedback Service", version="1.0.0", description="Feedback API", request_logger=logger)
add_pagination(app)


@app.get("/health")
async def health():
    return {"status": "ok"}

@app.post(
    "/feedback",
    response_model=FeedbackRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Feedback"
)
async def create_feedback(
    feedback_data: FeedbackCreate,
    service: FeedbackService = Depends(get_feedback_service),
    user: Optional[User] = Depends(optional_user)
):
    """
    Create a new user feedback
    
    - **name**: User name
    - **email**: User email
    - **description**: Problem description
    - **category**: Feedback category, such as 'bug', 'suggestion', 'other'
    - **sub_category**: Feedback sub-category for more detailed classification (optional)
    - **images**: List of image URLs (optional)
    """
    user_id = user.id if user else None
    logger.debug(f"Received feedback request: {feedback_data.name}, {feedback_data.email}, {feedback_data.category}")
    feedback = await service.create_feedback(feedback_data, user_id)
    return feedback


 
