from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr, HttpUrl


class FeedbackImage(BaseModel):
    image: bytes = Field(..., description="Image data")
    filename: str = Field(..., description="Image filename")
    content_type: str = Field(..., description="Image MIME type")


class FeedbackBase(BaseModel):
    name: str = Field(..., description="User name")
    email: EmailStr = Field(..., description="User email")
    description: str = Field(..., description="Problem description")
    category: str = Field(..., description="Feedback category, such as 'bug', 'suggestion', 'other'")
    sub_category: Optional[str] = Field(None, description="Feedback sub-category for more detailed classification")


class FeedbackCreate(FeedbackBase):
    images: Optional[List[str]] = Field(default=[], description="List of image URLs related to the feedback")


class FeedbackRead(FeedbackBase):
    id: str
    user_id: Optional[str] = None
    created_at: datetime
    status: str = Field(..., description="Feedback status, such as 'Pending', 'In Progress', 'Resolved'")
    admin_response: Optional[str] = None
    images: Optional[List[str]] = Field(default=[], description="List of image URLs related to the feedback")
    
    class Config:
        from_attributes = True


class FeedbackUpdate(BaseModel):
    status: Optional[str] = None
    admin_response: Optional[str] = None 