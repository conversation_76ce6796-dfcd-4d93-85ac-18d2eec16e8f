from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from src.feedback.service import FeedbackService
from src.feedback.repos import FeedbackRepo


async def get_feedback_repo(
    session: AsyncSession = Depends(get_session),
) -> FeedbackRepo:
    """Build and return FeedbackRepo instance"""
    return FeedbackRepo(session)


async def get_feedback_service(
    repo: FeedbackRepo = Depends(get_feedback_repo),
) -> FeedbackService:
    """Build and return FeedbackService instance"""
    return FeedbackService(repo)
