from typing import AsyncGenerator

from httpx import AsyncClient
from fastapi import HTT<PERSON>Ex<PERSON>, status

from .settings import settings
from src.suggester.logging import logger


class SuggesterClient:
    def __init__(self):
        self.client = AsyncClient(
            base_url=settings.SUGGESTER_URL
        )

    async def sanitized_stream(self, url: str, data: dict) -> AsyncGenerator:
        async with self.client.stream(method="POST", url=url, json=data, timeout=300000) as response:
            tmp = ''
            async for chunk in response.aiter_text():
                chunk = chunk.replace("\n", "\\n")
                tmp += chunk
                logger.debug(chunk)
                yield chunk
            logger.debug(tmp)

    async def stream(self, url: str, data: dict) -> AsyncGenerator:
        async with self.client.stream(method="POST", url=url, json=data, timeout=300000) as response:
            tmp = ''
            async for chunk in response.aiter_text():
                tmp += chunk
                logger.debug(chunk)
                yield chunk
            logger.debug(tmp)

    async def post(self, url: str, data: dict | None = None) -> list | dict | None:
        try:
            response = await self.client.post(url=url, json=data)
            if response.status_code >= 300:
                logger.error(f"Request to suggester failed with status code {response.status_code}\n"
                             f"Response details:\n{response}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Suggester request failed"
                )
            return response.json()
        except Exception as e:
            logger.error("Suggester request failed")
            logger.error(e)
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Suggester request failed")
