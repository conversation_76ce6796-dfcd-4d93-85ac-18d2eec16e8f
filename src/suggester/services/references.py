from typing import List
from typing import AsyncGenerator
from urllib.parse import urlparse

from fastapi import HTTPException, status

from src.common.constants import Environment
from src.suggester.settings import settings
from src.suggester.schemas import ArticleGenerationParams, Length, ContinuationParams
from src.database.models.Article import Article
from src.database.session import AsyncSession
from src.suggester.types import ParsedLinks
from src.suggester.client import SuggesterClient


length_conversion: dict[Length, int] = {
    Length.SHORT: 2000,
    Length.MEDIUM: 4000,
    Length.LONG: 6000
}

domains: dict[Environment, list[str]] = {
    Environment.LOCAL: ['localhost', '127.0.0.1'],
    Environment.PRODUCTION: ['toci.cn', 'to.ci'],
    Environment.TESTING: ['dev.to.ci'],
    Environment.STAGING: ['staging.to.ci']
}


class SuggestorService:
    def __init__(self):
        self.client = SuggesterClient()

    @staticmethod
    def transform_params(params: ArticleGenerationParams) -> dict:
        data = params.dict()
        data['token_limit'] = length_conversion[data['length']]
        del data['length']
        data['input'] = data['prompt']
        del data['prompt']
        return data

    async def generate_with_reference(
            self,
            params: ArticleGenerationParams
    ) -> AsyncGenerator:
        data = self.transform_params(params=params)
        return self.client.sanitized_stream(url='article/generate', data=data)

    async def get_article_continuation(self, params: ContinuationParams) -> AsyncGenerator:
        return self.client.stream(url="article/continue", data=params.dict())

    async def simple_stream_request(self, url: str, data: dict):
        return self.client.stream(url=url, data=data)

    async def simple_request(self, url: str, data: dict):
        return await self.client.post(url=url, data=data)


def get_suggestor_service():
    yield SuggestorService()
