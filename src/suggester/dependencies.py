from fastapi import HTTPException, status
from .schemas import ArticleGenerationParams, Contents, Text


def valid_reference_params(
    params: ArticleGenerationParams,
):
    if params.prompt == '':
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail='Prompt cannot be empty')
    if not params.keywords:
        params.keywords = None
    if params.tone == '':
        params.tone = None
    if params.audience == '':
        params.audience = None
    if params.goal == '':
        params.goal = None
    return params


def text_from_contents(contents: Contents) -> Text:
    return Text(text=contents.contents)
