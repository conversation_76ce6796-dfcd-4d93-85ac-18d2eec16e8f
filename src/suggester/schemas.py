from pydantic import BaseModel, ConfigDict
from typing import List, Optional, Literal
from strenum import LowercaseStrEnum
from enum import auto


class Length(LowercaseStrEnum):
    SHORT = auto()
    MEDIUM = auto()
    LONG = auto()


class ArticleGenerationParams(BaseModel):
    model_config = ConfigDict(extra='ignore')

    prompt: str
    keywords: Optional[List[str]] = None
    tone: Optional[str] = None
    audience: Optional[str] = None
    length: Optional[Length] = None
    goal: Optional[str] = None


class ContinuationParams(BaseModel):
    toc: str
    text: str


class Message(BaseModel):
    role: Literal["user", "assistant", "system"]
    content: str


class Messages(BaseModel):
    messages: list[Message]


class Text(BaseModel):
    text: str


class Contents(BaseModel):
    contents: str
