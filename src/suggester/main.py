from fastapi import FastAP<PERSON>, status, Depends
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from src.auth import current_user
from .schemas import ArticleGenerationParams, ContinuationParams, Messages, Text
from .dependencies import valid_reference_params, text_from_contents
from src.suggester.services.references import SuggestorService, get_suggestor_service
from src.common.dependencies import RateLimiter

from datetime import timedelta
from src.infra.logger import get_logger
from src.infra.app import create_app

logger = get_logger("suggester", level="INFO", file_path="latest.log")
app = create_app(title="Suggester", version="1.0", description="Suggestions API", request_logger=logger, root_path='/suggestions')

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.post(
    "/authorize",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(current_user),
        Depends(RateLimiter(
            amount=10,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/article/generate'
        ))
    ]
)
async def authorize():
    return {}


@app.post(
    "/reference",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(current_user),
        Depends(RateLimiter(
            amount=10,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/article/generate'
        )),
    ]
)
async def generate_with_reference(
        params: ArticleGenerationParams = Depends(valid_reference_params),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.generate_with_reference(params=params)
    return EventSourceResponse(response)


@app.post(
    "/article/generate",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(current_user),
        Depends(RateLimiter(
            amount=10,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/article/generate'
        )),
    ]
)
async def generate_article(
        params: ArticleGenerationParams = Depends(valid_reference_params),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.generate_with_reference(params=params)
    return StreamingResponse(response, media_type='text/event-stream')


@app.post(
    "/reference/continue",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(current_user),
        Depends(RateLimiter(
            amount=10,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/reference'
        )),
    ]
)
async def continue_article(
        params: ContinuationParams,
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.get_article_continuation(params=params)
    return EventSourceResponse(response)


@app.post(
    "/completion",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/completion'
        )),
    ]
)
async def get_completion(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='completion', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/chat",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/chat'
        )),
    ]
)
async def get_gpt_chat(
        messages: Messages,
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='chat', data=messages.dict())
    return EventSourceResponse(response)


@app.post(
    "/title",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/title'
        )),
    ]
)
async def get_title(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='title', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/summary",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/summary'
        )),
    ]
)
async def get_summary(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='summary', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/polish",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/polish'
        )),
    ]
)
async def polish(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='polish', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/simplify",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/simplify'
        )),
    ]
)
async def simplify(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='simplify', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/fix",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/fix'
        )),
    ]
)
async def fix(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='fix', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/formal_tone",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/formal_tone'
        )),
    ]
)
async def get_formal_tone(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='formal_tone', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/exciting_tone",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/exciting_tone'
        )),
    ]
)
async def get_exciting_tone(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='exciting_tone', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/friendly_tone",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/friendly_tone'
        )),
    ]
)
async def get_friendly_tone(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_stream_request(url='friendly_tone', data=text.dict())
    return EventSourceResponse(response)


@app.post(
    "/ner",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/ner'
        )),
    ]
)
async def get_ner(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_request(url='ner', data=text.dict())
    return response


@app.post(
    "/topics",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(RateLimiter(
            amount=15,
            time_range=timedelta(minutes=1),
            endpoint='POST:/suggestions/topics'
        )),
    ]
)
async def get_topics(
        text: Text = Depends(text_from_contents),
        service: SuggestorService = Depends(get_suggestor_service)
):
    response = await service.simple_request(url='topics', data=text.dict())
    return response
