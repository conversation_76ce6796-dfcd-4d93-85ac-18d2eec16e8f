import asyncio
from typing import <PERSON>ple, Optional
import time
import random
import asyncpg
import json
from decimal import Decimal

from fastapi import FastAPI

from src.common.redis_cli import RedisCli
from src.infra.logger import get_logger
from fastapi.responses import PlainTextResponse
import uvicorn

from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from solders.signature import Signature
from solders.transaction_status import TransactionConfirmationStatus
from sqlalchemy import text, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from solana.rpc.async_api import AsyncClient
from solana.exceptions import SolanaRpcException
from web3 import AsyncWeb3, AsyncHTTPProvider
from redis.asyncio import Redis
from web3.exceptions import TransactionNotFound

from src.common.constants import UserTransactionStatus, UserTransactionType
from src.database.models.UserTransaction import UserTransaction
from src.database.session import get_session_context, init_engines
from src.memecoin.schemas import Token<PERSON>ache
from src.memetracker.logger import logger
from src.memetracker.settings import settings
from src.common.constants import MEMECOIN_REDIS_ENDPOINT, PairStatus
from src.memecoin.utils import get_initial_token_price

# Redis client instance
_redis_client = None
# PostgreSQL 监听连接
_pg_conn = None

LAUNCH_TOPIC0 = ("39a8d2592053becd46f6b24ac2b68a5261b4fb9726542cc377cddfa6e1dbc987").lower()

async def get_redis_client():
    """Get Redis client instance"""
    global _redis_client
    if _redis_client is None:
        _redis_client = RedisCli.async_()
    return _redis_client


async def add_token_to_active_queue(token_address: str):
    """Add token address to the active token queue in Redis"""
    if not token_address or token_address == "native_token":
        return

    redis_client = await get_redis_client()
    queue_key = "memetracker:active_tokens"

    try:
        # Check if the token already exists in the queue
        exists = await redis_client.lpos(queue_key, token_address)
        if exists is None:  # Add only if not exists
            # Push to the right side of the queue
            await redis_client.rpush(queue_key, token_address)
            # Keep only the latest 100 tokens
            await redis_client.ltrim(queue_key, -100, -1)
            logger.debug(f"Added token {token_address} to active tokens")
        else:
            logger.debug(f"Token {token_address} already in active tokens")
    except Exception as e:
        logger.error(f"Failed to add token to active tokens: {e}")


async def invalidate_rankings_cache(token_symbol: str = ""):
    """
    清除排行榜全局缓存，确保新币立即出现在排行榜中
    
    Args:
        token_symbol: Token符号，用于日志记录
    """
    try:
        redis_client = await get_redis_client()
        
        # 删除所有排行榜类型的全局缓存
        ranking_types = ['daily', 'weekly', 'buyback']
        pipeline = redis_client.pipeline()
        
        for ranking_type in ranking_types:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type}"
            pipeline.delete(cache_key)
        
        # 执行批量删除
        deleted_keys = await pipeline.execute()
        deleted_count = sum(deleted_keys)
        
        if deleted_count > 0:
            logger.info(f"Successfully invalidated {deleted_count} rankings cache keys for new token: {token_symbol}")
        else:
            logger.debug(f"No rankings cache keys found to invalidate for token: {token_symbol}")
        
        return deleted_count
        
    except Exception as e:
        logger.error(f"Failed to invalidate rankings cache for token {token_symbol}: {str(e)}")
        return 0


async def update_token_set_from_pairs():
    """
    Query all token addresses from the pairs table and incrementally update them to Redis set
    """
    logger.info("Starting token set update from pairs table...")
    start_time = time.time()
    
    redis_client = await get_redis_client()
    set_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
    
    try:
        # Get all token addresses and best pair info from database
        async with get_session_context("read") as session:
            # 1) 获取所有 base（token 地址）
            base_query = text(
                """
                SELECT DISTINCT base FROM pair
                WHERE base IS NOT NULL AND base != 'native_token'
                """
            )
            base_result = await session.execute(base_query)
            token_addresses = [row[0] for row in base_result.fetchall()]

            logger.debug(f"Found {len(token_addresses)} unique token addresses from pairs table")

            # 2) 为每个 base 选一条最优 pair，优先 READY，其次按流动性与时间
            best_pair_query = text(
                """
                WITH ranked AS (
                    SELECT
                        base,
                        base_name,
                        base_symbol,
                        base_image_url,
                        collection_id,
                        status,
                        dex,
                        address,
                        liq,
                        created_at,
                        open_at,
                        id,
                        ROW_NUMBER() OVER (
                            PARTITION BY base
                            ORDER BY
                                CASE WHEN status = 1 THEN 0 WHEN status = 0 THEN 1 WHEN status = 2 THEN 2 ELSE 3 END ASC,
                                liq DESC NULLS LAST,
                                COALESCE(open_at, created_at) DESC NULLS LAST,
                                id DESC
                        ) AS rn
                    FROM pair
                    WHERE base IS NOT NULL AND base != 'native_token'
                )
                SELECT base, base_name, base_symbol, base_image_url, collection_id, status, dex, address
                FROM ranked WHERE rn = 1
                """
            )
            best_pair_result = await session.execute(best_pair_query)
            best_rows = best_pair_result.fetchall()

            # 构建 base -> 最优 pair 信息映射
            best_pair_map = {}
            for r in best_rows:
                try:
                    base = r[0]
                    best_pair_map[base] = {
                        "base_name": r[1] or "",
                        "base_symbol": r[2] or "",
                        "base_image_url": r[3] or "",
                        "collection_id": r[4] or "",
                        "status": int(r[5]) if r[5] is not None else 0,
                        "dex": (r[6] or "").lower(),
                        "pair": r[7] or "",
                    }
                except Exception:
                    continue

            if token_addresses:
                # 在新增/清理 Redis Set 前，先读取当前集合用于差集计算
                current_tokens_in_redis = await redis_client.smembers(set_key)
                current_tokens_in_redis = set(current_tokens_in_redis or [])

                # 增量加入 Redis Set
                added_count = await redis_client.sadd(set_key, *token_addresses)
                logger.debug(f"Successfully added {added_count} new token addresses to Redis set")

                # 清理陈旧 token
                valid_token_set = set(token_addresses)
                stale_tokens = [t for t in current_tokens_in_redis if t not in valid_token_set]
                if stale_tokens:
                    removed_count = await redis_client.srem(set_key, *stale_tokens)
                    logger.info(f"Cleaned up {removed_count} stale tokens from all_token_set: {stale_tokens}")

                set_size = await redis_client.scard(set_key)
                logger.debug(f"Current Redis set contains {set_size} token addresses")

                # 同步补全 Redis 中的 token 信息（缺啥补啥）
                fixed_count = 0
                async with redis_client.pipeline() as pipe:
                    for token in token_addresses:
                        info = best_pair_map.get(token)
                        if not info:
                            continue

                        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token}"
                        try:
                            token_data = await redis_client.hgetall(key)
                        except Exception:
                            token_data = {}

                        update_mapping = {}

                        # name, symbol, image_url, collection_id（仅空时补齐）
                        if not token_data.get("name") and info.get("base_name"):
                            update_mapping["name"] = info["base_name"]
                        if not token_data.get("symbol") and info.get("base_symbol"):
                            update_mapping["symbol"] = info["base_symbol"]
                        if not token_data.get("image_url") and info.get("base_image_url"):
                            update_mapping["image_url"] = info["base_image_url"]
                        if not token_data.get("collection_id") and info.get("collection_id"):
                            update_mapping["collection_id"] = info["collection_id"]

                        # status（仅在缺失或为 0 时补齐）
                        try:
                            status_val = token_data.get("status")
                            status_int = int(status_val) if status_val not in (None, "") else 0
                        except Exception:
                            status_int = 0
                        if status_int == 0 and info.get("status") is not None:
                            update_mapping["status"] = str(info["status"])

                        # dex（统一小写，仅空时补齐）
                        dex_val = token_data.get("dex")
                        if (dex_val is None or dex_val == "") and info.get("dex"):
                            update_mapping["dex"] = info["dex"]

                        # pair（仅空时补齐且格式看起来像地址）
                        pair_val = token_data.get("pair")
                        best_pair_addr = info.get("pair") or ""
                        if (pair_val is None or pair_val == "") and best_pair_addr:
                            update_mapping["pair"] = best_pair_addr

                        if update_mapping:
                            await pipe.hset(key, mapping=update_mapping)
                            fixed_count += 1

                    if fixed_count:
                        await pipe.execute()

                if fixed_count:
                    logger.info(f"Completed token info backfill for {fixed_count} tokens from pair table")
            else:
                logger.warning("No token addresses found")
                
    except Exception as e:
        logger.error(f"Error updating token set: {e}")
    
    total_duration = time.time() - start_time
    logger.info(f"Token set update completed, total duration: {total_duration:.2f} seconds")


async def setup_db_trigger_and_listener():
    """
    Setup database trigger and notification listener for real-time token set updates
    """
    global _pg_conn
    
    logger.info(f"Connecting to database for event listening...")
    
    try:
        # Create dedicated connection for listening
        _pg_conn = await asyncpg.connect(settings.DATABASE_LISTENER_URL)
        
        # Create trigger and notification function (if not exist)
        await _pg_conn.execute("""
            -- Create or replace notification function
            CREATE OR REPLACE FUNCTION notify_pair_changes() RETURNS TRIGGER AS $$
            BEGIN
                -- Send notification when pair records are added or updated
                PERFORM pg_notify('pair_changes', json_build_object(
                    'operation', TG_OP,
                    'table', TG_TABLE_NAME,
                    'base', NEW.base
                )::text);
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            
            -- Check if trigger exists, create if not
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'notify_pairs_changes_trigger') THEN
                    CREATE TRIGGER notify_pairs_changes_trigger
                    AFTER INSERT OR UPDATE ON pair
                    FOR EACH ROW
                    WHEN (NEW.base IS NOT NULL AND NEW.base != 'native_token')
                    EXECUTE FUNCTION notify_pair_changes();
                END IF;
            END $$;
        """)
        
        # Setup listening channel
        await _pg_conn.add_listener('pair_changes', on_pair_change_notification)
        logger.info("Database trigger and notification listener setup successfully")
        
        return True
    except Exception as e:
        logger.error(f"Failed to setup database trigger and listener: {e}")
        return False


async def on_pair_change_notification(conn, pid, channel, payload):
    """
    Process database notification events, update token set in Redis
    
    Args:
        conn: Database connection
        pid: Process ID
        channel: Notification channel
        payload: Notification content
    """
    try:
        # Parse notification content
        data = json.loads(payload)
        token_address = data.get('base')
        operation = data.get('operation')
        
        if not token_address or token_address == 'native_token':
            return
            
        logger.debug(f"Received database notification: operation={operation}, token={token_address}")
        
        # Get Redis client
        redis_client = await get_redis_client()
        set_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
        
        # Add to Redis set
        added = await redis_client.sadd(set_key, token_address)
        if added:
            logger.info(f"Real-time added new token to set: {token_address}")
        
    except Exception as e:
        logger.error(f"Error processing database notification: {e}")


async def fetch_transactions(
    session: AsyncSession, chain: str = settings.BLOCKCHAIN_TYPE
):
    """Fetch pending transactions along with token_address"""
    query = (
        select(
            UserTransaction.tx_hash,
            UserTransaction.status,
            UserTransaction.token_address,
            UserTransaction.type,
        )
        .where(
            and_(
                UserTransaction.status == UserTransactionStatus.AWAITING_CONFIRMATION,
                UserTransaction.tx_hash != None,
                UserTransaction.chain == chain,
            )
        )
        .limit(50)  # 增加限制以处理更多记录
    )
    result = await session.execute(query)
    return result.fetchall()

async def extract_launch_token(w3: AsyncWeb3, receipt: dict, topic0: str = LAUNCH_TOPIC0) -> Optional[str]:
    """
    遍历 logs，返回 Launch 事件的 tokenAddress
    """
    for log in receipt["logs"]:
        if log["topics"][0].hex().lower() == topic0:
            if len(log["topics"]) < 2:
                raise ValueError("Launch 事件未包含 tokenAddress")
            raw = log["topics"][1].hex()[-40:]
            return w3.to_checksum_address(f"0x{raw}")
    return None

async def process_bsc_transaction(
    w3: AsyncWeb3, tx_hash: str, tx_type: UserTransactionType, max_retries: int = 3, retry_delay: float = 1.0
):
    """Process each transaction to check its status with retry mechanism."""
    if not tx_hash:
        logger.warning("Received empty transaction hash for processing")
        return None, None, None, None

    retries = 0
    while retries <= max_retries:
        try:
            receipt = await w3.eth.get_transaction_receipt(tx_hash)
            if receipt is not None:
                token_address = None
                if receipt.status == 1:
                    status = UserTransactionStatus.CONFIRMED
                    if tx_type == UserTransactionType.CREATE_MEME:
                        token_address = await extract_launch_token(w3, receipt)
                else:
                    status = UserTransactionStatus.FAILED
                return tx_hash, status, tx_type, token_address
            else:
                return None, None, None, None
        except TransactionNotFound:
            logger.debug(
                f"Transaction {tx_hash} not found yet, retry {retries+1}/{max_retries}"
            )
            retry_delay = retry_delay * (1 + random.random())
            await asyncio.sleep(retry_delay)
            retries += 1
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout when fetching receipt for {tx_hash}, retry {retries+1}/{max_retries}"
            )
            retry_delay = retry_delay * (1 + random.random())
            await asyncio.sleep(retry_delay)
            retries += 1
        except Exception as e:
            logger.exception("Error fetching receipt for %s: %s", tx_hash, e)
            break

    logger.error(
        f"Failed to fetch transaction receipt after {max_retries} retries: {tx_hash}"
    )
    return None, None, None, None

async def set_token_info(token_cache: TokenCache, redis_client: Redis = None):
    try:
        if redis_client is None:
            redis_client = await get_redis_client()
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_cache.token_address}"
        data = token_cache.model_dump()
        await redis_client.hset(key, mapping=data)
        logger.info("Token info set in hash for key %s", key)
    except Exception as e:
        logger.exception(
            "Failed to set token info for %s: %s", token_cache.symbol, e
        )

async def update_token_redis(token_address: str, name: str, symbol: str, avatar: str, collection_id: str):
    """Update token information in Redis cache"""
    try:
        # Skip USDT and other stable coins - they shouldn't be processed as meme tokens
        if hasattr(settings, 'USDT_ADDRESS') and token_address == settings.USDT_ADDRESS:
            logger.debug(f"Skipping Redis update for USDT address: {token_address}")
            return
        redis_client = await get_redis_client()
        
        # Calculate initial token price using utility function
        price_per_token_usd = await get_initial_token_price(redis_client)
            
        total_supply_ui = settings.DEFAULT_TOTAL_SUPPLY
        market_cap = price_per_token_usd * total_supply_ui

        logger.info(f"Calculated bonding curve token price for {symbol}: ${price_per_token_usd} (market cap: ${market_cap})")

        # Create token cache object
        token_cache = TokenCache(
            token_address=token_address,
            name=name,
            symbol=symbol,
            image_url=avatar,
            status=PairStatus.READY.value,
            price=price_per_token_usd,
            market_cap=market_cap,
            collection_id=collection_id,
        )
        
        # Update token info in Redis
        await set_token_info(token_cache, redis_client)
        
        # Add token to all_token_set for memeprice processing
        all_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
        await redis_client.sadd(all_tokens_key, token_address)
        logger.info(f"Added token {token_address} to all_token_set")
        
        # Also add to active token queue
        await add_token_to_active_queue(token_address)
        
        logger.debug(f"Updated Redis for token {symbol} ({token_address})")
        
    except Exception as e:
        logger.warning(f"Failed to update token Redis for {symbol}: {str(e)}")
        # Set minimal cache info
        try:
            
            # Also add to all_token_set in fallback case
            all_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
            await redis_client.sadd(all_tokens_key, token_address)
            logger.info(f"Added token {token_address} to all_token_set (fallback)")
        except Exception as fallback_e:
            logger.error(f"Failed to set fallback token info: {fallback_e}")

async def update_bsc_transaction_status():
    """Update transaction status in the database with concurrency control using ThreadPoolExecutor."""
    logger.debug("Starting BSC transaction status update...")
    start_time = time.time()

    async with get_session_context("write") as session:
        transactions = await fetch_transactions(session)

    logger.debug(f"Retrieved {len(transactions)} pending BSC transactions")

    # 保存所有交易记录信息用于后续更新
    all_transactions = [(tx[0], tx[1], UserTransactionType(tx[3]), tx[2]) for tx in transactions]
    
    # Save transaction data: tx_hash -> token_address mapping (优先使用 CREATE_MEME 的 token_address)
    tx_tokens = {}
    for tx_hash, status, tx_type, token_address in all_transactions:
        if token_address:
            # 如果是 CREATE_MEME 类型，优先使用其 token_address
            if tx_type == UserTransactionType.CREATE_MEME:
                tx_tokens[tx_hash] = token_address
            elif tx_hash not in tx_tokens:
                # 如果还没有记录，使用当前的 token_address
                tx_tokens[tx_hash] = token_address
    
    # 去重 tx_hash 用于区块链状态查询，但保留类型信息
    unique_tx_hashes = {}
    for tx_hash, status, tx_type, token_address in all_transactions:
        if tx_hash not in unique_tx_hashes:
            unique_tx_hashes[tx_hash] = (status, tx_type)
    
    tx_status_list = [(tx_hash, status, tx_type) for tx_hash, (status, tx_type) in unique_tx_hashes.items()]

    results = []
    if tx_status_list:
        logger.debug(f"Preparing to process {len(tx_status_list)} BSC transactions")
        w3 = AsyncWeb3(AsyncHTTPProvider(settings.BSC_RPC_URL if settings.BLOCKCHAIN_TYPE == "BSC" else settings.ETH_RPC_URL))

        try:
            if not await w3.is_connected():
                logger.error("Failed to connect to BSC RPC")
                raise Exception("Failed to connect to BSC RPC")
            logger.debug("BSC RPC connection successful")
        except Exception as e:
            logger.error(f"BSC RPC connection error: {e}")
            return

        max_workers = (
            settings.BSC_MAX_WORKERS if hasattr(settings, "BSC_MAX_WORKERS") else 5
        )
        logger.debug(f"Using maximum concurrency: {max_workers}")

        async def process_tx_batch(batch):
            batch_results = []
            logger.debug(f"Starting batch processing with {len(batch)} transactions")
            for tx_hash, status, tx_type in batch:
                await asyncio.sleep(random.uniform(0, 0.2))
                result = await process_bsc_transaction(w3, tx_hash, tx_type)
                batch_results.append(result)
            logger.debug(
                f"Batch processing completed, got {len([r for r in batch_results if r[1] is not None])} valid results"
            )
            return batch_results

        batch_size = max_workers
        batches = [
            tx_status_list[i : i + batch_size]
            for i in range(0, len(tx_status_list), batch_size)
        ]
        logger.debug(
            f"Split {len(tx_status_list)} transactions into {len(batches)} batches"
        )

        all_results = []
        for i, batch in enumerate(batches):
            logger.debug(
                f"Processing batch {i+1}/{len(batches)} with {len(batch)} transactions"
            )
            batch_start_time = time.time()
            batch_results = await process_tx_batch(batch)
            all_results.extend(batch_results)
            batch_duration = time.time() - batch_start_time
            logger.debug(
                f"Batch {i+1} processing completed, duration: {batch_duration:.2f} seconds"
            )
            await asyncio.sleep(0.5)

        results = all_results

    updates = [
        (tx_hash, new_status, tx_type, token_address)
        for tx_hash, new_status, tx_type, token_address in results
        if tx_hash is not None and new_status is not None
    ]

    if updates:
        logger.info(f"About to update {len(updates)} transaction statuses")
        async with get_session_context("write") as session:
            for tx_hash, new_status, tx_type, token_address in updates:
                logger.debug(f"Updating transaction {tx_hash} status to {new_status}")
                update_query = text(
                    "UPDATE user_transactions SET status = :status WHERE tx_hash = :tx_hash"
                )
                await session.execute(
                    update_query,
                    {
                        "status": new_status,
                        "tx_hash": tx_hash,
                    },
                )

                if tx_type == UserTransactionType.CREATE_MEME and token_address:
                    logger.info(f"Updating pair {token_address} status to {PairStatus.READY}")
                    
                    # First, get the pair information before updating
                    pair_info_query = text(
                        "SELECT base_name, base_symbol, base_image_url, collection_id FROM pair WHERE creation_txid = :tx_hash"
                    )
                    pair_result = await session.execute(pair_info_query, {"tx_hash": tx_hash})
                    pair_info = pair_result.first()
                    
                    # Update the pair status
                    update_pair_query = text(
                        "UPDATE pair SET base = :token_address, status = :status WHERE creation_txid = :tx_hash"
                    )
                    await session.execute(
                        update_pair_query,
                        {
                            "token_address": token_address,
                            "status": PairStatus.READY,
                            "tx_hash": tx_hash,
                        }
                    )
                    
                    # Update Redis with pair information
                    if pair_info:
                        try:
                            await update_token_redis(
                                token_address=token_address,
                                name=pair_info.base_name or "Unknown",
                                symbol=pair_info.base_symbol or "UNK",
                                avatar=pair_info.base_image_url or "",
                                collection_id=pair_info.collection_id or ""
                            )
                            logger.info(f"Updated Redis cache for token {pair_info.base_symbol} ({token_address})")
                            
                            # 立即清除排行榜缓存，确保新币立即出现在排行榜中
                            await invalidate_rankings_cache(pair_info.base_symbol or "Unknown")
                            
                        except Exception as redis_e:
                            logger.error(f"Failed to update Redis for token {token_address}: {redis_e}")
                
                # Handle failed CREATE_MEME transactions (separate if, not elif)
                if tx_type == UserTransactionType.CREATE_MEME and new_status == UserTransactionStatus.FAILED:
                    logger.warning(f"CREATE_MEME transaction failed: {tx_hash}")
                    
                    # Get collection_id from pair table using tx_hash
                    collection_query = text(
                        "SELECT collection_id, base_symbol FROM pair WHERE creation_txid = :tx_hash"
                    )
                    collection_result = await session.execute(collection_query, {"tx_hash": tx_hash})
                    collection_info = collection_result.first()
                    
                    if collection_info and collection_info.collection_id:
                        collection_id = collection_info.collection_id
                        symbol = collection_info.base_symbol or "Unknown"
                        logger.info(f"Deleting collection {collection_id} for failed meme creation {symbol}")
                        
                        try:
                            # Delete the collection from database
                            delete_collection_query = text(
                                "DELETE FROM collections WHERE id = :collection_id"
                            )
                            await session.execute(delete_collection_query, {"collection_id": collection_id})
                            logger.info(f"Successfully deleted collection {collection_id} for failed meme {symbol}")
                            
                        except Exception as delete_e:
                            logger.error(f"Failed to delete collection {collection_id}: {delete_e}")
                    
                    # Update the pair status to FAILED (don't delete it)
                    try:
                        update_failed_pair_query = text(
                            "UPDATE pair SET status = :status WHERE creation_txid = :tx_hash"
                        )
                        await session.execute(
                            update_failed_pair_query,
                            {
                                "status": PairStatus.FAILED,
                                "tx_hash": tx_hash,
                            }
                        )
                        logger.info(f"Updated pair status to FAILED for tx_hash {tx_hash}")
                    except Exception as pair_e:
                        logger.error(f"Failed to update pair status for {tx_hash}: {pair_e}")

                # If transaction status is confirmed and has token_address, add to active tokens
                if (
                    new_status == UserTransactionStatus.CONFIRMED
                    and tx_hash in tx_tokens
                ):
                    token_address = tx_tokens[tx_hash]
                    logger.info(
                        f"Detected confirmed token transaction: {tx_hash}, token address: {token_address}"
                    )
                    await add_token_to_active_queue(token_address)

            await session.commit()
            logger.debug(f"Successfully updated {len(updates)} transaction statuses")

        logger.info(f"Updated status for {len(updates)} transactions.")
    else:
        logger.debug("No transaction status updates needed.")

    total_duration = time.time() - start_time
    logger.debug(
        f"BSC transaction status update completed, total duration: {total_duration:.2f} seconds"
    )


async def process_sol_transaction(solana_client: AsyncClient, tx_hash: str) -> Tuple:
    """Process each Solana transaction to check its status."""
    try:
        response = await solana_client.get_signature_statuses(
            [Signature.from_string(tx_hash)], search_transaction_history=True
        )
        if response.value and len(response.value) > 0:
            tx_status = response.value[0]

            if tx_status:
                if (
                    tx_status.confirmation_status
                    == TransactionConfirmationStatus.Finalized
                ):
                    if not tx_status.err:
                        return tx_hash, UserTransactionStatus.CONFIRMED
                    else:
                        return tx_hash, UserTransactionStatus.FAILED
                return None, None

    except SolanaRpcException as e:
        logger.exception("Solana RPC error for tx %s: %s", tx_hash, e)
    except Exception as e:
        logger.exception("Error fetching status for tx %s: %s", tx_hash, e)

    return None, None


async def update_sol_transaction_status():
    """Update transaction status in the database."""

    async with get_session_context("write") as session:
        transactions = await fetch_transactions(session)

    # 保存所有交易记录信息用于后续更新
    all_transactions = [(tx[0], tx[1], UserTransactionType(tx[3]), tx[2]) for tx in transactions]
    
    # Save transaction data: tx_hash -> token_address mapping (优先使用 CREATE_MEME 的 token_address)
    tx_tokens = {}
    for tx_hash, status, tx_type, token_address in all_transactions:
        if token_address:
            # 如果是 CREATE_MEME 类型，优先使用其 token_address
            if tx_type == UserTransactionType.CREATE_MEME:
                tx_tokens[tx_hash] = token_address
            elif tx_hash not in tx_tokens:
                # 如果还没有记录，使用当前的 token_address
                tx_tokens[tx_hash] = token_address

    # 去重 tx_hash 用于区块链状态查询
    unique_tx_hashes = set()
    tx_status_list = []
    for tx_hash, status, tx_type, token_address in all_transactions:
        if tx_hash not in unique_tx_hashes:
            unique_tx_hashes.add(tx_hash)
            tx_status_list.append((tx_hash, status))

    results = []
    if tx_status_list:
        solana_client = AsyncClient(settings.SOL_RPC_URL)

        try:
            is_connected = await solana_client.is_connected()
            if not is_connected:
                raise Exception("Failed to connect to Solana RPC")
        except Exception as e:
            logger.error(f"Failed to connect to Solana RPC: {e}")
            raise Exception("Failed to connect to Solana RPC")

        max_workers = (
            settings.SOL_MAX_WORKERS if hasattr(settings, "SOL_MAX_WORKERS") else 5
        )
        batch_size = max_workers

        batches = [
            tx_status_list[i : i + batch_size]
            for i in range(0, len(tx_status_list), batch_size)
        ]

        async def process_sol_batch(batch):
            batch_results = []
            for tx_hash, _ in batch:
                await asyncio.sleep(random.uniform(0, 0.2))
                result = await process_sol_transaction(solana_client, tx_hash)
                batch_results.append(result)
            return batch_results

        all_results = []
        for batch in batches:
            batch_results = await process_sol_batch(batch)
            all_results.extend(batch_results)
            await asyncio.sleep(0.5)

        results = all_results

        await solana_client.close()

    updates = [
        (tx_hash, new_status) for tx_hash, new_status in results if tx_hash is not None
    ]

    async with get_session_context("write") as session:
        for tx_hash, new_status in updates:
            update_query = text(
                "UPDATE user_transactions SET status = :status WHERE tx_hash = :tx_hash"
            )
            await session.execute(
                update_query,
                {
                    "status": new_status,
                    "tx_hash": tx_hash,  # Solana transaction hash remains unchanged, no need to remove "0x" prefix
                },
            )

            # Handle failed CREATE_MEME transactions for collection cleanup
            if new_status == UserTransactionStatus.FAILED:
                # Check if this is a CREATE_MEME transaction
                tx_type_query = text(
                    "SELECT type FROM user_transactions WHERE tx_hash = :tx_hash LIMIT 1"
                )
                tx_type_result = await session.execute(tx_type_query, {"tx_hash": tx_hash})
                tx_type_row = tx_type_result.first()
                
                if tx_type_row and UserTransactionType(tx_type_row.type) == UserTransactionType.CREATE_MEME:
                    logger.warning(f"Solana CREATE_MEME transaction failed: {tx_hash}")
                    
                    # Get collection_id from pair table using tx_hash
                    collection_query = text(
                        "SELECT collection_id, base_symbol FROM pair WHERE creation_txid = :tx_hash"
                    )
                    collection_result = await session.execute(collection_query, {"tx_hash": tx_hash})
                    collection_info = collection_result.first()
                    
                    if collection_info and collection_info.collection_id:
                        collection_id = collection_info.collection_id
                        symbol = collection_info.base_symbol or "Unknown"
                        logger.info(f"Deleting collection {collection_id} for failed Solana meme creation {symbol}")
                        
                        try:
                            # Delete the collection from database
                            delete_collection_query = text(
                                "DELETE FROM collections WHERE id = :collection_id"
                            )
                            await session.execute(delete_collection_query, {"collection_id": collection_id})
                            logger.info(f"Successfully deleted collection {collection_id} for failed Solana meme {symbol}")
                            
                        except Exception as delete_e:
                            logger.error(f"Failed to delete collection {collection_id}: {delete_e}")
                    
                    # Update the pair status to FAILED (don't delete it)
                    try:
                        update_failed_pair_query = text(
                            "UPDATE pair SET status = :status WHERE creation_txid = :tx_hash"
                        )
                        await session.execute(
                            update_failed_pair_query,
                            {
                                "status": PairStatus.FAILED,
                                "tx_hash": tx_hash,
                            }
                        )
                        logger.info(f"Updated Solana pair status to FAILED for tx_hash {tx_hash}")
                    except Exception as pair_e:
                        logger.error(f"Failed to update Solana pair status for {tx_hash}: {pair_e}")

            # If transaction status is confirmed and has token_address, add to active tokens
            elif new_status == UserTransactionStatus.CONFIRMED and tx_hash in tx_tokens:
                token_address = tx_tokens[tx_hash]
                logger.info(
                    f"Detected confirmed token transaction: {tx_hash}, token address: {token_address}"
                )
                await add_token_to_active_queue(token_address)

        await session.commit()

    logger.info(f"Updated status for {len(updates)} Solana transactions.")


def job_listener(event):
    """Log job execution and error status."""
    if event.exception:
        logger.error(f"Job {event.job_id} failed. Exception: {event.exception}")
    else:
        logger.debug(f"Job {event.job_id} executed successfully.")


async def cleanup_resources():
    """Close all resource connections"""
    global _redis_client, _pg_conn
    
    # Close Redis connection
    if _redis_client is not None:
        await _redis_client.aclose()
        _redis_client = None
    
    # Close PostgreSQL listening connection
    if _pg_conn is not None:
        await _pg_conn.close()
        _pg_conn = None
        
    logger.debug("Resources cleaned up")


logger = get_logger("memetracker", level="INFO", file_path="latest.log")
app = FastAPI(title="Memetracker Health Check")

@app.get("/health", response_class=PlainTextResponse)
async def health_check():
    """Simple health check endpoint for Docker"""
    return "OK"

async def init_http_server():
    """Initialize FastAPI server for health checks"""
    config = uvicorn.Config(app, host="0.0.0.0", port=8000, log_level="info")
    server = uvicorn.Server(config)
    await server.serve()

def main():
    """Main function to run the scheduled tasks."""
    # Initialize DB engines for this process
    init_engines()
    scheduler = AsyncIOScheduler(timezone="UTC")
    
    scheduler.add_job(
        update_bsc_transaction_status,  # async function
        trigger="interval",
        seconds=5,
        id="update_bsc_transaction_status",
        max_instances=1,
        coalesce=True,
        misfire_grace_time=5,
    )
    
    # Setup database trigger and listener
    scheduler.add_job(
        setup_db_trigger_and_listener,
        trigger="date",  # Run only once
        id="setup_db_listener",
        max_instances=1,
    )
    
    # Still keep periodic full update as backup, but with reduced frequency
    scheduler.add_job(
        update_token_set_from_pairs,
        trigger="interval",
        seconds=5,  # Reduce frequency to 30 minutes
        id="update_token_set_from_pairs",
        max_instances=1,
        coalesce=True,
        misfire_grace_time=60,
    )

    scheduler.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
    scheduler.start()
    logger.info("BSC transaction tracker scheduler started.")

    # Run both scheduler and FastAPI server
    loop = asyncio.get_event_loop()
    try:
        # Start FastAPI server
        loop.create_task(init_http_server())
        # Run the event loop forever
        loop.run_forever()
    except (KeyboardInterrupt, SystemExit):
        logger.info("Shutting down...")
    finally:
        # Ensure resources are cleaned up
        loop.run_until_complete(cleanup_resources())
        loop.close()


if __name__ == "__main__":
    main()
