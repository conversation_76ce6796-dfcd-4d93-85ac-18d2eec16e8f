from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

    DATABASE_LISTENER_URL: str = "postgresql://kratos:<EMAIL>:5432/toci"

    # Blockchain
    BLOCKCHAIN_TYPE: str
    BSC_RPC_URL: str
    BSC_CHAIN_ID: int
    BSC_CHAIN: str
    DEFAULT_TOTAL_SUPPLY: int = 1_000_000_000

    ETH_RPC_URL: str
    # Solana RPC URL
    SOL_RPC_URL: str

    REDIS_URL: str


settings = Settings()
