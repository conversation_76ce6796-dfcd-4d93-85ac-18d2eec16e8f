from src.mini_apps.dto import (
    AppCreateDTO,
    MiniAppDTO,
    MiniAppFetchingParams,
    MiniAppUpdateDTO,
    MiniAppDeleteDTO,
    Page,
)
from src.mini_apps.exceptions import PermissionDenied
from src.mini_apps.repos import MiniAppRepo, MiniAppInteractionsRepo


class MiniAppService:
    """Business logic of apps"""

    def __init__(self, repo: MiniAppRepo, interactions_repo: MiniAppInteractionsRepo):
        self._repo = repo
        self._interactions_repo = interactions_repo

    async def get_mini_app_by_id(self, app_id: str, current_user_id: str) -> MiniAppDTO:
        result = await self._repo.get_by_id_or_raise(
            app_id,
        )

        # Mark app as interacted
        await self._interactions_repo.create(
            app_id=app_id,
            author_id=current_user_id,
        )

        return result

    async def create_mini_app(
        self,
        cmd: AppCreateDTO,
    ) -> MiniAppDTO:
        return await self._repo.create(
            name=cmd.name,
            desc=cmd.description,
            is_mobile=cmd.is_mobile,
            is_web=cmd.is_web,
            icon=cmd.icon,
            cover=cmd.cover,
            version=cmd.version,
            url=cmd.url,
            author_id=cmd.author_id,
        )

    async def fetch_mini_apps(
        self,
        cmd: MiniAppFetchingParams,
    ):
        if cmd.only_mine:
            # If user wants applications created only by itself
            cmd.author_id = cmd.current_user_id

        # Some validations before call to `fetch_all` method.
        results = await self._repo.fetch_all(
            page=cmd.page,
            name=cmd.name,
            page_size=cmd.page_size,
            author_id=cmd.author_id,
            moderation_status=cmd.moderation_status,
            app_status=cmd.status,
        )

        return results

    @staticmethod
    def _ensure_current_user_is_owner(
        current_user_id: int,
        app: MiniAppDTO,
    ):
        if app.author_id != current_user_id:
            raise PermissionDenied("You can't update this app.")

    async def update_mini_app_partially(self, cmd: MiniAppUpdateDTO) -> MiniAppDTO:
        self._ensure_current_user_is_owner(
            cmd.current_user_id,
            cmd.needed_app,
        )

        # If `app_status` or `moderation_status` are requested to changed
        # We check for `current_user` is administrator.

        if (
            cmd.app_status is not None or cmd.moderation_status is not None
        ) and not cmd.is_current_user_admin:
            raise PermissionDenied("Only admins can update app's statuses.")

        updated = await self._repo.patch(
            app_id=cmd.needed_app.id,
            name=cmd.name,
            description=cmd.description,
            version=cmd.version,
            icon=cmd.icon,
            cover=cmd.cover,
            url=cmd.url,
            is_mobile=cmd.is_mobile,
            is_web=cmd.is_web,
            moderation_status=cmd.moderation_status,
            status=cmd.status,
        )

        return updated

    async def delete_mini_app(self, cmd: MiniAppDeleteDTO) -> MiniAppDTO:
        self._ensure_current_user_is_owner(
            cmd.current_user_id,
            cmd.app,
        )

        await self._repo.delete(
            cmd.app,
        )

        return cmd.app

    async def fetch_recent_apps(
        self,
        current_user_id: str,
    ) -> Page[MiniAppDTO]:
        """
        Fetches recently used mini-apps
        :param current_user_id: A current-user id
        :return: `Page` with `MiniAppDTO` object
        """
        return await self._interactions_repo.fetch(
            current_user_id,
        )
