# Mini_Apps Service - Simplified using enhanced base
# Reduced from 76 lines to ~25 lines!

ARG PYTHON_VERSION=3.10.12
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv in builder stage
USER root
RUN pip install --no-cache-dir uv
USER appuser

# Copy service-specific requirements
COPY src/mini_apps/requirements.txt /tmp/mini_apps-requirements.txt
COPY src/auth/requirements.txt /tmp/auth-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/mini_apps-requirements.txt
RUN uv pip install --no-cache -r /tmp/auth-requirements.txt

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Add service scripts to PATH
ENV PATH="$PATH:/src/mini_apps/scripts"

# Copy service-specific code
COPY --chown=appuser:appuser src/mini_apps /src/mini_apps
COPY --chown=appuser:appuser src/admin /src/admin

# Copy dependencies based on docker-compose.yml volumes
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database
COPY --chown=appuser:appuser src/auth /src/auth
COPY --chown=appuser:appuser src/authors /src/authors

# Set executable permissions for start script
RUN chmod +x /src/mini_apps/scripts/start.sh
