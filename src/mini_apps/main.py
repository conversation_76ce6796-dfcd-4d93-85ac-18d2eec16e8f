from fastapi import <PERSON><PERSON><PERSON>, Depends, Path, Query, HTTPException, status
from src.infra.logger import get_logger
from src.infra.app import create_app
from fastapi_pagination import Params
from starlette import status as status_codes

from src.mini_apps.dependencies import get_mini_app_service
from src.mini_apps.dto import (
    AppCreateDTO,
    MiniAppFetchingParams,
    MiniAppDTO,
    MiniAppUpdateDTO,
    MiniAppDeleteDTO,
)
from src.common.mini_apps.enums import MiniAppStatus, MiniAppModerationStatus
from src.mini_apps.exceptions import MiniAppNotFound, PermissionDenied
from src.mini_apps.schemas import (
    MiniAppCreationRequest,
    MiniAppSchema,
    Page,
    MiniAppUpdateRequest,
)
from src.mini_apps.services import MiniAppService
from src.auth import current_user as get_current_user
from src.common.dependencies import is_current_user_admin
from src.database.models import User


logger = get_logger("mini_apps", level="INFO", file_path="latest.log")
app = create_app(title="Mini-Apps", description="A microservice that dispatches requests to platform from mini-apps", version="1.0", request_logger=logger)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.get("/", summary="Get mini-apps", response_model=Page[MiniAppSchema])
async def fetch_mini_apps(
    name: str | None = Query(
        title="Name", description="For filtering by `name`", default=None
    ),
    author_id: str | None = Query(
        title="Author-ID",
        description="For filtering by `author-id`",
        default=None,
    ),
    status: MiniAppStatus = Query(
        title="App-status",
        description="For filtering by app's status, by default shows apps with `Active` status",
        default=MiniAppStatus.ACTIVE,
    ),
    only_mine: bool = Query(
        title="Only mine",
        description="Shows mini-apps created only by `current_user`",
        default=False,
    ),
    moderation_status: MiniAppModerationStatus | None = Query(
        title="Moderation-status",
        description="For filtering by moderation-status, by default shows apps with any status.",
        default=MiniAppModerationStatus.APPROVED,
    ),
    pagination_params: Params = Depends(),
    user: User = Depends(get_current_user),
    mini_app_service: MiniAppService = Depends(get_mini_app_service),
):
    cmd = MiniAppFetchingParams(
        name=name,
        author_id=author_id,
        page_size=pagination_params.size,
        page=pagination_params.page,
        moderation_status=moderation_status,
        current_user_id=user.id,
        only_mine=only_mine,
        status=status,
    )

    results = await mini_app_service.fetch_mini_apps(cmd)

    return results


@app.get(
    "/recent",
    response_model=Page[MiniAppSchema],
    summary="Get recent mini-apps",
    description="Endpoint for getting recent mini-apps",
)
async def fetch_recent_mini_apps(
    current_user: User = Depends(get_current_user),
    mini_app_service: MiniAppService = Depends(get_mini_app_service),
):
    """
    Endpoint for getting recent mini-apps
    :param mini_app_service: Initialized `MiniAppService`
    :param current_user: A current user o
    :return: `Page` with `MiniAppSchema` objects
    """
    return await mini_app_service.fetch_recent_apps(
        current_user_id=current_user.id,
    )


@app.post(
    "/",
    response_model=MiniAppSchema,
    summary="Create mini-app",
    status_code=status_codes.HTTP_201_CREATED,
)
async def register_app(
    data: MiniAppCreationRequest,
    current_user: User = Depends(get_current_user),
    miniapp_service: MiniAppService = Depends(get_mini_app_service),
):
    """
    Endpoint for creating mini-apps
    :param data: Request-json
    :param current_user: A current user
    :param miniapp_service:
    :return:
    """
    cmd = AppCreateDTO(
        **data.model_dump(),
        author_id=current_user.id,
    )
    # TODO(Ilyas): Validations
    created_app = await miniapp_service.create_mini_app(
        cmd,
    )

    return created_app


@app.get(
    "/{app_id}", response_model=MiniAppSchema, summary="Get an app by its identifier."
)
async def get_app_by_identifier(
    app_id: str = Path(title="App-ID"),
    current_user: User = Depends(get_current_user),
    mini_app_service: MiniAppService = Depends(get_mini_app_service),
):
    """
    Returns an app by its identifier
    :param current_user: A current-user
    :param app_id: Application's identifier
    :param mini_app_service: Initialized `AppService`
    :return:
    """

    try:
        mini_app = await mini_app_service.get_mini_app_by_id(
            app_id,
            current_user_id=current_user.id,
        )
    except MiniAppNotFound as e:
        raise HTTPException(
            status_code=status_codes.HTTP_404_NOT_FOUND, detail=e.args[0]
        )

    return mini_app


@app.patch("/{app_id}", response_model=MiniAppSchema, summary="Update app (partially)")
async def update_mini_app_partially(
    update_request: MiniAppUpdateRequest,
    current_user: User = Depends(get_current_user),
    is_admin: bool = Depends(is_current_user_admin),
    needed_app: MiniAppDTO = Depends(get_app_by_identifier),
    mini_app_service: MiniAppService = Depends(get_mini_app_service),
):
    """
    Endpoint for deleting an app by its identifier
    :param is_admin: Is current user administrator?
    :param update_request:
    :param current_user:
    :param needed_app:
    :param mini_app_service:
    :return:
    """
    cmd = MiniAppUpdateDTO(
        current_user_id=current_user.id,
        is_current_user_admin=is_admin,
        needed_app=needed_app,
        **update_request.model_dump(),
    )

    try:
        updated_app = await mini_app_service.update_mini_app_partially(
            cmd,
        )
    except PermissionDenied as e:
        raise HTTPException(
            detail=e.args[0],
            status_code=status_codes.HTTP_403_FORBIDDEN,
        )

    return updated_app


@app.delete("/{app_id}", response_model=MiniAppSchema, summary="Delete an app")
async def delete_mini_app(
    needed_app: MiniAppDTO = Depends(get_app_by_identifier),
    current_user: User = Depends(get_current_user),
    mini_app_service: MiniAppService = Depends(get_mini_app_service),
):
    """
    Endpoint for deleting an app by its identifier
    :param needed_app:
    :param current_user:
    :param mini_app_service:
    :return:
    """
    cmd = MiniAppDeleteDTO(
        app=needed_app,
        current_user_id=current_user.id,
    )

    try:
        deleted_app = await mini_app_service.delete_mini_app(
            cmd,
        )
    except PermissionDenied as e:
        raise HTTPException(
            detail=e.args[0],
            status_code=status_codes.HTTP_403_FORBIDDEN,
        )

    return deleted_app
