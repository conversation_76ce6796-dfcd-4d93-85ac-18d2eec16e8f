from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.mini_apps.repos import MiniAppRepo, MiniAppInteractionsRepo
from src.mini_apps.services import MiniAppService
from src.database.session import get_session


async def get_mini_app_repo(
    session: AsyncSession = Depends(get_session),
) -> MiniAppRepo:
    """Builds & returns `MiniApp` repository"""
    return MiniAppRepo(
        session,
    )


def get_interactions_repo(
    session: AsyncSession = Depends(get_session),
    mini_app_repo: MiniAppRepo = Depends(get_mini_app_repo),
) -> MiniAppInteractionsRepo:
    """
    Builds & returns `MiniAppInteractions` repository
    :param session: An `AsyncSession` object
    :param mini_app_repo: Initialized `MiniApp` repository
    :return: Initialized `MiniAppInteractions` repository
    """
    return MiniAppInteractionsRepo(
        mini_apps_repo=mini_app_repo,
        session=session,
    )


async def get_mini_app_service(
    repo: MiniAppRepo = Depends(get_mini_app_repo),
    interactions_repo: MiniAppInteractionsRepo = Depends(get_interactions_repo),
) -> MiniAppService:
    """Builds & returns `AppService` object."""
    return MiniAppService(repo, interactions_repo=interactions_repo)
