from dataclasses import dataclass
from typing import Generic, List, TypeVar

from src.database.models.MiniApp import MiniApp
from src.common.mini_apps.enums import MiniAppStatus, MiniAppModerationStatus

T = TypeVar("T")


@dataclass
class AppCreateDTO:
    """App creation cmd."""

    name: str
    description: str
    author_id: str
    is_web: bool
    is_mobile: bool
    version: str
    icon: str
    url: str
    cover: str


@dataclass
class AppFetchParams:
    page: int
    page_size: int


@dataclass
class MiniAppUpdateDTO:
    current_user_id: str
    is_current_user_admin: bool
    needed_app: "MiniAppDTO"
    name: str | None = None
    description: str | None = None
    version: str | None = None
    app_status: MiniAppStatus | None = None
    icon: str | None = None
    url: str | None = None
    is_web: bool | None = None
    is_mobile: bool | None = None
    cover: str | None = None
    moderation_status: MiniAppModerationStatus | None = None
    status: MiniAppStatus | None = None


@dataclass
class MiniAppDTO:
    id: str
    name: str
    description: str
    author_id: str
    is_web: bool
    is_mobile: bool
    url: str
    version: str
    icon: str
    cover: str
    likes_count: int
    comments_count: int
    tags: List[str]
    status: MiniAppStatus
    moderation_status: MiniAppModerationStatus
    
    @classmethod
    def from_model(cls, model: MiniApp):
        return cls(
            id=model.id,
            name=model.name,
            description=model.description,
            author_id=model.author_id,
            is_mobile=model.is_mobile,
            is_web=model.is_web,
            icon=model.icon,
            url=model.url,
            cover=model.cover,
            tags=model.tags,
            comments_count=model.comments_count,
            likes_count=model.likes_count,
            version=model.version,
            status=model.app_status,
            moderation_status=model.moderation_status,
        )


@dataclass
class Page(Generic[T]):
    page_size: int
    page: int
    total_pages: int
    total_items: int
    items: List[T]


@dataclass
class MiniAppFetchingParams:
    page_size: int
    page: int
    current_user_id: str
    only_mine: bool = False
    name: str | None = None
    author_id: str | None = None
    status: MiniAppStatus | None = None
    moderation_status: MiniAppModerationStatus | None = None


@dataclass
class MiniAppDeleteDTO:
    app: MiniAppDTO
    current_user_id: str
