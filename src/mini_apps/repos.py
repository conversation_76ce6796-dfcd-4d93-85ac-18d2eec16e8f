from typing import Callable, Any, List

from math import ceil
from sqlalchemy import select, Select, func, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, noload

from src.database.constants import PostStatus
from src.database.constants import Region
from src.mini_apps.constants import MINI_APP_INTERACTIONS_HISTORY_LIMIT
from src.mini_apps.dto import MiniAppDTO, Page, T
from src.common.mini_apps import enums
from src.common.mini_apps.enums import MiniAppStatus
from src.mini_apps.exceptions import MiniAppNotFound
from src.database.models.MiniApp import MiniApp, AuthorAppInteraction


class MiniAppRepo:
    """CRUD for mini-apps"""

    def __init__(
        self,
        session: AsyncSession,
    ):
        self._session = session

    async def get_by_id_or_raise(
        self,
        app_id: str,
    ) -> MiniAppDTO:
        """
        Returns a MiniApp by its identifier
        or raises `AppNotFound` error
        :param app_id: App's identifier
        :return:
        """
        results = await self._session.execute(
            select(
                MiniApp,
            )
            .where(
                MiniApp.id == app_id,
            )
            .options(
                joinedload(
                    MiniApp.author,
                )
            )
        )

        first_one = results.scalars().first()

        if first_one is None:
            raise MiniAppNotFound(f"App with id = {app_id} not found.")

        return MiniAppDTO.from_model(first_one)

    async def create(
        self,
        name: str,
        desc: str,
        icon: str,
        cover: str,
        version: str,
        author_id: str,
        url: str,
        is_web: bool = True,
        is_mobile: bool = False,
    ):
        """
        Creates a `MiniApp`
        :param url:
        :param icon:
        :param cover:
        :param is_web:
        :param is_mobile:
        :param name:
        :param desc:
        :param version:
        :param author_id:
        :return:
        """

        app = MiniApp(
            name=name,
            description=desc,
            icon=icon,
            author_id=author_id,
            is_web=is_web,
            version=version,
            cover=cover,
            url=url,
            status=PostStatus.POSTED,
            app_status=MiniAppStatus.ACTIVE,
            is_mobile=is_mobile,
            region=Region.OTHER,  # Currently, hardcoded region with `Region.OTHER`
        )

        self._session.add(
            app,
        )

        await self._session.flush()
        await self._session.commit()

        return MiniAppDTO.from_model(app)

    async def _fetch(
        self,
        query: Select,
        page: int,
        page_size: int,
        mapper: Callable[[Any], T],
        *args_to_mapper,
    ) -> Page[T]:
        """Executes `query` with pagination"""
        total = await self._session.scalar(
            query.order_by(None)
            .options(noload("*"))
            .with_only_columns(func.count(), maintain_column_froms=True)
        )
        q = query.offset((page - 1) * page_size).limit(page_size)
        items = [
            mapper(item, *args_to_mapper)
            for item in (await self._session.execute(q)).unique().scalars().all()
        ]
        total_pages = ceil(total / page_size)

        return Page(
            items=items,
            total_pages=total_pages,
            page=page,
            page_size=page_size,
            total_items=total,
        )

    async def fetch_all(
        self,
        page: int,
        page_size: int,
        name: str | None = None,
        author_id: str | None = None,
        moderation_status: enums.MiniAppModerationStatus = None,
        app_status: enums.MiniAppStatus = None,
        ids: List[str] = None,
    ) -> Page[MiniAppDTO]:
        stmt = select(
            MiniApp,
        )

        if ids is not None:
            stmt = stmt.where(MiniApp.id.in_(ids))

        if name is not None:
            stmt = stmt.where(MiniApp.name.like(f"%{name}%"))

        # If `author_id` provided, filter by `author_id`
        if author_id is not None:
            stmt = stmt.where(
                MiniApp.author_id == author_id,
            )

        if moderation_status is not None:
            stmt = stmt.where(
                MiniApp.moderation_status == moderation_status,
            )

        if app_status is not None:
            stmt = stmt.where(
                MiniApp.app_status == app_status,
            )

        result = await self._fetch(
            stmt,
            page=page,
            page_size=page_size,
            mapper=MiniAppDTO.from_model,  # type: ignore
        )

        return result

    async def patch(
        self,
        app_id: str,
        name: str | None = None,
        description: str | None = None,
        is_mobile: bool | None = None,
        url: str | None = None,
        is_web: bool | None = None,
        version: str | None = None,
        icon: str | None = None,
        cover: str | None = None,
        status: enums.MiniAppStatus | None = None,
        moderation_status: enums.MiniAppModerationStatus | None = None,
    ):
        stmt = update(
            MiniApp,
        ).where(MiniApp.id == app_id)

        if status is not None:
            stmt = stmt.values(
                status=status,
            )

        if moderation_status is not None:
            stmt = stmt.values(
                moderation_status=moderation_status,
            )

        if name is not None:
            stmt = stmt.values(
                name=name,
            )

        if is_mobile is not None:
            stmt = stmt.values(
                is_mobile=is_mobile,
            )

        if is_web is not None:
            stmt = stmt.values(
                is_web=is_web,
            )

        if description is not None:
            stmt = stmt.values(
                description=description,
            )

        if icon is not None:
            stmt = stmt.values(
                icon=icon,
            )

        if cover is not None:
            stmt = stmt.values(
                cover=cover,
            )

        if version is not None:
            stmt = stmt.values(
                version=version,
            )

        if url is not None:
            stmt = stmt.values(
                url=url,
            )

        await self._session.execute(
            stmt,
        )
        await self._session.commit()
        return await self.get_by_id_or_raise(
            app_id,
        )

    async def delete(self, app: MiniAppDTO):
        await self._session.execute(
            delete(MiniApp).where(
                MiniApp.id == app.id,
            )
        )
        await self._session.commit()


class MiniAppInteractionsRepo:
    def __init__(
        self,
        mini_apps_repo: MiniAppRepo,
        session: AsyncSession,
    ):
        """
        Initializes `self`
        :param mini_apps_repo: An initialized mini-apps repository
        :param session: An `AsyncSession` instance
        """
        self._mini_app_repo = mini_apps_repo
        self._session = session

    async def _get_record(
        self,
        author_id: str,
    ) -> AuthorAppInteraction:
        """
        Returns record which contains `ARRAY` of app-ids
        :param author_id: Author's identifier
        :return: `AuthorAppInteraction` instance
        """
        # In this case, we're convinced that we have one record per one user
        record = (
            (
                await self._session.execute(
                    select(AuthorAppInteraction).where(
                        AuthorAppInteraction.author_id == author_id
                    )
                )
            )
            .scalars()
            .first()
        )

        # Check for record existence
        if record is None:
            raise RuntimeError("Record at `author_app_interaction` does not exist.`")

        return record

    async def create(
        self,
        author_id: str,
        app_id: str,
    ):
        """
        Updates record in db (adds `APP_ID` to `ARRAY`)
        :param author_id: Author's identifier
        :param app_id: App's identifier
        :return:
        """

        record = await self._get_record(author_id)

        if record.apps is None:
            record.apps = []

        if app_id in record.apps:
            # Skip if app already exists
            record.apps.remove(app_id)
        # Pop first element if length of array is equal to 10
        if len(record.apps) == MINI_APP_INTERACTIONS_HISTORY_LIMIT:
            record.apps.pop(0)

        # Push app
        record.apps.append(app_id)

        await self._session.execute(
            update(AuthorAppInteraction)
            .where(
                AuthorAppInteraction.author_id == author_id,
            )
            .values(
                apps=record.apps,
            )
        )

        await self._session.flush()
        await self._session.commit()

    async def fetch(
        self,
        author_id: str,
    ) -> Page[MiniAppDTO]:
        """
        Fetches all recently apps
        :param author_id: Author's identifier
        :return: `Page[MiniAppDTO]` obj
        """
        record = await self._get_record(author_id)
        needed_apps_ids = record.apps

        result = []

        for app_id in needed_apps_ids[::-1]:
            app = await self._mini_app_repo.get_by_id_or_raise(
                app_id,
            )

            result.append(app)

        return Page(
            page_size=10,
            page=1,
            total_pages=1,
            total_items=len(needed_apps_ids),
            items=result,
        )
