from typing import Generic, List

from pydantic import BaseModel

from src.mini_apps.dto import T
from src.common.mini_apps.enums import MiniAppStatus, MiniAppModerationStatus


class MiniAppCreationRequest(BaseModel):
    name: str
    description: str
    is_web: bool
    is_mobile: bool
    version: str
    icon: str
    cover: str
    url: str


class MiniAppSchema(BaseModel):
    id: str
    name: str
    description: str
    author_id: str
    is_web: bool
    is_mobile: bool
    version: str
    url: str
    likes_count: int
    comments_count: int
    tags: List[str]
    icon: str
    cover: str
    status: MiniAppStatus
    moderation_status: MiniAppModerationStatus


class MiniAppUpdateRequest(BaseModel):
    name: str | None = None
    description: str | None = None
    version: str | None = None
    is_web: bool | None = None
    is_mobile: bool | None = None
    icon: str | None = None
    url: str | None = None
    cover: str | None = None
    status: MiniAppStatus | None = None
    moderation_status: MiniAppModerationStatus | None = None


class Page(BaseModel, Generic[T]):
    items: List[T]
    page: int
    total_items: int
    total_pages: int
