import uuid
from dataclasses import dataclass
from datetime import datetime

from src.database.models import Comment


@dataclass
class CommentOutDTO:
    """Created comment."""

    id: str
    text: str

    parent_id: str

    comments_count: int
    likes_count: int

    created_at: datetime
    updated_at: datetime

    type: str
    region: str
    author_id: str
    is_liked: bool | None

    @classmethod
    def from_alchemy(
        cls,
        comment_obj: Comment,
    ) -> 'CommentOutDTO':
        return CommentOutDTO(
            id=comment_obj.id,
            text=comment_obj.text,
            parent_id=comment_obj.parent_id,
            comments_count=comment_obj.comments_count,
            likes_count=comment_obj.likes_count,
            created_at=comment_obj.created_at,
            updated_at=comment_obj.updated_at,
            type=comment_obj.type,
            region=comment_obj.region,
            author_id=comment_obj.author_id,
            is_liked=getattr(comment_obj, "is_liked", None)
        )


@dataclass
class CreatedCommentDTO(CommentOutDTO):
    points_granted: float
