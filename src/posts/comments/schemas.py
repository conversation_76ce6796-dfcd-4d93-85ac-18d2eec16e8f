from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, ConfigDict

from src.database.constants import PostStatus
from src.database.schemas import CommentRead


class CommentCreateRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True, extra="allow")

    type: Literal["Comment"] = "Comment"

    text: str
    parent_id: str
    status: PostStatus = PostStatus.POSTED


class CreatedCommentSchema(CommentRead):
    points_granted: float = 0.0


