from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, ConfigDict

from src.database.constants import PostStatus
from src.database.schemas import CommentRead
from src.posts.base_schemas import BasePost

class Comment(BaseModel):
    type: Literal["Comment"] = "Comment"
    text: str
    parent_id: str
    status: PostStatus

    author_id: str
    likes: List[str]  # Store author IDs instead of Author objects
    is_liked: Optional[bool]

    comments_count: Optional[int]
    comments: List["CommentRead"]

    class Config:
        from_attributes = True

Comment.update_forward_refs()
