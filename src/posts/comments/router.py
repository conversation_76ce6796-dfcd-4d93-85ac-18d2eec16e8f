from fastapi import APIRouter, status, Depends, HTTPException, Path
from fastapi_pagination import add_pagination

from src.auth import current_user, optional_user
from src.database.models import User
from src.database.schemas import CommentRead
from src.posts.comments.dependencies import get_comment_service
from src.posts.comments.dto import CommentOutDTO
from src.posts.comments.exceptions import (
    InvalidParentPostID,
    AccessDenied,
    CommentNotFound,
)
from src.posts.comments.schemas import CommentCreateRequest, CreatedCommentSchema
from src.posts.comments.service import CommentService

router = APIRouter(
    prefix="/comments", tags=["Comments"], dependencies=[Depends(current_user)]
)
add_pagination(router)


@router.post(
    "/",
    summary="Create comment",
    status_code=status.HTTP_201_CREATED,
    response_model=CreatedCommentSchema,
)
async def create_comment(
    data: CommentCreateRequest,
    user: User = Depends(current_user),
    comment_service: CommentService = Depends(get_comment_service),
):
    """Put a comment"""

    try:
        created_comment = await comment_service.create_comment(
            text=data.text,
            parent_post_id=data.parent_id,
            current_user=user,
            status=data.status,
        )
    except InvalidParentPostID:
        raise HTTPException(
            detail="Invalid parent-id provided.",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        )

    return created_comment


@router.get("/{comment_id}", summary="Get concrete comment", response_model=CommentRead)
async def get_comment_by_id(
    comment_id: str = Path(
        title="Comment-ID",
        description="An ID of concrete comment",
    ),
    comment_service: CommentService = Depends(get_comment_service),
    user: User | None = Depends(optional_user)
):
    try:
        needed_comment = await comment_service.get_comment(
            comment_id=comment_id,
            user_id=getattr(user, "id", None)
        )
    except CommentNotFound as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.args[0],
        )

    return needed_comment


@router.delete(
    "/{comment_id}",
    summary="Delete comment",
    response_model=CommentRead,
)
async def delete_comment(
    needed_comment: CommentOutDTO = Depends(get_comment_by_id),
    user: User = Depends(current_user),
    comment_service: CommentService = Depends(get_comment_service),
):
    try:
        deleted_comment = await comment_service.delete_comment(
            needed_comment,
            user,
        )
    except AccessDenied as e:
        raise HTTPException(
            detail=e.args[0],
            status_code=status.HTTP_403_FORBIDDEN,
        )

    return deleted_comment
