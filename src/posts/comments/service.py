import logging

from sqlalchemy import select, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectin_polymorphic

from src.common.notifications.client import NotificationsClient
from src.common.points.enums import PointsAction
from src.common.notifications.enums import NotificationType
from src.common.points.service import PointService
from src.common.post_service import PostService
from src.database.constants import PostType, PostStatus
from src.database.models import User, Post, Comment, post_models
from src.posts.comments.dto import CommentOutDTO, CreatedCommentDTO
from src.posts.comments.exceptions import (
    InvalidParentPostID,
    CommentNotFound,
    AccessDenied,
)

logger = logging.getLogger(__name__)


class CommentService:
    def __init__(
        self,
        session: AsyncSession,
        notifications_client: NotificationsClient,
        point_service: PointService,
        post_service: PostService,
    ):
        self._session = session
        self._post_service = post_service
        self._point_service = point_service
        self._notifications_client = notifications_client

    async def _validate_post_id(
        self,
        post_id: str,
    ) -> Post:
        stmt = self._post_service.get_one(
            post_id,
        ).options(selectin_polymorphic(Post, post_models))

        result = await self._session.execute(
            stmt,
        )

        first_one = result.scalars().first()

        if first_one is None:
            raise InvalidParentPostID(f"Invalid parent post ID = {post_id}")

        return first_one  # type: ignore

    async def get_comment(
        self, comment_id: str, user_id: str | None = None
    ) -> CommentOutDTO:
        results = await self._session.execute(
            select(Comment)
            .where(Comment.id == comment_id)
            .options(self._post_service.is_liked_expression(user_id=user_id))
        )

        first_one = results.scalars().first()

        if first_one is None:
            raise CommentNotFound(f"Comment with ID = {comment_id} not found.")

        return CommentOutDTO.from_alchemy(
            first_one,
        )

    async def _notify_parent_author(
        self,
        created_comment: Comment,
        current_user: User,
        parent_post: Post,
    ):
        if current_user.id == parent_post.author_id or (
            parent_post.type
            not in [PostType.ARTICLE, PostType.IMAGE, PostType.ANSWER, PostType.COMMENT]
        ):
            # Notify only if reply created and only on selected content-types.
            return

        await self._notifications_client.notify(
            recipients_ids=[parent_post.author_id],
            notification_type=NotificationType.post_comment,
            meta={
                "parent_id": parent_post.id,
                "parent_type": parent_post.type,
                "parent_cover": getattr(parent_post, "cover", None),
                "parent_title": getattr(parent_post, "title", None),
                "comment_id": created_comment.id,
                "comment_text": created_comment.text,
                "comment_author_id": created_comment.author_id,
                "comment_author_avatar": created_comment.author.avatar,
                "comment_author_username": created_comment.author.username,
                "comment_author_name": created_comment.author.name,
            },
        )

    async def _issue_reward(
        self,
        created_comment: Comment,
        parent_post: Post,
        current_user: User,
    ) -> float:
        reward = 5.0

        if len(created_comment.text) > 50:
            # If length of text more than 50
            reward = 10.0

        await self._point_service.add_points(
            current_user.id,
            reward,
            PointsAction.COMMENT,
            {
                "post_id": parent_post.id,
                "post_type": parent_post.type,
                "author_id": parent_post.author_id,
                "cover": getattr(parent_post, "cover", None),
                "description": getattr(parent_post, "description", None),
                "url": getattr(parent_post, "url", None),
            },
        )

        return reward

    async def _create_model(
        self,
        text: str,
        parent_post: Post,
        current_user: User,
        status: PostStatus,
    ) -> Comment:
        comment_obj = Comment(
            parent_id=parent_post.id,
            region=parent_post.region,
            author_id=current_user.id,
            text=text,
            status=status,
        )

        self._session.add(comment_obj)
        await self._session.flush()
        await self._session.refresh(comment_obj)
        await self._session.flush()  # 使用 flush 而不是 commit，保持事务开启

        return comment_obj

    async def _increase_comments_count(
        self,
        parent_post_id: str,
    ):
        await self._session.execute(
            update(Post)
            .where(Post.id == parent_post_id)
            .values(comments_count=Post.comments_count + 1)
        )

    async def _decrease_comments_count(self, parent_post_id: str):
        await self._session.execute(
            update(Post)
            .where(Post.id == parent_post_id)
            .values(comments_count=Post.comments_count - 1)
        )

    async def create_comment(
        self,
        parent_post_id: str,
        current_user: User,
        text: str,
        status: PostStatus,
    ) -> CreatedCommentDTO:
        # Get parent post, we need `region` & creating notification if new comment is `reply`
        parent_post = await self._validate_post_id(
            parent_post_id,
        )

        # Create sqlalchemy's raw object
        comment_obj = await self._create_model(
            text,
            parent_post,
            current_user,
            status,
        )

        # Increase comments-count of parent-post.
        await self._increase_comments_count(parent_post_id)

        # Add points to user
        points_granted = await self._issue_reward(
            comment_obj,
            parent_post,
            current_user,
        )

        try:
            # Notify author of parent comment/post if needed.
            await self._notify_parent_author(
                comment_obj,
                current_user,
                parent_post,
            )
        except Exception as e:
            logger.error(
                "Error during sending push-notification",
                exc_info=e,
            )

        return CreatedCommentDTO(
            id=comment_obj.id,
            text=comment_obj.text,
            parent_id=comment_obj.parent_id,
            type=comment_obj.type,
            created_at=comment_obj.created_at,
            updated_at=comment_obj.updated_at,
            region=comment_obj.region,
            author_id=comment_obj.author_id,
            likes_count=comment_obj.likes_count,
            comments_count=comment_obj.comments_count,
            points_granted=points_granted,
            is_liked=comment_obj.is_liked,
        )

    async def delete_comment(
        self,
        comment: CommentOutDTO,
        current_user: User,
    ) -> CommentOutDTO:
        # Check for `current_user` is author

        if current_user.id != comment.author_id:
            raise AccessDenied("Only author can delete comment.")

        await self._session.execute(delete(Post).where(Post.id == comment.id))

        # Decrease comment-count of parent post
        await self._decrease_comments_count(
            comment.parent_id,
        )

        await self._session.flush()  # 使用 flush 而不是 commit，保持事务开启

        # Return given comment
        return comment
