from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.notifications.client import Notifications<PERSON><PERSON>
from src.common.notifications.dependencies import get_notification_client
from src.common.points.service import PointService, get_points_service
from src.common.post_service import PostService, get_post_service
from src.database.session import get_session
from src.posts.comments.service import CommentService


def get_comment_service(
    point_service: PointService = Depends(get_points_service),
    notifications_client: NotificationsClient = Depends(get_notification_client),
    session: AsyncSession = Depends(get_session),
    post_service: PostService = Depends(get_post_service),
) -> CommentService:
    """Get initialized `CommentService`"""
    return CommentService(
        session=session,
        notifications_client=notifications_client,
        point_service=point_service,
        post_service=post_service,
    )
