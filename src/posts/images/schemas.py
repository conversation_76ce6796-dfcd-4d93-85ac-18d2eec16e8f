from datetime import datetime
from typing import Literal, Optional, List, Dict, Any

from pydantic import BaseModel, ConfigDict, Field, computed_field, field_serializer, field_validator

from src.common.constants import FeedReason, PostStatus
from src.common.utils import format_cents_to_usd_string
from src.database.models import Comment as SQLAComment
from src.database.schemas.Author import AuthorRead
from src.database.schemas.Collection import CollectionRead
from src.database.schemas.Comment import CommentRead
from src.posts.comments.schemas_comments import Comment


class ImageBase(BaseModel):
    type: Literal["Image"] = "Image"

    title: Optional[str]
    cover: str
    description: Optional[str]
    tags: List[str]
    status: PostStatus
    width: int
    height: int

    class Config:
        from_attributes = True


class ImageItem(BaseModel):
    width: Optional[int] = 0
    height: Optional[int] = 0
    name: Optional[str] = ""
    content_type: Optional[str] = ""


class ImageDataStored(BaseModel):
    """Image data stored in database (without upload_url)"""
    url: str
    width: int
    height: int
    name: str
    content_type: str


class ImageData(BaseModel):
    """Complete image data (including URL)"""
    url: str
    upload_url: str
    width: int
    height: int
    name: str
    content_type: str


class ImageCreateRequest(BaseModel):
    title: Optional[str]
    description: str
    images: List[ImageItem] = Field(..., min_length=1, description="Image list, must contain at least one image")
    tags_list: Optional[List[str]] = None
    upload_url_expired_seconds: Optional[int] = 5 * 60
    status: PostStatus = PostStatus.POSTED
    holdview_amount: Optional[int] = 0
    # Optional token address bound to this post
    binding_token: Optional[str] = None


class ImageCreate(ImageCreateRequest):
    author_id: str


class ImageUpdate(BaseModel):
    title: Optional[str]
    description: Optional[str]
    cover: Optional[str]
    tags_list: List[str]

    created_at: Optional[datetime]
    status: Optional[PostStatus]


class ImageRead(ImageBase):
    id: str

    created_at: datetime
    updated_at: datetime

    comments_count: int
    likes_count: Optional[int]
    collections_count: int

    region: str
    interaction_rating: float

    images_data: List[ImageDataStored] = Field(default=[], description="Multiple images data (without upload_url)")
    
    # Holdview field
    is_locked: Optional[bool] = False
    holdview_amount_int: Optional[int] = Field(default=0, alias="holdview_amount")
    # When locked, return token info under key "binding_token"
    binding_token_info: Optional[Dict[str, str]] = None

    @field_validator('images_data', mode='before')
    @classmethod
    def validate_images_data(cls, v):
        """Handle None values from database and convert to empty list"""
        if v is None:
            return []
        return v

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

    @computed_field  # type: ignore[misc]
    @property
    def holdview_amount(self) -> str:
        return format_cents_to_usd_string(self.holdview_amount_int)


class ImageCreateResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    cover: str
    images_data: List[ImageData]
    total_images: int
    status: PostStatus
    created_at: datetime


class Image(ImageRead):
    author: AuthorRead
    comments: List[Comment]
    likes: List[AuthorRead]

    # model_config = ConfigDict(from_attributes=True, arbitrary_types_allowed=True)


class ImageFeed(Image):
    is_in_collection: Optional[bool]
    is_liked: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []
    collections: "List[CollectionRead]" = []
    subscribed_collections: "List[CollectionRead]" = []


ImageFeed.model_rebuild()
