from fastapi import Depends, status, Request
from fastapi.routing import APIRouter
from fastapi.responses import Response
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate

from src.auth.dependecies import current_user
from src.database.models import Image, User
from src.database.session import AsyncSession, get_session
from src.auth import optional_user
from src.posts.images.schemas import ImageCreateRequest, ImageCreateResponse
from ..logger import logger

from ..schemas import ImageSchema
from .service import ImageService, get_image_service
from src.common.elasticsearch.sync_client import create_es_sync_client
from src.common.holdview import get_holdview_service, HoldviewService
from src.common.simple_image_proxy_dependencies import get_simple_image_proxy
from src.common.simple_image_proxy import SimpleImageProxy
from sqlalchemy import select
from src.database.models.Pair import Pair
from src.posts.collections.schemas import CommitCreate
from src.posts.collections.commit_legacy import CommitService, get_commit_service


router = APIRouter(prefix="/images")
add_pagination(router)


@router.get(
    "/{post_id}",
    response_model=ImageSchema,
    status_code=status.HTTP_200_OK,
)
async def get_one(
        post_id: str,
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        image_service: ImageService = Depends(get_image_service),
        holdview_service: HoldviewService = Depends(get_holdview_service),
):
    # Get the post data
    stmt = image_service.get_one(post_id=post_id, user_id=getattr(user, "id", None))
    post = (await session.execute(stmt)).scalar_one()
    
    # Apply holdview lock and enrich token info via service helper
    post = await holdview_service.apply_holdview_lock(user, post)
    
    return post


@router.get(
    "",
    response_model=Page[ImageSchema],
    status_code=status.HTTP_200_OK,
)
async def get_all(
        author_id: str | None = None,
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        image_service: ImageService = Depends(get_image_service),
        holdview_service: HoldviewService = Depends(get_holdview_service),
):
    stmt = image_service.get_all(user_id=getattr(user, "id", None))
    if author_id:
        stmt = stmt.where(Image.author_id == author_id)
    page = await paginate(session, stmt, params)
    # Apply holdview locks to page items
    page.items = await holdview_service.apply_holdview_locks(user, list(page.items))
    return page

@router.post(
    "/create",
    response_model=ImageCreateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_image(
    create_data: ImageCreateRequest,
    user: User = Depends(current_user),
    service: ImageService = Depends(get_image_service),
    commit_service: CommitService = Depends(get_commit_service),
):
    result = await service.create(user.id, user.region, create_data)
    await service.session.commit()
    
    # 异步同步到 Elasticsearch
    es_sync_client = create_es_sync_client("images_service")
    es_sync_client.sync_image(str(result["id"]), "create", priority=5)
    
    # 若带有 binding_token，则尝试将帖子提交到对应的 collection
    try:
        token = getattr(create_data, "binding_token", None)
        if token:
            stmt = select(Pair.collection_id).where(Pair.base == token)
            col_res = await commit_service.session.execute(stmt)
            collection_id = col_res.scalar_one_or_none()
            if collection_id:
                commit_data = CommitCreate.model_validate({
                    "collection_id": collection_id,
                    "post_id": str(result["id"]),
                    "author_id": user.id,
                })
                commit = await commit_service.create(commit_data)
                # 若当前用户是集合作者将自动通过，否则忽略审批错误，保留为待审批
                try:
                    await commit_service.approve(commit.id)
                except Exception as e:
                    # print stack trace
                    logger.exception(f"Failed to approve commit： {str(e)}")
                await commit_service.session.commit()
    except Exception as e:
        # 提交失败不影响发帖主流程
        logger.exception(f"Failed to commit to collection： {str(e)}")
    
    return ImageCreateResponse(**result)


@router.get("/proxy")
async def image_proxy(
    request: Request,
    user: User = Depends(optional_user),
    holdview_service: HoldviewService = Depends(get_holdview_service),
    image_proxy: SimpleImageProxy = Depends(get_simple_image_proxy)
):
    """
    Image proxy endpoint using dependency injection.
    Handles blurred content serving based on user access permissions.
    """
    try:
        # Extract parameters from request
        params = dict(request.query_params)
        
        # Validate parameters and extract post_id  
        is_valid, extracted = image_proxy.validate_and_extract(params)
        if not is_valid:
            return Response(status_code=400, content="Invalid request parameters")
        
        post_id = extracted['post_id']
        
        # Check user access through holdview service
        user_id = str(user.id) if user else None
        has_access = await holdview_service.check_access(post_id, user_id)
        
        # Process image request
        image_bytes, content_type, cache_headers = await image_proxy.process_image_request(params, has_access)
        
        # Return image response with appropriate headers
        return Response(
            content=image_bytes,
            media_type=content_type,
            headers=cache_headers
        )
        
    except Exception as e:
        return Response(status_code=500, content=f"Proxy error: {str(e)}")
