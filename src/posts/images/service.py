from fastapi import Depends
from sqlalchemy import update
from src.database.models import Author
from src.database.models.Image import Image
from src.common.post_service import PostService
from src.database.session import get_session
from src.media_core.dependencies import get_cloudflare_service
from src.media_core.services import CloudflareService
from src.posts.images.schemas import ImageCreateRequest, ImageItem, ImageData
from sqlalchemy.ext.asyncio import AsyncSession
from src.posts.logger import logger
from src.authors.service import AuthorService, get_author_service
from src.common.recommender.client import RecommenderClient, get_recommender_client
from typing import List, Dict, Any
import json


class ImageService(PostService):
    model = Image

    def __init__(self, session: AsyncSession, cf: CloudflareService, author_service: AuthorService, recommender_client: RecommenderClient | None):
        super().__init__(session, author_service, recommender_client)
        self.cf = cf

    async def create(self, user_id: str, region: str, create_image: ImageCreateRequest) -> Dict[str, Any]:
         # Check if the session is already in a transaction
        if self.session.in_transaction():
            logger.info("Already in transaction")
            result = await self._create_image_post_logic(user_id, region, create_image)
            # 不要在嵌套事务中执行 commit，让外层事务控制
            return result
        else:
            async with self.session.begin():
                logger.info("Begin a transaction")
                result = await self._create_image_post_logic(user_id, region, create_image)
                return result

    async def _create_image_post_logic(self, user_id: str, region: str, create_data: ImageCreateRequest) -> Dict[str, Any]:

        upload_responses = []
        for image_item in create_data.images:
            response = await self.cf.get_upload_image_presigned_url(
                image_item.name, 
                image_item.content_type, 
                create_data.upload_url_expired_seconds
            )
            upload_responses.append((response, image_item))
        
        images_data = [
            {
                "url": response["file_url"],
                "width": item.width,
                "height": item.height,
                "name": item.name,
                "content_type": item.content_type
            }
            for response, item in upload_responses
        ]
        
        first_image = images_data[0] if images_data else None
        cover_url = first_image["url"] if first_image else ""
        
        # Use frontend provided tags_list
        tags_list = create_data.tags_list or []

        model = Image(
            title=create_data.title,
            cover=cover_url,
            description=create_data.description,
            tags_list=tags_list,
            width=first_image["width"] if first_image else 0,
            height=first_image["height"] if first_image else 0,
            status=create_data.status,
            author_id=user_id,
            region=region,
            images_data=images_data,
            holdview_amount=create_data.holdview_amount or 0,
            binding_token=(create_data.binding_token if getattr(create_data, 'binding_token', None) else None)
        )
      
        self.session.add(model)
        
        stmt = update(Author).where(Author.id == user_id).values(
            posts_count=Author.posts_count + 1
        )
        await self.session.execute(stmt)
        
        await self.session.flush()

        # Upsert tags and increase posts_count for tags_list
        try:
            await self._change_tags_counts(tags_list, +1)
        except Exception:
            pass

        if self.recommender_client:
            await self.recommender_client.add_post(model)

        response_images_data = [
            {**img_data, "upload_url": response["upload_url"]}
            for img_data, (response, _) in zip(images_data, upload_responses)
        ]

        return {
            "id": model.id,
            "title": model.title,
            "description": model.description,
            "cover": model.cover,
            "images_data": response_images_data,
            "total_images": len(response_images_data),
            "status": model.status,
            "created_at": model.created_at
        }


def get_image_service(
        session: AsyncSession = Depends(get_session),
        cf: CloudflareService = Depends(get_cloudflare_service),
        author_service: AuthorService = Depends(get_author_service),
        recommender_client: RecommenderClient | None = Depends(get_recommender_client)
):
    yield ImageService(session, cf, author_service, recommender_client)