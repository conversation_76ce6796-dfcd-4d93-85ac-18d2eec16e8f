import logging

logger = logging.getLogger("posts")
logger.setLevel("DEBUG")
fh = logging.FileHandler("latest.log")
ch = logging.StreamHandler()

formatter = logging.Formatter(
    "[%(asctime)s.%(msecs)03d][%(name)s][%(levelname)s][%(pathname)s:%(lineno)d]: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S")

fh.setFormatter(formatter)
ch.setFormatter(formatter)

logger.addHandler(ch)
logger.addHandler(fh)
