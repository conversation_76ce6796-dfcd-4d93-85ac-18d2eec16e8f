from typing import Optional, List, Literal
from datetime import datetime
import asyncio

from fastapi import Fast<PERSON><PERSON>, Query, Depends, status, HTTPException, Request
from fastapi.openapi.utils import get_openapi
from fastapi.responses import J<PERSON>NResponse
from fastapi_pagination import add_pagination, Params, Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import select, update, insert, or_, and_, delete, text
from sqlalchemy.orm import selectinload, with_expression, with_polymorphic, joinedload, selectin_polymorphic, aliased

from src.agora.constants import ChannelStatus
from src.common.utils import format_cents_to_usd_string
from src.database.schemas import NotificationType
from src.database.session import get_session, AsyncSession
from src.database.models import User, Post, post_models, Comment, View, Collection, Author, Commit
from src.database.models.Post import authors_likes
from src.database.models.SavedPosts import SavedPost
from src.database.constants import PostType, PostStatus
from src.auth import optional_user, current_user
from src.common.post_service import PostService, get_post_service
from src.common.dependencies import admin
from src.common.constants import CollectionContentType
from src.common.recommender import Interaction, ClickSchema, RecommenderClient
from src.common.exceptions import HTTPError
from src.infra.app import create_app
from src.infra.logger import get_logger
from src.notifications.service import NotificationService, get_notification_service

from src.posts.dependencies import valid_post_id, valid_click, recommender_client
from src.common.recommender.client import get_recommender_client
from src.posts.schemas import CommentSchema, PostWithComplaints, Click, PostUpdateRequest, CollectionSchema, \
    BatchHoldviewUpdateRequest, BatchHoldviewUpdateResponse, BatchUpdateStatus
from src.posts.collections.router import router as collections_router
from src.posts.images.router import router as images_router
from src.posts.videos.router import router as videos_router
from src.posts.files.router import router as files_router
from src.posts.complaints.router import router as complaint_router
from src.posts.collections.service import CollectionService, get_collection_service
from src.posts.collections.commit_legacy import CommitService
from src.authors.service import AuthorService, get_author_service
from src.posts.collections.schemas import CommitCreate, AddPostToCollectionsRequest, RemovePostFromCollectionsRequest
from src.posts.comments.router import router as comments_router
from src.posts.topics.router import router as topics_router
from src.posts.settings import settings
logger = get_logger("posts", level="INFO", file_path="latest.log")
from src.posts.utils import get_live_channel_info
from src.posts.balance_service import get_balance_service
from src.memecoin.services import MemeService
from src.memecoin.dependencies import get_meme_service
from src.common.redis_cli import RedisCli
from src.common.redis_cli.async_impl import AsyncRedisClient
from src.common.holdview import get_holdview_service, HoldviewService

app = create_app(title="Posts", version="1.0.0", description="This is your API", request_logger=logger)


@app.exception_handler(HTTPError)
async def http_error_handler(request: Request, exc: HTTPError):
    return JSONResponse(
        status_code=exc.error_code,
        content={"message": exc.detail},
    )


app.include_router(collections_router)
app.include_router(images_router)
app.include_router(videos_router)
app.include_router(files_router)
app.include_router(complaint_router)
app.include_router(comments_router)
app.include_router(topics_router)
## payments moved to separate microservice


add_pagination(app)



 

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}


@app.patch(
    "/batch/holdview",
    response_model=BatchHoldviewUpdateResponse,
    status_code=status.HTTP_200_OK,
)
async def batch_update_holdview(
    update_data: BatchHoldviewUpdateRequest,
    user: User = Depends(current_user),
    post_service: PostService = Depends(get_post_service)
):
    """Batch update holdview_amount for multiple posts - only owner can update their posts"""
    
    if not update_data.post_ids:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No post IDs provided")
    
    if update_data.holdview_amount < 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Holdview amount must be >= 0")
    
    # Use the service method for batch update
    result = await post_service.batch_update_holdview_amounts(
        post_ids=update_data.post_ids,
        holdview_amount=update_data.holdview_amount,
        user_id=user.id
    )
    
    # Convert service result to response format
    batch_statuses = [
        BatchUpdateStatus(
            id=post["id"],
            status=post["status"],
            error=post.get("error")
        )
        for post in result["posts"]
    ]
    
    return BatchHoldviewUpdateResponse(
        updated_count=result["updated_count"],
        posts=batch_statuses
    )


@app.get(
    "/batch",
    response_model=List[PostWithComplaints],
    status_code=status.HTTP_200_OK,
)
async def batch(
    ids: List[str] = Query(None),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(optional_user),
    post_service: PostService = Depends(get_post_service),
    collection_service: CollectionService = Depends(get_collection_service),
    holdview_service: HoldviewService = Depends(get_holdview_service),
):
    post_poly = with_polymorphic(Post, "*")
    stmt = select(post_poly).where(post_poly.id.in_(ids))

    # 调试日志：记录查询的 IDs（简化版本）
    logger.info(f"Batch query for IDs: {ids}")

    user_id = getattr(user, "id", None)
    stmt = stmt.options(
        joinedload(post_poly.author).options(
            joinedload(Author.permission),
            joinedload(Author.user)
        ),
        collection_service.subscribed_expression(user_id=user_id, polymorphic_entity=post_poly),
        post_service.is_liked_expression(user_id=user_id, polymorphic_entity=post_poly),
        post_service.in_collection_expression(user_id=user_id, polymorphic_entity=post_poly)
    )
    posts = (await session.execute(stmt)).scalars().all()

    # 编译并记录 SQL 查询（调试用）
    compiled = stmt.compile()
    logger.debug(f"Batch query SQL: {compiled}")
    
    # 在数据库会话仍然活跃时立即转换为字典，避免后续的延迟加载问题
    posts_dict = []
    for post in posts:
        # 检查并记录对象处理状态
        post_type = getattr(post, 'type', 'unknown')
        
        # 对 Collection 对象应用 ID 修复
        if post_type == 'Collection' and hasattr(post, 'fix_id_from_state'):
            post.fix_id_from_state()
            logger.info(f"Applied ID fix for Collection")
        
        post_id = post.id
        
        if post_id is None:
            logger.warning(f"Skipping post with None id, type: {post_type}")
            # 为 Collection 对象添加额外调试信息
            if post_type == 'Collection' and hasattr(post, '_sa_instance_state'):
                state = post._sa_instance_state
                identity_value = state.identity[0] if state.identity else None
                logger.warning(f"Collection SQLAlchemy identity: {identity_value}, but fix failed")
            continue
        
        # 记录成功处理的对象类型
        logger.info(f"Processing {post_type} with id: {post_id}")
            
        try:
            # 安全地获取所有属性，在会话活跃时进行
            post_dict = {
                'id': post.id,
                'type': post.type,
                'status': post.status,
                'created_at': post.created_at,
                'updated_at': post.updated_at,
                'comments_count': post.comments_count,
                'likes_count': post.likes_count,
                'collections_count': post.collections_count,
                'view_count': post.view_count,
                'region': post.region,
                'language': post.language,
                'author_id': post.author_id,
                'tags': getattr(post, 'tags', []),
                'complaint_count': getattr(post, 'complaint_count', 0),
                'holdview_amount': getattr(post, 'holdview_amount', 0),
                'binding_token': getattr(post, 'binding_token', None),
                'source': getattr(post, 'source', 'user'),
                'is_liked': getattr(post, 'is_liked', None),
                'is_in_collection': getattr(post, 'is_in_collection', None),
                'is_subscribed': getattr(post, 'is_subscribed', None),
            }
            
            # 安全地获取作者信息
            try:
                if hasattr(post, 'author') and post.author is not None:
                    author = post.author
                    # 在会话活跃时获取所有作者属性
                    post_dict['author'] = {
                        'id': author.id,
                        'username': author.username,
                        'name': author.name,
                        'avatar': author.avatar,
                        'region': getattr(author, 'region', ''),
                        'created_at': getattr(author, 'created_at', None),
                        'role': getattr(author, 'role', None),
                        'status': getattr(author, 'status', 'active'),
                        'bio': getattr(author, 'bio', None),
                        'verified': getattr(author, 'verified', False),
                        'followers_count': getattr(author, 'followers_count', 0),
                        'following_count': getattr(author, 'following_count', 0),
                    }
                else:
                    # 如果没有作者信息，提供基本信息
                    post_dict['author'] = {
                        'id': post.author_id,
                        'username': 'Unknown',
                        'name': 'Unknown',
                        'avatar': None,
                        'region': '',
                        'created_at': None,
                        'role': None,
                        'status': 'active',
                        'bio': None,
                        'verified': False,
                        'followers_count': 0,
                        'following_count': 0,
                    }
            except Exception as e:
                # 如果作者信息访问失败，提供基本信息
                logger.warning(f"Failed to access author info for post {post.id}: {str(e)}")
                post_dict['author'] = {
                    'id': post.author_id,
                    'username': 'Unknown',
                    'name': 'Unknown',
                    'avatar': None,
                    'region': '',
                    'created_at': None,
                    'role': None,
                    'status': 'active',
                    'bio': None,
                    'verified': False,
                    'followers_count': 0,
                    'following_count': 0,
                }
            
            # 安全地获取特定类型的属性
            if hasattr(post, 'title'):
                post_dict['title'] = getattr(post, 'title', None)
            if hasattr(post, 'text'):
                post_dict['text'] = getattr(post, 'text', None)
            if hasattr(post, 'description'):
                post_dict['description'] = getattr(post, 'description', None)
            if hasattr(post, 'cover'):
                post_dict['cover'] = getattr(post, 'cover', None)
            if hasattr(post, 'url'):
                post_dict['url'] = getattr(post, 'url', None)
            if hasattr(post, 'height'):
                post_dict['height'] = getattr(post, 'height', None)
            if hasattr(post, 'width'):
                post_dict['width'] = getattr(post, 'width', None)
            if hasattr(post, 'content_type'):
                post_dict['content_type'] = getattr(post, 'content_type', None)
            if hasattr(post, 'contents_count'):
                post_dict['contents_count'] = getattr(post, 'contents_count', None)
            if hasattr(post, 'original_cover'):
                post_dict['original_cover'] = getattr(post, 'original_cover', None)
            if hasattr(post, 'contents'):
                post_dict['contents'] = getattr(post, 'contents', [])
            
            posts_dict.append(post_dict)
        except Exception as e:
            # 如果转换失败，记录错误但继续处理其他帖子
            logger.warning(f"Failed to convert post {getattr(post, 'id', 'unknown')} to dict: {str(e)}")
            continue
    
    # 在转换为字典后，使用字典数据应用 holdview 锁
    # 这样可以避免在 ORM 对象上触发异步操作
    try:
        # 创建一个使用相同会话的 holdview service 实例
        from src.common.redis_cli import RedisCli
        redis_client = RedisCli.async_()
        holdview_service_with_session = HoldviewService(session, redis_client)
        
        # 获取访问权限结果 - 使用字典数据而不是 ORM 对象
        user_id = getattr(user, "id", None)
        
        # 处理匿名用户
        if user_id is None:
            for post_dict in posts_dict:
                holdview_amount = post_dict.get('holdview_amount', 0)
                post_dict['is_locked'] = holdview_amount > 0
        else:
            # 处理已登录用户
            # 分离需要检查的帖子
            gated_post_ids = []
            for post_dict in posts_dict:
                holdview_amount = post_dict.get('holdview_amount', 0)
                author_id = post_dict.get('author_id')
                
                # 如果帖子有 holdview 要求且不是作者自己的帖子
                if holdview_amount > 0 and author_id != user_id:
                    gated_post_ids.append(post_dict['id'])
                    post_dict['is_locked'] = True  # 默认锁定
                else:
                    post_dict['is_locked'] = False  # 公开帖子或作者自己的帖子
            
            # 如果有需要检查的帖子，批量查询购买记录
            if gated_post_ids:
                try:
                    # 使用 holdview service 的批量查询方法
                    user_purchases = await holdview_service_with_session._get_user_purchases_batch(str(user_id), gated_post_ids)
                    
                    # 更新访问权限
                    for post_dict in posts_dict:
                        post_id = post_dict['id']
                        if post_id in gated_post_ids:
                            has_access = user_purchases.get(post_id, False)
                            post_dict['is_locked'] = not has_access
                except Exception as e:
                    logger.error(f"Error checking user purchases: {str(e)}")
                    # 出错时保持默认的锁定状态
        
        # 对锁定的帖子应用内容保护
        for post_dict in posts_dict:
            if post_dict.get('is_locked', False):
                # 应用高斯模糊到图片内容
                if post_dict.get('cover'):
                    post_dict['cover'] = f"{post_dict['cover']}?blur=10"
                if post_dict.get('original_cover'):
                    post_dict['original_cover'] = f"{post_dict['original_cover']}?blur=10"
                
                # 隐藏敏感内容
                if post_dict.get('url') and post_dict.get('type') in ['Video', 'Image']:
                    post_dict['url'] = None
                
                # 添加绑定代币信息（可选，不影响主要功能）
                try:
                    post_id = post_dict['id']
                    token_info = await holdview_service_with_session.get_post_binding_token_info(post_id)
                    if token_info:
                        post_dict['binding_token_info'] = token_info
                except Exception:
                    # 忽略代币信息获取错误
                    pass
                    
    except Exception as e:
        logger.error(f"Error applying holdview locks: {str(e)}")
        # 出错时，将所有帖子标记为未锁定
        for post_dict in posts_dict:
            post_dict['is_locked'] = False
    
    return posts_dict


@app.get(
    "/",
    status_code=status.HTTP_200_OK
)
async def get_posts(
        author_id: Optional[str] = None,
        types: List[str] = Query(None),
        sort: str = "new",
        holdview: Literal["all", "public", "premium"] = "all",
        params: Params = Depends(),
        user: User = Depends(optional_user),
        session: AsyncSession = Depends(get_session),
        collection_service: CollectionService = Depends(get_collection_service),
        post_service: PostService = Depends(get_post_service),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    if user is None and author_id is None:
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, "Neither autor ID nor authorized user are present"
        )

    # Start fetching live channel info early if author_id is provided
    live_channel_task = None
    if author_id:
        live_channel_task = asyncio.create_task(get_live_channel_info(author_id))

    if not types:
        types = [
            PostType.IMAGE,
            PostType.VIDEO,
        ]

    collection_alias = aliased(Collection)
    stmt = select(Post).outerjoin(collection_alias, Post.id == collection_alias.id).where(
        Post.status == PostStatus.POSTED,
        Post.type.in_(types),
        Post.author_id == author_id,
        or_(
            Post.type == PostType.IMAGE,
            Post.type == PostType.VIDEO,
            and_(
                Post.type == PostType.COLLECTION,
                collection_alias.content_type == CollectionContentType.MIXED
            )
        )
    )

    # Apply holdview filter if requested
    if holdview == "public":
        stmt = stmt.where(Post.holdview_amount == 0)
    elif holdview == "premium":
        stmt = stmt.where(Post.holdview_amount > 0)

    if sort == "new":
        stmt = stmt.order_by(Post.created_at.desc())
    else:
        stmt = stmt.order_by(Post.created_at.asc())

    user_id = getattr(user, "id", None)
    stmt = stmt.options(
        selectin_polymorphic(Post, post_models),
        selectinload(Post.author),
        collection_service.subscribed_expression(user_id=user_id),
        post_service.is_liked_expression(user_id=user_id),
        post_service.in_collection_expression(user_id=user_id)
    )

    # Get paginated posts
    paginated_result = await paginate(session, stmt, params)

    # Apply holdview locks to items unless explicitly requesting public-only posts
    if holdview != "public":
        paginated_result.items = await holdview_service.apply_holdview_locks(
            user, list(paginated_result.items)
        )

    for item in paginated_result.items:
        item.holdview_amount = format_cents_to_usd_string(item.holdview_amount)

    # If we started a live channel task, wait for it
    live_channel_info = None
    if live_channel_task:
        live_channel_info = await live_channel_task

    # Convert paginated result to dict and add live channel info
    response = paginated_result.dict()
    if live_channel_info and live_channel_info.get("status") == ChannelStatus.LIVE:
        response['live_channel_info'] = live_channel_info

    return response


@app.get(
    "/{post_id}/comments",
    response_model=Page[CommentSchema],
    status_code=status.HTTP_200_OK,
)
async def get_comments(
    post_id: str,
    params: Params = Depends(),
    user: User = Depends(optional_user),
    session: AsyncSession = Depends(get_session),
):
    stmt = select(Comment).where(Comment.parent_id == post_id)
    if user:
        subq_liked = select(authors_likes.c.post_id).where(
            authors_likes.c.author_id == user.id
        )
        stmt = stmt.options(
            with_expression(Comment.is_liked, Comment.id.in_(subq_liked))
        )
    stmt = stmt.order_by(Comment.created_at.desc())
    return await paginate(session, stmt, params)


@app.get(
    "/with_complaints",
    status_code=status.HTTP_200_OK,
    response_model=Page[PostWithComplaints],
)
async def posts_with_complaints(
    author_id: Optional[str] = None,
    types: List[str] = Query(None),
    sort: Literal["new", "old", "most", "least"] = "most",
    params: Params = Depends(),
    user: User = Depends(admin),
    session: AsyncSession = Depends(get_session),
    post_service: PostService = Depends(get_post_service),
    holdview_service: HoldviewService = Depends(get_holdview_service)
):
    if not types:
        types = [
            PostType.ARTICLE,
            PostType.QUESTION,
            PostType.ANSWER,
            PostType.COLLECTION,
            PostType.TEXT,
            PostType.IMAGE,
            PostType.VIDEO,
        ]
    stmt = select(Post).where(
        Post.complaint_count > 0, Post.type.in_(types), Post.status == PostStatus.POSTED
    )
    if author_id:
        stmt = stmt.where(Post.author_id == author_id)
    match sort:
        case "most":
            stmt = stmt.order_by(Post.complaint_count.desc())
        case "least":
            stmt = stmt.order_by(Post.complaint_count.asc())
        case "new":
            # TODO: think how to do this
            stmt = stmt.order_by(Post.complaint_count.desc())
        case "old":
            # TODO: think how to do this
            stmt = stmt.order_by(Post.complaint_count.desc())
    result = await paginate(
        session,
        stmt.options(
            selectin_polymorphic(Post, post_models),
            selectinload(Post.author),
            post_service.is_liked_expression(user_id=user.id),
            post_service.in_collection_expression(user_id=user.id)
        ),
        params,
    )
    # Apply holdview locks to items
    result.items = await holdview_service.apply_holdview_locks(user, list(result.items))
    return result


@app.post(
    "/view",
    status_code=status.HTTP_201_CREATED,
)
async def add_view(
    post_id: str = Depends(valid_post_id),
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session)
):
    stmt = (
        select(View)
        .where(View.user_id == user.id, View.post_id == post_id)
        .order_by(View.updated_at.desc())
    )
    view = (await session.execute(stmt)).first()
    if view and (datetime.utcnow() - view[0].updated_at).total_seconds() <= settings.VIEW_FREQUENCY:
        raise HTTPException(status_code=status.HTTP_429_TOO_MANY_REQUESTS)

    stmt = update(Post).where(Post.id == post_id).values(view_count=Post.view_count + 1)
    await session.execute(stmt)
    if view is not None:
        stmt = (
            update(View)
            .where(
                View.post_id == post_id,
                View.user_id == user.id
            )
            .values(updated_at=datetime.utcnow())
        )
    else:
        stmt = insert(View).values(post_id=post_id, user_id=user.id)
    await session.execute(stmt)
    await session.commit()

    recommender_view = Interaction(user_id=user.id, post_id=post_id)
    await recommender_client.add_view(view=recommender_view)


@app.post(
    "/impression",
    status_code=status.HTTP_201_CREATED
)
async def add_impression(
    post_id: str = Depends(valid_post_id),
    user: User = Depends(current_user),
):
    impression = Interaction(post_id=post_id, user_id=user.id)
    await recommender_client.add_impression(impression=impression)


@app.post(
    "/share",
    status_code=status.HTTP_201_CREATED
)
async def add_share(
    post_id: str = Depends(valid_post_id),
    user: User = Depends(current_user),
):
    share = Interaction(post_id=post_id, user_id=user.id)
    await recommender_client.add_share(share=share)


@app.post(
    "/click",
    status_code=status.HTTP_201_CREATED
)
async def add_click(
    click: Click = Depends(valid_click),
    user: User = Depends(current_user)
):
    recommender_click = ClickSchema(
        post_id=click.post_id,
        user_id=user.id,
        source=click.source,
        current_recommendations=click.current_recommendations
    )
    await recommender_client.add_click(click=recommender_click)


@app.post(
    "/upvote/{post_id}",
    tags=["post"],
    status_code=status.HTTP_200_OK
)
async def upvote(
        post_id: str,
        user: User = Depends(current_user),
        session: AsyncSession = Depends(get_session),
        post_service: PostService = Depends(get_post_service),
        notification_service: NotificationService = Depends(get_notification_service)
):
    post: Post = await post_service.get(post_id)
    if post is None:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    if await post_service.is_author(post_id, user.id):
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    await post_service.upvote(post_id, user.id)
    
    author: Optional[Author] = await session.get(Author, user.id)

    await notification_service.create_notification(
        notification_type=NotificationType.post_upvote,
        recipients_ids=[post.author_id],
        meta={
            "post_id": post.id,
            "post_type": post.type,
            "post_title": getattr(post, "title", None),
            "post_cover": getattr(post, "cover", None),
            "author_id": user.id,
            "author_avatar": author.avatar,
            "author_username": author.username,
            "author_name": author.name
        }
    )
    return {
        "success": True
    }


@app.delete(
    "/{post_id}",
    tags=["post"],
    status_code=status.HTTP_200_OK
)
async def delete_post(
    post_id: str,
    user: User = Depends(current_user),
    post_service: PostService = Depends(get_post_service)
):
    """删除帖子 - 只支持Image和Video类型"""
    result = await post_service.delete(post_id, user.id)
    return result


@app.put(
    "/{post_id}",
    tags=["post"],
    status_code=status.HTTP_200_OK
)
async def update_post(
    post_id: str,
    update_data: PostUpdateRequest,
    user: User = Depends(current_user),
    post_service: PostService = Depends(get_post_service)
):
    """更新帖子 - 只支持Image和Video类型，可编辑字段：description, tags_list, images（images[0]自动设为cover）"""
    result = await post_service.update_post(post_id, user.id, update_data.model_dump())
    return result


@app.post(
    "/{post_id}/add_to_collections",
    tags=["post"],
    status_code=status.HTTP_200_OK,
    response_model=List[CollectionSchema],
)
async def add_post_to_collections(
        post_id: str,
        request: AddPostToCollectionsRequest,
        author: User = Depends(current_user),
        session: AsyncSession = Depends(get_session),
        author_service: AuthorService = Depends(get_author_service),
        recommender_client: RecommenderClient | None = Depends(get_recommender_client),
        redis_client: AsyncRedisClient = Depends(RedisCli.async_)
):
    """
    Add a post to multiple collections.
    Current user must be the author of all target collections.
    """
    # 创建使用共享 session 的服务实例
    collection_service = CollectionService(session, author_service, recommender_client)
    commit_service = CommitService(session, collection_service, recommender_client)
    
    collection_ids = request.collection_ids

    if not collection_ids:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "No collection IDs provided")

    # Verify post exists and is published
    stmt = select(Post).where(Post.id == post_id)
    result = await session.execute(stmt)
    post = result.scalars().first()

    if not post:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Post not found")

    if post.status != PostStatus.POSTED:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Unable to add drafted post")

    # Get all collections and verify permissions
    stmt = select(Collection).where(Collection.id.in_(collection_ids))
    result = await session.execute(stmt)
    collections = result.scalars().all()

    if len(collections) != len(collection_ids):
        raise HTTPException(status.HTTP_404_NOT_FOUND, "One or more collections not found")

    # Check if current user is the author of all collections
    for collection in collections:
        if collection.author_id != author.id:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, 
                f"Only collection author can add posts to collection {collection.id}"
            )

    # Add post to each collection
    updated_collections = []
    for collection in collections:
        # Check if post is already in collection
        if post_id in collection.contents:
            continue  # Skip if post already in collection

        # Create and approve commit
        service_create_data = CommitCreate.model_validate(
            {"collection_id": collection.id, "post_id": post_id, "author_id": author.id}
        )
        commit = await commit_service.create(service_create_data)
        await commit_service.approve(commit.id)
        
        # Refresh collection to get updated data
        await session.refresh(collection)
        updated_collections.append(collection)

    # Invalidate cache for the post that was added to collections
    try:
        from src.common.holdview import invalidate_post_cache
        await invalidate_post_cache(post_id, redis_client)
    except Exception as e:
        logger.warning(f"Failed to invalidate cache for post {post_id}: {str(e)}")

    # 提交事务以确保更改持久化
    await session.commit()
    return updated_collections


@app.post(
    "/{post_id}/remove_from_collections",
    tags=["post"],
    status_code=status.HTTP_200_OK,
    response_model=List[CollectionSchema],
)
async def remove_post_from_collections(
        post_id: str,
        request: RemovePostFromCollectionsRequest,
        author: User = Depends(current_user),
        session: AsyncSession = Depends(get_session),
        redis_client: AsyncRedisClient = Depends(RedisCli.async_)
):
    """
    Remove a post from multiple collections.
    Current user must be the author of all target collections.
    """
    collection_ids = request.collection_ids

    if not collection_ids:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "No collection IDs provided")

    # Verify post exists
    stmt = select(Post).where(Post.id == post_id)
    result = await session.execute(stmt)
    post = result.scalars().first()

    if not post:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Post not found")

    # Get all collections and verify permissions
    stmt = select(Collection).where(Collection.id.in_(collection_ids))
    result = await session.execute(stmt)
    collections = result.scalars().all()

    if len(collections) != len(collection_ids):
        raise HTTPException(status.HTTP_404_NOT_FOUND, "One or more collections not found")

    # Check if current user is the author of all collections
    for collection in collections:
        if collection.author_id != author.id:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN, 
                f"Only collection author can remove posts from collection {collection.id}"
            )

    # Remove post from each collection
    updated_collections = []
    for collection in collections:
        # Check if post is in collection
        if post_id not in collection.contents:
            continue  # Skip if post not in collection

        # Update collection contents first to maintain consistency
        stmt = text("""
            UPDATE collections 
            SET contents = ARRAY_REMOVE(contents, :post_id), contents_count = contents_count - 1
            WHERE id = :collection_id
        """).params(post_id=post_id, collection_id=collection.id)
        await session.execute(stmt)

        # Then delete SavedPost record
        stmt = (
            delete(SavedPost)
            .where(
                SavedPost.collection_id == collection.id,
                SavedPost.post_id == post_id,
                SavedPost.user_id == author.id
            )
        )
        await session.execute(stmt)

        # Delete Commit record
        stmt = delete(Commit).where(
            Commit.collection_id == collection.id, 
            Commit.post_id == post_id
        )
        await session.execute(stmt)

        # Update post collections count
        stmt = update(Post).where(Post.id == post_id).values(
            collections_count=Post.collections_count - 1
        )
        await session.execute(stmt)

        # Update author citations count if different author
        if post.author_id != collection.author_id:
            stmt = update(Author).where(Author.id == post.author_id).values(
                citations_count=Author.citations_count - 1
            )
            await session.execute(stmt)

        # Refresh collection to get updated data
        await session.refresh(collection)
        updated_collections.append(collection)

    # Invalidate cache for the post that was removed from collections
    try:
        from src.common.holdview import invalidate_post_cache
        await invalidate_post_cache(post_id, redis_client)
    except Exception as e:
        logger.warning(f"Failed to invalidate cache for post {post_id}: {str(e)}")

    # 提交事务以确保更改持久化
    await session.commit()
    return updated_collections