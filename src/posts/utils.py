"""
Posts utility functions
"""
import httpx
from typing import Optional
from src.posts.logger import logger


async def get_live_channel_info(author_id: str) -> Optional[dict]:
    """Get live channel information from agora service"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://toci-dev-01.aurora:8027/channels/user/{author_id}")
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None  # No live channel for this user
            else:
                logger.warning(f"Failed to get live channel info for user {author_id}: {response.status_code}")
                return None
    except Exception as e:
        logger.error(f"Error calling agora service for user {author_id}: {str(e)}")
        return None