from datetime import datetime
from enum import Enum
from typing import Optional, List, Literal, Dict, Any

from pydantic import BaseModel, field_serializer, computed_field, Field

from src.database.constants import PostStatus
from src.common.utils import format_cents_to_usd_string
from src.database.schemas.Video import UrlType
from src.authors.schemas import AuthorRead
from src.posts.comments.schemas_comments import Comment


class ProcessingStatus(str, Enum):
    PROCESSING = "processing"
    COMPLETED = "completed"


class VideoBase(BaseModel):
    type: Literal["Video"] = "Video"

    title: str
    cover: Optional[str]
    url: str
    url_type: Optional[UrlType]
    description: Optional[str]
    tags: List[str]

    status: PostStatus

    class Config:
        from_attributes = True


class VideoRead(VideoBase):
    id: str

    created_at: datetime
    updated_at: datetime

    comments_count: int
    likes_count: Optional[int]
    collections_count: int
    processing_status: Optional[ProcessingStatus]

    region: str
    width: Optional[int]
    height: Optional[int]
    upload_url: Optional[str] = None
    
    # Holdview field
    is_locked: Optional[bool] = False
    holdview_amount_int: Optional[int] = Field(default=0, alias="holdview_amount")
    free_seconds: Optional[int] = 0
    # When locked, return token info under key "binding_token"
    binding_token_info: Optional[Dict[str, str]] = None
    
    class Config:
        from_attributes = True

    @computed_field  # type: ignore[misc]
    @property
    def holdview_amount(self) -> str:
        return format_cents_to_usd_string(self.holdview_amount_int)


class VideoCreateRequest(BaseModel):
    title: Optional[str]
    description: str
    cover: Optional[str] = None
    tags: Optional[List[str]]
    name: str
    status: PostStatus = PostStatus.POSTED
    created_at: Optional[datetime] = None
    holdview_amount: Optional[int] = 0
    free_seconds: Optional[int] = 0
    # Optional token address bound to this post
    binding_token: Optional[str] = None


class Video(VideoRead):
    author: AuthorRead
    comments: List[Comment]
    likes: List[AuthorRead]
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

