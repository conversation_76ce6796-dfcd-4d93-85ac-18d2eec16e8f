from datetime import datetime

from fastapi import Depends, status, HTTPException
from fastapi.routing import APIRouter
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate

from src.auth import optional_user, current_user
from src.database.session import AsyncSession, get_session
from src.database.models import Video
from src.database.models.User import User
from src.posts.videos.dependencies import get_video_service
from src.posts.videos.schemas import VideoCreateRequest, VideoRead

from src.posts.schemas import VideoSchema
from src.posts.videos.service import VideoService
from src.common.elasticsearch.sync_client import create_es_sync_client
from src.common.holdview import get_holdview_service, HoldviewService
from sqlalchemy import select
from src.database.models.Pair import Pair
from src.posts.collections.schemas import CommitCreate
from src.posts.collections.commit_legacy import CommitService, get_commit_service

router = APIRouter(prefix="/videos")
add_pagination(router)


@router.get(
    "/{post_id}",
    response_model=VideoSchema,
    status_code=status.HTTP_200_OK,
)
async def get_one(
        post_id: str,
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        video_service: VideoService = Depends(get_video_service),
        holdview_service: HoldviewService = Depends(get_holdview_service),
):
    # Get the post data
    stmt = video_service.get_one(post_id=post_id, user_id=getattr(user, "id", None))
    result = await session.execute(stmt)
    video = result.scalar_one_or_none()
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Video not found")
    
    # Apply holdview lock and enrich token info via service helper
    user_id = getattr(user, "id", None)
    video = await holdview_service.apply_holdview_lock(user, video)
    
    return video


@router.get(
    "",
    response_model=Page[VideoSchema],
    status_code=status.HTTP_200_OK,
)
async def get_all(
        author_id: str | None = None,
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        video_service: VideoService = Depends(get_video_service),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    stmt = video_service.get_all(user_id=getattr(user, "id", None))
    if author_id:
        stmt = stmt.where(Video.author_id == author_id)
    page = await paginate(session, stmt, params)
    # Apply holdview locks to page items
    page.items = await holdview_service.apply_holdview_locks(user, list(page.items))
    return page


@router.post(
    "/create",
    summary="Create a video post",
    description="Create a video post",
    response_model=VideoRead,
    status_code=status.HTTP_201_CREATED
)
async def create(
        create_data: VideoCreateRequest,
        user: User = Depends(current_user),
        service: VideoService = Depends(get_video_service),
        commit_service: CommitService = Depends(get_commit_service)
):
    """
    Create new Video post.
    author_id is set by current user id.
    """
    model = await service.create(user.id, user.region, create_data)
    
    # 确保在返回前刷新模型以加载所有属性
    await service.session.refresh(model)
    await service.session.commit()
    
    # 确保所有属性都已加载，避免懒加载问题
    # 显式访问所有可能触发懒加载的属性
    _ = model.width
    _ = model.height
    _ = model.free_seconds
    _ = model.holdview_amount

    # 异步同步到 Elasticsearch
    es_sync_client = create_es_sync_client("videos_service")
    es_sync_client.sync_video(str(model.id), "create", priority=5)

    # 若带有 binding_token，则尝试将帖子提交到对应的 collection
    try:
        token = getattr(create_data, "binding_token", None)
        if token:
            stmt = select(Pair.collection_id).where(Pair.base == token)
            col_res = await commit_service.session.execute(stmt)
            collection_id = col_res.scalar_one_or_none()
            if collection_id:
                commit_data = CommitCreate.model_validate({
                    "collection_id": collection_id,
                    "post_id": str(model.id),
                    "author_id": user.id,
                })
                commit = await commit_service.create(commit_data)
                # 若当前用户是集合作者将自动通过，否则忽略审批错误，保留为待审批
                try:
                    await commit_service.approve(commit.id)
                except Exception:
                    pass
                await commit_service.session.commit()
    except Exception:
        # 提交失败不影响发帖主流程
        pass

    return model
