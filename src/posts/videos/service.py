from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.database.models import Author
from src.database.models.Video import Video
from src.common.post_service import PostService
from src.database.schemas.Video import UrlType
from src.media_core.services import CloudflareService
from src.posts.logger import logger
from src.posts.videos.schemas import VideoCreateRequest, ProcessingStatus
from src.authors.service import AuthorService
from src.common.recommender.client import RecommenderClient


class VideoService(PostService):
    model = Video

    def __init__(self, session: AsyncSession, cf: CloudflareService, author_service: AuthorService, recommender_client: RecommenderClient | None):
        super().__init__(session, author_service, recommender_client)
        self.cf = cf

    def get_one(self, post_id: str, user_id: str | None = None):
        stmt = (
            select(self.model)
            .where(self.model.id == post_id)
            .options(
                selectinload(self.model.author),
                self.in_collection_expression(user_id=user_id),
                self.is_liked_expression(user_id=user_id)
            )
        )
        return stmt

    async def create(self, user_id: str, region: str, create_video: VideoCreateRequest):
        """
        Create a video object and save metadata to the database.
        """
        # Check if the session is already in a transaction
        if self.session.in_transaction():
            logger.info("Already in transaction")
            return await self._create_video_logic(user_id, region, create_video)
        else:
            async with self.session.begin():
                logger.info("Begin a transaction")
                return await self._create_video_logic(user_id, region, create_video)

    async def _create_video_logic(self, user_id: str, region: str, create_video: VideoCreateRequest):
        """
        The actual implementation logic for creating a video.
        This method can be called within an existing transaction or within a new one.
        """
        response = await self.cf.get_upload_video_url(user_id, create_video.name, 3600)

        # Use user provided cover if available, otherwise use default Cloudflare thumbnail
        cover_url = create_video.cover if create_video.cover else f"https://videodelivery.net/{response.uid}/thumbnails/thumbnail.jpg"

        # Use frontend provided tags
        tags_list = create_video.tags or []

        model = Video(
            uid=response.uid,
            url=f"https://videodelivery.net/{response.uid}/manifest/video.m3u8",
            url_type=UrlType.CLOUDFLARE,
            processing_status=ProcessingStatus.PROCESSING,
            title=create_video.title,
            description=create_video.description,
            cover=cover_url,
            region=region,
            author_id=user_id,
            tags_list=tags_list,
            status=create_video.status,
            holdview_amount=create_video.holdview_amount or 0,
            free_seconds=create_video.free_seconds or 0,
            width=0,  # 明确设置默认值
            height=0,  # 明确设置默认值
            binding_token=(create_video.binding_token if getattr(create_video, 'binding_token', None) else None)
        )
        self.session.add(model)

        stmt = update(Author).where(Author.id == user_id).values(
            posts_count=Author.posts_count + 1
        )
        await self.session.execute(stmt)
        
        await self.session.flush()

        # Upsert tags and increase posts_count for tags_list
        try:
            await self._change_tags_counts(tags_list, +1)
        except Exception:
            pass

        if self.recommender_client:
            await self.recommender_client.add_post(model)

        # 设置额外的属性（upload_url 不是数据库字段，只在响应中使用）
        setattr(model, 'upload_url', response.url)

        return model
