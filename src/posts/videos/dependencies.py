from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from src.media_core.dependencies import get_cloudflare_service
from src.media_core.services import CloudflareService
from src.posts.videos.service import VideoService
from src.authors.service import AuthorService, get_author_service
from src.common.recommender.client import RecommenderClient, get_recommender_client


def get_video_service(
        session: AsyncSession = Depends(get_session),
        cf: CloudflareService = Depends(get_cloudflare_service),
        author_service: AuthorService = Depends(get_author_service),
        recommender_client: RecommenderClient | None = Depends(get_recommender_client)
):
    yield VideoService(session, cf, author_service, recommender_client)