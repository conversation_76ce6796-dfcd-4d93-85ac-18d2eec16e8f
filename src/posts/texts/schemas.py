

from datetime import datetime
from typing import List, Literal, Optional
from pydantic import BaseModel, field_serializer

from src.posts.collections.schemas import CollectionRead
from src.common.constants import FeedReason, PostStatus
from src.authors.schemas import AuthorRead
from src.posts.comments.schemas_comments import Comment


class TextRead(BaseModel):
    type: Literal["Text"] = "Text"
    id: str

    created_at: datetime
    updated_at: datetime
    author_id: str
    status: PostStatus

    text: str
    tags: List[str]

    region: str
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

    class Config:
        from_attributes = True


class TextFeed(TextRead):
    author: AuthorRead

    comments_count: Optional[int]
    comments: List[Comment]

    likes_count: Optional[int]
    likes: List[AuthorRead]

    collections_count: int
    is_in_collection: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []
    collections: List[CollectionRead] = []
    subscribed_collections: List[CollectionRead] = []