from fastapi import Depends, HTTPException, status
from fastapi.routing import APIRouter
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate

from src.auth import optional_user
from src.database.models import File
from src.database.session import AsyncSession, get_session
from src.database.models.User import User

from .service import FileService, get_file_service
from ..schemas import FileSchema


router = APIRouter(prefix="/files")
add_pagination(router)


@router.get(
    "/{post_id}",
    response_model=FileSchema,
    status_code=status.HTTP_200_OK,
)
async def get_one(
        post_id: str,
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        file_service: FileService = Depends(get_file_service)
):
    stmt = file_service.get_one(post_id=post_id, user_id=getattr(user, "id", None))
    return (await session.execute(stmt)).scalar_one()


@router.get(
    "",
    response_model=Page[FileSchema],
    status_code=status.HTTP_200_OK,
)
async def get_all(
        author_id: str | None = None,
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        file_service: FileService = Depends(get_file_service)
):
    stmt = file_service.get_all(user_id=getattr(user, "id", None))
    if author_id:
        stmt = stmt.where(File.author_id == author_id)
    return await paginate(session, stmt, params)
