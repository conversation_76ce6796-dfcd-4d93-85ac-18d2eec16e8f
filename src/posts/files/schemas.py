

from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, field_serializer

from src.common.constants import PostStatus
from src.posts.comments.schemas_comments import Comment
from src.authors.schemas import AuthorRead


class CreateSchema(BaseModel):
    type: Literal["File"] = "File"

    title: str
    url: str
    description: Optional[str]
    tags: List[str] = []
    status: PostStatus = PostStatus.POSTED

    class Config:
        from_attributes = True

class ReadSchema(CreateSchema):
    id: str
    author_id: str
    size: int
    format: str
    downloads_count: int

    status: PostStatus

    created_at: datetime
    updated_at: datetime

    comments_count: int
    likes_count: int
    collections_count: int

    region: str

    class Config:
        from_attributes = True

class File(ReadSchema):
    author: "AuthorRead"

    comments_count: int
    comments: List["Comment"]

    likes_count: int
    likes: List["AuthorRead"]

    collections_count: int
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

File.model_rebuild()
ReadSchema.model_rebuild()