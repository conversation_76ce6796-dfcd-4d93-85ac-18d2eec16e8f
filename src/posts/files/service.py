from src.database.models.File import File
from src.common.post_service import <PERSON>Service
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends
from src.database.session import get_session
from src.authors.service import AuthorService, get_author_service
from src.common.recommender.client import RecommenderClient, get_recommender_client


class FileService(PostService):
    model = File

    def __init__(self, session: AsyncSession, author_service: AuthorService, recommender_client: RecommenderClient | None):
        super().__init__(session, author_service, recommender_client)


def get_file_service(
    session: AsyncSession = Depends(get_session),
    author_service: AuthorService = Depends(get_author_service),
    recommender_client: RecommenderClient | None = Depends(get_recommender_client)
) -> FileService:
    return FileService(session, author_service, recommender_client)
