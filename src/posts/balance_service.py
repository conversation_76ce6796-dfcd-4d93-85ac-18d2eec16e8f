"""
Balance Service for holdview feature integration
"""
from typing import Optional, Dict, Any
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.models import Token
from src.memecoin.services import MemeService
from src.posts.logger import logger


class BalanceService:
    """Service for managing user balance information for holdview features"""
    
    def __init__(self, session: AsyncSession, memecoin_service: Optional[MemeService] = None):
        self.session = session
        self.memecoin_service = memecoin_service

    async def get_user_primary_balance(self, user_id: str) -> int:
        """
        Get user's primary token balance for holdview checks
        
        For now, this uses a simplified approach:
        1. Find the user's primary token (first token they created)
        2. Get balance for that token
        
        Args:
            user_id: User ID
            
        Returns:
            int: User's token balance (0 if no balance or error)
        """
        try:
            if not self.memecoin_service:
                logger.warning(f"Memecoin service not available for balance check for user {user_id}")
                return 0
            
            # Get user's primary token address
            token_address = await self._get_user_primary_token_address(user_id)
            
            if not token_address:
                logger.debug(f"No primary token found for user {user_id}")
                return 0
            
            # Get balance from memecoin service
            balance_data = await self.memecoin_service.get_token_amount(user_id, token_address)
            
            # Convert balance to integer
            balance = int(balance_data.amount) if hasattr(balance_data, 'amount') else 0
            
            logger.debug(f"User {user_id} balance: {balance} for token {token_address}")
            return balance
            
        except Exception as e:
            logger.error(f"Error getting primary balance for user {user_id}: {str(e)}")
            return 0

    async def _get_user_primary_token_address(self, user_id: str) -> Optional[str]:
        """
        Get user's primary token address
        
        For now, this returns the first token created by the user.
        In the future, this could be enhanced to:
        1. Use a user preference setting
        2. Use the most valuable token
        3. Use the most recently active token
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[str]: Token address or None
        """
        try:
            # Create a dummy post to use the token resolution logic
            # In the future, this should be refactored to a shared utility
            stmt = select(Token).where(Token.creator_id == user_id).limit(1)
            result = await self.session.execute(stmt)
            token = result.scalar_one_or_none()
            
            return token.token_address if token else None
            
        except Exception as e:
            logger.error(f"Error getting primary token address for user {user_id}: {str(e)}")
            return None

    async def get_balance_info_for_feed(self, user_id: Optional[str]) -> Dict[str, Any]:
        """
        Get balance information optimized for feed filtering
        
        Args:
            user_id: User ID (None for anonymous users)
            
        Returns:
            dict: Balance information for feed filtering
        """
        if not user_id:
            return {
                "user_id": None,
                "primary_balance": 0,
                "can_access_gated": False,
                "token_address": None
            }
        
        try:
            primary_balance = await self.get_user_primary_balance(user_id)
            token_address = await self._get_user_primary_token_address(user_id)
            
            return {
                "user_id": user_id,
                "primary_balance": primary_balance,
                "can_access_gated": primary_balance > 0,
                "token_address": token_address
            }
            
        except Exception as e:
            logger.error(f"Error getting balance info for feed for user {user_id}: {str(e)}")
            return {
                "user_id": user_id,
                "primary_balance": 0,
                "can_access_gated": False,
                "token_address": None,
                "error": str(e)
            }


def get_balance_service(
    session: AsyncSession,
    memecoin_service: Optional[MemeService] = None
) -> BalanceService:
    """Dependency to get BalanceService instance"""
    return BalanceService(session, memecoin_service)