


from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, field_serializer
from src.posts.collections.schemas import CollectionRead
from src.common.constants import FeedReason, Language, PostStatus
from src.authors.schemas import AuthorRead
from src.posts.articles.schemas import Translation
from src.posts.comments.schemas_comments import Comment

class QuestionCreateRequest(BaseModel):
    title: str
    text: Optional[str]
    cover: Optional[str]
    description: Optional[str]
    tags: List[str]

    status: PostStatus = PostStatus.DRAFTED
    created_at: Optional[datetime]

    class Config:
        from_attributes = True

class QuestionCreate(QuestionCreateRequest):
    type: Literal["Question"] = "Question"
    author_id: str

class QuestionRead(QuestionCreate):
    id: str

    created_at: datetime
    updated_at: datetime

    answers_count: int
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

class Question(QuestionRead):
    author: AuthorRead

    likes_count: Optional[int]
    likes: List[AuthorRead]

    collections_count: int
    is_in_collection: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []

    subscribers: List[AuthorRead]
    collections: "List[CollectionRead]" = []
    subscribed_collections: "List[CollectionRead]" = []

    original: bool
    language: Language
    content_rating: int
    content_zone: str
    rating_details: Optional[str]
    region: str


class AnswerCreateRequest(BaseModel):
    text: str
    description: str
    question_id: str
    cover: Optional[str]
    status: PostStatus = PostStatus.DRAFTED
    created_at: Optional[datetime]

    tags: Optional[List[str]]

    class Config:
        from_attributes = True

class AnswerCreate(AnswerCreateRequest):
    type: Literal["Answer"] = "Answer"

    author_id: str


class AnswerRead(AnswerCreate):
    id: str
    created_at: datetime
    updated_at: datetime
    author: AuthorRead

    status: PostStatus
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

class AnswerByQuestion(AnswerRead):
    is_best: Optional[bool]

    likes_count: Optional[int]
    likes: List[AuthorRead]

    comments_count: Optional[int]
    comments: List[Comment]

    collections_count: int
    is_in_collection: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []
    collections: "List[CollectionRead]" = []
    subscribed_collections: "List[CollectionRead]" = []

    original: bool
    language: Language
    content_rating: int
    content_zone: str
    rating_details: Optional[str]
    region: str

class AnswerWithTranslations(AnswerByQuestion):
    original_id: Optional[str]
    translations: Optional[List[Translation]]

class Answer(AnswerWithTranslations):
    question: Question



class QuestionCreateRequest(BaseModel):
    title: str
    text: Optional[str]
    cover: Optional[str]
    description: Optional[str]
    tags: List[str]

    status: PostStatus = PostStatus.DRAFTED
    created_at: Optional[datetime]

    class Config:
        from_attributes = True

class QuestionCreate(QuestionCreateRequest):
    type: Literal["Question"] = "Question"
    author_id: str


class QuestionRead(QuestionCreate):
    id: str

    created_at: datetime
    updated_at: datetime

    answers_count: int
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)

class Question(QuestionRead):
    author: AuthorRead

    likes_count: Optional[int]
    likes: List[AuthorRead]

    collections_count: int
    is_in_collection: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []

    subscribers: List[AuthorRead]
    collections: "List[CollectionRead]" = []
    subscribed_collections: "List[CollectionRead]" = []

    original: bool
    language: Language
    content_rating: int
    content_zone: str
    rating_details: Optional[str]
    region: str

Question.model_rebuild()

class QuestionWithAnswers(Question):
    answers: List[AnswerRead]