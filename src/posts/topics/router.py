from fastapi import Depends, status
from fastapi.routing import APIRouter
from fastapi_pagination import Page, Params, add_pagination

from .schemas import Tag, TagCreate
from .service import TopicService, get_topic_service
from src.posts.logger import logger
from typing import Optional


router = APIRouter(prefix="/topics")
add_pagination(router)


@router.get(
    "",
    status_code=status.HTTP_200_OK,
    response_model=Page[Tag]
)
async def get_topic(
    q: Optional[str] = None,
    params: Params = Depends(),
    topic_service: TopicService = Depends(get_topic_service),
):
    """
    Get all tags with pagination.
    Get similar tags by q string, if provided.
    """
    return await topic_service.search_by_title(title=q, params=params)


@router.post(
    "",
    status_code=status.HTTP_201_CREATED
)
async def create_topic(
    create_data: TagCreate,
    topic_service: TopicService = Depends(get_topic_service)
):
    """Create topic with given create_data."""
    return await topic_service.create(create_data)

