from typing import Optional, List, Tuple
from sqlalchemy import select, func
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi import Depends
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate as _paginate
from src.database.models import Tag
from src.database.session import AsyncSession
from src.posts.topics.schemas import TagCreate, Tag as TagSchema
from src.database.session import get_session
from src.posts.logger import logger

class TopicService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create(self, create_data: TagCreate) -> Tag:
        """Create a new tag"""
        if not create_data.title:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Title is required"
            )
        create_data.title = create_data.title.lower()
        
        try:
            # Check if tag already exists
            stmt = select(Tag).where(Tag.title == create_data.title)
            result = await self.session.execute(stmt)
            existing_tag = result.scalar_one_or_none()
            logger.warning(f"Existing tag: {existing_tag}")
            

            if existing_tag:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Tag with this title already exists"
                )

            # Create new tag
            tag = Tag(**create_data.model_dump())
            self.session.add(tag)
            await self.session.commit()
            await self.session.flush()

            return tag
        except Exception as e:
            await self.session.rollback()
            raise e
            

    async def get_by_title(self, title: str) -> Optional[Tag]:
        """Get tag by title"""
        stmt = select(Tag).where(Tag.title == title.lower())
        return (await self.session.execute(stmt)).scalar_one_or_none()

    async def search_by_title(
        self,
        title: Optional[str] = None,
        params: Optional[Params] = None
    ) -> Page[Tag]:
        """
        Search tags by title with pagination.
        
        Args:
            title: Optional title to filter tags
            params: Pagination parameters
            
        Returns:
            Page[Tag]: Paginated list of tags
        """
        stmt = select(Tag)
        if title:
            stmt = stmt.where(Tag.title.ilike(f"%{title}%"))
            
        if params is None:
            # If no pagination params, return all results
            result = await self.session.execute(stmt)
            return result.scalars().all()
            
        return await _paginate(self.session, stmt, params)
        
    async def get_tags_count(self) -> int:
        """Get total count of tags"""
        stmt = select(func.count()).select_from(select(Tag).subquery())
        result = await self.session.execute(stmt)
        return result.scalar_one()


def get_topic_service(session: AsyncSession = Depends(get_session)) -> TopicService:
    return TopicService(session)
