from typing import Type
from datetime import datetime

from fastapi import Depends, HTTPException, status
from sqlalchemy import select

from src.common.constants import CarnivalStatus
from src.common.recommender.schemas import CommitSchema
from src.database.models import Commit, Collection, Post, post_models
from src.database.schemas.Commit import CommitStatus
from src.database.session import get_session
from src.posts.collections.schemas import CommitCreate, CommitUpdate
from src.posts.collections.service import CollectionService, get_collection_service
from sqlalchemy.ext.asyncio import AsyncSession
from src.common.recommender.client import RecommenderClient, get_recommender_client
from src.posts.settings import settings
from sqlalchemy.orm import selectinload, selectin_polymorphic

class CommitService:
    def __init__(self, session: AsyncSession, collection_service: CollectionService, recommender_client: RecommenderClient | None):
        self.session = session
        self.collection_service = collection_service
        self.recommender_client = recommender_client

    async def get_one(self, commit_id: str):
        stmt = select(Commit).where(Commit.id == commit_id).options(
            selectinload(Commit.post).options(
                selectin_polymorphic(Post, post_models)
            ),
            selectinload(Commit.author)
        )
        result = await self.session.execute(stmt)
        commit = result.scalar_one_or_none()
        if commit is None:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Commit not found")
        return commit

    async def create(self, commit: CommitCreate):
        commit_data = commit.model_dump()
        commit_data.pop('type', None)  # Remove type field as it's not in the Commit model
        # Map optional message -> description column on Commit model
        if 'message' in commit_data:
            commit_data['description'] = commit_data.pop('message')
        commit_obj = Commit(**commit_data)
        self.session.add(commit_obj)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
        
        # 重新查询以预加载相关数据
        stmt = select(Commit).where(Commit.id == commit_obj.id).options(
            selectinload(Commit.post).options(
                selectin_polymorphic(Post, post_models)
            ),
            selectinload(Commit.author)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one()

    async def is_approved(self, commit_id: str):
        commit = await self.get_one(commit_id)
        return await self.collection_service.is_in_collection(commit.collection_id, commit.post_id)

    async def approve(self, commit_id: str):
        commit = await self.get_one(commit_id)
        if commit.status == CommitStatus.NEW:
            await self.collection_service.add_post(commit.collection_id, commit.post_id)
            await self.collection_service.add_contributor(commit.collection_id, commit.author_id)
            commit.status = CommitStatus.APPROVED
            self.session.add(commit)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

            query = select(Collection).where(commit.collection_id == Collection.id).options(
                selectinload(Collection.contributors)
            )
            collection: Collection = (await self.session.execute(query)).scalar_one()
            if collection.carnival_status == CarnivalStatus.NEW and len(collection.contributors) >= 3:
                collection.carnival_status = CarnivalStatus.STARTED
                collection.carnival_start_time = datetime.utcnow()
            
            # 只有当 recommender_client 不为 None 时才调用
            if self.recommender_client:
                recommender_commit = CommitSchema(
                    user_id=commit.author_id,
                    post_id=commit.post_id,
                    collection_id=commit.collection_id
                )
                await self.recommender_client.add_commit(recommender_commit)
            return commit
        else:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "Commit is not new status")
       
    async def reject(self, commit_id: str, update_data: CommitUpdate):
        model = await self.get_one(commit_id)
        if model.status == CommitStatus.NEW:
            model.status = CommitStatus.REJECTED
            model.reason = update_data.reason
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            return model
        raise HTTPException(status.HTTP_400_BAD_REQUEST)

def get_commit_service(
    session: AsyncSession = Depends(get_session),
    collection_service: CollectionService = Depends(get_collection_service),
    recommender_client: RecommenderClient | None = Depends(get_recommender_client)
):
    yield CommitService(session, collection_service, recommender_client)
