from fastapi import Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.common.constants import CollectionContentType, PostStatus, PostType
from src.common.exceptions import NotFoundError, NotAllowedError
from src.auth import current_user, optional_user
from src.database.models import Post, Collection, User, Commit
from src.database.session import get_session
from .schemas import CommitCreateRequest

from .service import CollectionService, get_collection_service


async def valid_collection_post(
        post_id: str,
        session: AsyncSession = Depends(get_session)
):
    stmt = select(Post).where(Post.id == post_id)
    result = await session.execute(stmt)
    post = result.scalar_one_or_none()
    if not post:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Post does not exist")
    if post.status != PostStatus.POSTED:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Unable to add drafted post.")
    return post_id

async def valid_collection_post_type(
        collection_id: str,
        post_id: str = Depends(valid_collection_post),
        session: AsyncSession = Depends(get_session)
):
    """
    Check post type for match with target collection content type.

    :param collection_id: Collection ID for post add.
    :param post_id: Post ID.
    :param session: Database session
    :return: Post ID.
    """
    stmt = select(Post).where(Post.id == post_id)
    result = await session.execute(stmt)
    post = result.scalar_one_or_none()
    
    stmt = select(Collection).where(Collection.id == collection_id)
    result = await session.execute(stmt)
    collection = result.scalar_one_or_none()
    
    if post is None or collection is None:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Post or collection does not exist")
    if (collection.content_type == CollectionContentType.MIXED and 
            (post.type == PostType.IMAGE or post.type == PostType.VIDEO)):
        return post_id

    if collection.content_type == post.type:
        return post_id
    raise HTTPException(status.HTTP_400_BAD_REQUEST, "Bad post type.")


async def valid_post(post_id: str, session: AsyncSession = Depends(get_session)) -> Post:
    stmt = select(Post).where(Post.id == post_id)
    post = (await session.execute(stmt)).first()
    if not post:
        raise NotFoundError(detail="Post does not exist")
    return post[0]


async def valid_collection(
        collection_id: str,
        user: User = Depends(optional_user),
        session: AsyncSession = Depends(get_session),
        collection_service: CollectionService = Depends(get_collection_service)
) -> Collection:
    stmt = select(Collection).where(Collection.id == collection_id).options(
        collection_service.subscribed_expression(user_id=getattr(user, "id", None))
    )
    collection = (await session.execute(stmt)).first()
    if not collection:
        raise NotFoundError(detail="Collection does not exist")
    return collection[0]


async def collection_author(
        collection: Collection = Depends(valid_collection),
        user: User = Depends(current_user)
) -> User:
    if collection.author_id != user.id:
        raise NotAllowedError(detail="Action can only be done by collection author")
    return user


async def valid_commit_data(
        data: CommitCreateRequest,
        user: User = Depends(current_user),
        session: AsyncSession = Depends(get_session)
):
    await valid_collection_post_type(data.collection_id, data.post_id, session)
    
    stmt = select(Collection).where(Collection.id == data.collection_id)
    result = await session.execute(stmt)
    collection = result.scalar_one_or_none()
    
    stmt = select(Post).where(Post.id == data.post_id)
    result = await session.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not collection or not post:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Collection or post does not exist")
    
    if post.status != PostStatus.POSTED:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Unable to add drafted post.")
    if collection.author_id == user.id:
        return data
    
    stmt = select(Commit).where(
        Commit.collection_id == data.collection_id,
        Commit.post_id == data.post_id,
        Commit.author_id == user.id
    )
    result = await session.execute(stmt)
    commit = result.scalar_one_or_none()
    
    if commit:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "Post already committed by this user")
    return data