from datetime import datetime
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from fastapi.params import Depends
from sqlalchemy import select, exists, literal, or_, delete, any_, text, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import with_expression, aliased, selectinload

from src.database.models.Collection import collections_subscriptions, collections_contributors
from src.database.models import Collection
from src.common.post_service import PostService
from src.database.models.Post import Post as PostModel
from src.database.models.Post import Post
from src.database.models.SavedPosts import SavedPost
from src.database.models.TokenHolder import TokenHolder
from src.database.models.Pair import Pair
from src.database.models.UserWallet import UserWallet
from src.database.models.Author import Author
from src.database.models.Commit import Commit
from src.database.session import get_session
from src.posts.logger import logger
from src.authors.service import get_author_service, AuthorService
from src.common.recommender.client import get_recommender_client, RecommenderClient
from src.common.holdview import invalidate_post_cache

from .schemas import CollectionCreateRequest
from src.common.constants import PostStatus, CarnivalStatus

class CollectionService(PostService):
    model = Collection

    def __init__(self, session: AsyncSession, author_service: AuthorService, recommender_client: RecommenderClient | None):
        super().__init__(session, author_service, recommender_client)

    async def _add_post(self, collection_id: str, post_id: str):
        collection = await self.session.get(Collection, collection_id)
        post: Optional[PostModel] = await self.session.get(PostModel, post_id)
        if post_id not in collection.contents:
            collection.contents.append(post_id)
            collection.contents_count += 1
            if post.author_id != collection.author_id:
                await self.author_service.citations_inc(post.author_id)
                
            # Invalidate cache for the post being added to collection
            await invalidate_post_cache(post_id)
        else:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "Post already in collection")
        collection.updated_at = datetime.utcnow()
        return collection
    
    async def add_contributor(self, collection_id: str, author_id: str):
        """
        直接在数据库中添加贡献者关系，避免通过ORM关系懒加载
        这是一个内部方法，用于优化性能和避免异步上下文问题
        
        Args:
            collection_id: 收藏集ID
            author_id: 用户ID
        """
        stmt = select(collections_contributors).where(
            collections_contributors.c.collection_id == collection_id,
            collections_contributors.c.author_id == author_id
        )
        result = await self.session.execute(stmt)
        if result.first() is not None:
            return
            
        await self.session.execute(
            collections_contributors.insert().values(
                collection_id=collection_id,
                author_id=author_id
            )
        )
        
        collection = await self.get(collection_id)
        if collection:
            collection.contributor_count += 1
            self.session.add(collection)
            
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    async def add_post(self, collection_id: str, post_id: str):
        model: Collection = await self._add_post(collection_id, post_id)
        author_id = model.author_id
        new_saved_post = SavedPost(user_id=author_id, post_id=post_id, collection_id=collection_id)
        post = await self.session.get(PostModel, post_id)
        post.collections_count += 1
        self.session.add(post)
        self.session.add(new_saved_post)
        self.session.add(model)
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    def get_all_by_author(self, author_id: str, **kwargs):
        collections = super().get_all_by_author(author_id, **kwargs)
        for collection in collections:
            posts = self.session.query(PostModel).filter(PostModel.id.in_(collection.contents)).limit(5).all()
            collection.posts = posts
        return collections

    def subscribed_expression(self, user_id: str | None = None, polymorphic_entity=None):
        subscribers_table = aliased(collections_subscriptions, name='subscribers_table')
        
        collection_id_col = polymorphic_entity.Collection.id if polymorphic_entity else self.model.id
        
        subscribed_subquery = exists(
            select(subscribers_table)
            .where(
                subscribers_table.c.author_id == user_id,
                subscribers_table.c.collection_id == collection_id_col
            )
        )
        target_attr = polymorphic_entity.Collection.is_subscribed if polymorphic_entity else self.model.is_subscribed
        expression = with_expression(
            target_attr,
            subscribed_subquery if user_id else literal(False)
        )
        return expression

    def contributor_expression(self, user_id: str | None = None):
        contributors_table = aliased(collections_contributors, name='contributors_table')
        contributor_subquery = exists(
            select(contributors_table)
            .where(
                contributors_table.c.author_id == user_id,
                contributors_table.c.collection_id == Collection.id
            )
        )
        expression = with_expression(
            self.model.is_contributor,
            contributor_subquery if user_id else literal(False)
        )
        return expression

    def can_commit_expression(self, user_id: str | None = None):
        """表达式，判断用户是否可以向集合提交内容"""
        if user_id is None:
            logger.debug("can_commit: user_id is None, returning False")
            return with_expression(
                self.model.can_commit,
                False
            )
        
        logger.debug(f"can_commit: checking for user_id={user_id}")
        
        # 检查用户是否是集合的作者
        is_author = self.model.author_id == user_id
        logger.debug(f"can_commit: is_author condition SQL: {str(is_author)}")
        
        # 如果用户不是作者，检查他们是否拥有相关token
        # 这里我们只检查当前集合，直接使用集合ID比较
        
        # 1. 获取用户的钱包地址
        user_wallets = select(UserWallet.pubkey).where(UserWallet.user_id == user_id)
        logger.debug(f"can_commit: user_wallets SQL: {str(user_wallets)}")
        
        # 2. 查找用户持有的token
        has_token = exists(
            select(1)
            .select_from(TokenHolder)
            .join(Pair, Pair.base == TokenHolder.token_address)
            .where(
                TokenHolder.address.in_(user_wallets),
                TokenHolder.amount > 0,
                Pair.collection_id == self.model.id
            )
        )
        logger.debug(f"can_commit: has_token SQL: {str(has_token)}")
        
        can_commit_condition = or_(is_author, has_token)
        logger.debug(f"can_commit: final condition SQL: {str(can_commit_condition)}")
        
        return with_expression(
            self.model.can_commit,
            can_commit_condition
        )

    def is_post_in_collection_expression(self, post_id: str | None = None):
        """表达式，判断指定的post是否在collection中"""
        if post_id is None:
            return with_expression(
                self.model.is_post_in_collection,
                literal(False)
            )
        
        contains_condition = post_id == any_(self.model.contents)
        
        return with_expression(
            self.model.is_post_in_collection,
            contains_condition
        )

    async def get(self, id: str):
        stmt = select(self.model).where(self.model.id == id).options(
            selectinload(self.model.author)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def is_subscribed(self, collection_id: str, author_id: str) -> bool:
        # 使用显式查询检查订阅关系，避免懒加载问题
        stmt = select(collections_subscriptions).where(
            collections_subscriptions.c.collection_id == collection_id,
            collections_subscriptions.c.author_id == author_id
        )
        result = await self.session.execute(stmt)
        return result.first() is not None

    async def subscribe(self, collection_id: str, author_id: str) -> None:
        # 检查是否已经订阅
        if await self.is_subscribed(collection_id, author_id):
            return
            
        # 添加订阅关系
        await self.session.execute(
            collections_subscriptions.insert().values(
                collection_id=collection_id,
                author_id=author_id
            )
        )
        
        # 更新订阅计数
        collection = await self.get(collection_id)
        if collection:
            collection.subscriber_count += 1
            self.session.add(collection)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    async def unsubscribe(self, collection_id: str, author_id: str) -> None:
        # 检查是否已经订阅
        if not await self.is_subscribed(collection_id, author_id):
            return
            
        # 移除订阅关系
        await self.session.execute(
            collections_subscriptions.delete().where(
                collections_subscriptions.c.collection_id == collection_id,
                collections_subscriptions.c.author_id == author_id
            )
        )
        
        # 更新订阅计数
        collection = await self.get(collection_id)
        if collection and collection.subscriber_count > 0:
            collection.subscriber_count -= 1
            self.session.add(collection)
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            
    async def _add_subscriber(self, collection_id: str, author_id: str) -> None:
        """
        直接在数据库中添加订阅关系，避免通过ORM关系懒加载
        这是一个内部方法，用于优化性能和避免异步上下文问题
        
        Args:
            collection_id: 收藏集ID
            author_id: 用户ID
        """
        stmt = select(collections_subscriptions).where(
            collections_subscriptions.c.collection_id == collection_id,
            collections_subscriptions.c.author_id == author_id
        )
        result = await self.session.execute(stmt)
        if result.first() is not None:
            return
            
        await self.session.execute(
            collections_subscriptions.insert().values(
                collection_id=collection_id,
                author_id=author_id
            )
        )
        
        collection = await self.get(collection_id)
        if collection:
            collection.subscriber_count += 1
            self.session.add(collection)
            
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
        
    async def _remove_subscriber(self, collection_id: str, author_id: str) -> None:
        """
        直接从数据库中移除订阅关系，避免通过ORM关系懒加载
        这是一个内部方法，用于优化性能和避免异步上下文问题
        
        Args:
            collection_id: 收藏集ID
            author_id: 用户ID
        """
        stmt = select(collections_subscriptions).where(
            collections_subscriptions.c.collection_id == collection_id,
            collections_subscriptions.c.author_id == author_id
        )
        result = await self.session.execute(stmt)
        if result.first() is None:
            return
            
        await self.session.execute(
            collections_subscriptions.delete().where(
                collections_subscriptions.c.collection_id == collection_id,
                collections_subscriptions.c.author_id == author_id
            )
        )
        
        collection = await self.get(collection_id)
        if collection and collection.subscriber_count > 0:
            collection.subscriber_count -= 1
            self.session.add(collection)
            
        await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启

    async def is_in_collection(self, collection_id: str, post_id: str):
        model: Optional[Collection] = await self.session.get(Collection, collection_id)
        return post_id in model.contents

    async def remove_post_from_all_collections(self, post_id: str):
        """
        从所有包含该帖子的collection中移除该帖子
        用于删除帖子时的清理操作
        """
        try:
            # 1. 查找所有包含该帖子的collection
            from sqlalchemy import any_
            stmt = select(Collection).where(
                post_id == any_(Collection.contents)
            )
            result = await self.session.execute(stmt)
            collections = result.scalars().all()
            
            # 2. 从每个collection中移除该帖子
            for collection in collections:
                if post_id in collection.contents:
                    collection.contents.remove(post_id)
                    collection.contents_count = max(0, collection.contents_count - 1)
                    collection.updated_at = datetime.utcnow()
                    self.session.add(collection)
                    
                    # 如果被移除的帖子作者不是collection作者，需要减少引用计数
                    post = await self.session.get(PostModel, post_id)
                    if post and post.author_id != collection.author_id:
                        await self.author_service.citations_dec(post.author_id)
            
            # 3. 删除相关的SavedPost记录
            await self.session.execute(
                delete(SavedPost).where(SavedPost.post_id == post_id)
            )
            
            # 4. 更新帖子的collections_count
            post = await self.session.get(PostModel, post_id)
            if post:
                post.collections_count = max(0, post.collections_count - len(collections))
                self.session.add(post)
            
            # 5. Invalidate cache - this method is called during post deletion cleanup
            if self.cache_invalidator:
                # Since the post is being removed from all collections (cleanup during deletion),
                # we need to invalidate its cache entry
                await self.cache_invalidator.invalidate_post_token(post_id)
            
            await self.session.flush()  # 使用 flush 而不是 commit，保持事务开启
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error removing post {post_id} from collections: {str(e)}")
            raise e
    
    async def create(self, collection: CollectionCreateRequest, user_id: str, user_region: str):
        try:
            collection = Collection(
                author_id=user_id,
                title=collection.title,
                description=collection.description,
                cover=collection.cover,
                content_type=collection.content_type,
                original_cover=collection.original_cover,
                carnival_status=CarnivalStatus.NEW,
                subscriber_count=0,
                contributor_count=0,
                contents_count=0,
                contents=[],
                tags_list=[],
                status=PostStatus.POSTED,
                region=user_region,
            )
            self.session.add(collection)
            await self.session.flush()
            await self.session.refresh(collection)
            await self.session.commit()
            return collection
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error creating collection: {str(e)}")
            raise e
        

    async def delete(self, collection_id: str, user_id: str):
        try:
            collection = await self.get(collection_id)
            if not collection:
                raise HTTPException(status.HTTP_404_NOT_FOUND, "Collection not found")

            if collection.author_id != user_id:
                raise HTTPException(status.HTTP_403_FORBIDDEN, "You are not the owner of this collection")
            
            if collection.contents:
                await self.remove_post_from_collection(collection_id, collection.contents, user_id)
            
            await self.session.delete(collection)
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error deleting collection {collection_id}: {str(e)}")
            raise e
    
    async def remove_collection(self, collection_id: str, post_ids: list[str], user_id: str):
        await self.remove_post_from_collection(collection_id, post_ids, user_id)
        await self.session.commit()
    
    async def remove_post_from_collection(self, collection_id: str, post_ids: list[str], user_id: str):
        stmt = select(Collection).where(Collection.id == collection_id)
        result = await self.session.execute(stmt)
        collection = result.scalars().first()

        if not collection:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Collection not found")
        
        if collection.author_id != user_id:
            raise HTTPException(status.HTTP_403_FORBIDDEN, "You are not the owner of this collection")
        
        try:
            removed_post_ids = []
            for post_id in post_ids:
                if post_id not in collection.contents:
                    continue
                
                removed_post_ids.append(post_id)
                
                # Update collection contents first to maintain consistency
                stmt = text("""
                    UPDATE collections 
                    SET contents = ARRAY_REMOVE(contents, :post_id), contents_count = contents_count - 1
                    WHERE id = :collection_id
                """).params(post_id=post_id, collection_id=collection.id)
                await self.session.execute(stmt)

                # Then delete SavedPost record  
                stmt = (
                    delete(SavedPost)
                    .where(
                        SavedPost.collection_id == collection.id,
                        SavedPost.post_id == post_id,
                        SavedPost.user_id == user_id
                    )
                )
                await self.session.execute(stmt)

                stmt = delete(Commit).where(Commit.collection_id == collection.id, Commit.post_id == post_id)
                await self.session.execute(stmt)

                # Update post collections count
                stmt = update(Post).where(Post.id == post_id).values(collections_count=Post.collections_count - 1)
                await self.session.execute(stmt)

                # Update author citations count if different author
                stmt = select(Post.author_id).where(Post.id == post_id)
                result = await self.session.execute(stmt)
                post_author_id = result.scalar_one_or_none()

                if post_author_id and post_author_id != collection.author_id:
                    stmt = update(Author).where(Author.id == post_author_id).values(citations_count=Author.citations_count - 1)
                    await self.session.execute(stmt)
            
            # Invalidate cache for removed posts
            if self.cache_invalidator and removed_post_ids:
                await self.cache_invalidator.invalidate_collection_posts_bulk_update(
                    collection_id, [], removed_post_ids
                )
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error removing post {collection.id} from collections: {str(e)}")
            raise e
        

def get_collection_service(
    session: AsyncSession = Depends(get_session),
    author_service: AuthorService = Depends(get_author_service),
    recommender_client: RecommenderClient | None = Depends(get_recommender_client)
):
    yield CollectionService(session, author_service, recommender_client)
