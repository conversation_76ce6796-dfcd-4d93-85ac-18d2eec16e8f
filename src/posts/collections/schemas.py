from enum import Enum

from src.posts.schemas import *

from datetime import datetime
from typing import List, Literal, Optional, Union, TYPE_CHECKING, ForwardRef

from pydantic import BaseModel, field_serializer, field_validator
from src.authors.schemas import AuthorRead
from src.common.constants import FeedReason, PostStatus, CollectionContentType
from src.common.utils import format_cents_to_usd_string
from src.posts.images.schemas import ImageFeed
from src.posts.videos.schemas import Video
from src.posts.schemas import ContentSchema
from fastapi_pagination import Page


class CommitStatus(str, Enum):
    NEW = "new"
    APPROVED = "accepted"
    REJECTED = "rejected"


class CommitReadStatus(str, Enum):
    READ = "read"
    UNREAD = "unread"

class AddPostToCollectionRequest(BaseModel):
    post_ids: List[str]

class RemovePostFromCollectionRequest(BaseModel):
    post_ids: List[str]

class AddPostToCollectionsRequest(BaseModel):
    collection_ids: List[str]

class RemovePostFromCollectionsRequest(BaseModel):
    collection_ids: List[str]

class CommitCreateRequest(BaseModel):
    collection_id: str
    post_id: str
    message: Optional[str] = None


class CommitCreate(CommitCreateRequest):
    type: Literal["Commit"] = "Commit"
    author_id: str

    class Config:
        from_attributes = True

class CommitSchema(CommitCreate):
    id: str
    created_at: datetime
    updated_at: datetime
    author: AuthorRead

    status: CommitStatus
    read_status: CommitReadStatus
    reason: Optional[str]
    post_id: Optional[str]
    post: Optional[Post]

    resolved_by: Optional[str]
    resolved_at: Optional[datetime]

class CommitPostSchema(CommitCreate):
    id: str
    created_at: datetime
    updated_at: datetime
    author: AuthorRead

    status: CommitStatus
    read_status: CommitReadStatus
    reason: Optional[str]
    post_id: Optional[str]
    post: Optional[ContentSchema]

    resolved_by: Optional[str]
    resolved_at: Optional[datetime]

class CommitPage(Page[CommitPostSchema]):
    unprocessed_count: int = 0

class CommitUpdate(BaseModel):
    reason: Optional[str]


class CollectionCreateRequest(BaseModel):
    title: str
    content_type: CollectionContentType
    description: Optional[str] = None
    cover: Optional[str] = None
    original_cover: Optional[str] = None
    tags: Optional[List[str]] = None

class CollectionCreate(CollectionCreateRequest):
    type: Literal["Collection"] = "Collection"
    author_id: str
    status: PostStatus = PostStatus.POSTED

    class Config:
        from_attributes = True

class CollectionRead(CollectionCreate):
    id: str
    created_at: datetime
    updated_at: datetime
    title: str
    description: Optional[str]
    cover: Optional[str]
    original_cover: Optional[str]
    tags: List[str]
    contents_count: int
    posts_count: int
    contents: List[str]
    carnival_status: str
    carnival_start_time: Optional[datetime]
    region: str
    
    # Holdview field (inherited from Post model)
    holdview_amount: Optional[str] = "0" # Deprecated

    @field_validator('holdview_amount', mode='before')
    def validate_holdview_amount(cls, v):
        # 在验证之前就转换为字符串
        if v is None:
            return format_cents_to_usd_string(None)
        if isinstance(v, (int, float)):
            return format_cents_to_usd_string(int(v))
        if isinstance(v, str):
            return format_cents_to_usd_string(int(v)) if v.isdigit() else v
        return str(v)

class Collection(CollectionRead):
    # author_id: str
    author: AuthorRead
    likes_count: Optional[int]
    likes:"List[AuthorRead]"
    collections_count: int
    is_in_collection: Optional[bool]
    feed_reason: Optional[FeedReason]
    contributors: "List[AuthorRead]"
    subscribed_authors_likes: "List[AuthorRead]" = []
    collections: List[CollectionRead] = []
    subscribed_collections: List[CollectionRead] = []
    subscribers: "List[AuthorRead]"
    previews: Optional[List[str]]
    interaction_rating: float

    @field_serializer('interaction_rating')
    def serialize_interaction_rating(self, value):
        return str(value)
    

class VideoFeed(Video):
    is_in_collection: Optional[bool]
    is_liked: Optional[bool]
    feed_reason: Optional[FeedReason]
    subscribed_authors_likes: List[AuthorRead] = []
    collections: List[CollectionRead] = []
    subscribed_collections: List[CollectionRead] = []

VideoFeed.model_rebuild()

class CollectionWithContentRead(Collection):
    posts: "Optional[List[Union[CollectionRead, CommentRead, ImageRead, VideoRead, FileRead]]]"

class CollectionWithContent(Collection):
    previews: Optional[List[str]]
    # posts: "Optional[List[Union[Article, TextFeed, QuestionWithAnswers, Answer, Collection, Comment, ImageFeed, VideoFeed, Video, File]]]"
    posts: Optional[List[Union[ImageFeed, VideoFeed]]]

Collection.update_forward_refs()
CollectionWithContent.update_forward_refs()
CollectionWithContentRead.update_forward_refs()