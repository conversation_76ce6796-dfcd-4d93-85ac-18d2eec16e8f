import logging
from math import ceil
from typing import Optional, List, Annotated, Literal, Union

from pydantic import BaseModel
from datetime import datetime, timezone
from src.common.utils import ensure_naive_utc
from fastapi import Depends, HTTPException, status, Query
from fastapi.routing import APIRouter
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate as sa_paginate
from fastapi_pagination.utils import disable_installed_extensions_check
from sqlalchemy import select, and_, text, delete, update, or_, func
from sqlalchemy.orm import with_expression, selectin_polymorphic, selectinload
from sqlalchemy.exc import NoResultFound

from src.notifications.constants import EType
from src.notifications.service import NotificationService, get_notification_service
from src.posts.collections.commit_legacy import CommitService, get_commit_service

from .schemas import CollectionWithContent, CollectionWithContentRead, CommitCreate, CommitCreateRequest, CommitUpdate, \
    AddPostToCollectionRequest, RemovePostFromCollectionRequest, CommitPostSchema
from src.common.exceptions import IncorrectDataError
from src.database.session import AsyncSession, get_session, Session
from src.database.models import Collection, User, Commit, Post, Author, post_models, Pair, TokensWatchlist, Comment
from src.database.models.Collection import collections_subscriptions, collections_contributors, Collection as CollectionModel
from src.database.models.SavedPosts import SavedPost
from src.database.schemas.Commit import CommitRead, CommitStatus
from src.database.schemas.Author import AuthorRead
from src.database.constants import CollectionContentType
from src.auth import current_user, optional_user
from src.common.post_service import PostService, get_post_service
from src.posts.logger import logger
from src.database.models.Post import authors_likes
from src.posts.settings import settings

from src.posts.schemas import CollectionSchema, CollectionWithIsContributor, ContentSchema
from src.posts.collections.schemas import ContentSchema, CollectionCreateRequest, CommitPage
from src.posts.collections.service import CollectionService, get_collection_service
from src.posts.collections.dependecies import valid_commit_data
from src.database.schemas import NotificationType
from src.im.service import TociImService, get_toci_im_service
from src.common.holdview import get_holdview_service, HoldviewService
from ...common.constants import PostStatus

disable_installed_extensions_check()

router = APIRouter(prefix="/collections")
add_pagination(router)


class CollectionIDWithPreviews(BaseModel):
    id: str
    previews: List[str]


@router.get(
    "/commits",
    response_model=Page[CommitRead],
    tags=["Commits"]
)
async def get_commits(
        params: Params = Depends(),
        status: Optional[CommitStatus] = None,
        session: AsyncSession = Depends(get_session),
        user: User = Depends(current_user)
):
    if status:
        stmt = select(Commit).where(and_(Commit.author_id == user.id, Commit.status == status))
    else:
        stmt = select(Commit).where(Commit.author_id == user.id)
    return await sa_paginate(
        session,
        stmt,
        params
    )

@router.get(
    "/commits/new_count",
    response_model=int,
    tags=["Commits"]
)
async def get_new_count(
        session: AsyncSession = Depends(get_session),
        user: User = Depends(current_user),
):
    stmt = select(Post).where(and_(Post.author_id == user.id, Post.type == "Collection"))
    result = await session.execute(stmt)
    collections = result.scalars().all()
    if not collections:
        return 0
    collection_ids = [collection.id for collection in collections]
    stmt = select(func.count()).select_from(Commit).where(
        and_(
            Commit.collection_id.in_(collection_ids),
            Commit.author_id != user.id,
            Commit.status == CommitStatus.NEW,
        )
    )
    
    result = await session.execute(stmt)
    return result.scalar_one_or_none() or 0


def _build_base_filters(post_id: str, status: Optional[CommitStatus], start: Optional[datetime], end: Optional[datetime]):
    conds = [Commit.collection_id == post_id]
    if status:
        conds.append(Commit.status == status)
    if start is not None:
        conds.append(Commit.created_at >= start)
    if end is not None:
        conds.append(Commit.created_at <= end)
    return and_(*conds)


@router.get(
    "/{post_id}/commits",
    response_model=CommitPage,
    tags=["Commits"]
)
async def get_collection_commits(
        post_id: str,
        params: Params = Depends(),
        status: Optional[CommitStatus] = None,
        start_time: Optional[datetime] = Query(None, description="Start time (UTC)"),
        end_time: Optional[datetime] = Query(None, description="End time (UTC)"),
        session: AsyncSession = Depends(get_session),
):
    # Get unprocessed commits count
    unprocessed_count = await session.scalar(
        select(func.count(Commit.id)).where(
            Commit.collection_id == post_id,
            Commit.status == CommitStatus.NEW
        )
    )

    if status:
        stmt = select(Commit).where(and_(Commit.collection_id == post_id, Commit.status == status))
    else:
        stmt = select(Commit).where(Commit.collection_id == post_id)

    # Normalize to UTC naive (DB stores naive UTC) and validate
    norm_start = ensure_naive_utc(start_time)
    norm_end = ensure_naive_utc(end_time)

    if norm_start and norm_end and norm_start > norm_end:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "start_time cannot be greater than end_time")

    if norm_start is not None:
        stmt = stmt.where(Commit.created_at >= norm_start)
    if norm_end is not None:
        stmt = stmt.where(Commit.created_at <= norm_end)

    filters = _build_base_filters(post_id, status, norm_start, norm_end)
    total = await session.scalar(select(func.count(Commit.id)).where(filters)) or 0

    offset = (params.page - 1) * params.size

    stmt = (stmt.options(
        # Load related post polymorphically and attach defaults/author on the relationship path
        selectinload(Commit.post)
            .selectin_polymorphic(post_models)
            .options(
                with_expression(Post.is_in_collection, None),
                with_expression(Post.is_liked, None),
                with_expression(Post.feed_reason, None),
                selectinload(Post.author),
            ),
        # Load the commit author as well
        selectinload(Commit.author)
    ).order_by(Commit.created_at.desc()).offset(offset)).limit(params.size)

    query_result = await session.execute(stmt)
    commits = query_result.scalars().all()

    items = [CommitPostSchema.model_validate(c, from_attributes=True) for c in commits]

    payload = {
        "items": items,
        "total": total,
        "page": params.page,
        "size": params.size,
        "pages": ceil(total / params.size) if params.size else 0,
        "unprocessed_count": unprocessed_count,
    }
    return CommitPage(**payload)


@router.get(
    "/{post_id}/contents",
    response_model=Page[ContentSchema],
    status_code=status.HTTP_200_OK,
)
async def get_contents(
        post_id: str,
        type: Optional[str] = None,
        sort: Optional[str] = None,
        holdview: Literal["all", "public", "premium"] = "all",
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        post_service: PostService = Depends(get_post_service),
        holdview_service: HoldviewService = Depends(get_holdview_service)
):
    stmt = (select(Post).join_from(Post, Commit)
            .where(Post.status == PostStatus.POSTED)
            .where(Commit.collection_id == post_id).where(Commit.status == CommitStatus.APPROVED))
    if type:
        stmt = stmt.where(Post.type == type)

    # Apply holdview filter if requested
    if holdview == "public":
        stmt = stmt.where(Post.holdview_amount == 0)
    elif holdview == "premium":
        stmt = stmt.where(Post.holdview_amount > 0)

    stmt = stmt.options(
        post_service.in_collection_expression(user_id=getattr(user, "id", None)),
        post_service.is_liked_expression(user_id=getattr(user, "id", None)),
        selectin_polymorphic(Post, post_models)
    )
    
    # Apply sorting
    if sort and sort == "new":
        stmt = stmt.order_by(Commit.created_at.desc())
    elif sort and sort == "old":
        stmt = stmt.order_by(Commit.created_at.asc())
    else:
        stmt = stmt.order_by(Post.likes_count)

    # Get paginated posts
    paginated_result = await sa_paginate(session, stmt, params)

    # Apply holdview locks to paginated items unless explicitly requesting public-only posts
    if holdview != "public":
        paginated_result.items = await holdview_service.apply_holdview_locks(
            user, paginated_result.items
        )

    return paginated_result


@router.get(
    "/{post_id}/previews",
    response_model=List[str],
    status_code=status.HTTP_200_OK
)
async def get_collection_previews(
        post_id: str,
        session: AsyncSession = Depends(get_session)
):
    stmt = text(f"""
        WITH collection_posts AS (
            SELECT UNNEST(contents) id
            FROM collections
            WHERE id = :post_id
        )
        SELECT COALESCE(articles.cover, answers.cover, questions.cover, images.cover, videos.cover) AS cover
        FROM collection_posts
            LEFT JOIN posts ON posts.id = collection_posts.id
            LEFT JOIN articles ON articles.id = collection_posts.id
            LEFT JOIN answers ON answers.id = collection_posts.id
            LEFT JOIN questions ON questions.id = collection_posts.id
            LEFT JOIN images ON images.id = collection_posts.id
            LEFT JOIN videos ON videos.id = collection_posts.id
        WHERE articles.cover IS NOT NULL
            OR answers.cover IS NOT NULL
            OR questions.cover IS NOT NULL
            OR images.cover IS NOT NULL
            OR videos.cover IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 5
    """).params({"post_id": post_id})
    return (await session.execute(stmt)).scalars()


@router.get(
    "/previews",
    response_model=List[CollectionIDWithPreviews]
)
async def get_previews(
        ids: List[str] = Query(None),
        session: AsyncSession = Depends(get_session)
):
    if ids is None:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "ID list is empty")
    stmt = text(f"""
        WITH collection_posts AS (
            SELECT UNNEST(contents) id, collections.id as collection_id
            FROM collections
            WHERE id=ANY(SELECT * FROM UNNEST(ARRAY{ids}))
        ),
        collections_covers AS (
            SELECT COALESCE(articles.cover, answers.cover, questions.cover, images.cover, videos.cover) AS cover,
                collection_id
            FROM collection_posts
                LEFT JOIN posts ON posts.id = collection_posts.id
                LEFT JOIN articles ON articles.id = collection_posts.id
                LEFT JOIN answers ON answers.id = collection_posts.id
                LEFT JOIN questions ON questions.id = collection_posts.id
                LEFT JOIN images ON images.id = collection_posts.id
                LEFT JOIN videos ON videos.id = collection_posts.id
            WHERE articles.cover IS NOT NULL
                OR answers.cover IS NOT NULL
                OR questions.cover IS NOT NULL
                OR images.cover IS NOT NULL
                OR videos.cover IS NOT NULL
            ORDER BY created_at DESC
        )
        SELECT collection_id AS id, (ARRAY_AGG(cover))[1:5] AS previews
        FROM collections_covers
        GROUP BY collection_id
    """)
    return (await session.execute(stmt)).all()


@router.get(
    "/batch",
    response_model=List[CollectionSchema],
    status_code=status.HTTP_200_OK,
)
async def get_batch(
        ids: Annotated[list[str] | None, Query()] = (),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        collection_service: CollectionService = Depends(get_collection_service)
):
    stmt = (
        select(Collection)
        .where(Collection.id.in_(ids))
        .options(collection_service.subscribed_expression(user_id=getattr(user, "id", None)))
    )
    return (await session.execute(stmt)).scalars()

@router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=CollectionSchema,
)
async def create_collection(
    request: CollectionCreateRequest,
    user: User = Depends(current_user),
    collection_service: CollectionService = Depends(get_collection_service),
):
    """
    Create a new collection.
    """
    return await collection_service.create(request, user_id=user.id, user_region=user.region)

@router.delete(
    "",
    status_code=status.HTTP_200_OK,
    response_model=dict,
)
async def delete_collection(
    collection_id: str,
    user: User = Depends(current_user),
    collection_service: CollectionService = Depends(get_collection_service),
):
    """
    Delete a collection.
    Only the collection owner can delete it.
    """
    await collection_service.delete(collection_id, user_id=user.id)
    return {"message": "Collection deleted successfully"}

@router.get(
    "",
    response_model=Page[CollectionSchema],
    status_code=status.HTTP_200_OK,
)
async def get_all(
        author_id: Optional[str] = None,
        contains_post: Optional[str] = None,
        post_id: Optional[str] = None,
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session),
        user: User = Depends(optional_user),
        collection_service: CollectionService = Depends(get_collection_service)
):
    stmt = select(Collection).where(
        Collection.content_type == CollectionContentType.MIXED
    )
    
    # 添加Pair表的join和chain过滤
    stmt = stmt.join(Pair, Pair.collection_id == Collection.id, isouter=True)
    stmt = stmt.where(or_(
        Pair.chain == settings.ETH_CHAIN_ID,
        Pair.chain.is_(None)  # 允许没有关联Pair的Collection
    ))
    
    if author_id:
        stmt = stmt.where(Collection.author_id == author_id)
    if contains_post:
        stmt = (
            stmt
            .join(SavedPost, onclause=Collection.id == SavedPost.collection_id)
            .where(SavedPost.post_id == contains_post)
        )
    stmt = stmt.options(
        collection_service.subscribed_expression(user_id=getattr(user, "id", None)),
        collection_service.is_post_in_collection_expression(post_id=post_id)
    )
    return await sa_paginate(
        session,
        stmt,
        params
    )

@router.get(
    "/watchlist",
    response_model=Page[CollectionSchema],
    status_code=status.HTTP_200_OK,
)
async def get_watchlist(
    author_id: str,
    params: Params = Depends(),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(optional_user),
    collection_service: CollectionService = Depends(get_collection_service)
):
    # 从TokensWatchlist查询用户关注的token_address，然后通过Pair表的collection_id找到对应的Collection
    stmt = (
        select(Collection)
        .join(Pair, Pair.collection_id == Collection.id)
        .join(TokensWatchlist, TokensWatchlist.token_address == Pair.base)
        .where(
            TokensWatchlist.author_id == author_id,
            Pair.chain == settings.ETH_CHAIN_ID,  # 添加chain过滤
            or_(
                Collection.content_type == CollectionContentType.MIXED,
            )
        )
    )
    stmt = stmt.options(collection_service.subscribed_expression(user_id=getattr(user, "id", None)))
    return await sa_paginate(
        session,
        stmt,
        params
    )


@router.get(
    "/subscribed",
    response_model=Page[CollectionSchema],
    status_code=status.HTTP_200_OK,
)
async def get_subscribed(
        author_id: str,
        sort: Literal["new", "old"] = "new",
        params: Params = Depends(),
        session: Session = Depends(get_session),
        user: User = Depends(optional_user),
        collection_service: CollectionService = Depends(get_collection_service)
):
    stmt = (
        select(Collection)
        .join(collections_subscriptions)
        .where(
            collections_subscriptions.c.author_id == author_id,
            Collection.content_type == CollectionContentType.MIXED,
        )
    )
    if sort == "new":
        stmt.order_by(collections_subscriptions.c.timestamp.desc())
    if sort == "old":
        stmt.order_by(collections_subscriptions.c.timestamp.asc())
    stmt = stmt.options(collection_service.subscribed_expression(user_id=getattr(user, "id", None)))
    return await sa_paginate(session, stmt, params)

@router.patch(
    "/subscribe",
    status_code=status.HTTP_200_OK,
    response_model=CollectionSchema
)
async def subscribe(
        collection_id: str,
        user: User = Depends(current_user),
        service: CollectionService = Depends(get_collection_service),
        notification_service: NotificationService = Depends(get_notification_service)
):
    """
    Subscribe current authorized user to collection by collection_id.
    Works as switch: every second query will unsubscribe user from it.
    """

    stmt = (
        select(Collection)
        .where(Collection.id == collection_id)
        .options(
            selectinload(Collection.subscribers),
            selectinload(Collection.contributors),
            selectinload(Collection.likes),  # 'likes' is inherited from Post model
            selectinload(Collection.comments) # Eager load comments for the Collection itself
        )
    )
    result = await service.session.execute(stmt)
    model = result.scalars().first()

    if await service.is_subscribed(collection_id, user.id):
        await service.unsubscribe(collection_id, user.id)
        return model
    else:
       
        stmt = select(Author).where(Author.id == user.id)
        result = await service.session.execute(stmt)
        follower = result.scalar_one()
        response = await service.subscribe(collection_id, user.id)
        await notification_service.create_notification(
            notification_type=EType.collection_subscribe,
            recipients_ids=[model.author_id],
            meta={
                "collection_id": model.id,
                "collection_cover": model.cover,
                "collection_title": model.title,
                "follower_id": user.id,
                "follower_avatar": follower.avatar,
                "follower_username": follower.username,
                "follower_name": follower.name
            }
        )
        return model


@router.get(
    "/{post_id}",
    response_model=CollectionWithIsContributor,
    status_code=status.HTTP_200_OK,
)
async def get_one(
        post_id: str,
        session: AsyncSession = Depends(get_session),
        collection_service: CollectionService = Depends(get_collection_service),
        user: User = Depends(optional_user)
):
    user_id: str | None = getattr(user, "id", None)
    stmt = select(Collection).where(Collection.id == post_id)
    stmt = stmt.options(
        collection_service.subscribed_expression(user_id),
        collection_service.contributor_expression(user_id),
        collection_service.can_commit_expression(user_id)
    )
    collection = (await session.execute(stmt)).first()
    if not collection:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Collection not found")
    return collection[0]


@router.get(
    "/{post_id}/contributors",
    status_code=status.HTTP_200_OK,
    response_model=Page[AuthorRead],
    tags=["Authors"]
)
async def get_collection_contributors(
        post_id: str,
        sort: Literal["new", "old"] = "new",
        params: Params = Depends(),
        session: AsyncSession = Depends(get_session)
):
    stmt = (
        select(Author)
        .join(collections_contributors, collections_contributors.c.author_id == Author.id)
        .where(collections_contributors.c.collection_id == post_id)
    )
    if sort == "new":
        stmt.order_by(collections_contributors.c.timestamp.desc())
    if sort == "old":
        stmt.order_by(collections_contributors.c.timestamp.asc())
    return await sa_paginate(session, stmt, params)


@router.post(
    "/{collection_id}/add",
    status_code=status.HTTP_200_OK,
    response_model=CollectionSchema,
)
async def add_to_collection(
        collection_id: str,
        request: AddPostToCollectionRequest,
        author: User = Depends(current_user),
        session: AsyncSession = Depends(get_session),
        collection_service: CollectionService = Depends(get_collection_service),
        commit_service: CommitService = Depends(get_commit_service)
):
    """
    Add posts to collection with collection_id.
    Current user must be collection's author.
    """
    # Validate post_ids from request body
    post_ids = request.post_ids

    if not post_ids:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "No post IDs provided")

    # Get collection
    stmt = select(Collection).where(Collection.id == collection_id)
    result = await session.execute(stmt)
    collection = result.scalars().first()

    if not collection:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Collection not found")

    # Check if current user is the collection author
    if collection.author_id != author.id:
        raise HTTPException(status.HTTP_403_FORBIDDEN, "Only collection author can add posts")

    # Create and approve commits for each post
    for post_id in post_ids:
        service_create_data = CommitCreate.model_validate(
            {"collection_id": collection.id, "post_id": post_id, "author_id": author.id}
        )
        commit = await commit_service.create(service_create_data)
        await commit_service.approve(commit.id)

    await session.commit()
    await session.refresh(collection)
    return collection


@router.post(
    "/{collection_id}/remove",
    status_code=status.HTTP_200_OK,
    response_model=CollectionSchema,
)
async def remove(
    collection_id: str,
    request: RemovePostFromCollectionRequest,
    author: User = Depends(current_user),
    collection_service: CollectionService = Depends(get_collection_service)
):
    """
    Remove posts from collection with collection_id.
    Current user must be collection's author.
    """
    post_ids = request.post_ids

    if not post_ids:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "No post IDs provided")

    return await collection_service.remove_collection(collection_id, post_ids, author.id)


@router.get(
    "/contents/fetch", 
    status_code=status.HTTP_200_OK,
    response_model=Union[List[CollectionWithContentRead], CollectionWithContent]
)
async def get_collection_content(
    collection_id: Optional[str] = None,
    type: Optional[str] = None,
    sort: Optional[str] = None,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(optional_user),
    service: CollectionService = Depends(get_collection_service)
):
    """
    Get collection's content if collection_id is provided.
    Get current user's collections if not.
    Return error if user unauthorized or collection_id is None.
    """

    logger.info(f"collection_id: {collection_id}")
    # print(f"collection_id: {collection_id}")
    # TODO: make sure it's public, if it's not, that it belongs to the user
    if collection_id is None:
        if user is None:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "User is not authorized")
        else:
            return service.get_all_by_author(user.id, region=user.region, collection_id=collection_id)
        
    # collection_model: Collection = await service.get(collection_id)
    stmt = (
        select(Collection)
        .where(Collection.id == collection_id)
        .options(
            selectinload(Collection.subscribers),
            selectinload(Collection.contributors),
            selectinload(Collection.likes),  # 'likes' is inherited from Post model
            selectinload(Collection.comments) # Eager load comments for the Collection itself
        )
    )
    result = await session.execute(stmt)
    collection_model = result.scalars().first()

    if collection_model is None:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Collection not found")
    
    subq_liked = (
            select(authors_likes.c.post_id).
            where(authors_likes.c.author_id == user.id)
        )
        
    query = select(Post).where(Post.id.in_(collection_model.contents))
    query = query.options(
        with_expression(Post.is_in_collection, Post.id.in_(collection_model.contents)),
        with_expression(Post.is_liked, Post.id.in_(subq_liked)),
        with_expression(Post.feed_reason, None),
        selectin_polymorphic(Post, post_models),
        selectinload(Post.likes),  # 预加载 likes
        selectinload(Post.comments).options(
            selectinload(Post.likes),         # 评论的点赞
            selectinload(Comment.comments)    # 评论的子评论
        )
    )
    
    # 输出原始SQL
    logger.debug(f"Raw SQL: {query.compile(compile_kwargs={'literal_binds': True})}")
    
    # 添加类型过滤
    if type and type != "All":
        query = query.filter(Post.type == type)
    # 添加排序
    if sort:
        if sort == "Popular":
            query = query.order_by(Post.likes_count.desc())
        elif sort == "Old":
            query = query.order_by(Post.created_at.asc())
        elif sort == "New":
            query = query.order_by(Post.created_at.desc())
        else:
            raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid sort type")
    
    # 执行查询
    result = await session.execute(query)
    posts = result.scalars().all()
    
    # 设置模型属性
    collection_model.posts = posts
    
    # 处理预览图片
    previews = []
    for post in posts:
        if len(previews) >= 3:
            break
        if hasattr(post, "cover") and post.cover is not None and len(post.cover) > 0:
            previews.append(post.cover)
    collection_model.previews = previews
    
    return collection_model


@router.post(
    "/commits/create",
    status_code=status.HTTP_201_CREATED,
    tags=["Commits"]
)
async def commit(
        create_data: CommitCreateRequest = Depends(valid_commit_data),
        user: User = Depends(current_user),
        service: CollectionService = Depends(get_collection_service),
        # points_service: PointsService = Depends(get_points_service),
        commit_service: CommitService = Depends(get_commit_service),
        notification_service: NotificationService = Depends(get_notification_service),
        post_service: PostService = Depends(get_post_service)
):
    """
    Create commit with create_data.
    Create request must contain target collection_id and post_id.
    Author will be set from authorized user id.
    Adds post directly if current user is collection author.
    """
    if await service.is_in_collection(create_data.collection_id, create_data.post_id):
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail="Post is already in collection")

    create_data_dict = create_data.dict()
    create_data_dict.update({"author_id": user.id})
    service_create_data = CommitCreate.parse_obj(create_data_dict)
    model: Commit = await commit_service.create(service_create_data)
    collection: Collection = await service.get(create_data.collection_id)
    if await post_service.is_author(create_data.collection_id, user.id):
        await commit_service.approve(model.id)
    else:
        # Build notification meta and include optional commit message
        notification_meta = {
            "commit_id": model.id,
            "commit_post_id": model.post_id,
            "commit_post_type": model.post.type,
            "commit_collection_id": model.collection_id,
            "commit_collection_title": collection.title,
            "commit_author_id": model.author_id,
            "commit_author_avatar": model.author.avatar,
            "commit_author_username": model.author.username,
            "commit_author_name": model.author.name,
        }
        commit_message = getattr(create_data, "message", None)
        if commit_message:
            notification_meta["commit_message"] = commit_message

        await notification_service.create_notification(
            notification_type=NotificationType.post_commit,
            recipients_ids=[collection.author_id],
            meta=notification_meta
        )

        await notification_service.create_notification(
            notification_type=NotificationType.commit_submit,
            recipients_ids=[model.post.author_id],
            meta=notification_meta
        )

    return {
        "commit_id": model.id
    }


@router.patch(
    "/{commit_id}/approve",
    status_code=status.HTTP_200_OK,
    response_model=CommitRead,
    tags=["Commits"]
)
async def commit_approve(
        commit_id: str,
        user: User = Depends(current_user),
        commit_service: CommitService = Depends(get_commit_service),
        notification_service: NotificationService = Depends(get_notification_service),
        post_service: PostService = Depends(get_post_service)
):
    """
    Approve commit and add it's post to target collection.
    Commit can be approved only by collection's author.
    Target collection shouldn't contain committed post.
    """
    model: Commit = await commit_service.get_one(commit_id)
    post: Post = await post_service.get(model.post_id)

    if post is None:
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    if await commit_service.is_approved(commit_id):
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    if await post_service.is_author(model.collection_id, user.id):
        response = await commit_service.approve(commit_id)
        stmt = select(Collection).where(Collection.id == model.collection_id).options(
            selectinload(Collection.author)
        )
        result = await commit_service.session.execute(stmt)
        collection: Collection = result.scalar_one()
        await notification_service.create_notification(
            notification_type=NotificationType.commit_approve,
            recipients_ids=[model.author_id],
            meta={
                "commit_post_id": model.post_id,
                "commit_post_type": model.post.type,
                "commit_post_title": getattr(model.post, "title", None),
                "commit_post_cover": getattr(model.post, "cover", None),
                "commit_collection_id": model.collection_id,
                "commit_collection_title": collection.title,
                "collection_author_id": user.id,
                "collection_author_avatar": collection.author.avatar,
                "collection_author_username": collection.author.username,
                "collection_author_name": collection.author.name
            }
        )

        await notification_service.create_notification(
            notification_type=NotificationType.post_save,
            recipients_ids=[model.post.author_id],
            meta={
                "commit_post_id": model.post_id,
                "commit_post_type": model.post.type,
                "commit_post_title": getattr(model.post, "title", None),
                "commit_post_cover": getattr(model.post, "cover", None),
                "commit_collection_id": model.collection_id,
                "commit_collection_title": collection.title,
                "collection_author_id": user.id,
                "collection_author_avatar": collection.author.avatar,
                "collection_author_username": collection.author.username,
                "collection_author_name": collection.author.name
            }
        )

        toci_im_service: TociImService = get_toci_im_service()
        await toci_im_service.join_collection_chat(
            collection_id=model.collection_id,
            user_id=model.author_id
        )
        return response
    raise HTTPException(status.HTTP_403_FORBIDDEN)


@router.patch(
    "/{collection_id}/commits/approve_all",
    status_code=status.HTTP_200_OK,
    response_model=List[CommitRead],
    tags=["Commits"]
)
async def commits_approve_all(
        collection_id: str,
        user: User = Depends(current_user),
        commit_service: CommitService = Depends(get_commit_service),
        notification_service: NotificationService = Depends(get_notification_service),
        post_service: PostService = Depends(get_post_service)
):
    """
    Approve all commits under the target collection.
    Only the collection's author can perform this action.
    """
    if not await post_service.is_author(collection_id, user.id):
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    # Load commits of the collection
    stmt = (
        select(Commit)
        .where(
            and_(
                Commit.collection_id == collection_id,
                Commit.status == CommitStatus.NEW,
            )
        )
        .options(
            selectinload(Commit.post),
            selectinload(Commit.author),
        )
        .order_by(Commit.created_at.asc())
    )
    result = await commit_service.session.execute(stmt)
    commits: List[Commit] = list(result.scalars().all())

    if not commits:
        return []

    # Preload collection for notifications
    stmt_collection = select(Collection).where(Collection.id == collection_id).options(
        selectinload(Collection.author)
    )
    collection: Collection = (await commit_service.session.execute(stmt_collection)).scalar_one()

    approved_commits: List[Commit] = []
    toci_im_service: TociImService = get_toci_im_service()

    for c in commits:
        # Validate post exists and not already approved
        post: Post = await post_service.get(c.post_id)
        if post is None:
            raise HTTPException(status.HTTP_400_BAD_REQUEST)
        if await commit_service.is_approved(c.id):
            # Skip already approved just in case
            continue

        # Approve via service
        approved = await commit_service.approve(c.id)
        approved_commits.append(approved)

        # Notify commit author
        await notification_service.create_notification(
            notification_type=NotificationType.commit_approve,
            recipients_ids=[c.author_id],
            meta={
                "commit_post_id": c.post_id,
                "commit_post_type": c.post.type,
                "commit_post_title": getattr(c.post, "title", None),
                "commit_post_cover": getattr(c.post, "cover", None),
                "commit_collection_id": c.collection_id,
                "commit_collection_title": collection.title,
                "collection_author_id": user.id,
                "collection_author_avatar": collection.author.avatar,
                "collection_author_username": collection.author.username,
                "collection_author_name": collection.author.name,
            },
        )

        # Add commit author to collection chat
        await toci_im_service.join_collection_chat(
            collection_id=c.collection_id,
            user_id=c.author_id,
        )

    return approved_commits


@router.patch(
    "/{commit_id}/reject",
    status_code=status.HTTP_200_OK,
    response_model=CommitRead,
    tags=["Commits"]
)
async def commit_reject(
        commit_id: str,
        update_data: CommitUpdate,
        user: User = Depends(current_user),
        commit_service: CommitService = Depends(get_commit_service),
        notification_service: NotificationService = Depends(get_notification_service),
        post_service: PostService = Depends(get_post_service)
):
    """
    Reject commit.
    Commit can be rejected only by collection's author.
    """
    model: Commit = await commit_service.get_one(commit_id)
    stmt = select(Collection).where(Collection.id == model.collection_id).options(
        selectinload(Collection.author)
    )
    result = await commit_service.session.execute(stmt)
    collection: Collection = result.scalar_one()

    if await post_service.is_author(model.collection_id, user.id):
        response = await commit_service.reject(commit_id, update_data)
        await notification_service.create_notification(
            notification_type=NotificationType.commit_reject,
            recipients_ids=[model.author_id],
            meta={
                "commit_id": model.id,
                "commit_post_id": model.post_id,
                "commit_post_type": model.post.type,
                "commit_post_title": getattr(model.post, "title", None),
                "commit_post_cover": getattr(model.post, "cover", None),
                "commit_collection_id": model.collection_id,
                "commit_collection_title": collection.title,
                "collection_author_id": user.id,
                "collection_author_avatar": collection.author.avatar,
                "collection_author_username": collection.author.username,
                "collection_author_name": collection.author.name
            }
        )
        return response
    raise HTTPException(status.HTTP_400_BAD_REQUEST)
