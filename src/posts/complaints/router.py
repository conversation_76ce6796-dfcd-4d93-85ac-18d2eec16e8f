from typing import Literal

from fastapi import Depends, status, HTTPException
from fastapi.routing import APIRouter
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, delete, text

from src.auth import current_user
from src.database.session import get_session
from src.database.models import Complaint, User, Post, Comment, Author
from src.database.schemas.ComplaintRead import ComplaintRead
from src.common.dependencies import admin
from src.common.constants import ComplaintStatus
from src.posts.logger import logger
from .dependencies import valid_optional_post_id, valid_optional_author_id, valid_complaint, valid_complaint_id
from .schemas import ComplaintCreate


router = APIRouter(prefix="/complaints")
add_pagination(router)


@router.get(
    "",
    status_code=status.HTTP_200_OK,
    response_model=Page[ComplaintRead],
    dependencies=[Depends(admin)]
)
async def get(
        params: Params = Depends(),
        sort: Literal["new", "old"] = "new",
        author_id: str | None = Depends(valid_optional_author_id),
        post_id: str | None = Depends(valid_optional_post_id),
        session: AsyncSession = Depends(get_session)
):
    stmt = select(Complaint).where(Complaint.status == ComplaintStatus.OPEN)
    if author_id:
        stmt = stmt.where(Complaint.author_id == author_id)
    if post_id:
        stmt = stmt.where(Complaint.post_id == post_id)
    if sort == "new":
        stmt = stmt.order_by(Complaint.created_at.desc())
    else:
        stmt = stmt.order_by(Complaint.created_at.asc())
    return await paginate(session, stmt, params)


@router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=ComplaintRead,
    deprecated=True,
    summary="创建投诉 (已弃用)",
    description="⚠️ **此接口已弃用** - 请使用统一的举报接口 `POST /reports/` 替代。该接口将在未来版本中移除。"
)
async def create(
        data: ComplaintCreate,
        user: User = Depends(current_user),
        session: AsyncSession = Depends(get_session)
):
    if not data.id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Valid ID required")

    if data.type == "post":
        stmt = select(Post).where(Post.id == data.id)
        post = (await session.execute(stmt)).first()
        if not post:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid post_id")

        stmt = insert(Complaint).values(author_id=user.id, post_id=data.id, reason=data.reason).returning(Complaint)
        complaint = (await session.execute(stmt)).fetchone()
        stmt = text(f"""
            UPDATE posts
            SET complaint_count = complaint_count + 1
            where id = '{data.id}'
        """)
        await session.execute(stmt)
    elif data.type == "comment":
        stmt = select(Comment).where(Comment.id == data.id)
        comment = (await session.execute(stmt)).first()
        if not comment:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid comment_id")

        stmt = insert(Complaint).values(author_id=user.id, comment_id=data.id, reason=data.reason).returning(Complaint)
        complaint = (await session.execute(stmt)).fetchone()
    elif data.type == "user":
        stmt = select(Author).where(Author.id == data.id)
        author = (await session.execute(stmt)).first()
        if not author:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user_id")

        stmt = insert(Complaint).values(author_id=user.id, reportee_id=data.id, reason=data.reason).returning(Complaint)
        complaint = (await session.execute(stmt)).fetchone()
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid post_id")
    await session.commit()
    return ComplaintRead.from_orm(complaint[0])


@router.delete(
    "/{complaint_id}",
    dependencies=[Depends(admin)]
)
async def delete_complaint(complaint_id: str = Depends(valid_complaint_id), session: AsyncSession = Depends(get_session)):
    stmt = delete(Complaint).where(Complaint.id == complaint_id).returning(Complaint)
    complaint: Complaint = (await session.execute(stmt)).fetchone()[0]
    stmt = text(f"""
            UPDATE posts
            SET complaint_count = complaint_count - 1
            where id = '{complaint.post_id}'
        """)
    await session.execute(stmt)
    await session.commit()


