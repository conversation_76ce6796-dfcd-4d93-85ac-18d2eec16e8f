from fastapi import status, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.auth import current_user
from src.database.models import Author, Post, Complaint, User
from src.database.session import get_session
from .schemas import ComplaintCreate


async def valid_optional_author_id(
        author_id: str | None = None,
        session: AsyncSession = Depends(get_session)
) -> str | None:
    if not author_id:
        return author_id
    stmt = select(Author).where(Author.id == author_id)
    author = (await session.execute(stmt)).first()
    if not author:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid author_id")
    return author_id


async def valid_optional_post_id(
        post_id: str | None = None,
        session: AsyncSession = Depends(get_session)
) -> str | None:
    if not post_id:
        return post_id
    stmt = select(Post).where(Post.id == post_id)
    post = (await session.execute(stmt)).first()
    if not post:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid post_id")
    return post_id


async def valid_complaint(
        data: ComplaintCreate,
        user: User = Depends(current_user),
        session: AsyncSession = Depends(get_session)
):
    post_id = await valid_optional_post_id(data.post_id, session)
    stmt = select(Complaint).where(and_(
        Complaint.reason == data.reason,
        Complaint.author_id == user.id,
        Complaint.post_id == post_id
    ))
    complaint = (await session.execute(stmt)).first()
    if complaint:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Duplicate complaint")
    return data


async def valid_complaint_id(
        complaint_id: str,
        session: AsyncSession = Depends(get_session)
):
    stmt = select(Complaint).where(Complaint.id == complaint_id)
    complaint = (await session.execute(stmt)).first()
    if not complaint:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Complaint with id {complaint} does not exist"
        )
    return complaint_id
