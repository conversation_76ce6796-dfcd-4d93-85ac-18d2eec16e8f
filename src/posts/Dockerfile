# Posts Service - Simplified using enhanced base
# Reduced from 76 lines to ~25 lines!

ARG PYTHON_VERSION=3.10.12
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv in builder stage
USER root
RUN pip install --no-cache-dir uv
USER appuser

# Copy service-specific requirements
COPY src/posts/requirements.txt /tmp/posts-requirements.txt
COPY src/auth/requirements.txt /tmp/auth-requirements.txt
COPY src/authors/requirements.txt /tmp/authors-requirements.txt
COPY src/media_core/requirements.txt /tmp/media_core-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/posts-requirements.txt
RUN uv pip install --no-cache -r /tmp/auth-requirements.txt
RUN uv pip install --no-cache -r /tmp/authors-requirements.txt
RUN uv pip install --no-cache -r /tmp/media_core-requirements.txt

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Add service scripts to PATH
ENV PATH="$PATH:/src/posts/scripts"

# Copy service-specific code and dependencies
COPY --chown=appuser:appuser src/posts /src/posts
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database
COPY --chown=appuser:appuser src/auth /src/auth
COPY --chown=appuser:appuser src/authors /src/authors
COPY --chown=appuser:appuser src/topics /src/topics
COPY --chown=appuser:appuser src/media_core /src/media_core
COPY --chown=appuser:appuser src/im /src/im
COPY --chown=appuser:appuser src/reports /src/reports
COPY --chown=appuser:appuser src/notifications /src/notifications
COPY --chown=appuser:appuser src/memecoin /src/memecoin
COPY --chown=appuser:appuser src/agora /src/agora

# Set executable permissions for start script
RUN chmod +x /src/posts/scripts/start.sh
