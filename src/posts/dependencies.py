from typing import Optional, Union
from fastapi import Depends, HTTPException, status, Body
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from src.database.models import Post, User, Image, Video
from src.auth import optional_user, current_user
from src.memecoin.services import MemeService
from src.memecoin.dependencies import get_meme_service
from .schemas import Click, PostId
from .settings import settings
from .logger import logger
from ..common.recommender import RecommenderClient


async def valid_post_id(post_id: PostId, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(Post).where(Post.id == post_id.post_id)
    post = (await session.execute(stmt)).first()
    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post does not exist")
    return post_id.post_id


async def valid_click(click: Click, session: AsyncSession = Depends(get_session)) -> Click:
    posts = set(click.current_recommendations or [])
    posts.add(click.post_id)
    stmt = select(func.count(Post.id)).where(Post.id.in_(posts))
    count = (await session.execute(stmt)).scalar_one()
    if count != len(posts):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Schema contains an invalid post id")
    return click


# 安全地创建 recommender_client，如果配置缺失则为 None
try:
    recommender_client = RecommenderClient(settings.RECOMMENDER_TOKEN) if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN else None
except Exception:
    recommender_client = None


