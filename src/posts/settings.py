from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8',case_sensitive=False, extra="ignore")

    DATABASE_URL: str
    RECOMMENDER_TOKEN: str
    VIEW_FREQUENCY: int = 3600

    ETH_CHAIN_ID: int
    
    # Image proxy security settings
    IMAGE_PROXY_SECRET_KEY: str = "default-secret-change-in-production"


settings = Settings()
