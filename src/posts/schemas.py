from pydantic import BaseModel, Field, validator, ConfigDict, field_validator, computed_field
from typing import Optional, Literal, Union, List, Dict, Any, Annotated

from src.common.recommender import ClickSource
from src.posts.images.schemas import ImageDataStored, ImageRead
from src.posts.videos.schemas import VideoRead


class BindingTokenInfo(BaseModel):
    """Token information for locked content display"""
    token_address: str
    token_symbol: str = ""
    image_url: str = ""

    model_config = ConfigDict(extra="ignore")

from src.database.schemas import (
    Post,
    AuthorRead,
    ArticleRead,
    CollectionRead,
    FileRead,
    CommentRead
)


class ArticleSchema(ArticleRead):
    author: AuthorRead
    view_count: int
    is_liked: Optional[bool]
    is_in_collection: Optional[bool]


class ImageSchema(ImageRead):
    author: AuthorRead
    is_liked: Optional[bool]
    is_in_collection: Optional[bool]


class FileSchema(FileRead):
    author: AuthorRead
    is_liked: Optional[bool]
    is_in_collection: Optional[bool]


class VideoSchema(VideoRead):
    author: AuthorRead
    is_liked: Optional[bool]
    is_in_collection: Optional[bool]

ContentSchema = Annotated[Union[ImageSchema, VideoSchema], Field(discriminator="type")]

class CollectionSchema(CollectionRead):
    author: AuthorRead
    is_subscribed: Optional[bool]
    is_post_in_collection: Optional[bool]

    # 原始最小单位存储，隐藏不直接返回
    holdview_amount: Optional[int] = Field(default=0, exclude=True)

    # 群聊加群门槛（UI数量）：floor(holdview_amount / DEFAULT_DECIMALS)
    @computed_field  # type: ignore[misc]
    @property
    def chat_join_threshold(self) -> int:
        try:
            raw = int(self.holdview_amount or 0)
            return raw
        except Exception:
            return 0


class CollectionWithIsContributor(CollectionSchema):
    is_contributor: Optional[bool]
    can_commit: Optional[bool]


class CommentSchema(CommentRead):
    author: AuthorRead
    is_liked: Optional[bool]


class PostRead(Post):
    title: Optional[str] = None
    text: Optional[str] = None
    description: Optional[str] = None
    cover: Optional[str] = None
    url: Optional[str] = None

    author: AuthorRead
    is_liked: Optional[bool]
    is_in_collection: Optional[bool]

    height: Optional[int] = None
    width: Optional[int] = None


class Collection(PostRead):
    type: Literal["Collection"]
    content_type: str
    contents_count: int
    original_cover: str | None
    is_subscribed: bool


class PostWithComplaints(PostRead):
    complaint_count: int


class Click(BaseModel):
    post_id: str
    source: ClickSource
    current_recommendations: list[str] | None = None


class PostId(BaseModel):
    post_id: str


class PostUpdateRequest(BaseModel):
    """更新帖子请求模型 - 支持description, tags_list, images字段"""
    description: Optional[str] = None
    tags_list: Optional[List[str]] = None
    images: Optional[List[ImageDataStored]] = None

    class Config:
        schema_extra = {
            "example": {
                "description": "Updated description",
                "tags_list": ["tag1", "tag2", "tag3"],
                "images": [
                    {
                        "url": "https://example.com/image1.jpg",
                        "width": 1920,
                        "height": 1080,
                        "name": "image1.jpg",
                        "content_type": "image/jpeg"
                    }
                ]
            }
        }


PostSchema = Union[Collection, PostRead]


class BatchHoldviewUpdateRequest(BaseModel):
    """Batch update holdview_amount for multiple posts"""
    post_ids: List[str]
    holdview_amount: int = Field(
        ge=0, 
        le=10000,
        description="USD price in cents (0 = free, 100 = $1.00, max = $100.00)"
    )

    @validator('holdview_amount')
    def validate_usd_cents(cls, v):
        if v < 0:
            raise ValueError('USD amount cannot be negative')
        if v > 10000:  # $100 maximum
            raise ValueError('USD amount cannot exceed $100.00 (10000 cents)')
        return v

    class Config:
        schema_extra = {
            "example": {
                "post_ids": [
                    "Bf8Acr3b0o1",
                    "BgFedQR3RdY"
                ],
                "holdview_amount": 150  # $1.50
            }
        }


class BatchUpdateStatus(BaseModel):
    """Status for individual post update"""
    id: str
    status: Literal["updated", "not_found", "error"]
    error: Optional[str] = None


class BatchHoldviewUpdateResponse(BaseModel):
    """Response for batch holdview update"""
    updated_count: int
    posts: List[BatchUpdateStatus]

    class Config:
        schema_extra = {
            "example": {
                "updated_count": 2,
                "posts": [
                    {"id": "Bf8Acr3b0o1", "status": "updated"},
                    {"id": "BgFedQR3RdY", "status": "updated"}
                ]
            }
        }
