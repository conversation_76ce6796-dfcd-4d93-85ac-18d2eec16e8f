from src.auth import current_user
from src.database.session import AsyncSession, get_session

from fastapi import Depends, HTTPException, status
from sqlalchemy import select
from src.database.models.User import User
from src.database.models.Permission import Permission
from src.database.models.Post import Post

from src.common.constants import PermissionTypes


async def super_admin(user: User = Depends(current_user), session: AsyncSession = Depends(get_session)) -> User:
    stmt = select(Permission).where(
        Permission.author_id == user.id,
        Permission.name == PermissionTypes.super_admin
    )
    permission = (await session.execute(stmt)).first()
    if not permission:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)
    return user


async def valid_post(post_id: str, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(Post).where(Post.id == post_id)
    post = (await session.execute(stmt)).first()
    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Post with id '{post}' does not exist")
    return post_id


async def valid_non_admin(user_id: str, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(User).where(User.id == user_id)
    user = (await session.execute(stmt)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"User with id '{user}' does not exist")
    stmt = select(Permission).where(Permission.author_id == user_id)
    permission: None | tuple[Permission] = (await session.execute(stmt)).first()
    if permission and permission[0].name in [PermissionTypes.admin, PermissionTypes.super_admin]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"Provided user is an admin")
    return user_id


async def valid_admin(user_id: str, session: AsyncSession = Depends(get_session)) -> str:
    stmt = select(Permission).where(Permission.author_id == user_id)
    permission: None | tuple[Permission] = (await session.execute(stmt)).first()
    if not permission or permission[0].name != PermissionTypes.admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"Provided user is not an admin")
    return user_id
