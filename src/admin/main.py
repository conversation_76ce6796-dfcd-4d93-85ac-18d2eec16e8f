from typing import Literal

from fastapi import Depends, status, FastAPI
from fastapi_pagination import add_pagination
from fastapi.openapi.utils import get_openapi

from src.common.dependencies import admin
from src.common.recommender import RecommenderClient
from src.infra.logger import get_logger
from src.infra.app import create_app

from src.admin.service import AdminService
from src.admin.dependecies import super_admin, valid_post, valid_non_admin, valid_admin
from src.admin.schemas import UserID, StartupModeRequest, StartupModeResponse, SetUserRoleRequest
from src.admin.settings import settings
logger = get_logger("admin", level="INFO", file_path="latest.log")

app = create_app(title="Admin", version="1.0", description="Admin API", request_logger=logger)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="Admin API",
        version="1.0.0",
        description="Admin API",
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }
    openapi_schema["security"] = [{"BearerAuth": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

add_pagination(app)

# 安全地创建 recommender_client，如果配置缺失则为 None
try:
    recommender_client = RecommenderClient(settings.RECOMMENDER_TOKEN) if hasattr(settings, 'RECOMMENDER_TOKEN') and settings.RECOMMENDER_TOKEN else None
except Exception:
    recommender_client = None

@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    return {"status": "ok"}

@app.post("/mode", response_model=StartupModeResponse, status_code=status.HTTP_200_OK)
async def get_startup_mode(
    request: StartupModeRequest,
    service: AdminService = Depends(AdminService.get_instance)
) -> StartupModeResponse:
    """
    根据build和os参数返回启动模式
    """
    mode = await service.get_startup_mode(build=request.build, os=request.os)
    return StartupModeResponse(mode=mode)

@app.post("/", status_code=status.HTTP_201_CREATED, dependencies=[Depends(super_admin)])
async def add_admin(
    user_id: UserID, service: AdminService = Depends(AdminService.get_instance)
):
    user_id: str = await valid_non_admin(
        user_id=user_id.user_id, session=service.session
    )
    await service.add_admin(user_id=user_id)


@app.delete(
    "/posts/{post_id}", status_code=status.HTTP_200_OK, dependencies=[Depends(admin)]
)
async def delete_post(
    post_id: str = Depends(valid_post),
    mode: Literal["mark", "delete"] = "delete",
    service: AdminService = Depends(AdminService.get_instance),
):
    match mode:
        case "mark":
            await service.mark_post_as_deleted(post_id)
        case "delete":
            await service.delete_post(post_id)
    if recommender_client:
        await recommender_client.delete_post(post_id=post_id)


@app.delete(
    "/users/{user_id}", status_code=status.HTTP_200_OK, dependencies=[Depends(admin)]
)
async def delete_user(
    user_id: str = Depends(valid_non_admin),
    mode: Literal["mark", "delete"] = "delete",
    service: AdminService = Depends(AdminService.get_instance),
):
    match mode:
        case "mark":
            await service.mark_user_as_suspended(user_id)
        case "delete":
            await service.delete_user(user_id)
    if recommender_client:
        await recommender_client.delete_user(user_id=user_id)


@app.post("/users/{user_id}/ban", summary="Bans user", dependencies=[Depends(admin)])
async def ban_user(
    user_id: str = Depends(valid_non_admin),
    service: AdminService = Depends(AdminService.get_instance),
):
    await service.ban_user(
        user_id=user_id,
    )

    return {"message": f"User {user_id} banned."}


@app.post("/users/{user_id}/unban", summary="Unbans user", dependencies=[Depends(admin)])
async def unban_user(
    user_id: str,
    service: AdminService = Depends(AdminService.get_instance),
):
    await service.unban_user(
        user_id=user_id,
    )

    return {"message": f"User {user_id} unbanned."}


@app.put(
    "/users/{user_id}/role",
    summary="Set user role",
    dependencies=[Depends(super_admin)]
)
async def set_user_role(
    user_id: str,
    body: SetUserRoleRequest,
    service: AdminService = Depends(AdminService.get_instance),
):
    await service.set_user_role(
        user_id=user_id,
        role=body.role,
    )


@app.delete(
    "/{user_id}", status_code=status.HTTP_200_OK, dependencies=[Depends(super_admin)]
)
async def remove_admin(
    user_id: str = Depends(valid_admin),
    service: AdminService = Depends(AdminService.get_instance),
):
    await service.remove_admin(
        user_id=user_id,
    )
