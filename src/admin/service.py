import json
import os
from datetime import datetime

from fastapi import Depends
from sqlalchemy import select, insert, delete, update, literal, any_, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session
from src.common.constants import PermissionTypes
from src.common.constants import ReportStatus
from src.common.constants import UserStatus
from src.database.models.Permission import Permission
from src.database.constants import PostStatus
from src.database.models.User import User
from src.database.models.Report import Report
from src.database.models.Collection import Collection
from src.database.models.SavedPosts import SavedPost
from src.database.models.Post import Post
from src.database.models.Translation import Translation

from .logger import logger


class AdminService:
    _config_cache = None  # 类变量，所有实例共享
    
    def __init__(self, session: AsyncSession):
        self.session = session

    @classmethod
    def _load_config(cls) -> dict:
        """加载配置文件（类方法，配置在所有实例间共享）"""
        if cls._config_cache is None:
            config_path = ""
            try:
                config_path = os.path.join(os.path.dirname(__file__), 'config.json')
                with open(config_path, 'r', encoding='utf-8') as f:
                    cls._config_cache = json.load(f)
                logger.info(f"Config loaded from {config_path}")
            except FileNotFoundError:
                logger.error(f"Config file not found: {config_path}")
                cls._config_cache = {"startup_modes": {"default": {"mode": "strict"}}}
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing config file: {e}")
                cls._config_cache = {"startup_modes": {"default": {"mode": "strict"}}}
            except Exception as e:
                logger.error(f"Error loading config: {e}")
                cls._config_cache = {"startup_modes": {"default": {"mode": "strict"}}}
        return cls._config_cache

    async def add_admin(self, user_id: str) -> None:
        stmt = insert(Permission).values(name=PermissionTypes.admin, author_id=user_id)
        await self.session.execute(stmt)
        await self.session.commit()

    async def remove_admin(self, user_id: str) -> None:
        stmt = delete(Permission).where(Permission.author_id == user_id)
        await self.session.execute(stmt)
        await self.session.commit()

    async def delete_from_collections(self, post_id: str):
        stmt = delete(SavedPost).where(SavedPost.post_id == post_id)
        await self.session.execute(stmt)
        stmt = select(Collection).where(literal(post_id) == any_(Collection.contents))
        collections = (await self.session.execute(stmt)).scalars().all()
        for collection in collections:
            collection.contents.remove(post_id)
            self.session.add(collection)

    async def delete_post(self, post_id: str) -> None:
        delete_subquery = (
            select(Translation.post_id)
            .where(Translation.original_id != Translation.post_id)  # type: ignore
            .where(Translation.original_id == post_id)
            .subquery()
        )
        stmt = delete(Post).where(Post.id == any_(delete_subquery))  # type: ignore
        await self.session.execute(stmt)
        stmt = delete(Post).where(Post.id == post_id)
        await self.session.execute(stmt)
        await self.delete_from_collections(post_id)
        await self.session.commit()

    async def mark_post_as_deleted(self, post_id: str) -> None:
        stmt = update(Post).where(Post.id == post_id).values()
        await self.session.execute(stmt)
        stmt = (
            update(Post)
            .where(Translation.original_id == post_id, Translation.post_id == Post.id)
            .values(status=PostStatus.DELETED)
        )
        await self.session.execute(stmt)
        stmt = (
            update(Report)
            .where(Report.target_id == post_id)
            .values(
                handled_at=datetime.now(),
                is_handled=True,
                status=ReportStatus.COMPLETED,
            )
        )
        await self.session.execute(stmt)
        await self.delete_from_collections(post_id)
        await self.session.commit()

    async def delete_user(self, user_id: str) -> None:
        stmt = delete(User).where(User.id == user_id)  # type: ignore
        await self.session.execute(stmt)
        await self.session.commit()

    async def mark_user_as_suspended(self, user_id: str) -> None:
        stmt = update(User).where(User.id == user_id).values(status=UserStatus.SUSPENDED)  # type: ignore
        await self.session.execute(stmt)
        await self.session.commit()

    async def ban_user(self, user_id: str):
        # Set status to `Banned`
        await self.session.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                status=UserStatus.BANNED,
            )
        )
        # Mark all posts as suspended
        await self.session.execute(
            update(Post)
            .where(and_(Post.author_id == user_id, Post.status == PostStatus.POSTED))
            .values(
                status=PostStatus.SUSPENDED,
            )
        )
        # Mark all scheduled posts as drafted
        await self.session.execute(
            update(Post)
            .where(and_(Post.author_id == user_id, Post.status == PostStatus.SCHEDULED))
            .values(
                status=PostStatus.DRAFTED,
            )
        )
        # Mark all reports as completed
        await self.session.execute(
            update(Report)
            .where(Report.target_id == user_id)
            .values(
                handled_at=datetime.now(),
                is_handled=True,
                status=ReportStatus.COMPLETED,
            )
        )

        posts_ids = (await self.session.execute(
            select(Post.id)
            .where(Post.author_id == user_id)
        )).scalars().all()

        await self.session.execute(
            update(Report)
            .where(Report.target_id.in_(posts_ids))
            .values(
                handled_at=datetime.now(),
                is_handled=True,
                status=ReportStatus.COMPLETED,
            )
        )

        await self.session.commit()

    async def unban_user(self, user_id: str):
        # Set status to `active`
        await self.session.execute(
            update(User).where(User.id == user_id).values(status=UserStatus.ACTIVE)
        )
        # Mark all suspended posts as `POSTED`
        await self.session.execute(
            update(Post)
            .where(and_(Post.author_id == user_id, Post.status == PostStatus.SUSPENDED))
            .values(
                status=PostStatus.POSTED,
            )
        )
        await self.session.commit()

    async def set_user_role(self, user_id: str, role: PermissionTypes):
        stmt = select(Permission).where(Permission.author_id == user_id)
        row: Permission | None = (await self.session.execute(stmt)).scalars().one_or_none()

        if row is None:
            stmt = insert(Permission).values(name=role, author_id=user_id)
        else:
            stmt = update(Permission).where(Permission.author_id == user_id).values(name=role)

        await self.session.execute(stmt)
        await self.session.commit()

    async def get_startup_mode(self, build: str, os: str) -> str:
        """
        根据build和os参数从配置文件返回启动模式
        """
        logger.info(f"Getting startup mode for build: {build}, os: {os}")
        
        config = self._load_config()
        startup_modes = config.get("startup_modes", {})
        
        # 标准化os参数
        os_lower = os.lower()
        
        # 查找对应的配置
        if os_lower in startup_modes:
            os_config = startup_modes[os_lower]
            # 首先查找具体的build版本
            if build in os_config:
                mode = os_config[build].get("mode", "strict")
                logger.info(f"Found specific config for {os_lower}/{build}: {mode}")
                return mode
            # 如果没有找到具体版本，使用默认配置
            elif "default" in os_config:
                mode = os_config["default"].get("mode", "strict")
                logger.info(f"Using default config for {os_lower}: {mode}")
                return mode
        
        # 如果没有找到对应的os配置，使用全局默认
        default_mode = startup_modes.get("default", {}).get("mode", "strict")
        logger.info(f"Using global default config: {default_mode}")
        return default_mode

    @classmethod
    def get_instance(cls, session: AsyncSession = Depends(get_session)):
        return cls(session)
