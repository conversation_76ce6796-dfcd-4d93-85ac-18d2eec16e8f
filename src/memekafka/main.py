import asyncio
import json
import time
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from types import SimpleNamespace

import httpx
from redis.asyncio import Redis
from confluent_kafka import Consumer
from sqlalchemy import text
from web3 import Web3

from src.common.blockchain import BlockchainConnectorGetter, BlockchainConnector
from src.common.caching.caching_client import CachingClient
from src.common.constants import UserTransactionType, UserTransactionStatus
from src.database.session import get_session_context, init_engines
from src.common.constants import PairStatus, MEMECOIN_REDIS_ENDPOINT
from src.memecoin.logger import logger
from src.memecoin.schemas import TokenCache
from src.memecoin.utils import get_bnb_price, get_sol_price, get_eth_price
from src.memekafka.constants import KafkaEvent
from src.memecoin.settings import settings
from src.common.redis_cli import RedisCli
from src.memekafka.utils import invalidate_rankings_cache, update_token_redis


def to_checksum_address(address: str) -> str:
    """Convert address to checksum format"""
    return Web3.to_checksum_address(address)


class KafkaConsumerService:

    def __init__(self, web3: BlockchainConnector):
        logger.info("Initializing KafkaConsumerService")
        self.w3 = web3
        # Use RedisCli as unified entry
        logger.info("Redis client initialized via RedisCli")
        
        self._caching_client: CachingClient = CachingClient(
            client=RedisCli.async_(),
            logger=logger,
        )
        logger.info("Redis caching client initialized")
        
        # Set chain_id based on blockchain type
        if settings.BLOCKCHAIN_TYPE == "SOL":
            self._chain_id = settings.SOLANA_CHAIN_ID
            self._topic = settings.KAFKA_SOL_TOPIC
        elif settings.BLOCKCHAIN_TYPE == "ETH":
            self._chain_id = settings.ETH_CHAIN_ID
            self._topic = settings.KAFKA_ETH_TOPIC
        else:  # Default to BSC/BNB
            self._chain_id = settings.BSC_CHAIN_ID
            self._topic = settings.KAFKA_BSC_TOPIC
        logger.info("Chain ID set to: %s", self._chain_id)
        
        self._consumer = self.create_consumer()
        logger.info(f"Kafka consumer created with group ID: {settings.KAFKA_MEMETRACE_GROUP_ID}")
        
        self._event_handlers = {
            KafkaEvent.TOKEN_CREATED.value: self.handle_token_created,
            KafkaEvent.BUY.value: self.handle_buy,
            KafkaEvent.SELL.value: self.handle_sell,
            KafkaEvent.SEND.value: self.handle_send,
            KafkaEvent.GRADUATE.value: self.handle_graduate,
            KafkaEvent.BUY_WITH_USDT.value: self.handle_buy_with_usdt,
            KafkaEvent.SELL_FOR_USDT.value: self.handle_sell_for_usdt,
            KafkaEvent.L1_TOKEN_CREATED.value: self.handle_l1_token_created,
            KafkaEvent.TOKEN_BRIDGED.value: self.handle_token_bridged,
            KafkaEvent.CREATE_L2_TOKEN.value: self.handle_create_l2_token,
            KafkaEvent.TOKEN_COMPLETED.value: self.handle_token_completed,
        }
        logger.info("Event handlers registered: %s", list(self._event_handlers.keys()))
        self._running = False

    async def _get_user_info(self, address: str) -> tuple[str, str]:
        """Get user_id and username for a given wallet address"""
        try:
            async with get_session_context("write") as session:
                # Query to get user info from wallet address
                query = text("""
                    SELECT uw.user_id, a.username
                    FROM user_wallet uw
                    LEFT JOIN authors a ON uw.user_id = a.user_id
                    WHERE uw.pubkey = :address
                    LIMIT 1
                """)
                result = await session.execute(query, {"address": address})
                row = result.fetchone()
                
                if row:
                    return row[0] or "", row[1] or ""
                return "", ""
        except Exception as e:
            logger.error(f"Error getting user info for address {address}: {e}")
            return "", ""

    def create_consumer(self):
        conf = {
            "bootstrap.servers": settings.KAFKA_BROKERS,
            "group.id": settings.KAFKA_MEMETRACE_GROUP_ID,
            "auto.offset.reset": "earliest",
            "enable.auto.commit": False,
            "fetch.min.bytes": 500000,
            "fetch.max.bytes": 5_000_000,
            "max.poll.interval.ms": 300000,
        }
        logger.debug("Creating Kafka consumer with config: %s", conf)
        consumer = Consumer(conf)
        
        consumer.subscribe([self._topic])
        logger.info("Subscribed to Kafka topic: %s", self._topic)
        return consumer

    async def close(self):
        """Close the service and release all resources"""
        logger.info("Starting to shut down KafkaConsumerService...")
        self._running = False
        
        # Close Kafka consumer
        if self._consumer:
            logger.info("Closing Kafka consumer...")
            self._consumer.close()
            self._consumer = None
        
        # Close Redis connection
        if self._caching_client and self._caching_client.client:
            logger.info("Closing Redis client...")
            await self._caching_client.client.close()
        
        # No pool disconnection needed
        
        logger.info("KafkaConsumerService fully closed")

    async def run_loop(self):
        processed_message_count = 0
        batch_size = 10
        poll_timeout = 1
        logger.info("Started fetching Kafka data with batch size: %d, poll timeout: %d", batch_size, poll_timeout)
        self._running = True
        try:
            while self._running:
                raw_msg_list = []
                for _ in range(batch_size):
                    if not self._running:
                        break
                    msg = self._consumer.poll(timeout=poll_timeout)
                    if msg is None:
                        break
                    if msg.error():
                        logger.warning("Consumer error: %s", msg.error())
                        continue
                    raw_msg_list.append(msg)

                if raw_msg_list:
                    logger.debug("Received %d messages from Kafka", len(raw_msg_list))
                    await self.process_chain_messages(raw_msg_list)
                    processed_message_count += len(raw_msg_list)
                    self._consumer.commit(asynchronous=True)
                    logger.info(
                        "Finished processing %d messages (total: %d)", 
                        len(raw_msg_list), processed_message_count
                    )
                else:
                    await asyncio.sleep(0.1)
        except Exception as e:
            logger.exception("Exception in consumer loop: %s", e)
        finally:
            logger.info("Exiting consumer loop, cleaning up resources")
            await self.close()

    async def process_chain_messages(self, raw_msg_list):
        start_time = time.perf_counter()
        query_buffer = []
        logger.info("Processing batch of %d messages", len(raw_msg_list))
        
        tasks = [
            self.process_single_message(raw_msg, query_buffer)
            for raw_msg in raw_msg_list
        ]
        await asyncio.gather(*tasks)
        logger.debug("All %d message processing tasks completed", len(tasks))

        async with get_session_context("write") as session:
            logger.debug("Executing %d database queries", len(query_buffer))
            for query, params in query_buffer:
                await session.execute(query, params)
            await session.commit()
            logger.debug("Database transaction committed successfully")

        elapsed = time.perf_counter() - start_time
        logger.info(
            "Processed %d messages and executed %d queries in %.2f seconds (%.2f msgs/sec).",
            len(raw_msg_list),
            len(query_buffer),
            elapsed,
            len(raw_msg_list) / elapsed if elapsed > 0 else 0
        )

    async def process_single_message(self, raw_msg, query_buffer):
        try:
            value_str = raw_msg.value().decode("utf-8")
            msg = json.loads(value_str)
            event = msg.get("event")
            logger.debug("Processing message with event type: %s", event)
            
            handler = self._event_handlers.get(event)
            if handler:
                logger.debug("Found handler for event: %s", event)
                await handler(msg, query_buffer)
                logger.debug("Handler for event %s completed successfully", event)
            else:
                logger.warning("Unknown event: %s, no handler registered", event)
        except json.JSONDecodeError as e:
            logger.warning("Invalid JSON message: %s", raw_msg.value())
            logger.exception(e)
        except Exception as e:
            logger.exception("Error processing message: %s", e)

    async def set_token_info(self, token_cache: TokenCache):
        try:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_cache.token_address}"
            logger.debug(
                "Setting token info in Redis for token=%s, symbol=%s, name=%s, url=%s",
                token_cache.token_address, token_cache.symbol, token_cache.name, token_cache.image_url
            )
            data = token_cache.model_dump()
            await self._caching_client.client.hset(key, mapping=data)
            logger.info("Token info successfully set in Redis for key: %s", key)
        except Exception as e:
            logger.exception(
                "Failed to set token info for %s: %s", token_cache.symbol, e
            )

    async def get_quote_price(self):
        blockchain_type = settings.BLOCKCHAIN_TYPE
        logger.debug("Getting %s quote price from Redis", blockchain_type)
        try:
            if blockchain_type == "SOL":
                quote_price = await get_sol_price(self._caching_client.client)
                logger.debug("SOL quote price: %s", quote_price)
            elif blockchain_type == "ETH":
                quote_price = await get_eth_price(self._caching_client.client)
                logger.debug("ETH quote price: %s", quote_price)
            else:
                quote_price = await get_bnb_price(self._caching_client.client)
                logger.debug("BNB quote price: %s", quote_price)
            return quote_price
        except Exception as e:
            logger.exception("Error retrieving %s price: %s", blockchain_type, e)
            return None

    async def update_token_price(
        self,
        token_address: str,
        price_in_quote: float,
        token_price: float,
        mc: float,
        progress: int,
        timestamp: int,
    ):
        try:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
            logger.debug(
                "Updating token price in Redis - token=%s, price=%.10f, mc=%.2f, progress=%d", 
                token_address, token_price, mc, progress
            )
            async with self._caching_client.client.pipeline() as pipe:
                await pipe.zadd(f"{key}:price", {str(token_price): timestamp})
                await pipe.expire(f"{key}:price", 3*24*3600) # save 3 days
                await pipe.hset(key, f"price_in_quote", str(price_in_quote))
                await pipe.hset(key, "price", str(token_price))
                await pipe.hset(key, "market_cap", str(mc))
                await pipe.hset(key, "progress", str(progress))
                await pipe.execute()
                logger.debug("Redis pipeline executed successfully for token: %s", token_address)
        except Exception as e:
            logger.exception(
                "Error updating token price in Redis for %s: %s", token_address, e
            )

    async def get_token_usd_price(self, token_address: str) -> float:
        """
        获取token的USD价格
        优先从Redis缓存中获取，如果没有则返回0.0
        """
        try:
            key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
            logger.debug("Getting token USD price from Redis for token: %s", token_address)
            
            # 从Redis获取token价格
            price_str = await self._caching_client.client.hget(key, "price")
            if price_str:
                price = float(price_str)
                logger.debug("Token USD price for %s: %.10f", token_address, price)
                return price
            else:
                logger.debug("No USD price found in Redis for token: %s", token_address)
                return 0.0
        except Exception as e:
            logger.exception("Error retrieving token USD price for %s: %s", token_address, e)
            return 0.0

    async def record_transaction_volume(self, token_address: str, volume_usd: float, timestamp: int):
        """
        Record transaction volume data for kline system
        Records volume directly to the kline tables since current kline system may not capture trade volumes
        
        Args:
            token_address: Token contract address
            volume_usd: Transaction volume in USD
            timestamp: Transaction timestamp
        """
        try:
            if volume_usd <= 0:
                logger.debug("Skipping volume recording for %s: volume_usd=%.6f", token_address, volume_usd)
                return
                
            # Store aggregated volume for current minute in Redis for immediate access
            # This allows the existing fetch_24h_volume method to work with real transaction volumes
            minute_timestamp = (timestamp // 60) * 60  # Round down to minute boundary
            volume_minute_key = f"{MEMECOIN_REDIS_ENDPOINT}:volume_1m:{token_address}:{minute_timestamp}"
            
            async with self._caching_client.client.pipeline() as pipe:
                # Increment volume for this minute
                await pipe.incrbyfloat(volume_minute_key, volume_usd)
                # Expire after 7 days
                await pipe.expire(volume_minute_key, 7*24*3600)
                await pipe.execute()
                
            logger.debug("Recorded transaction volume for %s: %.6f USD at minute %d", 
                        token_address, volume_usd, minute_timestamp)
        except Exception as e:
            logger.exception("Error recording transaction volume for %s: %s", token_address, e)

    async def handle_token_created(self, msg, query_buffer):
        data = SimpleNamespace(**msg.get("data", {}))
        token_address = to_checksum_address(data.TokenAddress)
        txid = data.TxID
        created_at = datetime.utcfromtimestamp(data.Timestamp)
        gas_price = data.EffectiveGasPrice
        gas_used = data.GasUsed
        
        logger.info(
            "Processing TOKEN_CREATED event: token=%s, txid=%s, created_at=%s",
            token_address, txid, created_at
        )

        info = await self._caching_client.get(
            MEMECOIN_REDIS_ENDPOINT,
            f"L2_LAUNCH_MEME:{txid}"
        )
        avatar = info.get("avatar", "") if info else ""
        collection_id = info.get("collection_id", "") if info else ""

        # token_cache = TokenCache(
        #     name=data.Name,
        #     symbol=data.Symbol,
        #     image_url=avatar,
        #     token_address=token_address,
        #     status=PairStatus.READY.value,
        # )
        # key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_cache.token_address}"
        # logger.debug("Checking if token exists in cache with key: %s", key)
        # cache = await self._caching_client.client.hgetall(key)
        # if cache:
        #     logger.debug("Token found in cache, skipping update to preserve existing data")
        #     logger.debug(f"[TOKEN_CREATED] Token {data.Symbol} already exists in cache, skipping write")
        # else:
        #     logger.debug("Token not found in cache, creating new token info")
        #     await self.set_token_info(token_cache)

        original_txid = info.get("original_txid", "")

        logger.debug("Updating user_transactions for tx_hash: %s", txid)
        query = text(
            "UPDATE user_transactions SET token_address = :token_address, gas_used = :gas_used, gas_limit = :gas_price, value = :value WHERE tx_hash = :tx_hash AND type = :type"
        )
        query_buffer.append(
            (
                query,
                {
                    "token_address": token_address,
                    "tx_hash": txid,
                    "gas_used": gas_used,
                    "gas_price": gas_price,
                    "value": 0.0,  # CREATE_MEME value should always be 0
                    "type": UserTransactionType.CREATE_MEME.value
                },
            )
        )

        logger.debug("Updating pair for creation_txid: %s", original_txid)
        pair_query = text(
            "UPDATE pair SET base = :token_address, status = :status, base_decimals = :base_decimals, base_created_at = :created_at WHERE creation_txid = :tx_hash"
        )
        query_buffer.append(
            (
                pair_query,
                {
                    "token_address": token_address,
                    "status": PairStatus.READY.value,
                    "base_decimals": 18,
                    "tx_hash": original_txid,
                    "created_at": created_at,
                },
            )
        )

        try:
            await update_token_redis(
                self._caching_client.client,
                token_address=token_address,
                name=data.Name,
                symbol=data.Symbol,
                avatar=avatar,
                collection_id=collection_id
            )
            logger.info(f"Updated Redis cache for token {data.Symbol} ({token_address})")

            # 立即清除排行榜缓存，确保新币立即出现在排行榜中
            await invalidate_rankings_cache(data.Symbol or "Unknown")
        except Exception as e:
            logger.exception(f"Failed to update Redis cache for token {data.Symbol}: {e}")

        logger.info("TOKEN_CREATED event processing completed for token: %s", token_address)

    async def handle_buy(self, msg, query_buffer):
        """
        已废弃：空函数。价格/MC/progress/成交量逻辑已迁移至 handle_buy_with_usdt。
        """
        return

    async def handle_sell(self, msg, query_buffer):
        """
        已废弃：空函数。价格/MC/progress/成交量逻辑已迁移至 handle_sell_for_usdt。
        """
        return

    async def handle_buy_with_usdt(self, msg, query_buffer):
        """
        处理用户用 USDT 买入：
        - 更新价格、MC、进度（progress）与成交量；
        - 同步写入 pair.mc 与 pair.bonding_curve；
        - 仍更新用户持仓与 user_transactions。
        """
        data = SimpleNamespace(**msg.get("data", {}))

        tx_hash = data.TxID
        usdt_amount_in = data.UsdtAmountIn
        token_amount_out = data.TokenAmountOut
        buyer = to_checksum_address(data.Buyer)
        token_address = to_checksum_address(data.TokenAddress)
        decimals = settings.DEFAULT_TOKEN_DECIMALS
        ts = data.Timestamp
        gas_price = data.EffectiveGasPrice
        gas_used = data.GasUsed

        logger.info(
            "Processing BUY_WITH_USDT event: token=%s, txid=%s, buyer=%s, usdt_in=%.6f, tokens_out=%.6f",
            token_address, tx_hash, buyer, usdt_amount_in / settings.USDT_DECIMALS, token_amount_out / settings.DEFAULT_DECIMALS
        )

        # 更新价格、成交量、MC 与进度
        if token_amount_out and token_amount_out > 0:
            price_in_quote = usdt_amount_in / token_amount_out * 1e12
            token_price = price_in_quote
            mc = token_price * settings.DEFAULT_TOTAL_SUPPLY
            progress = await self.w3.progress(token_address)
            await self.update_token_price(token_address, price_in_quote, token_price, mc, progress, ts)
            token_amount_standard = token_amount_out / settings.DEFAULT_DECIMALS if token_amount_out else 0.0
            volume_usd = token_amount_standard * token_price if token_amount_standard and token_price else 0.0
            await self.record_transaction_volume(token_address, volume_usd, ts)
            pair_query = text(
                "UPDATE pair SET mc = :market_cap, bonding_curve = :bonding_curve WHERE base = :token_address"
            )
            query_buffer.append(
                (
                    pair_query,
                    {
                        "market_cap": mc,
                        "bonding_curve": progress,
                        "token_address": token_address,
                    },
                )
            )

        logger.debug("Inserting token holder record for buyer: %s", buyer)
        # Get user info for buyer
        user_id, username = await self._get_user_info(buyer)
        
        insert_query = text(
            """
            INSERT INTO token_holder (chain, address, token_address, amount, decimals, user_id, username)
            VALUES (:chain, :buyer, :token_address, :amount, :decimals, :user_id, :username)
            ON CONFLICT (address, token_address)
            DO UPDATE SET
                amount = EXCLUDED.amount,
                user_id = CASE WHEN EXCLUDED.user_id != '' THEN EXCLUDED.user_id ELSE token_holder.user_id END,
                username = CASE WHEN EXCLUDED.username != '' THEN EXCLUDED.username ELSE token_holder.username END
            """
        )
        query_buffer.append(
            (
                insert_query,
                {
                    "chain": self._chain_id,
                    "buyer": buyer,
                    "token_address": token_address,
                    "amount": token_amount_out,
                    "decimals": decimals,
                    "user_id": user_id,
                    "username": username,
                },
            )
        )

        logger.debug("Updating user_transactions for tx_hash: %s", tx_hash)
        update_query = text(
            "UPDATE user_transactions SET token_address = :token_address, base_amount = :token_amount, quote_amount = :usdt_amount, service_fee = :service_fee, gas_used = :gas_used, gas_limit = :gas_price, value = :value, unit_price = :unit_price WHERE tx_hash = :tx_hash AND type = :type"
        )
        # Convert USDT amount to standard units for service fee calculation
        usdt_amount_standard = usdt_amount_in / 1e6

        # Calculate transaction value in USD: use cached price only
        token_amount_standard = token_amount_out / settings.DEFAULT_DECIMALS if token_amount_out else 0.0
        token_usd_price = await self.get_token_usd_price(token_address)
        transaction_value = token_amount_standard * token_usd_price if token_amount_standard and token_usd_price else 0.0
        service_fee = Decimal(transaction_value * 0.01)

        query_buffer.append(
            (
                update_query,
                {
                    "token_address": token_address,
                    "tx_hash": tx_hash,
                    "token_amount": token_amount_out,
                    "usdt_amount": usdt_amount_in,
                    "service_fee": service_fee,
                    "gas_used": gas_used,
                    "gas_price": gas_price,
                    "value": transaction_value,
                    "unit_price": token_usd_price,
                    "type": UserTransactionType.BUY_TOKEN.value
                },
            )
        )
        logger.info("BUY_WITH_USDT event processing completed for token: %s, service_fee: %d", token_address, service_fee)

    async def handle_sell_for_usdt(self, msg, query_buffer):
        """
        处理用户卖出换 USDT：
        - 更新价格、MC、进度（progress）与成交量；
        - 同步写入 pair.mc 与 pair.bonding_curve；
        - 更新 user_transactions。
        """
        data = SimpleNamespace(**msg.get("data", {}))

        tx_hash = data.TxID
        token_amount_in = data.TokenAmountIn
        usdt_amount_out = data.UsdtAmountOut
        seller = to_checksum_address(data.Seller)
        token_address = to_checksum_address(data.TokenAddress)
        ts = data.Timestamp
        gas_price = data.EffectiveGasPrice
        gas_used = data.GasUsed

        logger.info(
            "Processing SELL_FOR_USDT event: token=%s, txid=%s, seller=%s, tokens_in=%.6f, usdt_out=%.6f",
            token_address, tx_hash, seller, token_amount_in / settings.DEFAULT_DECIMALS, usdt_amount_out / settings.USDT_DECIMALS
        )

        # 更新价格、成交量、MC 与进度
        if token_amount_in and token_amount_in > 0:
            price_in_quote = usdt_amount_out / token_amount_in * 1e12
            token_price = price_in_quote
            mc = token_price * settings.DEFAULT_TOTAL_SUPPLY
            progress = await self.w3.progress(token_address)
            await self.update_token_price(token_address, price_in_quote, token_price, mc, progress, ts)
            token_amount_standard = token_amount_in / settings.DEFAULT_DECIMALS if token_amount_in else 0.0
            volume_usd = token_amount_standard * token_price if token_amount_standard and token_price else 0.0
            await self.record_transaction_volume(token_address, volume_usd, ts)
            pair_query = text(
                "UPDATE pair SET mc = :market_cap, bonding_curve = :bonding_curve WHERE base = :token_address"
            )
            query_buffer.append(
                (
                    pair_query,
                    {
                        "market_cap": mc,
                        "bonding_curve": progress,
                        "token_address": token_address,
                    },
                )
            )

        logger.debug("Updating user_transactions for tx_hash: %s", tx_hash)
        update_query = text(
            "UPDATE user_transactions SET token_address = :token_address, base_amount = :token_amount, quote_amount = :usdt_amount, service_fee = :service_fee, gas_used = :gas_used, gas_limit = :gas_price, value = :value, unit_price = :unit_price WHERE tx_hash = :tx_hash AND type = :type"
        )
        # Convert USDT amount to standard units for service fee calculation
        usdt_amount_standard = usdt_amount_out / 1e6

        # Calculate transaction value in USD using cached price only
        token_amount_standard = token_amount_in / settings.DEFAULT_DECIMALS if token_amount_in else 0.0
        token_usd_price = await self.get_token_usd_price(token_address)
        transaction_value = token_amount_standard * token_usd_price if token_amount_standard and token_usd_price else 0.0
        service_fee = Decimal(transaction_value * 0.01)  # Convert back to wei for storage
        
        query_buffer.append(
            (
                update_query,
                {
                    "token_address": token_address,
                    "tx_hash": tx_hash,
                    "token_amount": token_amount_in,
                    "usdt_amount": usdt_amount_out,
                    "service_fee": service_fee,
                    "gas_used": gas_used,
                    "gas_price": gas_price,
                    "value": transaction_value,
                    "unit_price": token_usd_price,
                    "type": UserTransactionType.SELL_TOKEN.value
                },
            )
        )
        logger.info("SELL_FOR_USDT event processing completed for token: %s, service_fee: %d", token_address, service_fee)

    async def handle_send(self, msg, query_buffer):
        data = SimpleNamespace(**msg.get("data", {}))

        from_address = to_checksum_address(data.From)
        to_address = to_checksum_address(data.To)
        token_address = to_checksum_address(data.TokenAddress)
        decimals = settings.DEFAULT_TOKEN_DECIMALS
        gas_price = data.EffectiveGasPrice
        gas_used = data.GasUsed
        transfer_amount = data.Value
        tx_hash = data.TxID

        logger.info(
            "Processing SEND event: token=%s, txid=%s, from=%s, to=%s, amount=%s",
            token_address, tx_hash, from_address, to_address, transfer_amount
        )

        logger.debug("Getting token balances from blockchain for from_address: %s and to_address: %s", from_address, to_address)
        from_balance, to_balance = await asyncio.gather(
            self.w3.get_token_balance_in_wei(from_address, token_address),
            self.w3.get_token_balance_in_wei(to_address, token_address),
        )
        logger.debug("Balances - from: %s, to: %s", from_balance, to_balance)

        logger.debug("Updating token holder record for from_address: %s", from_address)
        # Get user info for sender if needed
        from_user_id, from_username = await self._get_user_info(from_address)
        
        update_from_query = text(
            """
            UPDATE token_holder
            SET amount = :amount,
                user_id = CASE WHEN :user_id != '' THEN :user_id ELSE user_id END,
                username = CASE WHEN :username != '' THEN :username ELSE username END
            WHERE address = :from_address AND token_address = :token_address
            """
        )
        query_buffer.append(
            (
                update_from_query,
                {
                    "from_address": from_address,
                    "token_address": token_address,
                    "amount": from_balance,
                    "user_id": from_user_id,
                    "username": from_username,
                },
            )
        )

        logger.debug("Inserting or updating token holder record for to_address: %s", to_address)
        # Get user info for recipient
        to_user_id, to_username = await self._get_user_info(to_address)
        
        insert_to_query = text(
            """
            INSERT INTO token_holder (chain, address, token_address, amount, decimals, user_id, username)
            VALUES (:chain, :to_address, :token_address, :amount, :decimals, :user_id, :username)
            ON CONFLICT (address, token_address)
            DO UPDATE SET 
                amount = EXCLUDED.amount,
                user_id = CASE WHEN EXCLUDED.user_id != '' THEN EXCLUDED.user_id ELSE token_holder.user_id END,
                username = CASE WHEN EXCLUDED.username != '' THEN EXCLUDED.username ELSE token_holder.username END
            """
        )
        query_buffer.append(
            (
                insert_to_query,
                {
                    "chain": self._chain_id,
                    "to_address": to_address,
                    "token_address": token_address,
                    "amount": to_balance,
                    "decimals": decimals,
                    "user_id": to_user_id,
                    "username": to_username,
                },
            )
        )

        # 获取token的USD价格
        token_usd_price = await self.get_token_usd_price(token_address)
        # value应该是token数量 * token价格，需要将wei转换为标准单位
        token_amount_standard = transfer_amount / settings.DEFAULT_DECIMALS if transfer_amount else 0.0
        transaction_value = token_amount_standard * token_usd_price if token_amount_standard and token_usd_price else 0.0
        
        logger.debug("Updating user_transactions for tx_hash: %s", tx_hash)
        update_query = text(
            "UPDATE user_transactions SET token_address = :token_address, base_amount = :transfer_amount, gas_used = :gas_used, gas_limit = :gas_price, value = :value WHERE tx_hash = :tx_hash AND type = :type"
        )
        query_buffer.append(
            (
                update_query,
                {
                    "token_address": token_address,
                    "tx_hash": tx_hash,
                    "transfer_amount": transfer_amount,
                    "gas_used": gas_used,
                    "gas_price": gas_price,
                    "value": transaction_value,
                    "type": UserTransactionType.SEND_TOKEN.value
                },
            )
        )
        logger.info("SEND event processing completed for token: %s", token_address)

    async def handle_graduate(self, msg, query_buffer):
        data = SimpleNamespace(**msg.get("data", {}))

        pair_address = to_checksum_address(data.PairAddress)
        token_address = to_checksum_address(data.TokenAddress)
        # TODO: get from message
        quote_address = settings.USDT_ADDRESS
        base_amount = data.TokenAmount
        quote_amount = data.BnbAmount

        decimals = settings.DEFAULT_TOKEN_DECIMALS
        created_at = datetime.utcfromtimestamp(data.Timestamp)
        # gas_price = data.EffectiveGasPrice
        # gas_used = data.GasUsed
        # tx_hash = data.TxID
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_address}"
        await self._caching_client.client.hset(key, "dex", "pancake")
        try:
            # 在Redis中记录pair地址，供memeprice读取
            await self._caching_client.client.hset(key, "pair", pair_address)
        except Exception as e:
            logger.warning(f"Failed to set pair in Redis for {token_address}: {e}")
        logger.info(
            "Processing GRADUATE event: token=%s, pair=%s, created_at=%s, base_amount=%s, quote_amount=%s",
            token_address, pair_address, created_at, base_amount, quote_amount
        )

        logger.debug("Updating pair in database for token: %s", token_address)
        pair_query = text(
            "UPDATE pair SET dex = 'pancake', address = :pair_address, quote = :quote_address, quote_decimals = :decimals, "
            "base_amount = :base_amount, quote_amount = :quote_amount, created_at = :created_at, open_at = :opened_at "
            "WHERE base = :token_address"
        )
        query_buffer.append(
            (
                pair_query,
                {
                    "pair_address": pair_address,
                    "quote_address": quote_address,
                    "token_address": token_address,
                    "decimals": decimals,
                    "base_amount": base_amount,
                    "quote_amount": quote_amount,
                    "created_at": created_at,
                    "opened_at": created_at,
                },
            )
        )
        logger.info("GRADUATE event processing completed for token: %s, pair: %s", token_address, pair_address)


    async def handle_l1_token_created(self, msg, query_buffer):
        """处理 L1TokenCreated：记录L1 token地址并更新用户交易状态为 L1_CONFIRMED。"""
        try:
            data = SimpleNamespace(**msg.get("data", {}))
            txid = getattr(data, "TxID", None)
            l1_token = getattr(data, "TokenAddress", None)
            creator = getattr(data, "Creator", None)
            name = getattr(data, "Name", "")
            symbol = getattr(data, "Symbol", "")
            gas_used = getattr(data, "GasUsed", 0)
            gas_price = getattr(data, "EffectiveGasPrice", 0)
            creator_addr = to_checksum_address(creator)
            created_at = datetime.utcfromtimestamp(getattr(data, "Timestamp", int(time.time())))

            if not txid or not l1_token:
                logger.warning("L1_TOKEN_CREATED missing fields: txid=%s, token=%s", txid, l1_token)
                return

            l1_token = to_checksum_address(l1_token)
            logger.info("L1_TOKEN_CREATED: txid=%s, l1_token=%s, creator=%s", txid, l1_token, creator)

            # Get user info for the creator
            key_l1 = f"L1_MEME:{txid}"
            info = await self._caching_client.get(
                MEMECOIN_REDIS_ENDPOINT,
                key_l1,
            )
            user_id = info.get("creator_id", "")

            # First try to update existing CREATE_L1_MEME transaction
            update_tx_query = text(
                "UPDATE user_transactions SET token_address = :token_address, status = :status, gas_used = :gas_used, gas_limit = :gas_price WHERE tx_hash = :tx_hash AND type = :type"
            )
            query_buffer.append(
                (
                    update_tx_query,
                    {
                        "token_address": l1_token,
                        "status": UserTransactionStatus.L1_CONFIRMED.value,
                        "tx_hash": txid,
                        "type": UserTransactionType.CREATE_L1_MEME.value,
                        "gas_used": gas_used,
                        "gas_price": gas_price
                    },
                )
            )

            # Also create the CREATE_L1_MEME record if it doesn't exist (upsert pattern)
            if user_id:
                upsert_l1_query = text(
                    """INSERT INTO user_transactions 
                    (user_id, chain, type, status, tx_hash, token_address, from_address, gas_used, gas_limit, value) 
                    VALUES (:user_id, :chain, :type, :status, :tx_hash, :token_address, :from_address, :gas_used, :gas_price, :value)
                    ON CONFLICT (token_address, type, tx_hash) 
                    DO UPDATE SET 
                        status = EXCLUDED.status,
                        gas_used = EXCLUDED.gas_used,
                        gas_limit = EXCLUDED.gas_limit"""
                )
                query_buffer.append(
                    (
                        upsert_l1_query,
                        {
                            "user_id": user_id,
                            "chain": settings.BLOCKCHAIN_TYPE,
                            "type": UserTransactionType.CREATE_L1_MEME.value,
                            "status": UserTransactionStatus.L1_CONFIRMED.value,
                            "tx_hash": txid,
                            "token_address": l1_token,
                            "from_address": creator_addr,
                            "gas_used": gas_used,
                            "gas_price": gas_price,
                            "value": 0.0  # L1 token creation has no monetary value
                        },
                    )
                )



            l2_txid = await self.w3.create_l2_token(
                l1_token,
                name,
                symbol,
                info.get("repo_url", ""),
                info.get("avatar", ""),
                info.get("about", ""),
                creator_addr,
                gas=settings.DEFAULT_LAUNCH_GAS_LIMIT,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
            )
            # 在 L2 创建交易发起后，记录一条 CREATE_L2_MEME 事务，供后续确认跟踪
            
            if user_id:
                # 读取 L1 交易对应的 order_id（如果有），用于关联 L2 创建事务
                order_id = info.get("order_id", "")
                insert_l2_tx_query = text(
                    "INSERT INTO user_transactions (user_id, chain, type, status, from_address, payment_source, tx_hash, token_address, gas_limit, order_id) "
                    "VALUES (:user_id, :chain, :type, :status, :from_address, :payment_source, :tx_hash, :token_address, :gas_limit, :order_id)"
                )
                query_buffer.append(
                    (
                        insert_l2_tx_query,
                        {
                            "user_id": user_id,
                            "chain": settings.BLOCKCHAIN_TYPE,
                            "type": UserTransactionType.CREATE_L2_MEME.value,
                            "status": UserTransactionStatus.AWAITING_L1_CONFIRMATION.value,
                            "from_address": creator_addr,
                            "payment_source": creator_addr,
                            "tx_hash": l2_txid,
                            "token_address": None,
                            "gas_limit": settings.DEFAULT_LAUNCH_GAS_LIMIT,
                            "order_id": order_id
                        },
                    )
                )
                info["l1_token"] = l1_token
                await self._caching_client.set(
                    MEMECOIN_REDIS_ENDPOINT,
                    f"L2_MEME:{l2_txid}",
                    info,
                    expiration_time=timedelta(days=1),
                )
                logger.info("CREATE_L2_MEME: txid=%s, l1_token=%s, l2_token=%s, order_id=%s", txid, l1_token, l2_txid, order_id)
            else:
                logger.warning("CREATE_L2_MEME: user not found for creator address %s, skip inserting L2 tx record", creator_addr)
        except Exception as e:
            logger.exception("Error in handle_l1_token_created: %s", e)


    async def handle_token_bridged(self, msg, query_buffer):
        """处理 TokenBridged：记录桥接L1->L2信息，为后续内盘启动做准备。"""
        try:
            data = SimpleNamespace(**msg.get("data", {}))
            txid = getattr(data, "TxID", None)
            l1_token = getattr(data, "RemoteToken", None)
            l2_token = getattr(data, "LocalToken", None)
            total_supply = getattr(data, "Amount", 0)
            gas_used = getattr(data, "GasUsed", 0)
            gas_price = getattr(data, "EffectiveGasPrice", 0)

            if not txid or not l1_token or not l2_token:
                logger.warning("TOKEN_BRIDGED missing fields: tx=%s l1=%s l2=%s", txid, l1_token, l2_token)
                return

            l1_token = to_checksum_address(l1_token)
            l2_token = to_checksum_address(l2_token)
            logger.info("TOKEN_BRIDGED: txid=%s, l1=%s, l2=%s, amount=%s", txid, l1_token, l2_token, total_supply)

            info = await self._caching_client.get(
                MEMECOIN_REDIS_ENDPOINT,
                f"BRIDGE_MEME:{l1_token}:{l2_token}"
            )

            user_id = info.get("creator_id", "")
            order_id = info.get("order_id", "")
            creator_address = info.get("creator", "")

            # Update existing BRIDGE_L1_TO_L2 transaction to CONFIRMED status
            # The transaction was already created in handle_create_l2_token with AWAITING_L1_CONFIRMATION status
            bridge_update_query = text(
                """UPDATE user_transactions 
                SET status = :status, gas_used = :gas_used, gas_limit = :gas_price, to_address = :to_address
                WHERE order_id = :order_id AND type = :type AND status = :awaiting_status"""
            )
            query_buffer.append(
                (
                    bridge_update_query,
                    {
                        "status": UserTransactionStatus.CONFIRMED.value,  # Bridge event means bridging is complete
                        "gas_used": gas_used,
                        "gas_price": gas_price,
                        "to_address": l2_token,  # L2 token address as destination
                        "order_id": order_id,
                        "type": UserTransactionType.BRIDGE_L1_TO_L2.value,
                        "awaiting_status": UserTransactionStatus.AWAITING_L1_CONFIRMATION.value
                    },
                )
            )

            amount_usd_value = info.get("amount_to_buy", 0) / settings.USDT_DECIMALS
            amount_to_buy = Decimal(amount_usd_value)
            gas_usdt_amount = Decimal(int(info.get("gas_usdt_amount", 20000)) / settings.USDT_DECIMALS)

            launch_txid = await self.w3.launch(
                l2_token,
                info.get("name", ""),
                info.get("symbol", ""),
                info.get("repo_url", ""),
                info.get("avatar", ""),
                info.get("about", ""),
                info.get("creator", ""),
                info.get("secret", ""),
                amount_to_buy,
                gas_usdt_amount,
                gas=settings.DEFAULT_LAUNCH_GAS_LIMIT,
                gas_payer_secret=settings.GAS_PAYER_SECRET,
                is_with_usdt=True
            )

            # Record CREATE_MEME transaction for the launch
            insert_create_tx_query = text(
                """INSERT INTO user_transactions 
                (user_id, chain, type, status, from_address, payment_source, tx_hash, token_address, 
                 gas_limit, order_id, order_amount, value) 
                VALUES (:user_id, :chain, :type, :status, :from_address, :payment_source, :tx_hash, :token_address, 
                        :gas_limit, :order_id, :amount_to_buy, :value)"""
            )
            query_buffer.append(
                (
                    insert_create_tx_query,
                    {
                        "user_id": user_id,
                        "chain": settings.BLOCKCHAIN_TYPE,
                        "type": UserTransactionType.CREATE_MEME.value,
                        "status": UserTransactionStatus.AWAITING_CONFIRMATION.value,
                        "from_address": creator_address,
                        "payment_source": creator_address,
                        "tx_hash": launch_txid,
                        "token_address": l2_token,
                        "gas_limit": settings.DEFAULT_LAUNCH_GAS_LIMIT,
                        "order_id": order_id,
                        "amount_to_buy": Decimal(info.get("amount_to_buy", 0)),
                        "value": amount_usd_value  # Value in USDT
                    },
                )
            )

            # Record BUY_TOKEN transaction for the initial token purchase during launch
            # When a user launches a token, they simultaneously purchase amount_to_buy tokens
            if amount_to_buy > 0:
                insert_buy_tx_query = text(
                    """INSERT INTO user_transactions 
                    (user_id, chain, type, status, from_address, payment_source, tx_hash, token_address, 
                     quote_amount, service_fee, gas_limit, order_id, value, unit_price) 
                    VALUES (:user_id, :chain, :type, :status, :from_address, :payment_source, :tx_hash, :token_address, 
                            :usdt_amount, :service_fee, :gas_limit, :order_id, :value, :unit_price)"""
                )
                # Calculate service fee (1% of purchase amount)
                service_fee = Decimal(amount_usd_value * 0.01)
                
                query_buffer.append(
                    (
                        insert_buy_tx_query,
                        {
                            "user_id": user_id,
                            "chain": settings.BLOCKCHAIN_TYPE,
                            "type": UserTransactionType.BUY_TOKEN.value,
                            "status": UserTransactionStatus.AWAITING_CONFIRMATION.value,
                            "from_address": creator_address,
                            "payment_source": creator_address,
                            "tx_hash": launch_txid,  # Same tx_hash as CREATE_MEME since they happen together
                            "token_address": l2_token,
                            "usdt_amount": Decimal(info.get("amount_to_buy", 0)),  # USDT amount spent
                            "service_fee": service_fee,
                            "gas_limit": settings.DEFAULT_LAUNCH_GAS_LIMIT,
                            "order_id": order_id,
                            "value": amount_usd_value,  # Total value in USD
                            "unit_price": 0.0  # Price per token will be calculated later when tokens are received
                        },
                    )
                )
            await self._caching_client.set(
                MEMECOIN_REDIS_ENDPOINT,
                f"L2_LAUNCH_MEME:{launch_txid}",
                info,
                expiration_time=timedelta(days=1)
            )
            logger.info("L2_LAUNCH: txid=%s, l1_token=%s, l2_token=%s, total_supply=%s, order_id=%s", launch_txid, l1_token, l2_token, total_supply, order_id)
        except Exception as e:
            logger.exception("Error in handle_token_bridged: %s", e)


    async def handle_create_l2_token(self, msg, query_buffer):
        """处理 CreateL2Token：在二层创建Token，更新 pair/缓存 与交易记录。"""
        try:
            data = SimpleNamespace(**msg.get("data", {}))
            txid = getattr(data, "TxID", None)
            l2_token = getattr(data, "L2TokenAddress", None)
            name = getattr(data, "TokenName", None) or getattr(data, "name", "")
            symbol = getattr(data, "TokenSymbol", None) or getattr(data, "symbol", "")
            gas_used = getattr(data, "GasUsed", 0)
            gas_price = getattr(data, "EffectiveGasPrice", 0)

            if not txid or not l2_token:
                logger.warning("CREATE_L2_TOKEN missing fields: txid=%s, l2=%s", txid, l2_token)
                return

            l2_token = to_checksum_address(l2_token)
            logger.info("CREATE_L2_TOKEN: txid=%s, l2_token=%s, name=%s, symbol=%s", txid, l2_token, name, symbol)

            key_l2 = f"L2_MEME:{txid}"
            info = await self._caching_client.get(
                MEMECOIN_REDIS_ENDPOINT,
                key_l2
            )

            order_id = info.get("order_id", "")

            # Update CREATE_L2_MEME transaction to CONFIRMED status
            update_l2_query = text(
                """UPDATE user_transactions 
                SET status = :status, token_address = :token_address, gas_used = :gas_used, gas_limit = :gas_price
                WHERE tx_hash = :tx_hash AND type = :type"""
            )
            query_buffer.append(
                (
                    update_l2_query,
                    {
                        "status": UserTransactionStatus.CONFIRMED.value,
                        "token_address": l2_token,
                        "tx_hash": txid,
                        "type": UserTransactionType.CREATE_L2_MEME.value,
                        "gas_used": gas_used,
                        "gas_price": gas_price
                    },
                )
            )

            l1_token = info.get("l1_token")
            if l1_token and l2_token:
                bridge_txid = await self.w3.bridge_l1_to_l2(
                    l1_token,
                    l2_token,
                    gas=settings.DEFAULT_LAUNCH_GAS_LIMIT,
                    gas_payer_secret=settings.GAS_PAYER_SECRET,
                )

                # Create BRIDGE_L1_TO_L2 transaction record for the bridging operation
                user_id = info.get("creator_id", "")
                creator_address = info.get("creator", "")
                if user_id:
                    bridge_insert_query = text(
                        """INSERT INTO user_transactions 
                        (user_id, chain, type, status, tx_hash, token_address, from_address, to_address, 
                         gas_limit, order_id, value) 
                        VALUES (:user_id, :chain, :type, :status, :tx_hash, :l1_token, :from_address, :to_address, 
                                :gas_limit, :order_id, :value)"""
                    )
                    query_buffer.append(
                        (
                            bridge_insert_query,
                            {
                                "user_id": user_id,
                                "chain": settings.BLOCKCHAIN_TYPE,
                                "type": UserTransactionType.BRIDGE_L1_TO_L2.value,
                                "status": UserTransactionStatus.AWAITING_L1_CONFIRMATION.value,
                                "tx_hash": bridge_txid,
                                "l1_token": l1_token,
                                "from_address": creator_address,
                                "to_address": l2_token,
                                "gas_limit": settings.DEFAULT_LAUNCH_GAS_LIMIT,
                                "order_id": order_id,
                                "value": 0.0
                            },
                        )
                    )

                info["l2_token"] = l2_token
                await self._caching_client.set(
                    MEMECOIN_REDIS_ENDPOINT,
                    f"BRIDGE_MEME:{l1_token}:{l2_token}",
                    info,
                    expiration_time=timedelta(days=1),
                )
                logger.info("BRIDGE_MEME: txid=%s, l1_token=%s, l2_token=%s, order_id=%s", bridge_txid, l1_token, l2_token, order_id)
            else:
                logger.error(f"l1_token={l1_token}, l2_token={l2_token}, not all of them exist")

        except Exception as e:
            logger.exception("Error in handle_create_l2_token: %s", e)


    async def handle_token_completed(self, msg, query_buffer):
        """处理 Complete：内盘打满"""
        try:
            data = SimpleNamespace(**msg.get("data", {}))
            txid = getattr(data, "TxID", None)
            token_address = getattr(data, "TokenAddress", None)
            created_at = datetime.utcfromtimestamp(getattr(data, "Timestamp", int(time.time())))

            if not token_address:
                logger.warning("TOKEN_COMPLETED missing token address: txid=%s", txid)
                return

            token_address = to_checksum_address(token_address)
            logger.info("TOKEN_COMPLETED: txid=%s, token=%s", txid, token_address)

            # 使用 Redis 做幂等锁，避免重复触发毕业
            lock_key = f"GRADUATE_TRIGGERED:{token_address}"
            ttl_seconds = 15 * 60
            acquired = await self._caching_client.client.set(lock_key, "1", nx=True, ex=ttl_seconds)
            if not acquired:
                logger.info("TOKEN_COMPLETED: graduate already triggered for token=%s, skip", token_address)
                return

            # 触发 ETH 毕业
            try:
                grad_result = await self.w3.graduate(token_address)
                logger.info("TOKEN_COMPLETED: triggered graduation for token=%s, result=%s", token_address, grad_result)
            except Exception as e:
                logger.exception("TOKEN_COMPLETED: graduate failed for token=%s, error=%s", token_address, e)
                try:
                    await self._caching_client.client.delete(lock_key)
                except Exception as ie:
                    logger.warning("Failed to release graduate lock for token=%s: %s", token_address, ie)
                return
        except Exception as e:
            logger.exception("Error in handle_token_completed: %s", e)

async def main():
    """Main function for running the Kafka consumer service"""
    logger.info("Starting Kafka consumer service")
    # Initialize DB engines for this process
    init_engines()
    
    # Initialize BlockchainConnector
    logger.info("Initializing blockchain connection")
    if settings.BLOCKCHAIN_TYPE == "SOL":
        w3 = BlockchainConnectorGetter(
            logger=logger,
            chain_type="sol",
            rpc_url=settings.SOL_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
            },
        )()
    elif settings.BLOCKCHAIN_TYPE == "ETH":
        w3 = BlockchainConnectorGetter(
            logger=logger,
            chain_type="eth",
            rpc_url=settings.ETH_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.ETH_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
                "relayer_abi_path": settings.RELAYER_CONTRACT_ABI_PATH,
                "relayer_contract_address": settings.RELAYER_CONTRACT_ADDRESS,
                "l1_launcher_abi_path":settings.L1_LAUNCHER_ABI_PATH,
                "l1_launcher_contract_address":settings.L1_LAUNCHER_CONTRACT_ADDRESS
            },
        )()
    else:  # Default to BSC
        w3 = BlockchainConnectorGetter(
            logger=logger,
            chain_type="bsc",
            rpc_url=settings.BSC_RPC_URL,
            kms_address=settings.KMS_ADDRESS,
            config={
                "chain_id": settings.BSC_CHAIN_ID,
                "contract_address": settings.MEME_CONTRACT_ADDRESS,
                "meme_abi_path": settings.MEME_CONTRACT_ABI_PATH,
                "erc20_abi_path": settings.ERC20_CONTRACT_ABI_PATH,
            },
        )()
    await w3.init_connection()
    logger.info("Blockchain connection initialized successfully")

    # Create and run service
    kafka_service = KafkaConsumerService(w3)
    
    try:
        logger.info("Starting Kafka consumer loop")
        await kafka_service.run_loop()
    except KeyboardInterrupt:
        logger.info("Service interrupted, shutting down gracefully...")
    except Exception as e:
        logger.exception("Main program exception: %s", e)
    finally:
        # Ensure all resources are properly released
        logger.info("Cleaning up resources")
        await kafka_service.close()
        if hasattr(w3, 'close') and callable(getattr(w3, 'close')):
            await w3.close()
        logger.info("Program has exited normally")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Program interrupted")
