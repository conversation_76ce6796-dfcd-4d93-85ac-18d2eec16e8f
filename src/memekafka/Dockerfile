# Memekafka Service - Simplified using enhanced base
# Reduced from 76 lines to ~25 lines!

ARG PYTHON_VERSION=3.10.12
ARG BASE_IMAGE=gitlab.aurora:5050/toci/api/base:v1.1

# Build stage - only for service-specific dependencies
FROM ${BASE_IMAGE} AS builder

# Install uv in builder stage
USER root
RUN pip install --no-cache-dir uv
USER appuser

# Copy service-specific requirements
COPY src/memekafka/requirements.txt /tmp/memekafka-requirements.txt
COPY src/auth/requirements.txt /tmp/auth-requirements.txt

# Install service-specific dependencies
RUN uv pip install --no-cache -r /tmp/memekafka-requirements.txt
RUN uv pip install --no-cache -r /tmp/auth-requirements.txt

# Production stage - minimal additions to base
FROM ${BASE_IMAGE} AS prod

# Copy updated virtual environment with service dependencies
COPY --from=builder /opt/venv /opt/venv

# Add service scripts to PATH
ENV PATH="$PATH:/src/memekafka/scripts"

# Copy service-specific code
COPY --chown=appuser:appuser src/memekafka /src/memekafka
COPY --chown=appuser:appuser src/memecoin /src/memecoin

# Set executable permissions for start script
RUN chmod +x /src/memekafka/scripts/start.sh