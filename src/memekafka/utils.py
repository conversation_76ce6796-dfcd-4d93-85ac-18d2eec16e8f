import logging

from redis.asyncio import Redis

from src.common.constants import MEMECOIN_REDIS_ENDPOINT, PairStatus
from src.memecoin.schemas import TokenCache
from src.memecoin.utils import get_initial_token_price
from src.memekafka.settings import settings

logger = logging.getLogger(__name__)


async def add_token_to_active_queue(redis_client, token_address: str):
    """Add token address to the active token queue in Redis"""
    if not token_address or token_address == "native_token":
        return

    queue_key = "memetracker:active_tokens"

    try:
        # Check if the token already exists in the queue
        exists = await redis_client.lpos(queue_key, token_address)
        if exists is None:  # Add only if not exists
            # Push to the right side of the queue
            await redis_client.rpush(queue_key, token_address)
            # Keep only the latest 100 tokens
            await redis_client.ltrim(queue_key, -100, -1)
            logger.debug(f"Added token {token_address} to active tokens")
        else:
            logger.debug(f"Token {token_address} already in active tokens")
    except Exception as e:
        logger.error(f"Failed to add token to active tokens: {e}")


async def set_token_info(token_cache: TokenCache, redis_client: Redis = None):
    try:
        key = f"{MEMECOIN_REDIS_ENDPOINT}:{token_cache.token_address}"
        data = token_cache.model_dump()
        await redis_client.hset(key, mapping=data)
        logger.info("Token info set in hash for key %s", key)
    except Exception as e:
        logger.exception(
            "Failed to set token info for %s: %s", token_cache.symbol, e
        )


async def update_token_redis(redis_client, token_address: str, name: str, symbol: str, avatar: str, collection_id: str):
    """Update token information in Redis cache"""
    try:
        # Skip USDT and other stable coins - they shouldn't be processed as meme tokens
        if hasattr(settings, 'USDT_ADDRESS') and token_address == settings.USDT_ADDRESS:
            logger.debug(f"Skipping Redis update for USDT address: {token_address}")
            return

        # Calculate initial token price using utility function
        price_per_token_usd = await get_initial_token_price()

        total_supply_ui = settings.DEFAULT_TOTAL_SUPPLY
        market_cap = price_per_token_usd * total_supply_ui

        logger.info(
            f"Calculated bonding curve token price for {symbol}: ${price_per_token_usd} (market cap: ${market_cap})")

        # Create token cache object
        token_cache = TokenCache(
            token_address=token_address,
            name=name,
            symbol=symbol,
            image_url=avatar,
            status=PairStatus.READY.value,
            price=price_per_token_usd,
            market_cap=market_cap,
            collection_id=collection_id,
        )

        # Update token info in Redis
        await set_token_info(token_cache, redis_client)

        # Add token to all_token_set for memeprice processing
        all_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
        await redis_client.sadd(all_tokens_key, token_address)
        logger.info(f"Added token {token_address} to all_token_set")

        # Also add to active token queue
        await add_token_to_active_queue(redis_client, token_address)

        logger.debug(f"Updated Redis for token {symbol} ({token_address})")

    except Exception as e:
        logger.warning(f"Failed to update token Redis for {symbol}: {str(e)}")
        # Set minimal cache info
        try:

            # Also add to all_token_set in fallback case
            all_tokens_key = f"{MEMECOIN_REDIS_ENDPOINT}:all_token_set"
            await redis_client.sadd(all_tokens_key, token_address)
            logger.info(f"Added token {token_address} to all_token_set (fallback)")
        except Exception as fallback_e:
            logger.error(f"Failed to set fallback token info: {fallback_e}")


async def invalidate_rankings_cache(redis_client, token_symbol: str = ""):
    """
    清除排行榜全局缓存，确保新币立即出现在排行榜中

    Args:
        token_symbol: Token符号，用于日志记录
    """
    try:
        # 删除所有排行榜类型的全局缓存
        ranking_types = ['daily', 'weekly', 'buyback']
        pipeline = redis_client.pipeline()

        for ranking_type in ranking_types:
            cache_key = f"{MEMECOIN_REDIS_ENDPOINT}:global_rankings:{ranking_type}"
            pipeline.delete(cache_key)

        # 执行批量删除
        deleted_keys = await pipeline.execute()
        deleted_count = sum(deleted_keys)

        if deleted_count > 0:
            logger.info(f"Successfully invalidated {deleted_count} rankings cache keys for new token: {token_symbol}")
        else:
            logger.debug(f"No rankings cache keys found to invalidate for token: {token_symbol}")

        return deleted_count

    except Exception as e:
        logger.error(f"Failed to invalidate rankings cache for token {token_symbol}: {str(e)}")
        return 0