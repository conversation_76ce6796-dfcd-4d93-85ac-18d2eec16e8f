*.env
*env
*.idea
*__pycache__*
venv
*.log
*.txt
.vscode/
buy_back_transaction_history.json
buyback.json
start.py
.coverage
.pytest_cache/
.cursor/
.claude/
.run/
local_docs/
# Python
__pycache__
*.py[cod]
*$py.class
*.so
.Python
env/
.github/
venv/
.venv
.pytest_cache/
.coverage
.tox/
*.egg-info/
dist/
build/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS
.DS_Store
Thumbs.db

# Project specific
*.log
*.sqlite
*.db
.env
.env.*
!.env.example
node_modules/
logs/
tmp/
temp/
cache/

# Build artifacts
*.tar.gz
*.zip

.local