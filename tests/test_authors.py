import os
from dotenv import load_dotenv

# 切换到 authors 目录
os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "authors"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi import status
from tests.client import MemeFansClient
from src.authors.main import app  # 导入 FastAPI 应用

@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def client(ac: AsyncClient):
    author = MemeFansClient(ac, token="8mptJzMSDbWg9RaK05llWjBB9HOaRsB9yOkWkywe7pk", username="liuygo", email="<EMAIL>", id="BgJos9JePpZ")
    # await author.register()
    # await author.login()
    return author

@pytest.mark.asyncio
async def test_health(client: MemeFansClient):
    response = await client.get(f"/health")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json()["status"] == "ok"

@pytest.mark.asyncio
async def test_get_batch(client: MemeFansClient):
    response = await client.get(f"/batch", params={"ids": ["BghUkA7105P"]})
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert isinstance(response.json(), list)
    assert len(response.json()) == 1
    
    author = response.json()[0]
    # 检查所有字段是否存在
    assert "type" in author
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "email" in author
    assert "avatar" in author
    assert "original_avatar" in author
    assert "region" in author
    assert "user_id" in author
    assert "invitation_id" in author
    assert "created_at" in author
    assert "updated_at" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "gender" in author
    assert "role" in author
    assert "likes_count" in author
    assert "citations_count" in author
    assert "posts_count" in author

@pytest.mark.asyncio
async def test_get_author(client: MemeFansClient):
    get_response = await client.get(
        f"{client.id}",
    )
    assert get_response.status_code == status.HTTP_200_OK, get_response.text
    assert get_response.json() is not None
    
    author = get_response.json()
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "email" in author
    assert "created_at" in author
    assert "citations_count" in author
    assert "posts_count" in author
    assert "avatar" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "gender" in author
    # assert "tags_subscribed" in get_response.json(), "tags_subscribed field is missing"
    # assert "collections_subscribed" in get_response.json(), "collections_subscribed field is missing"
    # assert "followers" in get_response.json(), "followers field is missing"
    # assert "following" in get_response.json(), "following field is missing"

@pytest.mark.asyncio
async def test_get_me(client: MemeFansClient):
    response = await client.get(f"/me")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    author = response.json()
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "avatar" in author
    assert "region" in author
    assert "created_at" in author
    assert "role" in author
    assert "status" in author
    assert "email" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "likes_count" in author
    assert "citations_count" in author
    assert "gender" in author
    assert "original_avatar" in author

@pytest.mark.asyncio
async def test_get_invited(client: MemeFansClient):
    created_at_response = await client.get(f"/me/invited", params={"sort": "created_at"})
    print(created_at_response.json())
    assert created_at_response.status_code == 200
    assert created_at_response.json() is not None
    
    group_grade_response = await client.get(f"/me/invited", params={"sort": "group_grade"})
    print(group_grade_response.json())
    assert group_grade_response.status_code == 200
    assert group_grade_response.json() is not None

    direct_invited_count_response = await client.get(f"/me/invited", params={"sort": "direct_invited_count"})
    print(direct_invited_count_response.json())
    assert direct_invited_count_response.status_code == 200
    assert direct_invited_count_response.json() is not None

    group_size_response = await client.get(f"/me/invited", params={"sort": "group_size"})
    print(group_size_response.json())
    assert group_size_response.status_code == 200
    assert group_size_response.json() is not None
    

@pytest.mark.asyncio
async def test_get_grade(client: MemeFansClient):
    response = await client.get(f"/me/grade")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    author = response.json()
    # 基本字段检查
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "avatar" in author
    assert "region" in author
    assert "created_at" in author
    assert "role" in author
    assert "status" in author
    assert "email" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "likes_count" in author
    assert "citations_count" in author
    assert "gender" in author
    assert "original_avatar" in author
    
    # 等级相关字段检查
    assert "group_grade" in author
    assert "direct_invited_count" in author
    assert "group_size" in author

@pytest.mark.asyncio
async def test_get_my_wallet(client: MemeFansClient):
    response = await client.get(f"/me/wallet")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    author = response.json()
    # 基本字段检查
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "avatar" in author
    assert "region" in author
    assert "created_at" in author
    assert "role" in author
    assert "status" in author
    assert "email" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "likes_count" in author
    assert "citations_count" in author
    assert "gender" in author
    assert "original_avatar" in author
    
    # 钱包相关字段检查
    assert "points" in author
    assert "pending_points" in author
    assert "potential_points" in author
    assert "today_points" in author
    assert "epi" in author
    assert "wallet_address" in author

@pytest.mark.asyncio
async def test_get_notifications_count(client: MemeFansClient):
    response = await client.get(f"/me/notifications/count")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert "unread_count" in response.json()

@pytest.mark.asyncio
async def test_get_notifications(client: MemeFansClient):
    response = await client.get(f"/me/notifications")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查顶层字段
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 检查通知项字段
    if len(data["items"]) > 0:
        notification = data["items"][0]
        assert "type" in notification
        assert "meta" in notification
        assert "id" in notification
        assert "status" in notification
        assert "recipient_id" in notification
        assert "created_at" in notification
        
        # 检查 meta 字段（根据不同类型的通知可能有不同的字段）
        meta = notification["meta"]
        if notification["type"] == "post_commit":
            assert "commit_id" in meta
            assert "commit_post_id" in meta
            assert "commit_author_id" in meta
            assert "commit_post_type" in meta
            assert "commit_author_name" in meta
            assert "commit_author_avatar" in meta
            assert "commit_collection_id" in meta
            assert "commit_author_username" in meta
            assert "commit_collection_title" in meta
        elif notification["type"] == "commit_approve":
            assert "commit_post_id" in meta
            assert "commit_post_type" in meta
            assert "commit_post_cover" in meta
            assert "commit_post_title" in meta
            assert "collection_author_id" in meta
            assert "commit_collection_id" in meta
            assert "collection_author_name" in meta
            assert "commit_collection_title" in meta
            assert "collection_author_avatar" in meta
            assert "collection_author_username" in meta
        elif notification["type"] == "collection_subscribe":
            assert "follower_id" in meta
            assert "collection_id" in meta
            assert "follower_name" in meta
            assert "follower_avatar" in meta
            assert "collection_cover" in meta
            assert "collection_title" in meta
            assert "follower_username" in meta

@pytest.mark.asyncio
async def test_read_notifications(client: MemeFansClient):
    response = await client.patch(f"/me/notifications")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is None
    

@pytest.mark.asyncio
async def test_get_assets(client: MemeFansClient):
    response = await client.get(f"/me/assets")
    # print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查顶层字段
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 检查资产项字段
    if len(data["items"]) > 0:
        asset = data["items"][0]
        assert "amount" in asset
        assert "action" in asset
        assert "created_at" in asset
        assert "meta" in asset
        
        # 检查 meta 字段（根据不同的 action 类型可能有不同的字段）
        meta = asset["meta"]
        if asset["action"] == "comment":
            assert "url" in meta
            assert "cover" in meta
            assert "post_id" in meta
            assert "author_id" in meta
            assert "post_type" in meta
            assert "description" in meta
        elif asset["action"] == "daily_collection_rating":
            assert "cover" in meta
            assert "title" in meta
            assert "collection_id" in meta
        elif asset["action"] == "commit":
            assert "post_id" in meta
            assert "post_title" in meta
            assert "collection_id" in meta
            assert "collection_cover" in meta
            assert "collection_title" in meta
        elif asset["action"] == "commit_approve":
            assert "commit_id" in meta
            assert "post_type" in meta
            assert "post_cover" in meta
            assert "post_title" in meta
            assert "collection_cover" in meta
            assert "collection_title" in meta
        elif asset["action"] == "like":
            assert "url" in meta
            assert "cover" in meta
            assert "post_id" in meta
            assert "author_id" in meta
            assert "post_type" in meta
            assert "description" in meta
        elif asset["action"] == "save":
            assert "post_id" in meta
            assert "post_type" in meta
            assert "post_title" in meta
            assert "collection_id" in meta
            assert "collection_cover" in meta
            assert "collection_title" in meta


@pytest.mark.asyncio
async def test_points_passive_analytics(client: MemeFansClient):
    response = await client.get(f"/me/points_passive_analytics")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查所有统计字段
    assert "count" in data
    assert "likes_count" in data
    assert "likes_points" in data
    assert "comments_count" in data
    assert "comments_points" in data
    assert "commits_count" in data
    assert "commits_points" in data
    assert "saves_count" in data
    assert "saves_points" in data
    
    # 检查字段类型
    assert isinstance(data["count"], (int, float))
    assert isinstance(data["likes_count"], (int, float))
    assert isinstance(data["likes_points"], (int, float))
    assert isinstance(data["comments_count"], (int, float))
    assert isinstance(data["comments_points"], (int, float))
    assert isinstance(data["commits_count"], (int, float))
    assert isinstance(data["commits_points"], (int, float))
    assert isinstance(data["saves_count"], (int, float))
    assert isinstance(data["saves_points"], (int, float))
    
    # 检查数值非负
    assert data["count"] >= 0
    assert data["likes_count"] >= 0
    assert data["likes_points"] >= 0
    assert data["comments_count"] >= 0
    assert data["comments_points"] >= 0
    assert data["commits_count"] >= 0
    assert data["commits_points"] >= 0
    assert data["saves_count"] >= 0
    assert data["saves_points"] >= 0

@pytest.mark.asyncio
async def test_points_analytics(client: MemeFansClient):
    response = await client.get(f"/me/points_analytics")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查所有积分统计字段
    assert "like_amount" in data
    assert "like_points" in data
    assert "comment_amount" in data
    assert "comment_points" in data
    assert "commit_amount" in data
    assert "commit_points" in data
    assert "save_amount" in data
    assert "save_points" in data
    assert "commit_approve_amount" in data
    assert "commit_approve_points" in data


@pytest.mark.asyncio
async def test_get_followers(client: MemeFansClient):
    response = await client.get(f"/{client.id}/followers")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查分页相关字段
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 检查关注者信息字段
    if len(data["items"]) > 0:
        follower = data["items"][0]
        assert "id" in follower
        assert "name" in follower
        assert "username" in follower
        assert "avatar" in follower
        assert "region" in follower
        assert "created_at" in follower
        assert "role" in follower
        assert "status" in follower
        assert "is_following" in follower
        assert "follower_count" in follower

@pytest.mark.asyncio
async def test_email_exists(client: MemeFansClient):
    response = await client.get(f"/email_exists/{client.email}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == True
    
@pytest.mark.asyncio
async def test_phone_exists(client: MemeFansClient):
    user_info = await client.get(
        f"{client.id}",
    )
    assert user_info.status_code == status.HTTP_200_OK, user_info.text
    assert user_info.json() is not None
    print(f"user_info: {user_info.json()}")
    
    response = await client.get(f"/phone_exists/{user_info.json()['phone_number']}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == False
    
@pytest.mark.asyncio
async def test_username_exists(client: MemeFansClient):
    user_info = await client.get(
        f"{client.id}",
    )
    assert user_info.status_code == status.HTTP_200_OK, user_info.text
    assert user_info.json() is not None
    print(f"user_info: {user_info.json()}")
    response = await client.get(f"/username_exists/{user_info.json()['username']}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == True
    
@pytest.mark.asyncio
async def test_me_blocklist(client: MemeFansClient):
    response = await client.get(f"/me/blocklist")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查分页相关字段
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 检查黑名单用户信息字段
    if len(data["items"]) > 0:
        blocked_user = data["items"][0]
        assert "id" in blocked_user
        assert "name" in blocked_user
        assert "username" in blocked_user
        assert "avatar" in blocked_user
        assert "region" in blocked_user
        assert "created_at" in blocked_user
        assert "role" in blocked_user
        assert "status" in blocked_user
        assert "is_following" in blocked_user
        assert "follower_count" in blocked_user
        assert "is_blocked" in blocked_user

@pytest.mark.asyncio
async def test_me_block_and_unblock(client: MemeFansClient):
    block_response = await client.post(f"/me/blocks/BfmmR6ilmBo")
    print(block_response.json())
    assert block_response.status_code == 200
    assert block_response.json() is not None
    assert 'message' in block_response.json()
    unblock_response = await client.post(f"/me/unblocks/BfmmR6ilmBo")
    print(unblock_response.json())
    assert unblock_response.status_code == 200
    assert unblock_response.json() is not None
    assert 'message' in unblock_response.json()
    
   
@pytest.mark.asyncio
async def test_follow_and_unfollow(client: MemeFansClient):
    author_id = "BgLkByLAUul"
    follow_response = await client.post(f"/{author_id}/follow")
    print(f"author_id: {author_id} follower_id: {client.id}")
    print(f"follow_response: {follow_response.json()}")
    
    # 检查响应状态码
    assert follow_response.status_code == 200
    
    # 获取响应数据
    response_data = follow_response.json()
    assert response_data is not None
    
    # 检查必需字段存在性
    required_fields = [
        'id', 'name', 'username', 'email', 'avatar', 'region',
        'user_id', 'created_at', 'updated_at', 'description',
        'location', 'phone_number', 'likes_count', 'citations_count',
        'posts_count'
    ]
    
    for field in required_fields:
        assert field in response_data, f"Missing required field: {field}"
    
    # 检查字段类型
    assert isinstance(response_data['id'], str)
    assert isinstance(response_data['name'], str)
    assert isinstance(response_data['username'], str)
    assert isinstance(response_data['email'], str)
    assert isinstance(response_data['avatar'], str)
    assert isinstance(response_data['region'], str)
    assert isinstance(response_data['user_id'], str)
    assert isinstance(response_data['created_at'], str)
    assert isinstance(response_data['updated_at'], str)
    assert isinstance(response_data['description'], str)
    assert isinstance(response_data['location'], str)
    assert isinstance(response_data['phone_number'], str)
    assert isinstance(response_data['likes_count'], int)
    assert isinstance(response_data['citations_count'], int)
    assert isinstance(response_data['posts_count'], int)
    
    # 检查可选字段
    optional_fields = [
        'original_avatar', 'invitation_id', 'dedication',
        'country', 'language', 'education', 'birthday',
        'gender', 'role'
    ]
    
    for field in optional_fields:
        assert field in response_data, f"Missing optional field: {field}"
    
    # 检查字段值不为空
    non_empty_fields = ['id', 'name', 'username', 'email', 'avatar', 'region', 'user_id']
    for field in non_empty_fields:
        assert response_data[field], f"Field {field} should not be empty"
    
    # 检查时间戳格式
    from datetime import datetime
    try:
        datetime.fromisoformat(response_data['created_at'].replace('Z', '+00:00'))
        datetime.fromisoformat(response_data['updated_at'].replace('Z', '+00:00'))
    except ValueError:
        pytest.fail("Invalid datetime format")
    
    # 检查数值字段非负
    numeric_fields = ['likes_count', 'citations_count', 'posts_count']
    for field in numeric_fields:
        assert response_data[field] >= 0, f"Field {field} should be non-negative"

    followers_response = await client.get(f"/{author_id}/followers")
    print(followers_response.json())
    assert followers_response.status_code == 200
    assert followers_response.json() is not None
    
    followers_data = followers_response.json()
    # 检查分页相关字段
    assert "items" in followers_data
    assert len(followers_data["items"]) > 0
    
    # 检查 client.id 是否在 followers_data["items"] 中
    assert client.id in [item["id"] for item in followers_data["items"]]

    unfollow_response = await client.post(f"/{author_id}/unfollow")
    print(f"unfollow_response: {unfollow_response.json()}")
    assert unfollow_response.status_code == 200

    followers_response = await client.get(f"/{author_id}/followers")
    print(followers_response.json())
    assert followers_response.status_code == 200
    assert followers_response.json() is not None    
    assert "items" in followers_response.json()
    assert client.id not in [item["id"] for item in followers_response.json()["items"]]
    
 
@pytest.mark.asyncio
async def test_me_points_series(client: MemeFansClient):
    response = await client.get(f"/me/points_series")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    # 检查返回的是列表
    data = response.json()
    assert isinstance(data, list)
    
    # 检查每个数据点的字段
    for point in data:
        # 检查必需字段
        assert "date" in point, "Missing 'date' field in points series data"
        assert "amount" in point, "Missing 'amount' field in points series data"
        
        # 检查字段类型
        assert isinstance(point["date"], str), "Field 'date' should be string"
        assert isinstance(point["amount"], (int, float)), "Field 'amount' should be numeric"
        
        # 检查日期格式 (YYYY-MM-DD)
        from datetime import datetime
        try:
            datetime.strptime(point["date"], "%Y-%m-%d")
        except ValueError:
            pytest.fail(f"Invalid date format in points series data: {point['date']}")
        
        # 检查数值非负
        assert point["amount"] >= 0, "Field 'amount' should be non-negative"
    
    # 测试空列表的情况
    if not data:
        # 确保空列表也是有效的响应
        assert isinstance(data, list)
        assert len(data) == 0

@pytest.mark.asyncio
async def test_foreign_email_exists(client: MemeFansClient):
    response = await client.get(f"/foreign_email_exists/{client.email}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == True
    
@pytest.mark.asyncio
async def test_foreign_username_exists(client: MemeFansClient):
    response = await client.get(f"/foreign_username_exists/{client.username}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None

@pytest.mark.asyncio
async def test_user_name_exists(client: MemeFansClient):
    response = await client.get(f"/username_exists/{client.username}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == True
    
@pytest.mark.asyncio
async def test_email_exists(client: MemeFansClient):
    response = await client.get(f"/email_exists/{client.email}")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json() == True

    
@pytest.mark.asyncio
async def test_get_subscribers(client: MemeFansClient):
    response = await client.get(f"/subscribed_to", params={"collection_id": "Bgw-qdXWz2X"})
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    
    data = response.json()
    # 检查分页相关字段
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 检查分页字段类型
    assert isinstance(data["items"], list)
    assert isinstance(data["total"], int)
    assert isinstance(data["page"], int)
    assert isinstance(data["size"], int)
    assert isinstance(data["pages"], int)
    
    # 检查分页字段值
    assert data["total"] >= 0
    assert data["page"] > 0
    assert data["size"] > 0
    assert data["pages"] >= 0
    
    # 检查每个订阅者信息
    if len(data["items"]) > 0:
        subscriber = data["items"][0]
        # 检查基本字段
        assert "id" in subscriber
        assert "name" in subscriber
        assert "username" in subscriber
        assert "avatar" in subscriber
        assert "region" in subscriber
        assert "created_at" in subscriber
        assert "role" in subscriber
        assert "status" in subscriber
        
        # 检查标志字段
        assert "is_following" in subscriber
        assert "follower_count" in subscriber
        
        # 检查字段类型
        assert isinstance(subscriber["id"], str)
        assert isinstance(subscriber["name"], str)
        assert isinstance(subscriber["username"], str)
        assert isinstance(subscriber["avatar"], (str, type(None)))
        assert isinstance(subscriber["region"], str)
        assert isinstance(subscriber["created_at"], str)
        assert isinstance(subscriber["role"], (str, type(None)))
        assert isinstance(subscriber["status"], str)
        assert isinstance(subscriber["is_following"], bool)
        assert isinstance(subscriber["follower_count"], int)
        
        # 检查时间格式
        from datetime import datetime
        try:
            datetime.fromisoformat(subscriber["created_at"].replace('Z', '+00:00'))
        except ValueError:
            pytest.fail(f"Invalid datetime format: {subscriber['created_at']}")
        
        # 检查数值字段非负
        assert subscriber["follower_count"] >= 0


    

@pytest.mark.asyncio
async def test_get_subscribed_collections(client: MemeFansClient):
    author_id = "BgLkByLAUul"
    response = await client.get(f"/{author_id}/subscribed")
    assert response.status_code == 200
    data = response.json()
    assert data is not None

    # 字段存在性和类型检查
    expected_types = {
        "id": str,
        "type": str,
        "status": str,
        "created_at": str,
        "updated_at": str,
        "region": str,
        "author_id": str,
        "title": str,
        "description": str,
        "cover": str,
        "original_cover": str,
        "subscriber_count": int,
        "contributor_count": int,
        "content_type": str,
        "contents_count": int,
        "contents": list,
        "carnival_status": str,
        "carnival_start_time": (str, type(None)),  # 允许为 None
        "is_subscribed": bool,
    }
    assert "items" in data
    assert isinstance(data["items"], list)
    for item in data["items"]:
        for field, typ in expected_types.items():
            assert field in item, f"Missing field: {field} in item: {item}"
            if isinstance(typ, tuple):
                assert isinstance(item[field], typ), f"Field {field} type error: {item[field]} value: {item[field]}"
            else:
                assert isinstance(item[field], typ), f"Field {field} type error: {item[field]} value: {item[field]}"
    
    
