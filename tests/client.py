import os
from datetime import datetime
from typing import Optional, List, Any
from os import getenv

from uuid import uuid4
from httpx import AsyncClient, URL
from src.common.constants import PostStatus


class EndPoint:
    authors: str = "/author"
    articles: str = "/article"
    texts: str = "/texts"
    questions: str = "/question"
    answers: str = "/answer"
    collections: str = "/collections"
    images: str = "/images"
    videos: str = "/videos"
    comments: str = "/comments"
    topics: str = "/topics"
    devices: str = "/devices"
    commits: str = "/commits"
    notifications: str = "/notifications"
    suggestions: str = "/suggestions"
    invitations: str = "/invitations"
    register: str = "/register"
    verify: str = "/verify"
    login: str = "/login"
    upvote: str = "/upvote"
    files: str = "/files"
    versions: str = "/posts/versions"
    checkout: str = "/posts/checkout"
    translations: str = "/translations"

class MemeFansClient:
    def __init__(
        self,
        ac: AsyncClient,
        username: Optional[str] = None,
        password: Optional[str] = None,
        email: Optional[str] = None,
        name: Optional[str] = None,
        token: Optional[str] = None,
        id: Optional[str] = None,
        api_url: str = getenv("BASE_URL")
    ):
        """
        Client for MemeFansClient.
        Fields will be generated, if no info provided.
        """
        self.ac = ac
        self.username = username or str(uuid4())
        self.password = password or str(uuid4())
        self.email = email or str(uuid4()) + '@test.com'
        self.name = name or str(uuid4())
        self.api_url = api_url
       
        self.token = token or None
        self.id = id or None
        self.endpoints = EndPoint()
        self.headers = {
            "content-type": "application/json", 
            "Authorization": f"Bearer {self.token}",
        }

    async def get(
        self,
        url: URL | str,
        params: dict = None
    ):
        return await self.ac.get(url, headers=self.headers, params=params)

    async def post(
        self,
        url: URL | str,
        json: Any | None = None
    ):
        return await self.ac.post(url, headers=self.headers, json=json)

    async def put(
        self,
        url: URL | str,
        json: Any | None = None
    ):
        return await self.ac.put(url, headers=self.headers, json=json)

    async def patch(
        self,
        url: URL | str,
        json: Any | None = None
    ):
        return await self.ac.patch(url, headers=self.headers, json=json)

    async def delete(
        self,
        url: URL | str,
    ):
        return await self.ac.delete(url, headers=self.headers)

    async def register(self, invite_code: str = os.getenv("INVITE_CODE")):
        """
        Register user with credentials given in constructor.
        """
        return await self.ac.post(
            self.endpoints.register,
            headers=self.headers,
            json={
                "name": self.name,
                "email": self.email,
                "username": self.username,
                "password": self.password,
                "invite_code": invite_code,
                "region": "ru",
                "captcha": {"token": "10000000-aaaa-bbbb-cccc-000000000001"}
            }
        )

    async def verify(self, verify_token: str):
        """
        Verify current user with given token.
        """
        return await self.ac.post(
            self.endpoints.verify,
            headers=self.headers,
            json={
                "token": verify_token,
            }
        )

    async def login(self):
        """
        Login user.
        """
        login_response = await self.ac.post(
            self.endpoints.login,
            headers={
                "content-type": "application/x-www-form-urlencoded",
            },
            data={
                "username": self.email,
                "password": self.password,
            }
        )
        self.token = login_response.json().get("access_token")
        self.id = login_response.json().get("id")
        assert self.token
        assert self.id
        self.headers.update({"Authorization": f"Bearer {self.token}"})

    async def verify_and_login(self, verify_token: str):
        """
        Helper function with a pair of same named operations.
        """
        await self.verify(verify_token)
        await self.login()

    async def update_me(self, **kwargs):
        """
        Update current user.

        :param kwargs: dict of updated fields.
        :return: User update response.
        """
        return await self.put(
            "/me",
            json=kwargs
        )

    async def get_notifications(self):
        """
        Get all user's notifications.
        :return: List of notifications.
        """
        return await self.get(self.endpoints.notifications)

    async def author_subscribe(self, author_id: str):
        """
        Subscribe to author.

        :param author_id: Author ID to subscribe.
        :return: Author subscription response.
        """
        return await self.post(f"{self.endpoints.authors}/{author_id}/follow")

    async def upvote(
            self,
            post_id: str
    ):
        return await self.post(
            f"{self.endpoints.upvote}/{post_id}"
        )

    async def collection_pin(self, collection_id: str, post_id: str):
        return await self.post(
            f"{self.endpoints.collections}/pins?collection_id={collection_id}&post_id={post_id}"
        )

    async def profile_pin(self, post_id: str):
        return await self.post(
            f"{self.endpoints.authors}/pins?post_id={post_id}"
        )

    async def article_create(
        self,
        title: str = "Article Title",
        text: str = "Article text.",
        description: str = "Article Description",
        status: PostStatus = PostStatus.POSTED,
        tags: List[str] = ["test"],
        created_at: Optional[str] = None
    ):
        """
        Create article with given fields.

        :param title: Article title.
        :param text: Article text.
        :param description: Article description.
        :param status: Set Post status.
        :param tags: Article tags.
        :param created_at: Scheduled post time.
        :return: Article create response.
        """
        return await self.post(
            self.endpoints.articles,
            json={
                "title": title,
                "text": text,
                "description": description,
                "tags": tags,
                "status": status,
                "created_at": created_at
            }
        )

    async def article_update(
        self,
        post_id: str,
        title: Optional[str] = None,
        text: Optional[str] = None,
        description: Optional[str] = None,
        status: PostStatus = PostStatus.POSTED,
        tags: List[str] = ["test"]
    ):
        """
        Create article with given fields.

        :param post_id: Article ID.
        :param title: Article title.
        :param text: Article text.
        :param description: Article description.
        :param status: Set Post status.
        :param tags: Article tags.
        :return: Article update response.
        """
        return await self.put(
            f"{self.endpoints.articles}/{post_id}",
            json={
                "title": title,
                "text": text,
                "description": description,
                "tags": tags,
                "status": status
            }
        )

    async def text_create(
        self,
        text: str = "Text text.",
        status: PostStatus = PostStatus.POSTED,
        tags: List[str] = ["test"]
    ):
        """
        Create text post with given fields.

        :param text: Article text.
        :param status: Set Post status.
        :param tags: Text tags.
        :return: Text create response.
        """
        return await self.post(
            self.endpoints.texts,
            json={
                "text": text,
                "tags": tags,
                "status": status
            }
        )

    async def question_create(
        self,
        title: str = "Test question title.",
        text: str = '',
        description: str = '',
        tags: List[str] = ["test"],
        status: PostStatus = PostStatus.POSTED
    ):
        """
        Create question with given fields.
        """
        return await self.ac.post(
            self.endpoints.questions,
            headers=self.headers,
            json={
                "title": title,
                "text": text,
                "description": description,
                "tags": tags,
                "status": status
            }
        )

    async def question_get(
        self,
        question_id: str
    ):
        """
        Get question by question_id.
        """
        return await self.ac.get(f"{self.endpoints.questions}/{question_id}")

    async def question_subscribe(
        self,
        question_id: str
    ):
        """
        Subscribe to question.

        :param question_id: Question to subscribe.
        :return: Question subscribe response.
        """
        return await self.post(
            f"{self.endpoints.questions}/{question_id}/subscribe",
        )

    async def answer_create(
        self,
        question_id: str,
        text: str = "Answer text.",
        description: str = "Answer Description",
        tags: List[str] = ["test"],
        status: PostStatus = PostStatus.POSTED
    ):
        """
        Create answer with given fields.

        :param question_id: Question ID to which answer is created.
        :param text: Answer text.
        :param description: Answer description.
        :param tags: Post tags.
        :param status: Post status.
        :return: Answer create response.
        """
        return await self.ac.post(
            self.endpoints.answers,
            headers=self.headers,
            json={
                "text": text,
                "description": description,
                "question_id": question_id,
                "tags": tags,
                "status": status
            }
        )

    async def answer_get(
        self,
        answer_id: str
    ):
        """
        Get Answer by ID.
        """
        return await self.ac.get(f"{self.endpoints.answers}/{answer_id}")

    async def video_create(
        self,
        title: str = "Test question title.",
        url: str = "https://storage.yandexcloud.net/toci-bucket-01/40327bbd-b621-4262-aab7-e4a162243c1f.mp4",
        cover: Optional[str] = "cover",
        tags: List[str] = ["test"]
    ):
        """
        Create video post with given fields.

        :param title: Video title.
        :param url: Video url.
        :param cover: Video cover.
        :param tags: Post tags.
        :return: Video create response.
        """
        return await self.ac.post(
            self.endpoints.videos,
            headers=self.headers,
            json={
                "title": title,
                "url": url,
                "cover": cover,
                "tags": tags
            }
        )

    async def image_create(
        self,
        title: str = "Test image title.",
        cover: Optional[str] = "cover",
        status: PostStatus = PostStatus.POSTED,
        tags: List[str] = ["test"]
    ):
        """
        Create video post with given fields.

        :param title: Image title.
        :param cover: Image cover.
        :param status: Set Post status.
        :param tags: Post tags.
        :return: Image create response.
        """
        return await self.ac.post(
            self.endpoints.images,
            headers=self.headers,
            json={
                "title": title,
                "cover": cover,
                "tags": tags,
                "status": status
            }
        )

    async def file_create(
        self,
        title: str = "Test file title.",
        url: Optional[str] = "http://file.url",
        status: PostStatus = PostStatus.POSTED,
        tags: List[str] = ["test"]
    ):
        """
        Create File post with given fields.

        :param title: File title.
        :param url: File cover.
        :param status: Set Post status.
        :param tags: Post tags.
        :return: File create response.
        """
        return await self.ac.post(
            self.endpoints.files,
            headers=self.headers,
            json={
                "title": title,
                "url": url,
                "tags": tags,
                "status": status
            }
        )

    async def collection_create(
        self,
        title: str = "Collection Title",
        content_type: Optional[str] = "Article"
    ):
        """
        Create collection.

        :param title: Collection title.
        :param content_type: Collection content type.
        :return: Collection create response.
        """
        return await self.post(
            self.endpoints.collections,
            json={
                "title": title,
                "content_type": content_type
            }
        )

    async def collection_get(
        self,
        collection_id: str
    ):
        """
        Get collection by ID.
        :param collection_id: Collection ID.
        :return: Collection get response.
        """
        return await self.get(f"{self.endpoints.collections}?collection_id={collection_id}")

    async def collection_subscribe(
        self,
        collection_id: str
    ):
        """
        Subscribe to collection.

        :param collection_id: Collection to subscribe.
        :return: Collection subscribe response.
        """
        return await self.patch(
            f"{self.endpoints.collections}/subscribe?collection_id={collection_id}",
        )

    async def collection_add(
        self,
        collection_id: str,
        post_id: str
    ):
        """
        Commit post to collection.

        :param collection_id: Target collection ID.
        :param post_id: Post ID.
        :return: Collection add post response.
        """
        return await self.post(
            f"{self.endpoints.collections}/add?collection_id={collection_id}&post_id={post_id}"
        )

    async def collection_commit(
        self,
        collection_id: str,
        post_id: str
    ):
        """
        Commit post to collection.

        :param collection_id: Target collection ID.
        :param post_id: Post ID.
        :return: Commit create response.
        """
        return await self.post(
            self.endpoints.commits,
            json={
                "collection_id": collection_id,
                "post_id": post_id
            }
        )

    async def commit_approve(
        self,
        commit_id: str
    ):
        """
        Approve commit by ID.

        :param commit_id: Commit ID.
        :return: Commit approve response.
        """
        return await self.patch(
            f"{self.endpoints.commits}/{commit_id}/approve"
        )

    async def commit_reject(
        self,
        commit_id: str,
        reason: str = '',
    ):
        """
                Reject commit by ID.

                :param commit_id: Commit ID.
                :param reason: reason for rejection
                :return: Commit reject response.
                """
        return await self.patch(
            f"{self.endpoints.commits}/{commit_id}/reject",
            json={'reason': reason}
        )

    async def register_device(
        self,
        player_id: Optional[str] = None,
        device_id: Optional[str] = None
    ):
        """
        Create new device.

        :param player_id: Onesignal player ID.
        :param device_id: Device ID.
        :return: Device create response.
        """

        return await self.post(
            self.endpoints.devices,
            json={
                "player_id": player_id if player_id else str(uuid4()),
                "device_id": device_id if device_id else str(uuid4())
            }
        )

    async def comment_create(
        self,
        parent_id: str,
        text: str = "Comment text.",
        status: PostStatus = PostStatus.POSTED
    ):
        """
        Create Comment

        :param text: Comment text.
        :param status: Post status.
        :return: Comment create response.
        """
        return await self.ac.post(
            self.endpoints.comments,
            headers=self.headers,
            json={
                "text": text,
                "parent_id": parent_id,
                "status": status
            }
        )

