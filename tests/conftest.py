import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock

import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# 导入数据库引擎初始化函数
from src.database.session import init_engines, dispose_engines

@pytest_asyncio.fixture(scope="session")
def event_loop():
    """创建一个事件循环，用于异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

@pytest_asyncio.fixture(scope="session", autouse=True)
def setup_database():
    """初始化数据库引擎"""
    init_engines(echo=False)
    yield
    # 清理数据库引擎
    asyncio.get_event_loop().run_until_complete(dispose_engines())

@pytest_asyncio.fixture
async def async_session():
    async_engine = create_async_engine('postgresql+asyncpg://kratos:<EMAIL>:5432/toci')
    async_session = sessionmaker(
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
        bind=async_engine,
        class_=AsyncSession,
    )

    async with async_session() as session:
        await session.begin()
        yield session
        await session.rollback()
