import pytest
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import Test<PERSON>lient

from src.database.models import AppV<PERSON><PERSON>, User
from src.notifications.main import app
from src.notifications.service import AppVersionService


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_user():
    user = MagicMock(spec=User)
    user.id = "test-user-id"
    user.is_superuser = True
    return user


@pytest.fixture
def mock_app_version():
    version = MagicMock(spec=AppVersion)
    version.version = "1.0.0"
    version.build = 80
    version.target_os = "ios"
    version.release_date = datetime(2025, 6, 24, 7, 43, 13, 842000)
    version.release_notes = ""
    version.download_url = ""
    return version


class TestAppVersionEndpoint:
    """Test cases for the /app/version endpoint"""
    
    @pytest.mark.asyncio
    async def test_get_app_version_with_older_version(self, test_client, mock_app_version):
        """Test that older client version needs update"""
        with patch('src.notifications.main.get_app_version_service') as mock_get_service:
            mock_service = AsyncMock(spec=AppVersionService)
            mock_get_service.return_value = mock_service
            
            # Mock the database query
            mock_service.session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_app_version
            mock_service.session.execute.return_value = mock_result
            
            # Mock version parsing and comparison
            mock_service._parse_version = AppVersionService._parse_version
            mock_service._compare_versions = AppVersionService._compare_versions
            
            # Call the actual method
            mock_service.get_app_version.return_value = {
                "need_update": True,
                "force_update": False,
                "latest_build": 80,
                "latest_version": "1.0.0",
                "release_date": "2025-06-24T07:43:13.842000+00:00",
                "release_notes": "",
                "download_url": ""
            }
            
            response = test_client.get("/app/version?build=0&os=ios&version=0.9.0")
            
            assert response.status_code == 200
            data = response.json()
            assert data["need_update"] is True
            assert data["latest_version"] == "1.0.0"
    
    @pytest.mark.asyncio
    async def test_get_app_version_with_newer_version(self, test_client, mock_app_version):
        """Test that newer client version does not need update"""
        with patch('src.notifications.main.get_app_version_service') as mock_get_service:
            mock_service = AsyncMock(spec=AppVersionService)
            mock_get_service.return_value = mock_service
            
            # Mock the database query
            mock_service.session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_app_version
            mock_service.session.execute.return_value = mock_result
            
            # Mock version parsing and comparison
            mock_service._parse_version = AppVersionService._parse_version
            mock_service._compare_versions = AppVersionService._compare_versions
            
            # Call the actual method - newer version should not need update
            mock_service.get_app_version.return_value = {
                "need_update": False,
                "force_update": False,
                "latest_build": 80,
                "latest_version": "1.0.0",
                "release_date": "2025-06-24T07:43:13.842000+00:00",
                "release_notes": "",
                "download_url": ""
            }
            
            response = test_client.get("/app/version?build=0&os=ios&version=1.1.0")
            
            assert response.status_code == 200
            data = response.json()
            assert data["need_update"] is False
            assert data["force_update"] is False
    
    @pytest.mark.asyncio
    async def test_get_app_version_with_same_version(self, test_client, mock_app_version):
        """Test that same version does not need update"""
        with patch('src.notifications.main.get_app_version_service') as mock_get_service:
            mock_service = AsyncMock(spec=AppVersionService)
            mock_get_service.return_value = mock_service
            
            # Mock the database query
            mock_service.session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_app_version
            mock_service.session.execute.return_value = mock_result
            
            # Mock version parsing and comparison
            mock_service._parse_version = AppVersionService._parse_version
            mock_service._compare_versions = AppVersionService._compare_versions
            
            # Same version should not need update
            mock_service.get_app_version.return_value = {
                "need_update": False,
                "force_update": False,
                "latest_build": 80,
                "latest_version": "1.0.0",
                "release_date": "2025-06-24T07:43:13.842000+00:00",
                "release_notes": "",
                "download_url": ""
            }
            
            response = test_client.get("/app/version?build=80&os=ios&version=1.0.0")
            
            assert response.status_code == 200
            data = response.json()
            assert data["need_update"] is False
            assert data["force_update"] is False
    
    @pytest.mark.asyncio
    async def test_get_app_version_without_version_param(self, test_client, mock_app_version):
        """Test version check using only build number"""
        with patch('src.notifications.main.get_app_version_service') as mock_get_service:
            mock_service = AsyncMock(spec=AppVersionService)
            mock_get_service.return_value = mock_service
            
            # Mock the database query
            mock_service.session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_app_version
            mock_service.session.execute.return_value = mock_result
            
            # Without version param, should use build comparison
            mock_service.get_app_version.return_value = {
                "need_update": True,
                "force_update": False,
                "latest_build": 80,
                "latest_version": "1.0.0",
                "release_date": "2025-06-24T07:43:13.842000+00:00",
                "release_notes": "",
                "download_url": ""
            }
            
            response = test_client.get("/app/version?build=50&os=ios")
            
            assert response.status_code == 200
            data = response.json()
            assert data["need_update"] is True
            assert data["latest_build"] == 80
    
    @pytest.mark.asyncio
    async def test_get_app_version_force_update_major_version(self, test_client):
        """Test force update when major version difference is >= 1"""
        with patch('src.notifications.main.get_app_version_service') as mock_get_service:
            mock_service = AsyncMock(spec=AppVersionService)
            mock_get_service.return_value = mock_service
            
            # Mock app version with major version 2.0.0
            mock_app_v2 = MagicMock(spec=AppVersion)
            mock_app_v2.version = "2.0.0"
            mock_app_v2.build = 100
            mock_app_v2.target_os = "ios"
            mock_app_v2.release_date = datetime(2025, 7, 1, 0, 0, 0)
            mock_app_v2.release_notes = "Major update"
            mock_app_v2.download_url = "https://example.com/download"
            
            # Mock the database query
            mock_service.session = AsyncMock()
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_app_v2
            mock_service.session.execute.return_value = mock_result
            
            # Mock version parsing and comparison
            mock_service._parse_version = AppVersionService._parse_version
            mock_service._compare_versions = AppVersionService._compare_versions
            
            # Major version difference should force update
            mock_service.get_app_version.return_value = {
                "need_update": True,
                "force_update": True,
                "latest_build": 100,
                "latest_version": "2.0.0",
                "release_date": "2025-07-01T00:00:00+00:00",
                "release_notes": "Major update",
                "download_url": "https://example.com/download"
            }
            
            response = test_client.get("/app/version?build=0&os=ios&version=1.0.0")
            
            assert response.status_code == 200
            data = response.json()
            assert data["need_update"] is True
            assert data["force_update"] is True


class TestAppVersionService:
    """Test the AppVersionService directly"""
    
    def test_parse_version(self):
        """Test version parsing functionality"""
        service = AppVersionService(None, None)
        
        # Test basic version
        assert service._parse_version("1.2.3") == (1, 2, 3, 1000, 0)
        
        # Test version with v prefix
        assert service._parse_version("v1.2.3") == (1, 2, 3, 1000, 0)
        
        # Test pre-release versions
        assert service._parse_version("1.0.0-alpha") == (1, 0, 0, 100, 0)
        assert service._parse_version("1.0.0-alpha.1") == (1, 0, 0, 100, 1)
        assert service._parse_version("1.0.0-beta") == (1, 0, 0, 200, 0)
        assert service._parse_version("1.0.0-beta.2") == (1, 0, 0, 200, 2)
        assert service._parse_version("1.0.0-rc") == (1, 0, 0, 300, 0)
        assert service._parse_version("1.0.0-rc.1") == (1, 0, 0, 300, 1)
        
        # Test invalid version
        assert service._parse_version("invalid") == (0, 0, 0, 0, 0)
    
    def test_compare_versions(self):
        """Test version comparison functionality"""
        service = AppVersionService(None, None)
        
        # Test basic comparisons
        assert service._compare_versions("1.1.0", "1.0.0") == 1  # newer
        assert service._compare_versions("1.0.0", "1.1.0") == -1  # older
        assert service._compare_versions("1.0.0", "1.0.0") == 0  # same
        
        # Test with different patch versions
        assert service._compare_versions("1.0.1", "1.0.0") == 1
        assert service._compare_versions("1.0.0", "1.0.1") == -1
        
        # Test with pre-release versions
        assert service._compare_versions("1.0.0", "1.0.0-rc.1") == 1  # stable > rc
        assert service._compare_versions("1.0.0-rc.1", "1.0.0-beta.1") == 1  # rc > beta
        assert service._compare_versions("1.0.0-beta.1", "1.0.0-alpha.1") == 1  # beta > alpha
        
        # Test major version differences
        assert service._compare_versions("2.0.0", "1.9.9") == 1
        assert service._compare_versions("1.0.0", "2.0.0") == -1