import os
import random
from dotenv import load_dotenv
from tests.client import MemeFansClient

# 切换到 authors 目录
os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "memecoin"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
from unittest.mock import AsyncMock

import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy import select

from src.database.models.Pair import Pair
from src.database.models.Token import Token
from src.memecoin.main import app
from src.database.models import User, Collection


@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def client(ac: AsyncClient):
    author = MemeFansClient(ac, token="gx8uu9xc2ZHaQX5ocKohjzL77lqTYcFNdy10TPGn1Lk", username="lazyfooder", email="<EMAIL>", id="BgLkByLAUul")
    return author

@pytest_asyncio.fixture
async def test_user(async_session):
    stmt = select(User).limit(1)
    user = (await async_session.execute(stmt)).scalar()
    return user

@pytest_asyncio.fixture
async def test_token(async_session):
    stmt = select(Pair).limit(1)
    token = (await async_session.execute(stmt)).scalar()
    return token

@pytest_asyncio.fixture
async def test_collection(async_session):
    stmt = select(Collection).limit(1)
    coll = (await async_session.execute(stmt)).scalar()
    return coll


# 测试获取代币信息
@pytest.mark.asyncio
async def test_get_token_info(client, test_token):
    # 测试使用token_address
    response = await client.get(f"/token?token_address={test_token.base}")
    assert response.status_code in [200, 404]
    
    # 测试使用collection_id
    response = await client.get("/token?collection_id=collection123")
    assert response.status_code in [200, 404]
    
    # 测试错误情况 - 没有提供参数
    response = await client.get("/token")
    assert response.status_code == 400


# 测试根据post_id获取代币列表
@pytest.mark.asyncio
async def test_get_token_list_by_post_id(client):
    response = await client.get("/post123/tokens")
    assert response.status_code in [200, 404]


# 测试获取代币列表
@pytest.mark.asyncio
async def test_get_token_list(client):
    response = await client.get("/token_list?list_type=top")
    assert response.status_code in [200, 404]


# 测试获取K线数据
@pytest.mark.asyncio
async def test_get_token_kline(client, test_token):
    response = await client.get(f"/kline?token_address={test_token.base}&interval=5m")
    assert response.status_code == 200


# 测试获取代币持有人列表
@pytest.mark.asyncio
async def test_get_holder_list(client, test_token):
    response = await client.get(f"/holder_list?token_address={test_token.base}")
    assert response.status_code == 200


# 测试获取用户资产
@pytest.mark.asyncio
async def test_get_user_assets(client):
    response = await client.get("/profile/assets")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]


# 测试获取用户创建的meme
@pytest.mark.asyncio
async def test_get_my_memes(client):
    response = await client.get("/profile/memes")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]

@pytest.mark.asyncio
async def test_get_out_bnb_amount(client, test_token):
    payload = {
        "token_address": "0x8C071D9Bc186046C0E1487D8d9F8F8C56022E464",
        "token_amount": "100"
    }
    response = await client.get(f"/get_out_bnb_amount?token_address={payload['token_address']}&token_amount={payload['token_amount']}")
    print("get_out_bnb_amount response: ", response.json())
    assert response.status_code == 200

# 测试获取输出代币数量
@pytest.mark.asyncio
async def test_get_out_token_amount(client, test_token):


    # payload_no_token_address = {
    #     "token_address": "0x8C071D9Bc186046C0E1487D8d9F8F8C56022E464",
    #     "usd_amount": "0.5"
    # }
    # response = await client.get(f"/get_out_token_amount?usd_amount={payload_no_token_address['usd_amount']}")
    # print("get_out_token_amount response: ", response.json())
    # assert response.status_code == 200
    
    payload = {
        "token_address": "0x7C4d6B1b5F784Ed69ee7FFAABa03B77FeBC2942d",
        "usd_amount": "0.5"
    }
    response = await client.get(f"/get_out_token_amount?token_address={payload['token_address']}&usd_amount={payload['usd_amount']}")
    print("get_out_token_amount response: ", response.json())
    assert response.status_code == 200


# 测试创建meme代币
@pytest.mark.asyncio
async def test_create_meme(client, test_collection):
    payload = {
        "collection_id": str(random.randint(1, 1000000)),
        "name": "Test Meme",
        "symbol": "TIGUAN",
        "repo_url": "https://github.com/test/repo",
        "about": "This is a test meme",
        "avatar": "avatar_url",
        "bnb_amount": "0.1",
        "gas": 40000000,
        "is_with_usdt": True
    }
    
    response = await client.post("/create_meme", json=payload)
    print("collection_id: ", test_collection.id)
    print("create_meme response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]


# 测试购买代币
@pytest.mark.asyncio
async def test_buy_token(client, test_token):
    payload = {
        "token_address": "0x446d6537D8EEF74Ea6f0cBa6361D0d58f7f06B22",
        "amount": "0.1",
        "gas": 150000
    }

    
    response = await client.post("/buy_token", json=payload)
    print("buy_token response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]


@pytest.mark.asyncio
async def test_buy_token_with_usdt(client, test_token):
    payload = {
        "token_address": "0xE03df454D793E4dB211C94f096721e483f869aFf",
        "amount": "1000",
        "gas": 500000,
        "is_with_usdt": True
    }

    
    response = await client.post("/buy_token", json=payload)
    print("buy_token response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]

# 测试出售代币
@pytest.mark.asyncio
async def test_sell_token_for_usdt(client, test_token):
    payload = {
        "token_address": "0x446d6537D8EEF74Ea6f0cBa6361D0d58f7f06B22",
        "amount": "100",
        "gas": 4000000,
        "is_with_usdt": True
    }
    
    response = await client.post("/sell_token", json=payload)
    print("buy_token response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]

# 测试发送代币
@pytest.mark.asyncio
async def test_send_token_by_relayer(client, test_token):
    payload = {
        "token_address": "0x446d6537D8EEF74Ea6f0cBa6361D0d58f7f06B22",
        "to_address": "0x4696b98C202439Df3535Bf280cc1015e1ead1a9b",
        "amount": "10",
        "gas": 4000000,
    }
    
    response = await client.post("/send_token", json=payload)
    print("test_send_token_by_relayer response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]



@pytest.mark.asyncio
async def test_send_usdt_by_relayer(client, test_token):
    payload = {
        "token_address": "0x9507d2Fda7b1f9eD353Db31fC3A3403cbff207bd",
        "to_address": "0x4696b98C202439Df3535Bf280cc1015e1ead1a9b",
        "amount": "10",
        "gas": 4000000,
    }
    
    response = await client.post("/send_token", json=payload)
    print("test_send_usdt_by_relayer response: ", response.json())
    # 根据实际情况调整
    assert response.status_code in [201, 401, 403, 400]

# 测试接收代币
@pytest.mark.asyncio
async def test_receive_token(client):
    response = await client.get("/receive_token")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]


# 测试WebSocket连接
@pytest.mark.asyncio
async def test_ws_token_prices():
    # 需要模拟WebSocket客户端和连接
    # 这个测试比较复杂，需要使用特殊库来测试WebSocket，此处简化处理
    pass


# 测试获取交易历史
@pytest.mark.asyncio
async def test_get_transaction_history(client):
    response = await client.get("/transaction_history")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]


# 测试检查交易状态
@pytest.mark.asyncio
async def test_get_transaction_status(client):
    payload = ["0x123", "0x456"]
    response = await client.post("/transaction_status", json=payload)
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403, 400]
    
    # 测试空列表
    response = await client.post("/transaction_status", json=[])
    assert response.status_code == 200


# 测试获取估计gas费
@pytest.mark.asyncio
async def test_get_estimated_gas(client):
    response = await client.get("/estimated_gas?gas_type=create")
    print("get_estimated_gas create response: ", response.json())
    response = await client.get("/estimated_gas?gas_type=buy&function_params=['0x8C071D9Bc186046C0E1487D8d9F8F8C56022E464']")
    print("get_estimated_gas buy response: ", response.json())
    response = await client.get("/estimated_gas?gas_type=sell&function_params=['0x8C071D9Bc186046C0E1487D8d9F8F8C56022E464']")
    print("get_estimated_gas sell response: ", response.json())
    response = await client.get("/estimated_gas?gas_type=transfer_bnb")
    print("get_estimated_gas transfer_bnb response: ", response.json())
    response = await client.get("/estimated_gas?gas_type=transfer_token")
    print("get_estimated_gas transfer_token response: ", response.json())
    assert response.status_code == 200


# 测试获取BNB余额
@pytest.mark.asyncio
async def test_get_bnb_balance(client):
    response = await client.get("/bnb_balance")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]


# 测试获取代币数量
@pytest.mark.asyncio
async def test_get_token_amount(client, test_token):
    response = await client.get(f"/token_amount?token_address={test_token.base}")
    # 根据实际情况调整
    assert response.status_code in [200, 401, 403]


# 测试获取输入BNB数量
@pytest.mark.asyncio
async def test_get_in_bnb_amount(client, test_token):
    response = await client.get(f"/get_in_bnb_amount?token_address={test_token.base}&token_amount=100")
    assert response.status_code == 200



# 测试获取收费后的输出BNB数量
@pytest.mark.asyncio
async def test_get_out_bnb_amount_after_fee(client, test_token):
    response = await client.get(f"/get_out_bnb_amount_after_fee?token_address={test_token.base}&token_amount=100")
    assert response.status_code == 200


# 测试获取代币进度
@pytest.mark.asyncio
async def test_get_token_progress(client, test_token):
    response = await client.get(f"/get_token_progress?token_address={test_token.base}")
    assert response.status_code == 200 