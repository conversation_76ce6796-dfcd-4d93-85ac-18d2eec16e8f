from src.database.session import get_session
from src.database.models.TociIm import TociIm
from src.database.models import User
from src.im.main import app
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import insert, select, delete
from starlette.testclient import TestClient
import pytest_asyncio
from unittest.mock import AsyncMock, patch
import uuid
import pytest
import os
from dotenv import load_dotenv

# 切换到 im 目录
os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "im"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv("../../.env")


@pytest_asyncio.fixture
async def client():
    """创建测试客户端"""
    with TestClient(app) as client:
        yield client


@pytest.mark.asyncio
async def test_by_im_batch_limit_50_ids(client: TestClient):
    """测试批量获取 - 超过50个ID的限制"""
    # 创建超过50个ID的字符串（包含已存在的和不存在的）
    existing_ids = ["BhUOtbJOe4u", "TestIM123", "AnotherIM456"]
    fake_ids = [f"FakeID{i}" for i in range(1, 50)]  # 47个假ID
    all_ids = existing_ids + fake_ids

    im_ids = ",".join(all_ids)

    response = client.get(f"/by_im?im_id=8946972030")
    print(response.json())
    assert response.status_code == 200

    data = response.json()

    # 验证只返回存在的前3个ID（因为后面都是假的）
    assert len(data) == 3
    assert "BhVIYkfnz16" in data
    assert "TestIM123" in data
    assert "AnotherIM456" in data
