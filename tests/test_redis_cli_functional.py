#!/usr/bin/env python3
"""
Functional Redis Client Test

Simple functional test for Redis client that can run with or without a Redis server.
"""

import asyncio
import json
import sys
import os
from unittest.mock import patch, MagicMock
import pytest

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Mock the settings before importing redis clients
class MockSettings:
    REDIS_POOL = {'decode_responses': True, 'max_connections': 10}
    host = "localhost"
    port = 6379
    db = 0

# Mock the settings module
sys.modules['src.common.settings'] = type('MockModule', (), {
    'settings': MockSettings()
})

from src.common.redis_cli import RedisCli


class TestRedisCli:
    """Test class for RedisCli functionality"""
    
    def test_redis_cli_instances(self):
        """Test that RedisCli provides correct client instances"""
        # Test async client
        async_client = RedisCli.async_()
        assert async_client is not None
        assert hasattr(async_client, 'get_json')
        assert hasattr(async_client, 'set_json')
        assert hasattr(async_client, 'health_check')
        assert hasattr(async_client, 'get_pool_stats')
        
        # Test sync client
        sync_client = RedisCli.sync()
        assert sync_client is not None
        assert hasattr(sync_client, 'get_json')
        assert hasattr(sync_client, 'set_json')
        assert hasattr(sync_client, 'health_check')
        assert hasattr(sync_client, 'get_pool_stats')
    
    def test_client_method_signatures(self):
        """Test that both clients have the same method signatures"""
        sync_client = RedisCli.sync()
        async_client = RedisCli.async_()
        
        # Test that both have the same utility methods (only test implemented methods)
        expected_methods = [
            'get_json', 'set_json', 'mget_json', 'mset_json',
            'safe_delete', 'set_with_ttl', 'health_check',
            'get_pool_stats', 'get_info', 'get_memory_usage'
        ]
        
        for method in expected_methods:
            assert hasattr(sync_client, method), f"SyncRedisClient missing method: {method}"
            assert hasattr(async_client, method), f"AsyncRedisClient missing method: {method}"
    
    def test_sync_client_with_mocked_redis(self):
        """Test sync client with mocked Redis operations"""
        sync_client = RedisCli.sync()
        
        # Mock the underlying Redis methods
        with patch.object(sync_client, 'get', return_value=b'{"test": "data"}') as mock_get, \
             patch.object(sync_client, 'set', return_value=True) as mock_set, \
             patch.object(sync_client, 'ping', return_value=True) as mock_ping:
            
            # Test JSON operations
            result = sync_client.set_json("test:key", {"test": "data"})
            assert result is True
            mock_set.assert_called_once()
            
            # Test get_json
            data = sync_client.get_json("test:key")
            assert data == {"test": "data"}
            mock_get.assert_called_once_with("test:key")
            
            # Test health check
            health = sync_client.health_check()
            assert health is True
            mock_ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_async_client_with_mocked_redis(self):
        """Test async client with mocked Redis operations"""
        async_client = RedisCli.async_()
        
        # Create async mock functions
        async def mock_get(key):
            return b'{"test": "data"}'
        
        async def mock_set(key, value, ex=None):
            return True
            
        async def mock_ping():
            return True
        
        # Mock the underlying Redis methods
        with patch.object(async_client, 'get', side_effect=mock_get) as patched_get, \
             patch.object(async_client, 'set', side_effect=mock_set) as patched_set, \
             patch.object(async_client, 'ping', side_effect=mock_ping) as patched_ping:
            
            # Test JSON operations
            result = await async_client.set_json("test:key", {"test": "data"})
            assert result is True
            patched_set.assert_called_once()
            
            # Test get_json
            data = await async_client.get_json("test:key")
            assert data == {"test": "data"}
            patched_get.assert_called_once_with("test:key")
            
            # Test health check
            health = await async_client.health_check()
            assert health is True
            patched_ping.assert_called_once()
    
    def test_sync_client_error_handling(self):
        """Test sync client error handling"""
        sync_client = RedisCli.sync()
        
        # Mock Redis operations to raise exceptions
        with patch.object(sync_client, 'get', side_effect=Exception("Redis error")) as mock_get:
            # Test that get_json returns default on error
            result = sync_client.get_json("test:key", default="error_default")
            assert result == "error_default"
            mock_get.assert_called_once_with("test:key")
        
        with patch.object(sync_client, 'set', side_effect=Exception("Redis error")) as mock_set:
            # Test that set_json returns False on error
            result = sync_client.set_json("test:key", {"test": "data"})
            assert result is False
    
    @pytest.mark.asyncio
    async def test_async_client_error_handling(self):
        """Test async client error handling"""
        async_client = RedisCli.async_()
        
        # Mock Redis operations to raise exceptions
        with patch.object(async_client, 'get', side_effect=Exception("Redis error")) as mock_get:
            # Test that get_json returns default on error
            result = await async_client.get_json("test:key", default="error_default")
            assert result == "error_default"
            mock_get.assert_called_once_with("test:key")
        
        with patch.object(async_client, 'set', side_effect=Exception("Redis error")) as mock_set:
            # Test that set_json returns False on error
            result = await async_client.set_json("test:key", {"test": "data"})
            assert result is False
    
    def test_connection_pool_stats(self):
        """Test that connection pool stats are accessible"""
        sync_client = RedisCli.sync()
        async_client = RedisCli.async_()
        
        # Both clients should have connection pools
        assert hasattr(sync_client, 'connection_pool')
        assert hasattr(async_client, 'connection_pool')
        
        # Test that get_pool_stats method exists (but may fail due to Redis version differences)
        assert hasattr(sync_client, 'get_pool_stats')
        assert hasattr(async_client, 'get_pool_stats')
        
        # Try to get stats, but handle potential attribute errors gracefully
        try:
            sync_stats = sync_client.get_pool_stats()
            # Verify stats have expected keys if successful
            expected_keys = ['created_connections', 'available_connections', 'in_use_connections', 'max_connections']
            for key in expected_keys:
                assert key in sync_stats, f"Missing key {key} in sync stats"
        except AttributeError as e:
            # This is expected if Redis version doesn't have certain pool attributes
            print(f"Pool stats not fully supported in this Redis version: {e}")
        
        try:
            async_stats = async_client.get_pool_stats()
            # Verify stats have expected keys if successful  
            expected_keys = ['created_connections', 'available_connections', 'in_use_connections', 'max_connections']
            for key in expected_keys:
                assert key in async_stats, f"Missing key {key} in async stats"
        except AttributeError as e:
            # This is expected if Redis version doesn't have certain pool attributes
            print(f"Async pool stats not fully supported in this Redis version: {e}")


class TestRedisIntegration:
    """Integration tests that can work with or without real Redis"""
    
    def test_json_serialization(self):
        """Test JSON serialization/deserialization logic"""
        sync_client = RedisCli.sync()
        
        # Test various data types
        test_cases = [
            {"string": "test", "number": 42, "boolean": True, "null": None},
            [1, 2, 3, "four", {"five": 6}],
            "simple string",
            42,
            True,
            None
        ]
        
        for test_data in test_cases:
            # Mock the set/get to test serialization logic
            serialized = json.dumps(test_data)
            
            with patch.object(sync_client, 'set', return_value=True) as mock_set, \
                 patch.object(sync_client, 'get', return_value=serialized.encode('utf-8')) as mock_get:
                
                # Test round-trip
                sync_client.set_json("test:key", test_data)
                result = sync_client.get_json("test:key")
                
                assert result == test_data, f"Failed for data: {test_data}"
    
    def test_batch_operations_logic(self):
        """Test batch operations logic"""
        sync_client = RedisCli.sync()
        
        test_data = {
            "key1": {"name": "Alice", "age": 30},
            "key2": {"name": "Bob", "age": 25},
            "key3": {"name": "Charlie", "age": 35}
        }
        
        # Mock mset to return True
        with patch.object(sync_client, 'mset', return_value=True) as mock_mset:
            result = sync_client.mset_json(test_data)
            assert result is True
            
            # Verify that mset was called with JSON-serialized data
            call_args = mock_mset.call_args[0][0]
            for key, value in test_data.items():
                assert key in call_args
                assert json.loads(call_args[key]) == value
        
        # Mock mget to return serialized values
        serialized_values = [json.dumps(value).encode('utf-8') for value in test_data.values()]
        
        with patch.object(sync_client, 'mget', return_value=serialized_values) as mock_mget:
            result = sync_client.mget_json(list(test_data.keys()))
            
            assert result == test_data
            mock_mget.assert_called_once_with(list(test_data.keys()))


if __name__ == "__main__":
    """Run tests directly with python"""
    print("🧪 Running Redis Client Functional Tests...")
    
    # Run sync tests
    test_instance = TestRedisCli()
    
    try:
        test_instance.test_redis_cli_instances()
        print("✅ Redis CLI instances test passed")
        
        test_instance.test_client_method_signatures()
        print("✅ Method signatures test passed")
        
        test_instance.test_sync_client_with_mocked_redis()
        print("✅ Sync client with mocked Redis test passed")
        
        test_instance.test_sync_client_error_handling()
        print("✅ Sync client error handling test passed")
        
        test_instance.test_connection_pool_stats()
        print("✅ Connection pool stats test passed")
        
        # Test integration
        integration_test = TestRedisIntegration()
        integration_test.test_json_serialization()
        print("✅ JSON serialization test passed")
        
        integration_test.test_batch_operations_logic()
        print("✅ Batch operations logic test passed")
        
        print("\n🎉 All functional tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    # Run async tests
    async def run_async_tests():
        try:
            await test_instance.test_async_client_with_mocked_redis()
            print("✅ Async client with mocked Redis test passed")
            
            await test_instance.test_async_client_error_handling()
            print("✅ Async client error handling test passed")
            
            print("🎉 All async tests passed!")
            
        except Exception as e:
            print(f"❌ Async test failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(run_async_tests())