import os
from dotenv import load_dotenv

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import asyncio
import aiohttp
import os
import glob
import uuid
import io
from PIL import Image
from sqlalchemy import select, update
from typing import List

from src.media_core.cloudflare_helper import CloudflareHelper
from src.media_core.repos import CloudflareRepo
from src.database.session import SessionWrite
from src.media_core.services import CloudflareService
from src.database.models import Video

# Global lock for video updates to prevent concurrent modifications
video_update_lock = asyncio.Lock()


async def update_video_cover(session, video_id: int, new_cover_url: str) -> bool:
    """
    更新视频的封面 URL

    :param session: SQLAlchemy async session
    :param video_id: 视频 ID
    :param new_cover_url: 新的封面 URL
    :return: 更新是否成功
    """
    try:
        stmt = update(Video).where(Video.id == video_id).values(cover=new_cover_url)
        await session.execute(stmt)
        await session.commit()
        return True
    except Exception:
        await session.rollback()
        return False


async def get_videos_with_cloudflare_thumbnails(session, size: int = 20) -> List[Video]:
    """
    获取使用 Cloudflare Stream 缩略图的视频记录

    :param session: SQLAlchemy async session
    :param size: 返回的视频数量
    :return: 视频记录列表
    """
    stmt = select(Video).where(Video.cover.like("https://customer-fzuyyy7va6ohx90h.cloudflarestream.com/%")).limit(size)
    result = await session.execute(stmt)
    return result.scalars().all()


async def test_upload():
    image_url = "http://imagedelivery.net/BE1TAx1da-oIYxcOANXnlA/470c3ab4-e441-4ad1-4e44-95ce68068100/avatar"

    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(image_url) as response:
                if response.status == 200:
                    image_data = await response.read()
                    print(f"Successfully downloaded image from {image_url}")
                else:
                    print(f"Failed to download image: HTTP {response.status}")
                    return None
        except Exception as e:
            print(f"Error downloading image: {e}")
            return None

    # Initialize helper (requires real credentials)
    helper = CloudflareHelper()

    try:
        unique_filename = f"{uuid.uuid4()}.jpg"
        result = await helper.upload_image_to_r2(
            file_content=image_data,
            file_name=unique_filename,
            content_type="image/jpeg"
        )
        print(f"Upload successful! Image URL: {result}")
        return result
    except Exception as e:
        print(f"Upload failed: {e}")
        return None


async def upload_all_png_images():
    """Upload all PNG images from tests folder to R2 and print their URLs"""
    # Find all PNG files in tests folder
    tests_dir = os.path.join(os.path.dirname(__file__), "tests")
    png_files = glob.glob(os.path.join(tests_dir, "*.png"))

    if not png_files:
        print("No PNG files found in tests folder")
        return

    print(f"Found {len(png_files)} PNG files to upload:")
    for png_file in png_files:
        print(f"  - {os.path.basename(png_file)}")

    # Initialize helper
    helper = CloudflareHelper()

    # Upload each PNG file
    for png_file in png_files:
        try:
            with open(png_file, "rb") as f:
                image_data = f.read()

            original_name = os.path.basename(png_file)
            # Generate UUID-based filename preserving the original extension
            file_name = f"{uuid.uuid4()}{os.path.splitext(original_name)[1]}"
            print(f"Uploading {original_name} as {file_name}...")

            result = await helper.upload_image_to_r2(
                file_content=image_data,
                file_name=file_name,
                content_type="image/png"
            )

            print(f"  URL: {result}")

        except Exception as e:
            print(f"✗ Failed to upload {file_name}: {e}")


async def process_single_video(session, service, video):
    """Process a single video's thumbnail update asynchronously"""
    try:
        print(f"Processing video {video.id} - {video.cover}")
        cover = await service.upload_thumbnail_to_r2(video.cover)
        if cover:
            # Use the global lock to prevent concurrent updates
            async with video_update_lock:
                await update_video_cover(session, video.id, cover)
                print(f"Updated video {video.id} cover to: {cover}")
            return True
        return False
    except Exception as e:
        print(f"Error processing video {video.id}: {str(e)}")
        return False

async def update_video_thumbnail():
    """
    逐条读取DB videos表的数据，将cover字段前缀为https://customer-fzuyyy7va6ohx90h.cloudflarestream.com/的数据里cover上传到r2，修改cover字段
    使用异步方式批量处理视频
    """
    async with SessionWrite() as session:
        # We still need CloudflareService for the upload_thumbnail_to_r2 method
        service = CloudflareService(CloudflareRepo(session), CloudflareHelper())
        
        while True:
            # Get a batch of videos to process
            videos = await get_videos_with_cloudflare_thumbnails(session, 50)  # Increased batch size
            if not videos:
                print("No more videos to process")
                break
                
            print(f"Processing batch of {len(videos)} videos...")
            
            # Process videos concurrently
            tasks = [process_single_video(session, service, video) for video in videos]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Log results
            success_count = sum(1 for r in results if r is True)
            print(f"Batch complete. Successfully processed {success_count}/{len(videos)} videos")
            
            # Small delay between batches to prevent overwhelming the system
            await asyncio.sleep(1)


if __name__ == "__main__":
    asyncio.run(update_video_thumbnail())
