#!/usr/bin/env python3
"""
幂等性功能测试脚本

测试 order_id 重复请求处理机制
"""

import asyncio
import requests
import time
import uuid
from typing import Dict, Any

# 配置
API_BASE_URL = "http://localhost:8000"  # 请根据实际情况修改
TEST_TOKEN = "_5T1clAkV-XuJ10Ku0U3XHs9SXhvstg1xI6Ev5NOmHs"  # 请替换为有效的 JWT token

# 测试请求头
HEADERS = {
    "Authorization": f"Bearer {TEST_TOKEN}",
    "Content-Type": "application/json"
}

def test_create_meme_idempotency():
    """测试创建 Meme 的幂等性"""
    print("测试创建 Meme 的幂等性...")
    
    order_id = f"test_create_{uuid.uuid4().hex[:8]}"
    
    # 准备请求数据
    request_data = {
        "collection_id": f"test_collection_{uuid.uuid4().hex[:8]}",
        "name": "Test Meme Token",
        "symbol": "TMT",
        "repo_url": "https://github.com/test/test",
        "about": "Test meme token for idempotency testing",
        "avatar": "https://example.com/avatar.png",
        "bnb_amount": "0.01",
        "gas": 300000,
        "gas_price": 10_000_000,
        "order_id": order_id,
        "is_with_usdt": True
    }
    
    # 第一次请求
    print(f"发送第一次请求 (order_id: {order_id})...")
    response1 = requests.post(
        f"{API_BASE_URL}/create_meme",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第一次请求状态: {response1.status_code}")
    if response1.status_code == 201:
        result1 = response1.json()
        print(f"第一次请求成功: {result1}")
    else:
        print(f"第一次请求失败: {response1.text}")
        return
    
    # 等待一下
    time.sleep(1)
    
    # 第二次请求（重复的 order_id）
    print(f"发送第二次请求 (相同 order_id: {order_id})...")
    response2 = requests.post(
        f"{API_BASE_URL}/create_meme",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第二次请求状态: {response2.status_code}")
    if response2.status_code == 409:
        print("✅ 幂等性检查成功: 第二次请求被正确拒绝")
        print(f"错误信息: {response2.json()}")
    else:
        print("❌ 幂等性检查失败: 第二次请求没有被拒绝")
        if response2.status_code == 201:
            print(f"第二次请求结果: {response2.json()}")


def test_buy_token_idempotency():
    """测试购买代币的幂等性"""
    print("\n测试购买代币的幂等性...")
    
    order_id = f"test_buy_{uuid.uuid4().hex[:8]}"
    
    # 准备请求数据
    request_data = {
        "token_address": "0x1234567890123456789012345678901234567890",  # 示例地址
        "amount": "0.01",
        "gas": 250000,
        "gas_price": 10_000_000,
        "order_id": order_id,
        "is_with_usdt": True
    }
    
    # 第一次请求
    print(f"发送第一次购买请求 (order_id: {order_id})...")
    response1 = requests.post(
        f"{API_BASE_URL}/buy_token",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第一次请求状态: {response1.status_code}")
    if response1.status_code in [201, 400]:  # 可能因为测试环境而失败
        print(f"第一次请求响应: {response1.text}")
    
    # 等待一下
    time.sleep(1)
    
    # 第二次请求（重复的 order_id）
    print(f"发送第二次购买请求 (相同 order_id: {order_id})...")
    response2 = requests.post(
        f"{API_BASE_URL}/buy_token",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第二次请求状态: {response2.status_code}")
    if response2.status_code == 409:
        print("✅ 幂等性检查成功: 第二次请求被正确拒绝")
        print(f"错误信息: {response2.json()}")
    else:
        print("❌ 幂等性检查可能有问题，或第一次请求就失败了")
        print(f"第二次请求响应: {response2.text}")


def test_sell_token_idempotency():
    """测试卖出代币的幂等性"""
    print("\n测试卖出代币的幂等性...")
    
    order_id = f"test_sell_{uuid.uuid4().hex[:8]}"
    
    # 准备请求数据
    request_data = {
        "token_address": "0x1234567890123456789012345678901234567890",  # 示例地址
        "amount": "1000",  # 要卖出的代币数量
        "gas": 250000,
        "gas_price": 10_000_000,
        "order_id": order_id,
        "is_with_usdt": False
    }
    
    # 第一次请求
    print(f"发送第一次卖出请求 (order_id: {order_id})...")
    response1 = requests.post(
        f"{API_BASE_URL}/sell_token",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第一次请求状态: {response1.status_code}")
    print(f"第一次请求响应: {response1.text}")
    
    # 等待一下
    time.sleep(1)
    
    # 第二次请求（重复的 order_id）
    print(f"发送第二次卖出请求 (相同 order_id: {order_id})...")
    response2 = requests.post(
        f"{API_BASE_URL}/sell_token",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第二次请求状态: {response2.status_code}")
    if response2.status_code == 409:
        print("✅ 幂等性检查成功: 第二次请求被正确拒绝")
        print(f"错误信息: {response2.json()}")
    else:
        print("❌ 幂等性检查可能有问题，或第一次请求就失败了")
        print(f"第二次请求响应: {response2.text}")


def test_send_asset_idempotency():
    """测试发送资产的幂等性"""
    print("\n测试发送资产的幂等性...")
    
    order_id = f"test_send_{uuid.uuid4().hex[:8]}"
    
    # 准备请求数据
    request_data = {
        "token_address": "native_token",  # 发送原生代币
        "to_address": "0x9876543210987654321098765432109876543210",  # 示例接收地址
        "amount": "0.001",
        "gas": 100000,
        "gas_price": 10_000_000,
        "order_id": order_id
    }
    
    # 第一次请求
    print(f"发送第一次发送请求 (order_id: {order_id})...")
    response1 = requests.post(
        f"{API_BASE_URL}/send_asset",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第一次请求状态: {response1.status_code}")
    print(f"第一次请求响应: {response1.text}")
    
    # 等待一下
    time.sleep(1)
    
    # 第二次请求（重复的 order_id）
    print(f"发送第二次发送请求 (相同 order_id: {order_id})...")
    response2 = requests.post(
        f"{API_BASE_URL}/send_asset",
        json=request_data,
        headers=HEADERS
    )
    
    print(f"第二次请求状态: {response2.status_code}")
    if response2.status_code == 409:
        print("✅ 幂等性检查成功: 第二次请求被正确拒绝")
        print(f"错误信息: {response2.json()}")
    else:
        print("❌ 幂等性检查可能有问题，或第一次请求就失败了")
        print(f"第二次请求响应: {response2.text}")


def main():
    """主测试函数"""
    print("=" * 60)
    print("Memecoin API 幂等性测试")
    print("=" * 60)
    print("注意: 请确保API服务正在运行，并且已设置有效的JWT token")
    print("=" * 60)
    
    # 运行所有测试
    test_buy_token_idempotency()
    test_sell_token_idempotency()
    test_send_asset_idempotency()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main() 