
import os
from dotenv import load_dotenv

# 切换到 authors 目录
# os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "feed"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import uuid

import pytest_asyncio
from httpx import AsyncClient
from fastapi import FastAPI
from sqlalchemy import insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.feed.main import app
from src.database.models import Post, User, Author, Collection
from src.database.constants import PostType, PostStatus, Region
from src.database.session import get_session
from tests.client import MemeFansClient

@pytest_asyncio.fixture
async def client():
    client = AsyncClient(app=app, base_url="http://feed:8000")
    yield client
    await client.aclose()

@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def memeclient(ac: AsyncClient):
    author = MemeFansClient(ac, token="_5T1clAkV-XuJ10Ku0U3XHs9SXhvstg1xI6Ev5NOmHs", username="liuygo", email="<EMAIL>", id="BgJos9JePpZ")
    # await author.register()
    # await author.login()
    return author

@pytest_asyncio.fixture
async def test_user(async_session):
    stmt = select(User).limit(1)
    user = (await async_session.execute(stmt)).scalar()
    return user

@pytest_asyncio.fixture
async def test_post(async_session):
    stmt = select(Post).limit(1)
    post = (await async_session.execute(stmt)).scalar()
    return post

@pytest_asyncio.fixture
async def test_get_feed_anonymous(client: AsyncClient):
    """测试匿名用户获取feed"""
    response = await client.get("/feed")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data

    for item in data["items"]:
        assert item["status"] == PostStatus.POSTED


@pytest_asyncio.fixture
async def test_get_feed_authenticated(client: AsyncClient, test_user: User, monkeypatch):
    """测试已认证用户获取feed"""
    # 模拟认证用户
    async def mock_optional_user():
        return test_user
    
    monkeypatch.setattr("src.feed.main.optional_user", mock_optional_user)
    
    response = await client.get("/feed")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    
    # 验证返回的帖子包含feed_reason字段
    for item in data["items"]:
        assert "id" in item
        assert "type" in item
        assert "status" in item

@pytest.mark.asyncio
async def test_get_recommendations(memeclient: MemeFansClient):
    response = await memeclient.get("/recommendations")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    assert len(data["items"]) > 0
    
    for item in data["items"]:
        assert "id" in item
        assert "type" in item
        assert "status" in item

@pytest.mark.asyncio
async def test_get_tag_recommendations(memeclient: MemeFansClient):
    response = await memeclient.get("/tag_recommendations?tag=量子力学")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    assert len(data["items"]) > 0
    
    for item in data["items"]:
        assert "id" in item
        assert "type" in item
        assert "status" in item


@pytest.mark.asyncio
async def test_get_recommendations_anonymous(client: AsyncClient):
    """测试匿名用户获取推荐"""
    response = await client.get("/recommendations")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    assert len(data["items"]) > 0
    
    for item in data["items"]:
        assert "id" in item
        assert "type" in item
        assert "status" in item


@pytest.mark.asyncio
async def test_get_recommendations_with_type(client: AsyncClient):
    """测试指定类型的推荐"""
    response = await client.get("/recommendations?post_type=Image")
    assert response.status_code == 200
    
    data = response.json()
    # 由于测试数据中没有TEXT类型的帖子，items可能为空
    assert "items" in data


@pytest.mark.asyncio
async def test_get_recommendations_authenticated(client: AsyncClient, test_user: User, monkeypatch):
    """测试已认证用户获取推荐"""
    # 模拟认证用户
    async def mock_optional_user():
        return test_user
    
    monkeypatch.setattr("src.feed.main.optional_user", mock_optional_user)
    
    # 模拟推荐服务
    class MockRecommenderService:
        async def get_recommendations(self, region, user_id, params, post_type, languages):
            # 返回测试数据
            return {"items": [], "total": 0, "page": 1, "size": 10}
    
    monkeypatch.setattr("src.feed.main.RecommenderServiceGetter", lambda: lambda: MockRecommenderService())
    
    response = await client.get("/recommendations")
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_get_similar_posts(client: AsyncClient, test_post: Post, monkeypatch):
    """测试获取相似帖子"""
    post_id = test_post.id
    
    # 模拟推荐客户端类
    class MockRecommenderClient:
        async def get_similar_posts(self, post_id, params, post_type, region, languages):
            return []
    
    # 模拟推荐服务
    class MockRecommenderService:
        def __init__(self, client=None, session=None):
            self.caching_client = client
            self.session = session
            self.recommender_client = MockRecommenderClient()
            
        async def get_similar(self, post_id, params, region, post_type, user_id, languages):
            # 返回测试数据
            return {"items": [], "total": 0, "page": 1, "size": 10}
        
        async def get_similar_post_ids(self, post_id, params, region, post_type, languages):
            return []
    
    # 多重模拟，确保所有可能的调用路径
    monkeypatch.setattr("src.feed.main.recommender_client", MockRecommenderClient())
    monkeypatch.setattr("src.feed.service.RecommenderClient", lambda recommender_token: MockRecommenderClient())
    monkeypatch.setattr("src.feed.main.RecommenderServiceGetter", lambda: lambda *args, **kwargs: MockRecommenderService())
    
    response = await client.get(f"/similar/{post_id}")
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_get_similar_posts_invalid_id(client: AsyncClient):
    """测试使用无效ID获取相似帖子"""
    invalid_id = str(uuid.uuid4())
    response = await client.get(f"/similar/{invalid_id}")
    assert response.status_code == 404

@pytest.mark.asyncio
async def test_update_cache_all(client: AsyncClient):
    response = await client.post("/cache/update/all")
    assert response.status_code == 201


@pytest.mark.asyncio
async def test_get_feed(memeclient: MemeFansClient):
    response = await memeclient.get("/feed")
    assert response.status_code == 200
    
    data = response.json()
    assert "items" in data
    assert len(data["items"]) > 0
    
    # 验证返回的帖子包含feed_reason字段
    for item in data["items"]:
        assert "id" in item
        assert "type" in item
        assert "status" in item
    assert response.status_code == 200
