from dotenv import load_dotenv

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import pytest_asyncio
from httpx import AsyncClient
from src.auth.main import app
from tests.client import MemeFansClient


@pytest_asyncio.fixture
async def ac():
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def client(ac: AsyncClient):
    author = MemeFansClient(ac, token="8mptJzMSDbWg9RaK05llWjBB9HOaRsB9yOkWkywe7pk", username="liuygo", email="<EMAIL>", id="BgJos9JePpZ")
    # await author.register()
    # await author.login()
    return author

@pytest.mark.asyncio
async def test_register(client: MemeFansClient):
    response = await client.post("/register", json={
        "name": "Test",
        "username": "test",
        "phone_number": "15106135528",
        "region": "US"
    })
    print(response.json())
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_register_device(client: MemeFansClient):
    response = await client.post("/devices", json={
        "player_id": "BeKmg6vZ04D",
        "device_id": "test",
        "device_info": "test1"
    })
    print(response.json())
    assert response.status_code == 201

@pytest.mark.asyncio
async def test_unregister_device(client: MemeFansClient):
    response = await client.delete("/devices/test")
    print(response.json())
    assert response.status_code == 200