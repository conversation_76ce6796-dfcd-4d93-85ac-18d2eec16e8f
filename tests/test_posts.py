import os
from dotenv import load_dotenv

# 切换到 authors 目录
# os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "posts"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import pytest_asyncio
from httpx import AsyncClient
from src.posts.main import app
from tests.client import MemeFansClient

@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def client(ac: AsyncClient):
    author = MemeFansClient(ac, token="_5T1clAkV-XuJ10Ku0U3XHs9SXhvstg1xI6Ev5NOmHs", username="liuygo", email="<EMAIL>", id="BgJos9JePpZ")
    # await author.register()
    # await author.login()
    return author

@pytest.mark.asyncio
async def test_health(client: MemeFansClient):
    response = await client.get(f"/health")
    print(response.json())
    assert response.status_code == 200
    assert response.json() is not None
    assert response.json()["status"] == "ok"

@pytest.mark.asyncio
async def test_add_impression(
    client: MemeFansClient,
):
    """Test adding an impression for a post"""
    response = await client.post(
        "/impression",
        json={"post_id": "Bg1W4G3ECL3"}
    )
    assert response.status_code == 201

@pytest.mark.asyncio
async def test_add_share(
    client: MemeFansClient,
):
    """Test adding a share for a post"""
    response = await client.post(
        "/share",
        json={"post_id": "Bg1W4G3ECL3"}
    )
    assert response.status_code == 201

@pytest.mark.asyncio
async def test_add_click(
    client: MemeFansClient,
):
    """Test adding a click for a post"""
    response = await client.post(
        "/click",
        json={
            "post_id": "Bg1W4G3ECL3",
            "source": "Feed"
        }   
    )
    assert response.status_code == 201

@pytest.mark.asyncio
async def test_add_view(
    client: MemeFansClient,
):
    """Test adding a view for a post"""
    response = await client.post(
        "/view",
        json={"post_id": "Bg1W4G3ECL3"}
    )
    assert response.status_code == 201

@pytest.mark.asyncio
async def test_get_posts():
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get(f"/", params={"author_id": "BgLkByLAUul"})
        assert response.status_code == 200
        data = response.json()
        assert data is not None
        
        # Check pagination fields
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        
        # Check if items is a list and not empty
        assert isinstance(data["items"], list)
        assert len(data["items"]) > 0
        
        # Check required fields in first item
        first_item = data["items"][0]
        required_fields = [
            "id", "type", "status", "created_at", "updated_at",
            "comments_count", "likes_count", "collections_count",
            "view_count", "region", "language", "author_id",
            "tags", "title", "text", "description", "cover",
            "url", "author", "is_liked", "is_in_collection"
        ]
        
        for field in required_fields:
            assert field in first_item, f"Missing required field: {field}"
        
        # Check author fields
        author = first_item["author"]
        author_fields = [
            "id", "name", "username", "avatar", "region",
            "created_at", "role", "status"
        ]
        
        for field in author_fields:
            assert field in author, f"Missing required author field: {field}"
