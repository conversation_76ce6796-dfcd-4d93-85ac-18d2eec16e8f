import json
import os
from dotenv import load_dotenv

# 切换到 authors 目录
# os.chdir(os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "posts"))

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import pytest_asyncio
from httpx import AsyncClient
from src.posts.main import app
from tests.client import MemeFansClient
from fastapi import status

@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def moderator_client(ac: AsyncClient):
    """版主账号"""
    moderator = MemeFansClient(
        ac,
        token="t5ZOB8GiJcwn2CkT1-4QMBh4q3JVIKWsxaAqYSKKoxY",
        username="lazyfooder",
        email="<EMAIL>",
        id="BgLkByLAUul"
    )
    return moderator

@pytest_asyncio.fixture
async def client(ac: AsyncClient):
    """默认测试账号（保持向后兼容）"""
    author = MemeFansClient(
        ac,
        token="8mptJzMSDbWg9RaK05llWjBB9HOaRsB9yOkWkywe7pk",
        username="liuygo",
        email="<EMAIL>",
        id="BgJos9JePpZ"
    )
    return author

@pytest.mark.asyncio
async def test_get_collections_commits(moderator_client: MemeFansClient):
    response = await moderator_client.get(f"/collections/commits")
    print(response.json())
    assert response.status_code == 200
    
    data = response.json()
    assert data is not None
    
    # 分页信息检查
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    
    # 类型检查
    assert isinstance(data["items"], list)
    assert isinstance(data["total"], int)
    assert isinstance(data["page"], int)
    assert isinstance(data["size"], int)
    assert isinstance(data["pages"], int)
    
    # 检查 items 数组中的每个 commit
    if len(data["items"]) > 0:
        commit = data["items"][0]
        # commit 基本字段检查
        assert "id" in commit
        assert "created_at" in commit
        assert "updated_at" in commit
        assert "collection_id" in commit
        assert "post_id" in commit
        assert "status" in commit
        assert "reason" in commit
        assert "author_id" in commit
        assert "resolved_by" in commit
        assert "resolved_at" in commit
        
        # post 对象检查
        assert "post" in commit
        post = commit["post"]
        assert "id" in post
        assert "type" in post
        assert "status" in post
        assert "created_at" in post
        assert "updated_at" in post
        assert "comments_count" in post
        assert "likes_count" in post
        assert "collections_count" in post
        assert "view_count" in post
        assert "region" in post
        assert "language" in post
        assert "author_id" in post
        assert "tags" in post
        
        # author 对象检查
        assert "author" in commit
        author = commit["author"]
        assert "id" in author
        assert "name" in author
        assert "username" in author
        assert "avatar" in author
        assert "region" in author
        assert "created_at" in author
        assert "role" in author
        assert "status" in author
        
        # 类型检查
        assert isinstance(post["comments_count"], int)
        assert isinstance(post["likes_count"], int)
        assert isinstance(post["collections_count"], int)
        assert isinstance(post["view_count"], int)
        assert isinstance(post["tags"], list)
        
        # 状态值检查
        assert commit["status"] in ["new", "accepted", "rejected"]
        assert post["status"] in ["posted", "draft", "deleted"]
        assert author["status"] in ["active", "inactive", "blocked"]

@pytest.mark.asyncio
async def test_get_collections_contents(client: MemeFansClient):
    response = await client.get(f"/collections/contents/fetch",params={ "collection_id": "Bgw-qdXWz2X","type": "All", "sort": "Popular"})
    print(response.json())
    assert response.status_code == 200
    
    data = response.json()
    assert data is not None
    
    # 基本字段检查
    assert "title" in data
    assert "content_type" in data
    assert "description" in data
    assert "cover" in data
    assert "original_cover" in data
    assert "tags" in data
    assert "type" in data
    assert "author_id" in data
    assert "status" in data
    assert "id" in data
    assert "created_at" in data
    assert "updated_at" in data
    assert "contents_count" in data
    assert "posts_count" in data
    assert "contents" in data
    assert "carnival_status" in data
    assert "carnival_start_time" in data
    assert "region" in data
    assert "likes_count" in data
    assert "likes" in data
    assert "collections_count" in data
    assert "is_in_collection" in data
    assert "feed_reason" in data
    assert "previews" in data
    assert "interaction_rating" in data
    assert "posts" in data
    
    # author 对象字段检查
    assert "author" in data
    author = data["author"]
    assert "type" in author
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "email" in author
    assert "avatar" in author
    assert "original_avatar" in author
    assert "region" in author
    assert "user_id" in author
    assert "invitation_id" in author
    assert "created_at" in author
    assert "updated_at" in author
    assert "dedication" in author
    assert "description" in author
    assert "location" in author
    assert "country" in author
    assert "language" in author
    assert "education" in author
    assert "birthday" in author
    assert "phone_number" in author
    assert "likes_count" in author
    assert "citations_count" in author
    assert "posts_count" in author
    assert "gender" in author
    assert "role" in author
    
    # contributors 数组检查
    assert "contributors" in data
    assert isinstance(data["contributors"], list)
    if len(data["contributors"]) > 0:
        contributor = data["contributors"][0]
        assert "type" in contributor
        assert "id" in contributor
        assert "name" in contributor
        assert "username" in contributor
        assert "email" in contributor
        assert "avatar" in contributor
        assert "region" in contributor
        assert "user_id" in contributor
        assert "created_at" in contributor
        assert "updated_at" in contributor
    
    # subscribers 数组检查
    assert "subscribers" in data
    assert isinstance(data["subscribers"], list)
    if len(data["subscribers"]) > 0:
        subscriber = data["subscribers"][0]
        assert "type" in subscriber
        assert "id" in subscriber
        assert "name" in subscriber
        assert "username" in subscriber
        assert "email" in subscriber
        assert "avatar" in subscriber
        assert "region" in subscriber
        assert "user_id" in subscriber
        assert "created_at" in subscriber
        assert "updated_at" in subscriber
    
    # posts 数组检查
    assert isinstance(data["posts"], list)
    if len(data["posts"]) > 0:
        post = data["posts"][0]
        assert "type" in post
        assert "title" in post
        assert "cover" in post
        assert "description" in post
        assert "status" in post
        assert "id" in post
        assert "created_at" in post
        assert "updated_at" in post
        assert "comments_count" in post
        assert "likes_count" in post
        assert "collections_count" in post
        assert "region" in post
        assert "interaction_rating" in post
        assert "is_in_collection" in post
        assert "is_liked" in post
        assert "feed_reason" in post
        assert "subscribed_authors_likes" in post
        assert "collections" in post
        assert "subscribed_collections" in post
        
        # post author 检查
        assert "author" in post
        post_author = post["author"]
        assert "id" in post_author
        assert "name" in post_author
        assert "username" in post_author
        assert "avatar" in post_author
        assert "region" in post_author
        assert "created_at" in post_author
        assert "role" in post_author
        assert "status" in post_author
        
        # post comments 检查
        assert "comments" in post
        assert isinstance(post["comments"], list)
        if len(post["comments"]) > 0:
            comment = post["comments"][0]
            assert "type" in comment
            assert "text" in comment
            assert "parent_id" in comment
            assert "status" in comment
            assert "author_id" in comment
            assert "likes" in comment
            assert "is_liked" in comment
            assert "comments_count" in comment
            assert "comments" in comment
        
        # post likes 检查
        assert "likes" in post
        assert isinstance(post["likes"], list)
        if len(post["likes"]) > 0:
            like = post["likes"][0]
            assert "id" in like
            assert "name" in like
            assert "username" in like
            assert "avatar" in like
            assert "region" in like
            assert "created_at" in like
            assert "role" in like
            assert "status" in like

@pytest.mark.asyncio
async def test_get_collections_posts(moderator_client: MemeFansClient):
    post_id = "Bgw-qdXWz2X"
    response = await moderator_client.get(f"/collections/{post_id}")
    print(response.json())
    assert response.status_code == 200
    
    data = response.json()
    assert data is not None
    
    # 基本字段检查
    assert "id" in data
    assert "type" in data
    assert "status" in data
    assert "created_at" in data
    assert "updated_at" in data
    assert "region" in data
    assert "author_id" in data
    assert "title" in data
    assert "description" in data
    assert "cover" in data
    assert "original_cover" in data
    assert "subscriber_count" in data
    assert "contributor_count" in data
    assert "content_type" in data
    assert "contents_count" in data
    assert "contents" in data
    assert "carnival_status" in data
    assert "carnival_start_time" in data
    assert "is_subscribed" in data
    assert "is_contributor" in data
    assert "can_commit" in data
    
    # author 对象字段检查
    assert "author" in data
    author = data["author"]
    assert "id" in author
    assert "name" in author
    assert "username" in author
    assert "avatar" in author
    assert "region" in author
    assert "created_at" in author
    assert "role" in author
    assert "status" in author
    
    # 类型检查
    assert isinstance(data["contents"], list)
    assert isinstance(data["subscriber_count"], int)
    assert isinstance(data["contributor_count"], int)
    assert isinstance(data["contents_count"], int)
    assert isinstance(data["is_subscribed"], bool)
    assert isinstance(data["is_contributor"], bool)
    assert isinstance(data["can_commit"], bool)

@pytest.mark.asyncio
async def test_add_and_remove_post_to_collection(moderator_client: MemeFansClient):

    collection_id = "Bg0ixXaUhEU"
    post_id = "Bg33sjR8DRj"

    add_response = await moderator_client.post(
        f"/collections/author/add?collection_id={collection_id}&post_id={post_id}"
    )
    print(add_response.json())
    assert add_response.status_code == 200

    remove_response = await moderator_client.post(
        f"/collections/{collection_id}/remove/{post_id}"
    )
    print(remove_response.json())
    assert remove_response.status_code == 200

@pytest.mark.asyncio
async def test_subscribe_and_unsubscribe_collection(client: MemeFansClient): 
    collection_id = "Bg0ixXaUhEU"

    subscribe_response = await client.patch(f"/collections/subscribe?collection_id={collection_id}")
    print(subscribe_response.json())
    assert subscribe_response.status_code == 200

    unsubscribe_response = await client.patch(f"/collections/subscribe?collection_id={collection_id}")
    print(unsubscribe_response.json())
    assert unsubscribe_response.status_code == 200

@pytest.mark.asyncio
async def test_get_contents_by_collection_id(moderator_client: MemeFansClient):
    collection_id = "Bg0ixXaUhEU"
    response = await moderator_client.get(f"/collections/{collection_id}/contents")
    data = response.json()
    assert response.status_code == 200
    
    # Check pagination fields
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    assert len(data["items"]) > 0
    
    # Check items array if not empty
    if data["items"]:
        post = data["items"][0]
        # Check basic post fields
        assert "id" in post
        assert "type" in post
        assert "status" in post
        assert "created_at" in post
        assert "updated_at" in post
        assert "comments_count" in post
        assert "likes_count" in post
        assert "collections_count" in post
        assert "view_count" in post
        assert "region" in post
        assert "language" in post
        assert "author_id" in post
        assert "tags" in post
        assert "title" in post
        assert "cover" in post
        assert "description" in post
        assert "width" in post
        assert "height" in post
        assert "is_liked" in post
        assert "is_in_collection" in post
        
        # Check author object fields
        assert "author" in post
        author = post["author"]
        assert "id" in author
        assert "name" in author
        assert "username" in author
        assert "avatar" in author
        assert "region" in author
        assert "created_at" in author
        assert "role" in author
        assert "status" in author
        
        # Check video specific fields if type is Video
        if post["type"] == "Video":
            assert "url" in post
            assert "url_type" in post
            assert "processing_status" in post
        
        # Type checks
        assert isinstance(post["comments_count"], int)
        assert isinstance(post["likes_count"], int)
        assert isinstance(post["collections_count"], int)
        assert isinstance(post["view_count"], int)
        assert isinstance(post["tags"], list)
        assert isinstance(post["is_liked"], bool)
        assert isinstance(post["is_in_collection"], bool)
        
        # Status value checks
        assert post["status"] in ["posted", "draft", "deleted"]
        assert author["status"] in ["active", "inactive", "blocked"]

@pytest.mark.asyncio
async def test_get_commit_by_collection_id(moderator_client: MemeFansClient):
    collection_id = "Bg0ixXaUhEU"
    response = await moderator_client.get(f"/collections/{collection_id}/commits")
    data = response.json()
    assert response.status_code == 200
    
    # Check pagination fields
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert "pages" in data
    assert "unprocessed_count" in data
    assert len(data["items"]) > 0
    
    # Check commit fields if there are any items
    if data["items"]:
        commit = data["items"][0]
        # Check basic commit fields
        assert "id" in commit
        assert "created_at" in commit
        assert "updated_at" in commit
        assert "collection_id" in commit
        assert "post_id" in commit
        assert "status" in commit
        assert "reason" in commit
        assert "author_id" in commit
        assert "resolved_by" in commit
        assert "resolved_at" in commit
        
        # Check post object fields
        assert "post" in commit
        post = commit["post"]
        assert "id" in post
        assert "type" in post
        assert "status" in post
        assert "created_at" in post
        assert "updated_at" in post
        assert "comments_count" in post
        assert "likes_count" in post
        assert "collections_count" in post
        assert "view_count" in post
        assert "region" in post
        assert "language" in post
        assert "author_id" in post
        assert "tags" in post
        
        # Check author object fields
        assert "author" in commit
        author = commit["author"]
        assert "id" in author
        assert "name" in author
        assert "username" in author
        assert "avatar" in author
        assert "region" in author
        assert "created_at" in author
        assert "role" in author
        assert "status" in author
    

@pytest.mark.asyncio
async def test_get_new_commits_count(moderator_client: MemeFansClient):
    # Test with no collections
    response = await moderator_client.get(
        "/collections/commits/new_count"
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json() > 0

@pytest.mark.asyncio
async def test_create_collection(moderator_client: MemeFansClient):
    response = await moderator_client.post(
        "/collections",
        json={
            "title": "Test Collection",
            "content_type": "Mixed",
            "description": "Test Collection Description",
            "cover": "Test Collection Cover",
            "original_cover": "Test Collection Original Cover",
            "tags": ["Test Collection Tag 1", "Test Collection Tag 2"]
        }
    )
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json()["id"] is not None

@pytest.mark.asyncio
async def test_delete_collection(moderator_client: MemeFansClient):
    response = await moderator_client.delete(
        "/collections?collection_id=BhXouTmxEhg"
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"message": "Collection deleted successfully"}
