#!/usr/bin/env python3
"""
SMS Client Test

Test suite for the SMS client functionality including:
1. SMS client initialization
2. Sending verification codes
3. Verifying codes
4. Fallback mechanism from NxCloud to Twilio

Usage:
    python -m pytest tests/test_sms_client.py -v
    or
    python tests/test_sms_client.py
"""

import asyncio
import sys
import os
from datetime import timedelta
import pytest

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.auth.sms_client import SMSClient, SMSProvider


class MockCachingClient:
    """Mock caching client for testing"""
    
    def __init__(self):
        self.cache = {}
    
    async def set(self, endpoint: str, key: str, value: str, expiration_time: timedelta = None):
        """Set a value in the cache"""
        cache_key = f"{endpoint}:{key}"
        self.cache[cache_key] = value
        print(f"📝 Cached: {cache_key} = {value}")
    
    async def get(self, endpoint: str, key: str):
        """Get a value from the cache"""
        cache_key = f"{endpoint}:{key}"
        value = self.cache.get(cache_key)
        print(f"🔍 Retrieved: {cache_key} = {value}")
        return value


class TestSMSClient:
    """Test class for SMS Client"""
    
    @pytest.fixture
    def mock_cache(self):
        """Fixture to provide mock caching client"""
        return MockCachingClient()
    
    @pytest.fixture
    def sms_client(self, mock_cache):
        """Fixture to provide SMS client with mock cache"""
        return SMSClient(caching_client=mock_cache)
    
    def test_sms_client_initialization(self, sms_client):
        """Test SMS client initialization"""
        assert sms_client is not None
        assert hasattr(sms_client, '_client')
        assert sms_client._client.provider == SMSProvider.NXCLOUD
        print("✅ SMS Client initialized successfully")
    
    def test_sms_client_configuration(self, sms_client):
        """Test SMS client configuration"""
        client = sms_client._client
        assert hasattr(client, 'appkey')
        assert hasattr(client, 'secretkey')
        assert hasattr(client, 'base_url')
        assert client.base_url == "http://api2.nxcloud.com"
        print("✅ SMS Client configuration verified")
    
    @pytest.mark.asyncio
    async def test_send_verification_code_method(self, sms_client):
        """Test send_verification_code method exists and can be called"""
        test_phone = "+**********"  # Dummy phone number
        
        # This will test the method but won't actually send SMS with dummy number
        try:
            result = await sms_client.send_verification_code(
                phone_number=test_phone,
                endpoint="test",
                expiration_time=timedelta(minutes=5)
            )
            # The result could be True or False depending on the actual API response
            assert isinstance(result, bool)
            print(f"✅ send_verification_code method executed, result: {result}")
        except Exception as e:
            # Expected to fail with dummy data, but method should exist
            print(f"⚠️ send_verification_code failed (expected with dummy data): {str(e)}")
            assert "send_verification_code" in str(type(sms_client._client).__dict__)
    
    @pytest.mark.asyncio
    async def test_verify_method(self, sms_client, mock_cache):
        """Test verify method"""
        test_phone = "+**********"
        test_code = "123456"
        
        # First cache a test code
        await mock_cache.set("test", test_phone, test_code, timedelta(minutes=5))
        
        # Then try to verify it
        is_valid = await sms_client.verify(
            phone_number=test_phone,
            code=test_code,
            endpoint="test"
        )
        
        # Should return True since we cached the exact code
        assert isinstance(is_valid, bool)
        print(f"✅ verify method executed, result: {is_valid}")
    
    def test_send_message_method(self, sms_client):
        """Test send_message method"""
        test_phone = "+**********"
        
        message_list = [
            {
                "phone": test_phone,
                "content": "[Test] This is a test message"
            }
        ]
        
        try:
            result = sms_client.send_message(message_list)
            assert isinstance(result, dict)
            print(f"✅ send_message method executed, result type: {type(result)}")
        except Exception as e:
            # Expected to fail with dummy data, but method should exist
            print(f"⚠️ send_message failed (expected with dummy data): {str(e)}")
            assert "send_message" in str(type(sms_client).__dict__)
    
    def test_has_required_methods(self, sms_client):
        """Test that SMS client has all required methods"""
        required_methods = [
            'send_verification_code',
            'verify',
            'send_message'
        ]
        
        for method_name in required_methods:
            assert hasattr(sms_client, method_name), f"Missing method: {method_name}"
            assert callable(getattr(sms_client, method_name)), f"Method not callable: {method_name}"
        
        print("✅ All required methods present and callable")
    
    @pytest.mark.asyncio
    async def test_twilio_fallback_scenario(self, sms_client, mock_cache):
        """Test Twilio fallback scenario when NxCloud fails"""
        test_phone = "+**********"
        test_code = "123456"
        endpoint = "test_fallback"
        
        print("\n🔄 Testing Twilio fallback scenario...")
        
        # Step 1: Simulate NxCloud failure by mocking a failed response
        # We can't easily mock the actual send_message call in this test setup,
        # but we can test the verification path when twilio_fallback is set
        
        # Step 2: Manually set the twilio_fallback flag as if NxCloud failed
        await mock_cache.set("twilio_fallback", test_phone, "true", timedelta(minutes=5))
        print("📝 Set twilio_fallback flag for phone number")
        
        # Step 3: Test that verify method detects the fallback flag
        # Note: This will try to use Twilio verification, which may fail in test environment
        # but we can at least verify the logic path is correct
        try:
            is_valid = await sms_client.verify(
                phone_number=test_phone,
                code=test_code,
                endpoint=endpoint
            )
            print(f"✅ Twilio fallback verification attempted, result: {is_valid}")
            assert isinstance(is_valid, bool)
        except Exception as e:
            # Expected to fail in test environment without real Twilio credentials
            print(f"⚠️ Twilio fallback failed (expected in test environment): {str(e)}")
            # Verify that the error is related to Twilio, not our logic
            assert any(keyword in str(e).lower() for keyword in ['twilio', 'auth', 'credential', 'token'])
        
        # Step 4: Test that the fallback flag is properly read
        fallback_flag = await mock_cache.get("twilio_fallback", test_phone)
        assert fallback_flag == "true", "Twilio fallback flag should be set"
        print("✅ Twilio fallback flag correctly set and retrieved")
        
        # Step 5: Test normal verification path (without fallback flag)
        test_phone_normal = "+1987654321"
        await mock_cache.set(endpoint, test_phone_normal, test_code, timedelta(minutes=5))
        
        is_valid_normal = await sms_client.verify(
            phone_number=test_phone_normal,
            code=test_code,
            endpoint=endpoint
        )
        assert is_valid_normal == True, "Normal verification should succeed with correct cached code"
        print("✅ Normal verification path works correctly")
        
        print("🎉 Twilio fallback scenario test completed!")
    
    @pytest.mark.asyncio
    async def test_nxcloud_response_formats(self, mock_cache):
        """Test different NxCloud response formats"""
        print("\n📋 Testing NxCloud response format handling...")
        
        sms_client = SMSClient(caching_client=mock_cache)
        
        # Test successful NxCloud response format
        nxcloud_success_response = {
            "result": "请求成功",
            "code": "0",  # String format as returned by NxCloud API
            "messageid": "test123"
        }
        
        # Test the success condition logic
        success = (
            nxcloud_success_response.get('code') == "0" or
            nxcloud_success_response.get('code') == 0 or
            nxcloud_success_response.get('status_code') == 200 or
            nxcloud_success_response.get('success') == True
        )
        assert success == True, "NxCloud success response should be recognized"
        print("✅ NxCloud success response format correctly recognized")
        
        # Test failed NxCloud response format
        nxcloud_failure_response = {
            "result": "失败",
            "code": "1",  # Non-zero code indicates failure
            "error": "Invalid credentials"
        }
        
        failure = (
            nxcloud_failure_response.get('code') == "0" or
            nxcloud_failure_response.get('code') == 0 or
            nxcloud_failure_response.get('status_code') == 200 or
            nxcloud_failure_response.get('success') == True
        )
        assert failure == False, "NxCloud failure response should not be recognized as success"
        print("✅ NxCloud failure response format correctly recognized")
        
        # Test HTTP error response format
        http_error_response = {
            "error": "HTTP 500: Internal Server Error",
            "success": False,
            "status_code": 500
        }
        
        http_error = (
            http_error_response.get('code') == "0" or
            http_error_response.get('code') == 0 or
            http_error_response.get('status_code') == 200 or
            http_error_response.get('success') == True
        )
        assert http_error == False, "HTTP error response should not be recognized as success"
        print("✅ HTTP error response format correctly recognized")
        
        print("🎉 Response format tests completed!")


def check_environment():
    """Check if required environment variables are set"""
    print("🔍 Checking environment configuration...")
    
    try:
        from src.auth.settings import settings
        
        required_vars = [
            ('SMS_NX_APPKEY', 'NxCloud App Key'),
            ('SMS_NX_SECRET', 'NxCloud Secret Key'),
            ('TWILIO_ACCOUNT_SID', 'Twilio Account SID'),
            ('TWILIO_AUTH_TOKEN', 'Twilio Auth Token'),
            ('TWILIO_SERVICE_ID', 'Twilio Service ID'),
        ]
        
        print("\nEnvironment Variables Status:")
        for var_name, description in required_vars:
            value = getattr(settings, var_name, None)
            status = "✅ Set" if value else "❌ Not set"
            print(f"{description}: {status}")
        
        sms_option = getattr(settings, 'SMS_OPTION', 'Not set')
        print(f"SMS_OPTION: {sms_option}")
        
    except Exception as e:
        print(f"❌ Error checking environment: {str(e)}")


async def run_interactive_test():
    """Interactive test that prompts user for phone number and code"""
    print("🧪 SMS Client Interactive Test")
    print("=" * 50)
    
    # Get phone number from user
    phone_number = input("📱 Enter phone number (with country code, e.g., +**********): ").strip()
    
    if not phone_number:
        print("❌ Phone number is required")
        return
    
    # Initialize SMS client
    mock_cache = MockCachingClient()
    sms_client = SMSClient(caching_client=mock_cache)
    print(f"✅ SMS Client initialized with provider: {sms_client._client.provider.value}")
    
    # Test sending verification code
    print(f"\n📤 Sending verification code to {phone_number}...")
    try:
        success = await sms_client.send_verification_code(
            phone_number=phone_number,
            endpoint="interactive_test",
            expiration_time=timedelta(minutes=5)
        )
        
        if success:
            print("✅ Verification code sent successfully")
            
            # Prompt user for the received code
            received_code = input("🔢 Enter the verification code you received: ").strip()
            
            if received_code:
                # Test code verification
                print(f"\n🔐 Verifying code {received_code}...")
                is_valid = await sms_client.verify(
                    phone_number=phone_number,
                    code=received_code,
                    endpoint="interactive_test"
                )
                
                if is_valid:
                    print("✅ Code verification successful!")
                else:
                    print("❌ Code verification failed")
            else:
                print("❌ No code entered")
        else:
            print("❌ Failed to send verification code")
            
    except Exception as e:
        print(f"❌ Error during interactive test: {str(e)}")


if __name__ == "__main__":
    print("📱 SMS Client Test Suite")
    print("=" * 50)
    
    # Check environment
    check_environment()
    
    # Ask user what type of test to run
    print("\nSelect test type:")
    print("1. Unit tests (pytest)")
    print("2. Interactive test (requires real phone number)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        # Run pytest
        print("\n🚀 Running unit tests...")
        pytest.main([__file__, "-v"])
    elif choice == "2":
        # Run interactive test
        asyncio.run(run_interactive_test())
    else:
        print("❌ Invalid choice")