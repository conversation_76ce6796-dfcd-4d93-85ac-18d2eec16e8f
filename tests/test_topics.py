from dotenv import load_dotenv

# 加载环境变量必须在导入任何应用代码之前
load_dotenv(".env")

import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi import status
from src.posts.main import app

@pytest_asyncio.fixture
async def ac():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.mark.asyncio
async def test_get_topics(ac: AsyncClient):
    """Test the /health endpoint returns expected response."""
    response = await ac.get("/topics")
    assert response.status_code == status.HTTP_200_OK
    assert len(response.json()['items']) == 50

    response = await ac.get("/topics?size=1")
    assert response.status_code == status.HTTP_200_OK
    assert len(response.json()['items']) == 1

    response = await ac.get("/topics?size=50&page=1000")
    assert response.status_code == status.HTTP_200_OK
    assert len(response.json()['items']) == 0

@pytest.mark.asyncio
async def test_get_topics_by_title(ac: AsyncClient):
    response = await ac.get("/topics?q=vova")
    assert response.status_code == status.HTTP_200_OK
    assert len(response.json()['items']) > 0

@pytest.mark.asyncio
async def test_create_topic(ac: AsyncClient):
    response = await ac.post("/topics", json={"title": "viva", "description": "viva", "cover": "viva"})
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json()["title"] == "viva"

