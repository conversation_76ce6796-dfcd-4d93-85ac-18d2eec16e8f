#!/usr/bin/env python3
"""
<PERSON>ript to analyze SavedPosts consistency issues and generate repair SQL
This script should be run before applying the consistency migration to understand
the current state of data inconsistencies.

Usage:
    python scripts/analyze_saved_posts_consistency.py [--output-file report.json] [--generate-sql repair.sql]
"""

import asyncio
import json
import argparse
from datetime import datetime
from typing import Dict, Any, List, Tuple
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.common.consistency.saved_posts_validator import SavedPostsConsistencyService
import logging

# Configure logging
logger = logging.getLogger(__name__)


class ConsistencyAnalyzer:
    """Analyzer for SavedPosts consistency issues"""
    
    def __init__(self, database_url: str):
        self.engine = create_async_engine(database_url)
        self.SessionLocal = sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    
    async def analyze(self) -> Dict[str, Any]:
        """Perform comprehensive consistency analysis"""
        logger.info("Starting SavedPosts consistency analysis...")
        
        async with self.SessionLocal() as session:
            service = SavedPostsConsistencyService(session)
            
            # Get full consistency report
            report = await service.check_consistency()
            
            # Get additional statistics
            stats = await self._get_detailed_statistics(session)
            
            analysis_result = {
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "consistency_report": report.get_summary(),
                "detailed_issues": {
                    "missing_saved_posts": [
                        {
                            "collection_id": issue.collection_id,
                            "post_id": issue.post_id,
                            "user_id": issue.user_id
                        }
                        for issue in report.missing_saved_posts
                    ],
                    "orphaned_saved_posts": [
                        {
                            "collection_id": issue.collection_id,
                            "post_id": issue.post_id,
                            "user_id": issue.user_id
                        }
                        for issue in report.orphaned_saved_posts
                    ],
                    "incorrect_counts": [
                        {
                            "collection_id": issue.collection_id,
                            "expected_count": issue.expected_count,
                            "actual_count": issue.actual_count
                        }
                        for issue in report.incorrect_counts
                    ]
                },
                "database_statistics": stats,
                "repair_recommendations": self._generate_repair_recommendations(report, stats)
            }
            
            logger.info(f"Analysis completed. Found {report.total_issues} issues.")
            return analysis_result
    
    async def _get_detailed_statistics(self, session: AsyncSession) -> Dict[str, Any]:
        """Get detailed database statistics"""
        stats = {}
        
        # Total counts
        total_collections_query = text("SELECT COUNT(*) FROM collections")
        total_saved_posts_query = text("SELECT COUNT(*) FROM saved_posts")
        
        result = await session.execute(total_collections_query)
        stats["total_collections"] = result.scalar()
        
        result = await session.execute(total_saved_posts_query)
        stats["total_saved_posts"] = result.scalar()
        
        # Collections with content
        collections_with_content_query = text("""
            SELECT COUNT(*) FROM collections 
            WHERE array_length(contents, 1) > 0
        """)
        result = await session.execute(collections_with_content_query)
        stats["collections_with_content"] = result.scalar()
        
        # Average contents per collection
        avg_contents_query = text("""
            SELECT AVG(COALESCE(array_length(contents, 1), 0)) as avg_contents
            FROM collections
        """)
        result = await session.execute(avg_contents_query)
        stats["avg_contents_per_collection"] = float(result.scalar() or 0)
        
        # Collections by contents_count accuracy
        count_accuracy_query = text("""
            SELECT 
                SUM(CASE WHEN contents_count = COALESCE(array_length(contents, 1), 0) THEN 1 ELSE 0 END) as accurate,
                SUM(CASE WHEN contents_count != COALESCE(array_length(contents, 1), 0) THEN 1 ELSE 0 END) as inaccurate
            FROM collections
        """)
        result = await session.execute(count_accuracy_query)
        row = result.first()
        stats["contents_count_accuracy"] = {
            "accurate": row.accurate,
            "inaccurate": row.inaccurate
        }
        
        # Largest collections (potential performance impact)
        largest_collections_query = text("""
            SELECT id, contents_count, array_length(contents, 1) as actual_count
            FROM collections
            ORDER BY COALESCE(array_length(contents, 1), 0) DESC
            LIMIT 10
        """)
        result = await session.execute(largest_collections_query)
        stats["largest_collections"] = [
            {
                "id": row.id,
                "contents_count": row.contents_count,
                "actual_count": row.actual_count
            }
            for row in result
        ]
        
        # Users with most saved posts
        top_users_query = text("""
            SELECT user_id, COUNT(*) as saved_posts_count
            FROM saved_posts
            GROUP BY user_id
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        result = await session.execute(top_users_query)
        stats["top_users_by_saved_posts"] = [
            {
                "user_id": row.user_id,
                "saved_posts_count": row.saved_posts_count
            }
            for row in result
        ]
        
        return stats
    
    def _generate_repair_recommendations(self, report, stats: Dict[str, Any]) -> Dict[str, Any]:
        """Generate repair recommendations based on analysis"""
        recommendations = {
            "priority": "medium",
            "estimated_repair_time": "5-15 minutes",
            "backup_recommended": True,
            "maintenance_window_needed": False,
            "steps": []
        }
        
        total_issues = report.total_issues
        
        if total_issues == 0:
            recommendations["priority"] = "none"
            recommendations["steps"] = ["No issues found - no repair needed"]
            return recommendations
        
        if total_issues > 1000:
            recommendations["priority"] = "high"
            recommendations["estimated_repair_time"] = "30-60 minutes"
            recommendations["maintenance_window_needed"] = True
        elif total_issues > 100:
            recommendations["priority"] = "medium"
            recommendations["estimated_repair_time"] = "15-30 minutes"
        else:
            recommendations["priority"] = "low"
            recommendations["estimated_repair_time"] = "5-10 minutes"
        
        # Generate specific steps
        steps = []
        
        if report.missing_saved_posts:
            steps.append(f"Create {len(report.missing_saved_posts)} missing SavedPost records")
        
        if report.orphaned_saved_posts:
            steps.append(f"Remove {len(report.orphaned_saved_posts)} orphaned SavedPost records")
        
        if report.incorrect_counts:
            steps.append(f"Fix contents_count for {len(report.incorrect_counts)} collections")
        
        steps.extend([
            "Apply database triggers to prevent future inconsistencies",
            "Verify consistency after repair",
            "Monitor for new issues"
        ])
        
        recommendations["steps"] = steps
        
        return recommendations
    
    def generate_repair_sql(self, analysis_result: Dict[str, Any]) -> str:
        """Generate SQL script to repair inconsistencies"""
        sql_parts = [
            "-- SavedPosts Consistency Repair Script",
            f"-- Generated on: {datetime.utcnow().isoformat()}",
            f"-- Total issues to repair: {analysis_result['consistency_report']['total_issues']}",
            "",
            "BEGIN;",
            "",
            "-- Create backup tables (recommended)",
            "CREATE TABLE saved_posts_backup_" + datetime.utcnow().strftime("%Y%m%d_%H%M%S") + " AS SELECT * FROM saved_posts;",
            "CREATE TABLE collections_backup_" + datetime.utcnow().strftime("%Y%m%d_%H%M%S") + "_counts AS SELECT id, contents_count FROM collections;",
            "",
        ]
        
        # Repair missing SavedPost records
        missing_posts = analysis_result['detailed_issues']['missing_saved_posts']
        if missing_posts:
            sql_parts.extend([
                f"-- Fix {len(missing_posts)} missing SavedPost records",
                "INSERT INTO saved_posts (user_id, post_id, collection_id, created_at)",
                "VALUES"
            ])
            
            value_parts = []
            for issue in missing_posts:
                value_parts.append(f"    ('{issue['user_id']}', '{issue['post_id']}', '{issue['collection_id']}', NOW())")
            
            sql_parts.append(",\n".join(value_parts))
            sql_parts.extend([
                "ON CONFLICT (user_id, post_id, collection_id) DO NOTHING;",
                ""
            ])
        
        # Remove orphaned SavedPost records
        orphaned_posts = analysis_result['detailed_issues']['orphaned_saved_posts']
        if orphaned_posts:
            sql_parts.extend([
                f"-- Remove {len(orphaned_posts)} orphaned SavedPost records"
            ])
            
            for issue in orphaned_posts:
                sql_parts.append(
                    f"DELETE FROM saved_posts WHERE user_id = '{issue['user_id']}' "
                    f"AND post_id = '{issue['post_id']}' AND collection_id = '{issue['collection_id']}';"
                )
            
            sql_parts.append("")
        
        # Fix incorrect counts
        incorrect_counts = analysis_result['detailed_issues']['incorrect_counts']
        if incorrect_counts:
            sql_parts.extend([
                f"-- Fix {len(incorrect_counts)} incorrect contents_count values"
            ])
            
            for issue in incorrect_counts:
                sql_parts.append(
                    f"UPDATE collections SET contents_count = {issue['expected_count']} "
                    f"WHERE id = '{issue['collection_id']}';"
                )
            
            sql_parts.append("")
        
        sql_parts.extend([
            "-- Verify repairs",
            "SELECT 'Verification Results:' as status;",
            "",
            "-- Check for remaining missing SavedPost records",
            """SELECT 'Missing SavedPosts' as issue_type, COUNT(*) as count
FROM (
    SELECT c.id as collection_id, c.author_id, unnest(c.contents) as post_id
    FROM collections c
    WHERE EXISTS (
        SELECT 1 FROM unnest(c.contents) as post_id
        WHERE NOT EXISTS (
            SELECT 1 FROM saved_posts sp
            WHERE sp.collection_id = c.id AND sp.post_id = post_id::text
        )
    )
) missing;""",
            "",
            "-- Check for remaining orphaned SavedPost records",
            """SELECT 'Orphaned SavedPosts' as issue_type, COUNT(*) as count
FROM saved_posts sp
JOIN collections c ON sp.collection_id = c.id
WHERE NOT (sp.post_id = ANY(c.contents));""",
            "",
            "-- Check for remaining incorrect counts",
            """SELECT 'Incorrect Counts' as issue_type, COUNT(*) as count
FROM collections
WHERE contents_count != COALESCE(array_length(contents, 1), 0);""",
            "",
            "COMMIT;",
            "",
            "-- If verification shows 0 issues, the repair was successful!",
            "-- If issues remain, investigate and consider manual intervention."
        ])
        
        return "\n".join(sql_parts)
    
    async def close(self):
        """Close database connections"""
        await self.engine.dispose()


async def main():
    parser = argparse.ArgumentParser(description="Analyze SavedPosts consistency")
    parser.add_argument("--database-url", help="Database URL (defaults to DATABASE_URL_ASYNC env var)")
    parser.add_argument("--output-file", help="Output file for analysis results (JSON)")
    parser.add_argument("--generate-sql", help="Generate repair SQL file")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Get database URL
    database_url = args.database_url or os.getenv("DATABASE_URL_ASYNC")
    if not database_url:
        print("Error: Database URL not provided. Use --database-url or set DATABASE_URL_ASYNC environment variable.")
        sys.exit(1)
    
    # Create analyzer
    analyzer = ConsistencyAnalyzer(database_url)
    
    try:
        # Run analysis
        analysis_result = await analyzer.analyze()
        
        # Output results
        if args.output_file:
            with open(args.output_file, 'w') as f:
                json.dump(analysis_result, f, indent=2)
            print(f"Analysis results written to {args.output_file}")
        
        # Generate SQL if requested
        if args.generate_sql:
            repair_sql = analyzer.generate_repair_sql(analysis_result)
            with open(args.generate_sql, 'w') as f:
                f.write(repair_sql)
            print(f"Repair SQL written to {args.generate_sql}")
        
        # Console output
        print("\n=== SavedPosts Consistency Analysis ===")
        print(f"Analysis timestamp: {analysis_result['analysis_timestamp']}")
        print(f"Total collections checked: {analysis_result['consistency_report']['total_collections_checked']}")
        print(f"Total saved_posts checked: {analysis_result['consistency_report']['total_saved_posts_checked']}")
        print(f"Total issues found: {analysis_result['consistency_report']['total_issues']}")
        
        if analysis_result['consistency_report']['total_issues'] > 0:
            print("\nIssues by type:")
            for issue_type, count in analysis_result['consistency_report']['issues_by_type'].items():
                if count > 0:
                    print(f"  {issue_type}: {count}")
            
            print(f"\nRepair priority: {analysis_result['repair_recommendations']['priority']}")
            print(f"Estimated repair time: {analysis_result['repair_recommendations']['estimated_repair_time']}")
            print(f"Maintenance window needed: {analysis_result['repair_recommendations']['maintenance_window_needed']}")
        else:
            print("\nNo consistency issues found! 🎉")
        
        if args.verbose:
            print(f"\nDatabase statistics:")
            stats = analysis_result['database_statistics']
            print(f"  Total collections: {stats['total_collections']}")
            print(f"  Collections with content: {stats['collections_with_content']}")
            print(f"  Average contents per collection: {stats['avg_contents_per_collection']:.2f}")
            print(f"  Contents count accuracy: {stats['contents_count_accuracy']['accurate']} accurate, {stats['contents_count_accuracy']['inaccurate']} inaccurate")
    
    finally:
        await analyzer.close()


if __name__ == "__main__":
    asyncio.run(main())