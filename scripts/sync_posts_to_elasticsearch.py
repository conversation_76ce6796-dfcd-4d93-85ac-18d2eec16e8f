#!/usr/bin/env python3
"""
PostgreSQL数据全量同步到Elasticsearch脚本
支持语言独立索引架构 - 通过ingest pipeline自动语言检测和路由
支持增量同步和全量重建
"""
import asyncio
import sys
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    load_dotenv(env_path)
    print(f"✅ 已加载环境变量文件: {env_path}")
except ImportError:
    print("⚠️  python-dotenv 未安装，请运行: pip install python-dotenv")
except Exception as e:
    print(f"⚠️  加载 .env 文件失败: {e}")

from sqlalchemy import select, and_, func
from sqlalchemy.orm import sessionmaker

from src.database.session import get_sync_context
from src.database.models import Video, Image, Author, Post
from src.database.models.Pair import Pair  # MemeCoin数据
from src.common.elasticsearch import (
    get_elasticsearch_client, 
    UnifiedLanguageIndexManager
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LanguageIndexSyncProcessor:
    """语言独立索引同步处理器"""
    
    def __init__(self):
        self.es_client = None
        self.unified_manager = None
        self.batch_size = 500  # 批量处理大小（降低以减少压力）
        self.concurrent_batches = 2  # 并发批次数量（降低避免卡死）
        
    async def init_es_client(self):
        """初始化ES客户端和管理器"""
        self.es_client = await get_elasticsearch_client()
        self.unified_manager = UnifiedLanguageIndexManager(self.es_client.client)
        
        if not await self.es_client.ping():
            raise Exception("无法连接到Elasticsearch")
    
    def _build_post_document_for_pipeline(self, post, author) -> Dict[str, Any]:
        """
        构建用于ingest pipeline处理的Posts文档
        Pipeline会自动检测语言并路由到对应索引
        """
        return {
            "id": str(post.id),
            "type": post.type,
            "status": post.status or "draft",
            "title": getattr(post, 'title', '') or "",
            "description": getattr(post, 'description', '') or "",
            "text": getattr(post, 'text', '') or "",
            "tags": post.tags or [],
            "author": {
                "id": str(author.id),
                "name": author.name or "",
                "username": author.username or "",
                "avatar": getattr(author, 'avatar', None)
            },
            "created_at": post.created_at.isoformat() if post.created_at else datetime.utcnow().isoformat(),
            "updated_at": post.updated_at.isoformat() if post.updated_at else datetime.utcnow().isoformat(),
            
            # 媒体文件字段（Video和Image子类型）
            "cover": getattr(post, 'cover', None),
            "url": getattr(post, 'url', None),  # videos专用
            "height": getattr(post, 'height', None),
            "width": getattr(post, 'width', None),
            "images_data": getattr(post, 'images_data', None),  # images专用
            
            "indexed_at": datetime.utcnow().isoformat()
        }
    
    def _build_author_document_for_pipeline(self, author) -> Dict[str, Any]:
        """
        构建用于ingest pipeline处理的Authors文档
        Pipeline会自动检测语言并路由到对应索引
        """
        return {
            "id": str(author.id),
            "status": getattr(author, 'status', 'active'),
            "name": author.name or "",
            "username": author.username or "",
            "avatar": getattr(author, 'avatar', None),
            "description": getattr(author, 'description', '') or "",
            "country": getattr(author, 'country', '') or "",
            "language": getattr(author, 'language', '') or "",
            "phone_number": getattr(author, 'phone_number', '') or "",
            "email": getattr(author, 'email', '') or "",
            "created_at": author.created_at.isoformat() if author.created_at else datetime.utcnow().isoformat(),
            "updated_at": author.updated_at.isoformat() if author.updated_at else datetime.utcnow().isoformat(),
            "region": getattr(author, 'region', '') or "",
            "indexed_at": datetime.utcnow().isoformat()
        }
    
    def _build_memecoin_document_for_pipeline(self, memecoin) -> Dict[str, Any]:
        """
        构建用于ingest pipeline处理的MemeCoin文档
        Pipeline会自动检测语言并路由到对应索引
        """
        return {
            "id": str(memecoin.id),
            "chain": getattr(memecoin, 'chain', 0),
            "address": getattr(memecoin, 'base', '') or "",  # 使用base作为address
            "dex": getattr(memecoin, 'dex', '') or "",
            
            # 映射字段：数据库字段 -> ES索引字段
            "name": getattr(memecoin, 'base_name', '') or "",      # base_name -> name
            "symbol": getattr(memecoin, 'base_symbol', '') or "",  # base_symbol -> symbol
            "description": getattr(memecoin, 'base_description', '') or "",  # base_description -> description
            "image_url": getattr(memecoin, 'base_image_url', '') or "",  # base_image_url -> image_url
            
            # 保留原字段以便调试
            "base": getattr(memecoin, 'base', '') or "",
            "base_name": getattr(memecoin, 'base_name', '') or "",
            "base_symbol": getattr(memecoin, 'base_symbol', '') or "",
            "base_description": getattr(memecoin, 'base_description', '') or "",
            "base_image_url": getattr(memecoin, 'base_image_url', '') or "",
            "base_total_supply": str(getattr(memecoin, 'base_total_supply', 0)),
            
            "creator_id": getattr(memecoin, 'creator_id', '') or "",
            "creator": getattr(memecoin, 'creator_id', '') or "",  # 映射creator字段
            "collection_id": getattr(memecoin, 'collection_id', '') or "",
            "status": getattr(memecoin, 'status', 0),
            "bonding_curve": getattr(memecoin, 'bonding_curve', 0),
            "created_at": memecoin.created_at.isoformat() if memecoin.created_at else datetime.utcnow().isoformat(),
            "base_created_at": memecoin.base_created_at.isoformat() if memecoin.base_created_at else None,
            "indexed_at": datetime.utcnow().isoformat()
        }
    
    async def sync_posts_batch_with_pipeline(self, session, posts: List, progress_callback=None) -> tuple[int, int]:
        """批量同步posts到语言特定索引（使用pipeline）"""
        if not posts:
            return 0, 0
        
        # 获取所有作者ID
        author_ids = list(set(post.author_id for post in posts))
        
        # 批量查询作者信息
        authors_query = select(Author).where(Author.id.in_(author_ids))
        authors_result = session.execute(authors_query)
        authors = {author.id: author for author in authors_result.scalars().all()}
        
        # 构建ES文档（为bulk API格式化）
        documents = []
        for post in posts:
            author = authors.get(post.author_id)
            if not author:
                logger.warning(f"找不到作者信息: post_id={post.id}, author_id={post.author_id}")
                continue
            
            document = self._build_post_document_for_pipeline(post, author)
            # 为bulk API添加_id字段
            document["_id"] = str(post.id)
            documents.append(document)
        
        # 使用bulk API批量索引到ES
        if documents:
            from src.common.elasticsearch.mappings import get_prefixed_index_name
            pipeline_name = self.unified_manager.posts_manager.pipeline_name
            temp_index = get_prefixed_index_name("posts", "temp")
            
            try:
                logger.debug(f"    🔄 开始批量索引 {len(documents)} 个posts到ES...")
                
                success_count, error_count = await self.es_client.bulk_index_documents(
                    index=temp_index,
                    documents=documents,
                    pipeline=pipeline_name,
                    refresh="false"  # 不等待刷新，提高性能
                )
                
                logger.debug(f"    ✅ ES批量索引完成: 成功={success_count}, 失败={error_count}")
                
                if progress_callback:
                    progress_callback("posts", len(documents), success_count, error_count)
                
                return success_count, error_count
                
            except Exception as e:
                logger.error(f"批量索引posts失败: {e}")
                if progress_callback:
                    progress_callback("posts", len(documents), 0, len(documents))
                return 0, len(documents)
        
        return 0, 0
    
    async def sync_authors_batch_with_pipeline(self, session, authors: List, progress_callback=None) -> tuple[int, int]:
        """批量同步authors到语言特定索引（使用pipeline）"""
        if not authors:
            return 0, 0
        
        # 构建ES文档（为bulk API格式化）
        documents = []
        for author in authors:
            document = self._build_author_document_for_pipeline(author)
            # 为bulk API添加_id字段
            document["_id"] = str(author.id)
            documents.append(document)
        
        # 使用bulk API批量索引到ES
        if documents:
            from src.common.elasticsearch.mappings import get_prefixed_index_name
            pipeline_name = self.unified_manager.authors_manager.pipeline_name
            temp_index = get_prefixed_index_name("authors", "temp")
            
            try:
                success_count, error_count = await self.es_client.bulk_index_documents(
                    index=temp_index,
                    documents=documents,
                    pipeline=pipeline_name,
                    refresh="false"  # 不等待刷新，提高性能
                )
                
                if progress_callback:
                    progress_callback("authors", len(documents), success_count, error_count)
                
                return success_count, error_count
                
            except Exception as e:
                logger.error(f"批量索引authors失败: {e}")
                if progress_callback:
                    progress_callback("authors", len(documents), 0, len(documents))
                return 0, len(documents)
        
        return 0, 0
    
    async def sync_memecoins_batch_with_pipeline(self, session, memecoins: List, progress_callback=None) -> tuple[int, int]:
        """批量同步memecoins到语言特定索引（使用pipeline）"""
        if not memecoins:
            return 0, 0
        
        # 构建ES文档（为bulk API格式化）
        documents = []
        for memecoin in memecoins:
            document = self._build_memecoin_document_for_pipeline(memecoin)
            # 为bulk API添加_id字段
            document["_id"] = str(memecoin.id)
            documents.append(document)
        
        # 使用bulk API批量索引到ES
        if documents:
            from src.common.elasticsearch.mappings import get_prefixed_index_name
            pipeline_name = self.unified_manager.memecoins_manager.pipeline_name
            temp_index = get_prefixed_index_name("memecoins", "temp")
            
            try:
                success_count, error_count = await self.es_client.bulk_index_documents(
                    index=temp_index,
                    documents=documents,
                    pipeline=pipeline_name,
                    refresh="false"  # 不等待刷新，提高性能
                )
                
                if progress_callback:
                    progress_callback("memecoins", len(documents), success_count, error_count)
                
                return success_count, error_count
                
            except Exception as e:
                logger.error(f"批量索引memecoins失败: {e}")
                if progress_callback:
                    progress_callback("memecoins", len(documents), 0, len(documents))
                return 0, len(documents)
        
        return 0, 0
    
    async def full_sync_posts(self, since_date: datetime = None) -> Dict[str, int]:
        """全量同步posts（使用语言检测pipeline）"""
        logger.info("🎬 开始全量同步posts到语言特定索引...")
        
        await self.init_es_client()
        
        stats = {
            "total_processed": 0,
            "total_success": 0,
            "total_errors": 0,
            "videos": 0,
            "images": 0
        }
        
        # 构建查询条件
        base_conditions = [Post.status == "posted"]  # 只同步已发布的内容
        if since_date:
            base_conditions.append(Post.updated_at >= since_date)
        
        def progress_callback(entity_type, processed, success, errors):
            stats["total_processed"] += processed
            stats["total_success"] += success
            stats["total_errors"] += errors
            logger.info(f"  📊 批次完成: {entity_type}, 处理={processed}, 成功={success}, 失败={errors}")
        
        with get_sync_context() as session:
            # 分别处理Videos和Images
            for post_type in ["Video", "Image"]:
                logger.info(f"  📈 同步{post_type}...")
                
                # 计算总数
                count_query = select(func.count(Post.id)).where(
                    and_(Post.type == post_type, *base_conditions)
                )
                total_count = session.execute(count_query).scalar()
                logger.info(f"  📋 待同步{post_type}数量: {total_count:,}")
                
                if total_count == 0:
                    continue
                
                # 并行分批处理
                offset = 0
                batch_count = 0
                
                while offset < total_count:
                    logger.info(f"  🔍 开始处理批次组: offset={offset:,}/{total_count:,}")
                    
                    # 准备多个并发批次
                    batch_tasks = []
                    current_offset = offset
                    
                    for i in range(self.concurrent_batches):
                        if current_offset >= total_count:
                            break
                        
                        logger.debug(f"    📥 查询批次 {i+1}: offset={current_offset}, limit={self.batch_size}")
                        
                        posts_query = select(Post).where(
                            and_(Post.type == post_type, *base_conditions)
                        ).offset(current_offset).limit(self.batch_size)
                        
                        posts_result = session.execute(posts_query)
                        posts = posts_result.scalars().all()
                        
                        logger.debug(f"    ✅ 获取到 {len(posts)} 个{post_type}")
                        
                        if not posts:
                            break
                        
                        # 创建异步任务
                        task = self.sync_posts_batch_with_pipeline(session, posts, progress_callback)
                        batch_tasks.append(task)
                        
                        current_offset += self.batch_size
                    
                    logger.info(f"  🚀 开始并行执行 {len(batch_tasks)} 个批次...")
                    
                    # 并行执行所有批次（带超时）
                    if batch_tasks:
                        try:
                            # 设置180秒超时
                            results = await asyncio.wait_for(
                                asyncio.gather(*batch_tasks, return_exceptions=True),
                                timeout=180.0
                            )
                            
                            logger.info(f"  ✅ 并行执行完成，处理结果...")
                            
                            for i, result in enumerate(results):
                                if isinstance(result, Exception):
                                    logger.error(f"批次 {i+1} 处理失败: {result}")
                                    continue
                                
                                success_count, error_count = result
                                if post_type == "Video":
                                    stats["videos"] += success_count
                                else:
                                    stats["images"] += success_count
                                    
                        except asyncio.TimeoutError:
                            logger.error(f"批次组执行超时（180秒），跳过此组...")
                            break
                        except Exception as e:
                            logger.error(f"并行执行批次时出错: {e}")
                            break
                    
                    offset = current_offset
                    batch_count += len(batch_tasks)
                    
                    # 进度报告（每组都报告）
                    logger.info(f"  📊 {post_type} 进度: {offset:,}/{total_count:,} ({offset/total_count*100:.1f}%) - 已处理批次组: {batch_count // self.concurrent_batches + 1}")
        
        return stats
    
    async def full_sync_authors(self, since_date: datetime = None) -> Dict[str, int]:
        """全量同步authors（使用语言检测pipeline）"""
        logger.info("👤 开始全量同步authors到语言特定索引...")
        
        await self.init_es_client()
        
        stats = {
            "total_processed": 0,
            "total_success": 0,
            "total_errors": 0
        }
        
        def progress_callback(entity_type, processed, success, errors):
            stats["total_processed"] += processed
            stats["total_success"] += success
            stats["total_errors"] += errors
            logger.info(f"  📊 批次完成: 处理={processed}, 成功={success}, 失败={errors}")
        
        with get_sync_context() as session:
            # 构建查询条件
            base_conditions = []
            if since_date:
                base_conditions.append(Author.updated_at >= since_date)
            
            # 计算总数
            count_query = select(func.count(Author.id))
            if base_conditions:
                count_query = count_query.where(and_(*base_conditions))
            
            total_count = session.execute(count_query).scalar()
            logger.info(f"  📋 待同步作者数量: {total_count:,}")
            
            if total_count == 0:
                return stats
            
            # 分批处理
            offset = 0
            batch_count = 0
            
            while offset < total_count:
                authors_query = select(Author).offset(offset).limit(self.batch_size)
                if base_conditions:
                    authors_query = authors_query.where(and_(*base_conditions))
                
                authors_result = session.execute(authors_query)
                authors = authors_result.scalars().all()
                
                if not authors:
                    break
                
                await self.sync_authors_batch_with_pipeline(session, authors, progress_callback)
                
                offset += self.batch_size
                batch_count += 1
                
                # 进度报告
                if batch_count % 10 == 0:
                    logger.info(f"  🔄 Authors 进度: {offset:,}/{total_count:,} ({offset/total_count*100:.1f}%)")
        
        return stats
    
    async def full_sync_memecoins(self, since_date: datetime = None) -> Dict[str, int]:
        """全量同步memecoins（使用语言检测pipeline）"""
        logger.info("🪙 开始全量同步memecoins到语言特定索引...")
        
        await self.init_es_client()
        
        stats = {
            "total_processed": 0,
            "total_success": 0,
            "total_errors": 0
        }
        
        def progress_callback(entity_type, processed, success, errors):
            stats["total_processed"] += processed
            stats["total_success"] += success
            stats["total_errors"] += errors
            logger.info(f"  📊 批次完成: 处理={processed}, 成功={success}, 失败={errors}")
        
        with get_sync_context() as session:
            # 构建查询条件
            base_conditions = []
            if since_date:
                base_conditions.append(Pair.created_at >= since_date)
            
            # 计算总数
            count_query = select(func.count(Pair.id))
            if base_conditions:
                count_query = count_query.where(and_(*base_conditions))
            
            total_count = session.execute(count_query).scalar()
            logger.info(f"  📋 待同步代币数量: {total_count:,}")
            
            if total_count == 0:
                return stats
            
            # 分批处理
            offset = 0
            batch_count = 0
            
            while offset < total_count:
                memecoins_query = select(Pair).offset(offset).limit(self.batch_size)
                if base_conditions:
                    memecoins_query = memecoins_query.where(and_(*base_conditions))
                
                memecoins_result = session.execute(memecoins_query)
                memecoins = memecoins_result.scalars().all()
                
                if not memecoins:
                    break
                
                await self.sync_memecoins_batch_with_pipeline(session, memecoins, progress_callback)
                
                offset += self.batch_size
                batch_count += 1
                
                # 进度报告
                if batch_count % 10 == 0:
                    logger.info(f"  🔄 MemeCoins 进度: {offset:,}/{total_count:,} ({offset/total_count*100:.1f}%)")
        
        return stats
    
    async def full_sync_all(self, since_date: datetime = None) -> Dict[str, Any]:
        """全量同步所有实体到语言特定索引"""
        logger.info("🚀 开始全量同步所有实体到语言特定索引...")
        start_time = datetime.now()
        
        # 确保所有索引都已初始化
        await self.init_es_client()
        logger.info("✅ 确保所有语言索引已初始化...")
        await self.unified_manager.initialize_all_indices(force_recreate=False)
        
        all_stats = {}
        
        try:
            # 1. 同步Posts
            posts_stats = await self.full_sync_posts(since_date)
            all_stats["posts"] = posts_stats
            
            # 2. 同步Authors
            authors_stats = await self.full_sync_authors(since_date)
            all_stats["authors"] = authors_stats
            
            # 3. 同步MemeCoins
            memecoins_stats = await self.full_sync_memecoins(since_date)
            all_stats["memecoins"] = memecoins_stats
            
            # 汇总统计
            total_stats = {
                "total_processed": sum(stats.get("total_processed", 0) for stats in all_stats.values()),
                "total_success": sum(stats.get("total_success", 0) for stats in all_stats.values()),
                "total_errors": sum(stats.get("total_errors", 0) for stats in all_stats.values()),
                "duration": (datetime.now() - start_time).total_seconds()
            }
            all_stats["summary"] = total_stats
            
            # 最后刷新所有索引，确保数据立即可搜索
            logger.info("🔄 正在刷新所有索引...")
            await self.refresh_all_indices()
            
            logger.info("✅ 全量同步到语言特定索引完成:")
            logger.info(f"  📊 总处理: {total_stats['total_processed']:,}")
            logger.info(f"  ✅ 总成功: {total_stats['total_success']:,}")
            logger.info(f"  ❌ 总失败: {total_stats['total_errors']:,}")
            logger.info(f"  ⏱️  总耗时: {total_stats['duration']:.2f}秒")
            
            return all_stats
            
        except Exception as e:
            logger.error(f"❌ 全量同步失败: {str(e)}")
            raise
    
    async def incremental_sync(self, hours: int = 24) -> Dict[str, Any]:
        """增量同步（近期更新的数据）"""
        logger.info(f"🔄 开始增量同步最近{hours}小时的数据到语言特定索引...")
        since_date = datetime.utcnow() - timedelta(hours=hours)
        return await self.full_sync_all(since_date)
    
    async def refresh_all_indices(self):
        """刷新所有语言索引，确保数据立即可搜索"""
        try:
            from src.common.elasticsearch.mappings import SUPPORTED_LANGUAGES, get_prefixed_index_name
            
            indices_to_refresh = []
            for index_type in ["posts", "authors", "memecoins"]:
                for language in SUPPORTED_LANGUAGES:
                    index_name = get_prefixed_index_name(index_type, language)
                    indices_to_refresh.append(index_name)
            
            # 批量刷新索引
            for index_name in indices_to_refresh:
                try:
                    await self.es_client.client.indices.refresh(index=index_name)
                except Exception as e:
                    logger.warning(f"刷新索引失败: {index_name}, error: {e}")
            
            logger.info(f"✅ 刷新了 {len(indices_to_refresh)} 个索引")
            
        except Exception as e:
            logger.error(f"刷新索引失败: {e}")

    async def close(self):
        """关闭ES客户端"""
        if self.es_client:
            await self.es_client.close()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Elasticsearch语言独立索引同步工具")
    parser.add_argument(
        'action',
        choices=['full', 'incremental', 'posts', 'authors', 'memecoins'],
        help='同步操作: full(全量), incremental(增量), posts(仅posts), authors(仅authors), memecoins(仅memecoins)'
    )
    parser.add_argument(
        '--hours',
        type=int,
        default=24,
        help='增量同步的小时数（默认24小时）'
    )
    parser.add_argument(
        '--since',
        help='同步指定日期之后的数据 (格式: YYYY-MM-DD)'
    )
    
    args = parser.parse_args()
    
    processor = LanguageIndexSyncProcessor()
    
    try:
        since_date = None
        if args.since:
            since_date = datetime.strptime(args.since, '%Y-%m-%d')
        
        if args.action == 'full':
            stats = await processor.full_sync_all(since_date)
            
        elif args.action == 'incremental':
            stats = await processor.incremental_sync(args.hours)
            
        elif args.action == 'posts':
            stats = await processor.full_sync_posts(since_date)
            
        elif args.action == 'authors':
            stats = await processor.full_sync_authors(since_date)
            
        elif args.action == 'memecoins':
            stats = await processor.full_sync_memecoins(since_date)
        
        # 输出最终统计
        logger.info("📈 同步统计详情:")
        for key, value in stats.items():
            if isinstance(value, dict):
                logger.info(f"  {key}:")
                for sub_key, sub_value in value.items():
                    logger.info(f"    {sub_key}: {sub_value}")
            else:
                logger.info(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"❌ 同步失败: {str(e)}")
        sys.exit(1)
    finally:
        await processor.close()


if __name__ == "__main__":
    asyncio.run(main()) 