import argparse
import logging
import os
import re
from typing import Optional, <PERSON><PERSON>

from sqlalchemy import select

from src.database.models.UserWallet import UserWallet
from src.database.session import get_sync_session

try:
    from web3 import Web3
except Exception as e:  # pragma: no cover
    raise RuntimeError("web3 is required to run this script. Please ensure it is installed.") from e


logger = logging.getLogger("normalize_bsc_pubkeys")

# Match 0x + 40 hex chars (case-insensitive)
EVM_ADDRESS_RE = re.compile(r"^0x[a-fA-F0-9]{40}$")


def is_evm_like_address(address: Optional[str]) -> bool:
    if not address:
        return False
    address = address.strip()
    return bool(EVM_ADDRESS_RE.match(address))


def to_checksum_safely(address: str) -> Tuple[Optional[str], Optional[Exception]]:
    try:
        return Web3.to_checksum_address(address), None
    except Exception as err:
        return None, err


def configure_logging(verbose: bool) -> None:
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s %(levelname)s %(name)s - %(message)s",
    )


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Normalize UserWallet pubkeys to EIP-55 checksum for type=bsc",
    )
    parser.add_argument("--batch-size", type=int, default=1000, help="Number of rows per batch")
    parser.add_argument("--start-id", type=str, default=None, help="Resume from wallets with id > start-id (lexicographic)")
    parser.add_argument("--limit", type=int, default=None, help="Max number of rows to scan (not just updates)")
    parser.add_argument("--dry-run", action="store_true", help="Do not write to DB; only report changes")
    parser.add_argument("--verbose", action="store_true", help="Verbose logging")
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    configure_logging(args.verbose)

    if not os.environ.get("DATABASE_URL"):
        logger.warning("DATABASE_URL is not set; ensure it is configured before running this script.")

    total_scanned = 0
    total_updated = 0
    total_skipped_equal = 0
    total_invalid = 0

    last_id: Optional[str] = args.start_id
    remaining_scan = args.limit if args.limit is not None else None

    logger.info("Starting normalization: type=bsc, batch_size=%s, start_id=%s, limit=%s, dry_run=%s",
                args.batch_size, last_id, args.limit, args.dry_run)

    while True:
        with get_sync_session() as session:
            stmt = (
                select(UserWallet)
                .where(UserWallet.type == "bsc")
                .order_by(UserWallet.id.asc())
                .limit(args.batch_size)
            )
            if last_id:
                stmt = stmt.where(UserWallet.id > last_id)

            result = session.execute(stmt)
            wallets = [row[0] for row in result.fetchall()]

            if not wallets:
                break

            batch_scanned = 0
            batch_updated = 0
            batch_skipped_equal = 0
            batch_invalid = 0

            for wallet in wallets:
                batch_scanned += 1
                if remaining_scan is not None and total_scanned + batch_scanned > remaining_scan:
                    # Reached the overall scan limit
                    wallets = wallets[:batch_scanned]
                    break

                current = (wallet.pubkey or "").strip()
                if not is_evm_like_address(current):
                    batch_invalid += 1
                    continue

                checksummed, err = to_checksum_safely(current)
                if err is not None:
                    batch_invalid += 1
                    logger.debug("Checksum failed for id=%s pubkey=%s error=%s", wallet.id, current, err)
                    continue

                if checksummed == current:
                    batch_skipped_equal += 1
                    continue

                # Apply update
                wallet.pubkey = checksummed
                batch_updated += 1

            total_scanned += batch_scanned
            total_updated += batch_updated
            total_skipped_equal += batch_skipped_equal
            total_invalid += batch_invalid

            if batch_updated > 0 and not args.dry_run:
                logger.info("Committing batch: scanned=%s updated=%s skipped_equal=%s invalid=%s last_id=%s",
                            batch_scanned, batch_updated, batch_skipped_equal, batch_invalid, wallets[-1].id)
                # Commit via context manager on exit
            else:
                # If no updates or dry-run, avoid committing anything
                session.rollback()

            last_id = wallets[-1].id

        # If we had a global scan limit and we've reached it, break
        if remaining_scan is not None and total_scanned >= remaining_scan:
            break

    logger.info("Done. scanned=%s updated=%s skipped_equal=%s invalid=%s",
                total_scanned, total_updated, total_skipped_equal, total_invalid)


if __name__ == "__main__":
    main()


