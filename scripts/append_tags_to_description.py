"""
Append sanitized hashtags built from posts.tags_list into Image/Video descriptions.

Rules (confirmed):
- Separate with a newline from existing description.
- If original description is empty, write only the hashtag line (no leading blank line).
- Preserve original letter casing from input tags; only remove special symbols and whitespace.
- No limits on number or length of tags.

Usage examples:
  - Dry run all (default filters to POSTED):
      python scripts/append_tags_to_description.py --dry-run

  - Process only images, commit every 500 rows:
      python scripts/append_tags_to_description.py --types image --batch-size 500

  - Process both images and videos, include all statuses, limit first 1000:
      python scripts/append_tags_to_description.py --include-all-status --limit 1000

Environment:
- Uses DATABASE_URL via src.database.session.get_sync_context().
- Will try to load environment variables from etc/api.env if present (KEY=VALUE lines).
"""

from __future__ import annotations

import os
import sys
import argparse
import unicodedata
from pathlib import Path
from typing import Iterable, List, Set, Tuple

from sqlalchemy import select, update
from sqlalchemy.orm import Session


# Ensure project root on sys.path so that `src` is importable when running as a script
CURRENT_FILE = Path(__file__).resolve()
PROJECT_ROOT = CURRENT_FILE.parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))


from src.database.session import get_sync_context  # noqa: E402
from src.database.models.Post import Post  # noqa: E402
from src.database.models.Image import Image as ImageModel  # noqa: E402
from src.database.models.Video import Video as VideoModel  # noqa: E402
from src.database.constants import PostStatus  # noqa: E402


def try_load_env_file(path: Path) -> None:
    """Best-effort load KEY=VALUE lines from a .env-like file without external deps.

    Existing environment variables are preserved (os.environ.setdefault).
    """
    if not path.exists() or not path.is_file():
        return
    for raw_line in path.read_text(encoding="utf-8").splitlines():
        line = raw_line.strip()
        if not line or line.startswith("#"):
            continue
        if "=" not in line:
            continue
        key, value = line.split("=", 1)
        key = key.strip()
        value = value.strip().strip("'\"")
        if key:
            os.environ.setdefault(key, value)


def is_allowed_tag_char(ch: str) -> bool:
    """Return True if character is allowed inside a hashtag token (after '#').

    Allowed categories: Letters (L*), Numbers (N*), Combining marks (M*).
    Disallowed: whitespace (isspace or Z*), punctuation (P*), symbols (S*), control/other (C*).
    This aligns with frontend regex: #[^\s\p{P}\p{S}][^\s\p{P}\p{S}]*
    """
    if not ch:
        return False
    if ch.isspace():
        return False
    cat = unicodedata.category(ch)
    return bool(cat and cat[0] in {"L", "N", "M"})


def sanitize_tag_component(text: str) -> str:
    """Remove disallowed characters while preserving original casing of remaining chars."""
    return "".join(ch for ch in (text or "") if is_allowed_tag_char(ch))


def split_tags(raw: str) -> List[str]:
    """Split a raw comma-separated tag string into components. Trim spaces around parts."""
    return [part.strip() for part in (raw or "").split(",") if part and part.strip()]


def build_hashtags_from_tags_list(tags_list: Iterable[str]) -> List[str]:
    """Build ordered unique hashtags (preserving original case) from tags_list.

    - tags_list may contain either a single tag item or a comma-separated string.
    - For each component: strip, remove disallowed chars; skip empty; prepend '#'.
    - Deduplicate by exact hashtag token while preserving first occurrence order.
    """
    ordered: List[str] = []
    seen: Set[str] = set()
    for item in tags_list or []:
        if item is None:
            continue
        components = split_tags(item) if ("," in item) else [item.strip()]
        for comp in components:
            if not comp:
                continue
            cleaned = sanitize_tag_component(comp)
            if not cleaned:
                continue
            hashtag = f"#{cleaned}"
            if hashtag not in seen:
                seen.add(hashtag)
                ordered.append(hashtag)
    return ordered


def extract_existing_hashtags(description: str | None) -> Set[str]:
    """Extract existing hashtags from description by whitespace tokenization.

    The extractor normalizes tokens by keeping only '#' plus allowed characters,
    to match how we sanitize generated hashtags.
    """
    if not description:
        return set()
    tokens = description.split()
    existing: Set[str] = set()
    for tok in tokens:
        if not tok or not tok.startswith("#"):
            continue
        # Normalize token similar to our sanitizer
        normalized = "#" + sanitize_tag_component(tok.lstrip("#"))
        if normalized and normalized != "#":
            existing.add(normalized)
    return existing


def append_hashtags_to_description(
    original_description: str | None, new_hashtags: List[str], existing: Set[str]
) -> Tuple[str | None, int]:
    """Append not-yet-present hashtags to the description per rules.

    Returns (new_description_or_original, appended_count).
    """
    if not new_hashtags:
        return original_description, 0
    to_append = [h for h in new_hashtags if h not in existing]
    if not to_append:
        return original_description, 0
    line = " ".join(to_append)
    if not original_description or not original_description.strip():
        return line, len(to_append)
    return original_description.rstrip() + "\n" + line, len(to_append)


def process_batch_for_model(
    session: Session,
    model_name: str,
    batch_size: int,
    offset: int,
    include_all_status: bool,
) -> Tuple[int, int, int]:
    """Process a batch for a specific model (image or video). Returns (scanned, updated, appended_tags)."""
    if model_name == "image":
        model = ImageModel
    elif model_name == "video":
        model = VideoModel
    else:
        raise ValueError("model_name must be 'image' or 'video'")

    stmt = (
        select(model.id, model.description, Post.tags_list)
        .join(Post, Post.id == model.id)
    )
    if not include_all_status:
        stmt = stmt.where(Post.status == PostStatus.POSTED)
    stmt = stmt.order_by(Post.created_at.asc()).limit(batch_size).offset(offset)

    rows = session.execute(stmt).all()
    scanned = len(rows)
    updated_count = 0
    appended_tags_count = 0

    for row in rows:
        entity_id, description, tags_list = row
        hashtags = build_hashtags_from_tags_list(tags_list or [])
        if not hashtags:
            continue
        existing = extract_existing_hashtags(description)
        new_desc, appended = append_hashtags_to_description(description, hashtags, existing)
        if appended > 0 and new_desc is not None and new_desc != description:
            upd = (
                update(model)
                .where(model.id == entity_id)
                .values(description=new_desc)
            )
            session.execute(upd)
            updated_count += 1
            appended_tags_count += appended

    return scanned, updated_count, appended_tags_count


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Append sanitized hashtags from posts.tags_list to Image/Video descriptions.",
    )
    parser.add_argument(
        "--types",
        choices=["image", "video", "both"],
        default="both",
        help="Which post types to process",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=500,
        help="Number of rows per batch",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Optional limit on total rows scanned per type",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Do not commit; only show what would change",
    )
    parser.add_argument(
        "--include-all-status",
        action="store_true",
        help="Include all statuses (default processes only POSTED)",
    )

    args = parser.parse_args()

    # Load environment best-effort
    try_load_env_file(PROJECT_ROOT / "etc" / "api.env")

    # Sanity check for DATABASE_URL
    if not os.environ.get("DATABASE_URL"):
        print("[ERROR] DATABASE_URL is not set. Set it in environment or etc/api.env.")
        sys.exit(1)

    total_scanned = 0
    total_updated = 0
    total_appended_tags = 0

    with get_sync_context() as session:
        # Determine models to process
        models: List[str] = ["image", "video"] if args.types == "both" else [args.types]

        for name in models:
            print(f"[INFO] Processing type: {name}")
            offset = 0
            scanned_for_type = 0
            updated_for_type = 0
            appended_for_type = 0

            while True:
                # Respect overall limit per type
                if args.limit is not None and scanned_for_type >= args.limit:
                    break
                # Adjust effective batch size when approaching limit
                effective_batch = args.batch_size
                if args.limit is not None:
                    remaining = args.limit - scanned_for_type
                    if remaining <= 0:
                        break
                    effective_batch = min(effective_batch, remaining)

                scanned, updated, appended = process_batch_for_model(
                    session=session,
                    model_name=name,
                    batch_size=effective_batch,
                    offset=offset,
                    include_all_status=args.include_all_status,
                )

                if scanned == 0:
                    break

                scanned_for_type += scanned
                updated_for_type += updated
                appended_for_type += appended
                total_scanned += scanned
                total_updated += updated
                total_appended_tags += appended

                print(
                    f"  [BATCH] offset={offset} scanned={scanned} updated={updated} appended_tags={appended}"
                )

                # Move window forward
                offset += scanned

                # Commit per batch unless dry-run
                if not args.dry_run and updated > 0:
                    session.commit()

            # End of type
            print(
                f"[TYPE] {name}: scanned={scanned_for_type}, updated_rows={updated_for_type}, appended_tags={appended_for_type}"
            )

        # Final commit if not dry-run
        if args.dry_run:
            print("[DRY-RUN] No changes were committed.")
        else:
            session.commit()
            print("[DONE] Changes committed.")

    # Summary
    print(
        f"[SUMMARY] scanned={total_scanned}, updated_rows={total_updated}, appended_tags={total_appended_tags}"
    )


if __name__ == "__main__":
    main()


