#!/usr/bin/env python3
"""
将数据库中的用户资料同步到 OpenIM：昵称、头像、扩展字段（ex）。

- 支持分页、并发、dry-run；
- 默认仅同步已有 IM 映射（toci_im）；
- 可选自动为缺失映射的用户注册 IM 账户（--create-missing）；
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from argparse import ArgumentParser
from typing import Optional, List, Tuple, Dict, Set

# 将项目根目录加入路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试加载 .env
try:
    from dotenv import load_dotenv
    env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(env_path):
        load_dotenv(env_path)
except Exception:
    pass

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.session import get_session, init_engines
from src.database.models import User
from src.database.models.Author import Author
from src.database.models.TociIm import TociIm
from src.common.im_service import ImService


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("sync_openim_user_profiles")


def choose_nickname(author: Author) -> str:
    if author and getattr(author, 'name', None):
        return author.name
    return ""


def choose_avatar(author: Author, allow_empty: bool) -> Optional[str]:
    avatar = None
    if author and getattr(author, 'avatar', None):
        avatar = author.avatar
    elif getattr(author, 'avatar_url', None):
        avatar = author.avatar_url
    # 返回空字符串用于明确清空头像
    if avatar is None and allow_empty:
        return ""
    return avatar


def build_ex(author: Author) -> Dict[str, str]:
    ex: Dict[str, str] = {
        "user_id": author.id,
    }
    if author and getattr(author, 'username', None):
        ex["username"] = author.username
    return ex


async def fetch_users_batch(session: AsyncSession, offset: int, limit: int, only_with_im: bool) -> List[Tuple[Author, Optional[TociIm]]]:
    stmt = select(Author, TociIm).join(
        TociIm, TociIm.toci_id == Author.id, isouter=True
    ).offset(offset).limit(limit)

    result = await session.execute(stmt)
    rows = result.all()

    items: List[Tuple[Author, Optional[TociIm]]] = []
    for row in rows:
        author: Author = row[0]
        toci_im: Optional[TociIm] = row[1]
        if only_with_im and toci_im is None:
            continue
        items.append((author, toci_im))

    return items


async def ensure_im_account(im_service: ImService, session: AsyncSession, author: Optional[Author]) -> Optional[TociIm]:
    """为没有映射的用户注册 IM 账户，并返回 TociIm 记录（若注册成功）。"""
    try:
        avatar = getattr(author, 'avatar', None) or getattr(author, 'avatar_url', None) or ""
        username = None
        if author and getattr(author, 'name', None):
            username = author.name
        elif author and getattr(author, 'username', None):
            username = author.username

        register_result = await im_service.register(author.id, username=username, avatar_url=avatar)
        # 手动插入映射，由于 ImService.register 只返回结果，不做落库
        toci_im = TociIm(
            toci_id=register_result.toci_id,
            im_id=register_result.im_id,
            im_login=register_result.im_login,
            im_password=register_result.im_password,
        )
        session.add(toci_im)
        await session.commit()
        return toci_im
    except Exception as e:
        logger.error(f"为用户 {author.id} 注册 IM 失败: {e}")
        await session.rollback()
        return None


async def sync_one(im_service: ImService, author: Optional[Author], toci_im: TociIm, update_empty: bool, dry_run: bool) -> bool:
    nickname = choose_nickname(author)
    avatar = choose_avatar(author, allow_empty=update_empty)
    ex = build_ex(author)

    if dry_run:
        logger.info(f"[DRY-RUN] 将更新 IM: user_id={author.id}, im_id={toci_im.im_id}, nickname={nickname!r}, faceURL={avatar!r}, ex={json.dumps(ex, ensure_ascii=False)}")
        return True

    ok = await im_service.update_user_info(
        im_id=toci_im.im_id,
        nickname=nickname,
        face_url=avatar,
        ex=ex,
    )

    if not ok:
        logger.error(f"更新失败: user_id={author.id}, im_id={toci_im.im_id}")
    return ok


async def run_sync(limit: int, offset: int, concurrency: int, only_with_im: bool, create_missing: bool, update_empty: bool, dry_run: bool) -> Dict[str, int]:
    # 已同步记录文件（本地JSON）
    SYNCED_FILE = os.path.join(os.path.dirname(__file__), "synced_openim_profiles.json")

    def _load_synced_ids(path: str) -> Set[str]:
        try:
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return set(map(str, data))
                    if isinstance(data, dict) and "ids" in data and isinstance(data["ids"], list):
                        return set(map(str, data["ids"]))
        except Exception:
            pass
        return set()

    def _save_synced_ids(path: str, ids: Set[str]) -> None:
        try:
            tmp = f"{path}.tmp"
            with open(tmp, "w", encoding="utf-8") as f:
                json.dump(sorted(list(ids)), f, ensure_ascii=False)
            os.replace(tmp, path)
        except Exception:
            logger.warning("保存已同步列表失败", exc_info=True)

    stats = {
        "total": 0,
        "updated": 0,
        "skipped_no_im": 0,
        "created_im": 0,
        "failed": 0,
    }

    sem = asyncio.Semaphore(concurrency)
    synced_ids: Set[str] = _load_synced_ids(SYNCED_FILE)

    async for session in get_session():
        im_service = ImService(session)

        try:
            # 分批获取
            batch = await fetch_users_batch(session, offset=offset, limit=limit, only_with_im=False)
            stats["total"] = len(batch)
            logger.info(f"准备同步 {stats['total']} 个用户（offset={offset}, limit={limit}）")

            async def _process(item: Tuple[Author, Optional[TociIm]]):
                async with sem:
                    author, toci_im = item
                    local_session: AsyncSession = session

                    # 跳过已同步
                    if author and getattr(author, 'id', None) and author.id in synced_ids:
                        return

                    if toci_im is None:
                        if only_with_im:
                            stats["skipped_no_im"] += 1
                            return
                        if not create_missing:
                            stats["skipped_no_im"] += 1
                            logger.info(f"跳过未创建IM账户的用户: {author.id}")
                            return
                        new_mapping = await ensure_im_account(im_service, local_session, author)
                        if new_mapping is None:
                            stats["failed"] += 1
                            return
                        toci_im = new_mapping
                        stats["created_im"] += 1

                    ok = await sync_one(im_service, author, toci_im, update_empty, dry_run)
                    if ok:
                        stats["updated"] += 1
                        if not dry_run and author and getattr(author, 'id', None):
                            synced_ids.add(author.id)
                    else:
                        stats["failed"] += 1

            tasks = [asyncio.create_task(_process(item)) for item in batch]
            if tasks:
                await asyncio.gather(*tasks)

        finally:
            try:
                await im_service.aclose()
            except Exception:
                pass

    # 保存已同步列表
    _save_synced_ids(SYNCED_FILE, synced_ids)

    return stats


async def main_async():
    init_engines()

    parser = ArgumentParser(description="同步用户资料到 OpenIM")
    parser.add_argument("--limit", type=int, default=1000, help="本次处理的最大用户数")
    parser.add_argument("--offset", type=int, default=0, help="查询偏移量")
    parser.add_argument("--concurrency", type=int, default=5, help="并发任务数")
    parser.add_argument("--only-with-im", action="store_true", help="仅同步已有 IM 映射的用户")
    parser.add_argument("--create-missing", action="store_true", help="为缺失 IM 账户的用户自动注册")
    parser.add_argument("--update-empty", action="store_true", help="允许将空头像同步到 OpenIM（清空头像）")
    parser.add_argument("--dry-run", action="store_true", help="预演，不实际调用 OpenIM")

    args = parser.parse_args()

    stats = await run_sync(
        limit=args.limit,
        offset=args.offset,
        concurrency=args.concurrency,
        only_with_im=args.only_with_im,
        create_missing=args.create_missing,
        update_empty=args.update_empty,
        dry_run=args.dry_run,
    )

    logger.info(
        "完成：total=%s, updated=%s, created_im=%s, skipped_no_im=%s, failed=%s",
        stats["total"], stats["updated"], stats["created_im"], stats["skipped_no_im"], stats["failed"],
    )


if __name__ == "__main__":
    asyncio.run(main_async())


