#!/usr/bin/env python3
"""
Elasticsearch初始化脚本 - 语言独立索引架构
支持通过ingest pipeline进行自动语言检测和路由
"""

import os
import sys
import asyncio
import argparse
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.common.elasticsearch import get_elasticsearch_client, UnifiedLanguageIndexManager
from src.common.elasticsearch.mappings import SUPPORTED_LANGUAGES
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class LanguageIndexInitializer:
    """语言索引初始化器"""
    
    def __init__(self):
        self.es_client = None
        self.unified_manager = None
    
    async def _get_clients(self):
        """获取ES客户端和管理器"""
        if self.es_client is None:
            self.es_client = await get_elasticsearch_client()
            self.unified_manager = UnifiedLanguageIndexManager(self.es_client.client)
        return self.es_client, self.unified_manager
    
    async def check_cluster_health(self) -> bool:
        """检查集群健康状态"""
        try:
            es_client, _ = await self._get_clients()
            health = await es_client.client.cluster.health()
            
            status = health.get('status', 'unknown')
            logger.info(f"🏥 集群健康状态: {status}")
            logger.info(f"📊 节点数量: {health.get('number_of_nodes', 0)}")
            logger.info(f"📈 活跃分片: {health.get('active_shards', 0)}")
            
            if status in ['green', 'yellow']:
                logger.info("✅ 集群状态正常")
                return True
            else:
                logger.error("❌ 集群状态异常")
                return False
                
        except Exception as e:
            logger.error(f"❌ 无法连接到Elasticsearch: {e}")
            return False
    
    async def initialize_all_indices(self, force_recreate: bool = False) -> Dict[str, bool]:
        """初始化所有索引"""
        logger.info("🚀 开始初始化语言独立索引架构...")
        
        try:
            _, unified_manager = await self._get_clients()
            
            # 检查是否需要安装语言识别模型
            await self._check_language_model()
            
            # 初始化所有索引
            results = await unified_manager.initialize_all_indices(force_recreate)
            
            # 输出结果
            self._print_initialization_results(results)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            return {}
    
    async def _check_language_model(self):
        """检查语言识别模型是否可用"""
        try:
            es_client, _ = await self._get_clients()
            
            # 检查 lang_ident_model_1 是否存在
            try:
                await es_client.client.ml.get_trained_models(model_id="lang_ident_model_1")
                logger.info("✅ 语言识别模型已安装: lang_ident_model_1")
            except:
                logger.warning("⚠️  语言识别模型未找到: lang_ident_model_1")
                logger.info("💡 请确保已安装Elasticsearch的ingest-attachment和ml插件")
                
        except Exception as e:
            logger.warning(f"⚠️  无法检查语言识别模型: {e}")
    
    def _print_initialization_results(self, results: Dict[str, bool]):
        """打印初始化结果"""
        logger.info("\n📋 初始化结果:")
        logger.info("=" * 60)
        
        total_count = len(results)
        success_count = sum(1 for success in results.values() if success)
        
        for index_type, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            logger.info(f"  {index_type:15} : {status}")
        
        logger.info("=" * 60)
        logger.info(f"📊 总计: {success_count}/{total_count} 成功")
        
        if success_count == total_count:
            logger.info("🎉 所有索引初始化完成！")
        else:
            logger.warning(f"⚠️  {total_count - success_count} 个索引初始化失败")
    
    async def initialize_single_index(self, index_type: str, force_recreate: bool = False) -> bool:
        """初始化单个索引类型"""
        logger.info(f"🎯 初始化单个索引类型: {index_type}")
        
        try:
            _, unified_manager = await self._get_clients()
            
            if index_type == "posts":
                success = await unified_manager.posts_manager.initialize(force_recreate)
            elif index_type == "authors":
                success = await unified_manager.authors_manager.initialize(force_recreate)
            elif index_type == "memecoins":
                success = await unified_manager.memecoins_manager.initialize(force_recreate)
            elif index_type == "search_history":
                success = await unified_manager.search_history_manager.initialize(force_recreate)
            else:
                logger.error(f"❌ 不支持的索引类型: {index_type}")
                return False
            
            status = "✅ 成功" if success else "❌ 失败"
            logger.info(f"📊 {index_type} 索引初始化: {status}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 初始化 {index_type} 索引失败: {e}")
            return False
    
    async def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细的索引状态"""
        logger.info("📊 获取详细索引状态...")
        
        try:
            _, unified_manager = await self._get_clients()
            
            # 获取健康状态
            health_status = await unified_manager.get_health_status()
            
            # 格式化输出
            self._print_detailed_status(health_status)
            
            return health_status
            
        except Exception as e:
            logger.error(f"❌ 获取状态失败: {e}")
            return {}
    
    def _print_detailed_status(self, health_status: Dict[str, Any]):
        """打印详细状态"""
        logger.info("\n📈 详细索引状态:")
        logger.info("=" * 80)
        
        total_docs = 0
        total_size = 0
        
        for index_type, type_stats in health_status.items():
            logger.info(f"\n📁 {index_type.upper()}:")
            logger.info("-" * 40)
            
            type_docs = 0
            type_size = 0
            
            for index_name, stats in type_stats.items():
                docs_count = stats.get('docs_count', 0)
                store_size = stats.get('store_size', 0)
                health = stats.get('health', 'unknown')
                
                # 格式化大小
                size_mb = store_size / (1024 * 1024) if store_size > 0 else 0
                
                logger.info(f"  {index_name:20} : {docs_count:8,} docs, {size_mb:6.1f} MB, {health}")
                
                type_docs += docs_count
                type_size += store_size
            
            if type_stats:
                type_size_mb = type_size / (1024 * 1024) if type_size > 0 else 0
                logger.info(f"  {'TOTAL':20} : {type_docs:8,} docs, {type_size_mb:6.1f} MB")
            
            total_docs += type_docs
            total_size += type_size
        
        # 总计
        logger.info("=" * 80)
        total_size_mb = total_size / (1024 * 1024) if total_size > 0 else 0
        logger.info(f"📊 系统总计: {total_docs:,} 文档, {total_size_mb:.1f} MB")
        
        # 语言分布
        self._print_language_distribution(health_status)
    
    def _print_language_distribution(self, health_status: Dict[str, Any]):
        """打印语言分布统计"""
        logger.info("\n🌍 语言分布:")
        logger.info("-" * 40)
        
        language_stats = {}
        
        for index_type, type_stats in health_status.items():
            if index_type == 'search_history':
                continue  # 搜索历史不按语言分
                
            for index_name, stats in type_stats.items():
                # 从索引名称提取语言
                parts = index_name.split('_')
                if len(parts) >= 2:
                    language = parts[-1]
                    if language in SUPPORTED_LANGUAGES:
                        if language not in language_stats:
                            language_stats[language] = {'docs': 0, 'size': 0}
                        
                        language_stats[language]['docs'] += stats.get('docs_count', 0)
                        language_stats[language]['size'] += stats.get('store_size', 0)
        
        # 排序并显示
        sorted_languages = sorted(
            language_stats.items(), 
            key=lambda x: x[1]['docs'], 
            reverse=True
        )
        
        for language, stats in sorted_languages:
            docs = stats['docs']
            size_mb = stats['size'] / (1024 * 1024) if stats['size'] > 0 else 0
            logger.info(f"  {language:8} : {docs:8,} docs, {size_mb:6.1f} MB")
    
    async def delete_all_indices(self, confirm: bool = False) -> bool:
        """删除所有索引"""
        if not confirm:
            logger.warning("⚠️  这将删除所有Elasticsearch索引！")
            logger.warning("⚠️  请使用 --confirm 参数确认删除")
            return False
        
        logger.warning("🗑️  开始删除所有索引...")
        
        try:
            _, unified_manager = await self._get_clients()
            success = await unified_manager.delete_all_indices()
            
            if success:
                logger.info("✅ 所有索引删除完成")
            else:
                logger.error("❌ 删除索引失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 删除索引失败: {e}")
            return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Elasticsearch语言独立索引初始化工具')
    parser.add_argument(
        'action',
        choices=['init', 'init-single', 'status', 'health', 'delete-all'],
        help='执行的操作'
    )
    parser.add_argument(
        '--index-type',
        choices=['posts', 'authors', 'memecoins', 'search_history'],
        help='索引类型（用于init-single）'
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help='强制重建索引'
    )
    parser.add_argument(
        '--confirm',
        action='store_true',
        help='确认删除操作'
    )
    
    args = parser.parse_args()
    
    initializer = LanguageIndexInitializer()
    
    # 检查集群健康状态
    if not await initializer.check_cluster_health():
        logger.error("❌ 集群不健康，退出")
        sys.exit(1)
    
    # 执行操作
    if args.action == 'init':
        logger.info("🎯 执行完整初始化...")
        results = await initializer.initialize_all_indices(args.force)
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        if success_count == total_count:
            logger.info("🎉 初始化完成！")
            sys.exit(0)
        else:
            logger.error(f"❌ 初始化失败: {success_count}/{total_count}")
            sys.exit(1)
    
    elif args.action == 'init-single':
        if not args.index_type:
            logger.error("❌ --index-type 参数是必需的")
            sys.exit(1)
        
        success = await initializer.initialize_single_index(args.index_type, args.force)
        sys.exit(0 if success else 1)
    
    elif args.action == 'status':
        await initializer.get_detailed_status()
        
    elif args.action == 'health':
        # 健康检查已在开始时执行
        pass
        
    elif args.action == 'delete-all':
        success = await initializer.delete_all_indices(args.confirm)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断，退出")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        sys.exit(1) 