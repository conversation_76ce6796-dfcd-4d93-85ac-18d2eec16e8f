x-base-service: &base-service
  env_file:
    - /etc/api.env
  restart: unless-stopped
  networks:
    - toci-net
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "5"
      compress: "true"

x-api-service: &api-service
  <<: *base-service
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 10s
  command: ["start.sh"]

x-build-args: &build-args
  context: .
  args:
    BUILDKIT_INLINE_CACHE: 1

services:
  base-image:
    build:
      <<: *build-args
      dockerfile: Dockerfile.base
    image: gitlab.aurora:5050/toci/api/base:v1.1

  feed:
    image: gitlab.aurora:5050/toci/api/feed:${VERSION:-latest}
    container_name: feed
    build: 
      <<: *build-args
      dockerfile: src/feed/Dockerfile
      target: prod
    ports:
      - "8011:8000"
    <<: *api-service

  posts:
    image: gitlab.aurora:5050/toci/api/posts:${VERSION:-latest}
    container_name: posts
    build:
      <<: *build-args
      dockerfile: src/posts/Dockerfile
      target: prod
    ports:
      - "8012:8000"
    <<: *api-service

#  suggester:
#    image: gitlab.aurora:5050/toci/api/suggester:${VERSION:-latest}
#    container_name: suggester
#    build:
#      <<: *build-args
#      dockerfile: src/suggester/Dockerfile
#      target: prod
#    ports:
#      - "8013:8000"
#    <<: *api-service

  notifications:
    image: gitlab.aurora:5050/toci/api/notifications:${VERSION:-latest}
    container_name: notifications
    build:
      <<: *build-args
      dockerfile: src/notifications/Dockerfile
      target: prod
    ports:
      - "8014:8000"
    <<: *api-service

#  ai:
#    image: gitlab.aurora:5050/toci/api/ai:${VERSION:-latest}
#    container_name: ai
#    build:
#      <<: *build-args
#      dockerfile: src/ai/Dockerfile
#      target: prod
#    ports:
#      - "8015:8000"
#    <<: *api-service
#    env_file:
#      - /etc/ai.env

  im:
    image: gitlab.aurora:5050/toci/api/im:${VERSION:-latest}
    container_name: im
    build:
      <<: *build-args
      dockerfile: src/im/Dockerfile
      target: prod
    ports:
      - "8019:8000"
    <<: *api-service

#  mini_apps:
#    image: gitlab.aurora:5050/toci/api/mini_apps:${VERSION:-latest}
#    container_name: mini_apps
#    build:
#      <<: *build-args
#      dockerfile: src/mini_apps/Dockerfile
#      target: prod
#    ports:
#      - "8020:8000"
#    <<: *api-service

  tags:
    image: gitlab.aurora:5050/toci/api/tags:${VERSION:-latest}
    container_name: tags
    build:
      <<: *build-args
      dockerfile: src/tags/Dockerfile
      target: prod
    ports:
      - "8021:8000"
    <<: *api-service

  reports:
    image: gitlab.aurora:5050/toci/api/reports:${VERSION:-latest}
    container_name: reports
    build:
      <<: *build-args
      dockerfile: src/reports/Dockerfile
      target: prod
    ports:
      - "8022:8000"
    <<: *api-service

  authors:
    image: gitlab.aurora:5050/toci/api/authors:${VERSION:-latest}
    container_name: authors
    build:
      <<: *build-args
      dockerfile: src/authors/Dockerfile
      target: prod
    ports:
      - "8016:8000"
    <<: *api-service

  auth:
    image: gitlab.aurora:5050/toci/api/auth:${VERSION:-latest}
    container_name: auth
    build:
      <<: *build-args
      dockerfile: src/auth/Dockerfile
      target: prod
    ports:
      - "8017:8000"
    <<: *api-service
    volumes:
      - /etc/private.pem:/src/auth/private.pem

  admin:
    image: gitlab.aurora:5050/toci/api/admin:${VERSION:-latest}
    container_name: admin
    build:
      <<: *build-args
      dockerfile: src/admin/Dockerfile
      target: prod
    ports:
      - "8018:8000"
    <<: *api-service

  memecoin:
    image: gitlab.aurora:5050/toci/api/memecoin:${VERSION:-latest}
    container_name: memecoin
    build:
      <<: *build-args
      dockerfile: src/memecoin/Dockerfile
      target: prod
    ports:
      - "8023:8000"
    <<: *api-service
    ulimits:
      nofile:
        soft: 200000
        hard: 200000

  payments:
    image: gitlab.aurora:5050/toci/api/payments:${VERSION:-latest}
    container_name: payments
    build:
      <<: *build-args
      dockerfile: src/payments/Dockerfile
      target: prod
    ports:
      - "8029:8000"
    <<: *api-service

  search:
    image: gitlab.aurora:5050/toci/api/search:${VERSION:-latest}
    container_name: search
    build: 
      <<: *build-args
      dockerfile: src/search/Dockerfile
      target: prod
    ports:
      - "8025:8000"
    <<: *api-service

  media_core:
    image: gitlab.aurora:5050/toci/api/media_core:${VERSION:-latest}
    container_name: media_core
    build:
      <<: *build-args
      dockerfile: src/media_core/Dockerfile
      target: prod
    ports:
      - "8024:8000"
    <<: *api-service

  feedback:
    image: gitlab.aurora:5050/toci/api/feedback:${VERSION:-latest}
    container_name: feedback
    build:
      <<: *build-args
      dockerfile: src/feedback/Dockerfile
      target: prod
    ports:
      - "8026:8000"
    <<: *api-service

  agora:
    image: gitlab.aurora:5050/toci/api/agora:${VERSION:-latest}
    container_name: agora
    build:
      <<: *build-args
      dockerfile: src/agora/Dockerfile
      target: prod
    ports:
      - "8027:8000"
    <<: *api-service

  worker:
    image: gitlab.aurora:5050/toci/api/worker:${VERSION:-latest}
    container_name: worker
    build:
      <<: *build-args
      dockerfile: src/worker/Dockerfile
      target: prod
    command: celery -A src.worker.celery_app worker --loglevel=info
    <<: *base-service
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G

  worker_beat:
    image: gitlab.aurora:5050/toci/api/worker:${VERSION:-latest}
    container_name: worker_beat
    command: celery -A src.worker.celery_app beat --loglevel=info
    <<: *base-service

  analytics:
    image: gitlab.aurora:5050/toci/api/analytics:${VERSION:-latest}
    container_name: analytics
    build:
      <<: *build-args
      dockerfile: src/analytics/Dockerfile
      target: prod
    ports:
      - "8030:8000"
    <<: *api-service

#  Dev/Prod cloudflared should be run in different tokens
#  cloudflared:
#    image: cloudflare/cloudflared:latest
#    container_name: cloudflared
#    command: tunnel --no-autoupdate run --token eyJhIjoiMWY2Y2U4YjgwYjY5OTNlMjhhZTYwYWZmOWVmNjYyNTYiLCJ0IjoiOGJhODQ2YmEtMjQ3YS00ZGJkLWIyYWEtYzFlMWM1Nzg2MDgwIiwicyI6IlpEazNNREptT0RrdE9EWm1OUzAwWm1abUxXSXpNelF0TldNMk5qVXlNbVpsWlRKbSJ9

networks:
  toci-net:
    external: true
