stages:
  - build-base
  - build
  - deploy

variables:
  VERSION: ${CI_COMMIT_SHA:0:8}
  BASE_TAG: v1.1
  # Enable Docker BuildKit for faster builds
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

build_base_image:
  stage: build-base
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "master"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "staging"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG
  image: docker:latest
  services:
    - docker:dind
  variables:
    TAG: $CI_REGISTRY/toci/api/base:$BASE_TAG
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker pull $TAG || true
    - docker build -f ./Dockerfile.base --cache-from $TAG --tag $TAG .
    - docker push $TAG


.build:
  stage: build
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "master"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "staging"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG == "test"
    - if: $CI_COMMIT_BRANCH == "test_ci"
    - if: $CI_COMMIT_TAG
  image: docker:latest
  services:
    - docker:dind
  variables:
    TAG: $CI_REGISTRY/toci/api/$SERVICE:v${VERSION}
    LATEST_TAG: $CI_REGISTRY/toci/api/$SERVICE:latest
    BASE_TAG_VAR: $CI_REGISTRY/toci/api/base:$BASE_TAG
    DOCKER_BUILDKIT: 1
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Pull images for cache
    - docker pull $BASE_TAG_VAR || echo "Base image not found"
    - docker pull $LATEST_TAG || true
    # Build with optimizations
    - docker build -f ./src/$SERVICE/Dockerfile --build-arg BASE_IMAGE=$BASE_TAG_VAR --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $LATEST_TAG --cache-from $BASE_TAG_VAR --target prod --tag $TAG --tag $LATEST_TAG .
    - docker push $TAG
    - docker push $LATEST_TAG
  needs:
    - build_base_image


build_feed:
  variables:
    SERVICE: feed
  extends: .build

build_posts:
  variables:
    SERVICE: posts
  extends: .build

#build_suggester:
#  variables:
#    SERVICE: suggester
#  extends: .build

build_notifications:
  variables:
    SERVICE: notifications
  extends: .build

#build_ai:
#  variables:
#    SERVICE: ai
#  extends: .build

build_authors:
  variables:
    SERVICE: authors
  extends: .build

build_auth:
  variables:
    SERVICE: auth
  extends: .build

build_admin:
  variables:
    SERVICE: admin
  extends: .build

build_im:
  variables:
    SERVICE: im
  extends: .build

#build_mini_apps:
#  variables:
#    SERVICE: mini_apps
#  extends: .build

build_tags:
  variables:
    SERVICE: tags
  extends: .build

build_reports:
  variables:
    SERVICE: reports
  extends: .build

build_memecoin:
  variables:
    SERVICE: memecoin
  extends: .build

build_payments:
  variables:
    SERVICE: payments
  extends: .build

build_search:
  variables:
    SERVICE: search
  extends: .build

build_memetracker:
  variables:
    SERVICE: memetracker
  extends: .build

build_memeprice:
  variables:
    SERVICE: memeprice
  extends: .build

build_memekline:
  variables:
    SERVICE: memekline
  extends: .build

build_memekafka:
  variables:
    SERVICE: memekafka
  extends: .build

build_media_core:
  variables:
    SERVICE: media_core
  extends: .build

build_feedback:
  variables:
    SERVICE: feedback
  extends: .build

build_worker:
  variables:
    SERVICE: worker
  extends: .build

build_agora:
  variables:
    SERVICE: agora
  extends: .build

build_analytics:
  variables:
    SERVICE: analytics
  extends: .build

.deploy: &deploy
  stage: deploy
  image: docker:latest
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo "Checking env variables..."
    - if [ -z "$TARGET_HOST" ]; then echo "错误：TARGET_HOST为空"; exit 1; fi
    - if [ -z "$TARGET_HOST_USER" ]; then echo "错误：TARGET_HOST_USER为空"; exit 1; fi
    - echo "target host：$TARGET_HOST"
    - echo "target user：$TARGET_HOST_USER"
    - echo "trying to get access key"
    - ssh-keyscan -H "$TARGET_HOST" >> ~/.ssh/known_hosts || (echo "无法获取主机密钥"; exit 1)
    - echo "SSH setup completed，testing connection..."
    - ssh -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$TARGET_HOST_USER@$TARGET_HOST" "echo SSH连接成功" || (echo "SSH连接失败"; exit 1)
  script:
    - DOCKER_HOST="$TARGET_HOST_USER@$TARGET_HOST"
    # Optimized deployment: only transfer compose files
    - scp docker-compose*.yml $DOCKER_HOST:~/
    - |
      ssh $DOCKER_HOST "
        cd ~/ &&
        docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY &&
        echo 'Pulling new images in parallel...' &&
        VERSION=${VERSION} docker compose $DOCKER_COMPOSE_FILE pull --parallel &&
        echo 'Stopping old containers...' &&
        docker compose $DOCKER_COMPOSE_FILE down --rmi all &&
        echo 'Starting new containers...' &&
        VERSION=${VERSION} docker compose $DOCKER_COMPOSE_FILE up -d --no-build &&
        echo 'Cleaning up old images...' &&
        docker image prune -f"

deploy_dev:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_COMMIT_BRANCH == "develop"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"
  variables:
    VERSION: ${CI_COMMIT_SHA:0:8}
    TARGET_HOST: toci-dev-01.aurora
    TARGET_HOST_USER: ubuntu
    DOCKER_COMPOSE_FILE: -f docker-compose.yml
  <<: *deploy

deploy_dev_kline:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_COMMIT_BRANCH == "develop"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"
      when: manual
  variables:
    VERSION: ${CI_COMMIT_SHA:0:8}
    TARGET_HOST: toci-dev-01.aurora
    TARGET_HOST_USER: ubuntu
    DOCKER_COMPOSE_FILE: -f docker-compose-kline.yml
  <<: *deploy

deploy_staging:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_COMMIT_BRANCH == "staging"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "staging"
  variables:
    VERSION: ${CI_COMMIT_SHA:0:8}
    TARGET_HOST: toci-staging-aws-01.aurora
    TARGET_HOST_USER: ubuntu
    DOCKER_COMPOSE_FILE: -f docker-compose.yml
  <<: *deploy

deploy_prod_api:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_COMMIT_BRANCH == "master"
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_TAG
  variables:
    VERSION: ${CI_COMMIT_SHA:0:8}
    TARGET_HOST: memefans-prod-sg-01.aurora
    TARGET_HOST_USER: ubuntu
    DOCKER_COMPOSE_FILE: -f docker-compose.yml
  <<: *deploy

deploy_prod_kline:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_COMMIT_BRANCH == "master"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "master"
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual
  variables:
    VERSION: ${CI_COMMIT_SHA:0:8}
    TARGET_HOST: memefans-kline-prod-sg-01.aurora
    TARGET_HOST_USER: ubuntu
    DOCKER_COMPOSE_FILE: -f docker-compose-kline.yml
  <<: *deploy
