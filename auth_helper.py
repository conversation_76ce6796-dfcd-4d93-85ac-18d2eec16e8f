#!/usr/bin/env python3
"""
Authentication Helper for Load Testing

This utility helps generate and manage authentication tokens for load testing.
It can login users and extract tokens for use in the load testing script.
"""

import asyncio
import aiohttp
import argparse
import json
import sys
from typing import List, Dict, Optional


class AuthHelper:
    """Helper class for managing authentication tokens"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def login_user(self, email: str, password: str) -> Optional[str]:
        """
        Login a user and extract the authentication token
        
        Args:
            email: User email
            password: User password
            
        Returns:
            Authentication token if successful, None otherwise
        """
        login_url = f"{self.base_url}/login"
        
        login_data = {
            "username": email,  # FastAPI Users typically uses 'username' field
            "password": password
        }
        
        try:
            async with self.session.post(login_url, data=login_data) as response:
                if response.status == 200:
                    result = await response.json()
                    # Extract token from response
                    token = result.get('access_token')
                    if token:
                        print(f"✓ Successfully logged in user: {email}")
                        return token
                    else:
                        print(f"✗ Login successful but no token found for: {email}")
                        return None
                else:
                    error_text = await response.text()
                    print(f"✗ Login failed for {email}: HTTP {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            print(f"✗ Login error for {email}: {str(e)}")
            return None
    
    async def verify_token(self, token: str) -> bool:
        """
        Verify that a token is valid by making a test request
        
        Args:
            token: Authentication token to verify
            
        Returns:
            True if token is valid, False otherwise
        """
        # Try to access a protected endpoint to verify the token
        test_url = f"{self.base_url}/recommendations"
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            async with self.session.get(test_url, headers=headers) as response:
                if response.status in [200, 401]:  # 401 means token was processed but might be expired
                    return response.status == 200
                else:
                    return False
        except Exception:
            return False
    
    async def batch_login(self, credentials: List[Dict[str, str]]) -> List[str]:
        """
        Login multiple users and collect their tokens
        
        Args:
            credentials: List of dicts with 'email' and 'password' keys
            
        Returns:
            List of valid authentication tokens
        """
        tokens = []
        
        for cred in credentials:
            email = cred.get('email')
            password = cred.get('password')
            
            if not email or not password:
                print(f"✗ Skipping invalid credentials: {cred}")
                continue
            
            token = await self.login_user(email, password)
            if token:
                # Verify the token works
                if await self.verify_token(token):
                    tokens.append(token)
                    print(f"✓ Token verified for: {email}")
                else:
                    print(f"✗ Token verification failed for: {email}")
        
        return tokens


def load_credentials_from_file(file_path: str) -> List[Dict[str, str]]:
    """Load user credentials from JSON file"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Support both array of credentials and single credential object
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and 'users' in data:
            return data['users']
        else:
            return [data]  # Single credential object
    
    except FileNotFoundError:
        print(f"Credentials file {file_path} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in credentials file: {e}")
        return []


def save_tokens_to_file(tokens: List[str], output_file: str):
    """Save tokens to a JSON file"""
    token_data = {
        "auth_tokens": tokens,
        "generated_at": asyncio.get_event_loop().time(),
        "count": len(tokens)
    }
    
    with open(output_file, 'w') as f:
        json.dump(token_data, f, indent=2)
    
    print(f"✓ Saved {len(tokens)} tokens to {output_file}")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Authentication helper for load testing")
    
    parser.add_argument("--base-url", default="http://localhost:8000", 
                       help="Base URL of the API")
    parser.add_argument("--credentials-file", 
                       help="JSON file containing user credentials")
    parser.add_argument("--email", help="Single user email for login")
    parser.add_argument("--password", help="Single user password for login")
    parser.add_argument("--verify-token", help="Verify a specific token")
    parser.add_argument("--output", default="auth_tokens.json", 
                       help="Output file for generated tokens")
    
    args = parser.parse_args()
    
    async with AuthHelper(args.base_url) as auth_helper:
        
        # Verify a single token
        if args.verify_token:
            is_valid = await auth_helper.verify_token(args.verify_token)
            if is_valid:
                print("✓ Token is valid")
                sys.exit(0)
            else:
                print("✗ Token is invalid or expired")
                sys.exit(1)
        
        # Single user login
        if args.email and args.password:
            token = await auth_helper.login_user(args.email, args.password)
            if token:
                save_tokens_to_file([token], args.output)
                print(f"Token: {token}")
            else:
                print("Failed to obtain token")
                sys.exit(1)
            return
        
        # Batch login from file
        if args.credentials_file:
            credentials = load_credentials_from_file(args.credentials_file)
            if not credentials:
                print("No valid credentials found")
                sys.exit(1)
            
            print(f"Logging in {len(credentials)} users...")
            tokens = await auth_helper.batch_login(credentials)
            
            if tokens:
                save_tokens_to_file(tokens, args.output)
                print(f"\nSuccessfully generated {len(tokens)} tokens")
                for i, token in enumerate(tokens, 1):
                    print(f"Token {i}: {token}")
            else:
                print("No valid tokens generated")
                sys.exit(1)
            return
        
        # No action specified
        print("Please specify an action:")
        print("  --email and --password for single user login")
        print("  --credentials-file for batch login")
        print("  --verify-token to verify a token")
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())
