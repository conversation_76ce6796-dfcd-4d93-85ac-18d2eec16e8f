services:
  mailhog:
    image: mailhog/mailhog
    container_name: mailhog
    logging:
      driver: "none"
    ports:
      - "1025:1025"
      - "8025:8025"

  feed:
    image: feed
    build:
      context: .
      dockerfile: src/feed/Dockerfile

  posts:
    image: posts
    build:
      context: .
      dockerfile: src/posts/Dockerfile

#  suggester:
#    image: suggester
#    build:
#      context: .
#      dockerfile: src/suggester/Dockerfile

  notifications:
    image: notifications
    build:
      context: .
      dockerfile: src/notifications/Dockerfile

#  ai:
#    image: ai
#    build:
#      context: .
#      dockerfile: src/ai/Dockerfile

  im:
    image: im
    build:
      context: .
      dockerfile: src/im/Dockerfile

#  mini_apps:
#    image: mini_apps
#    build:
#      context: .
#      dockerfile: src/mini_apps/Dockerfile

  tags:
    image: tags
    build:
      context: .
      dockerfile: src/tags/Dockerfile

  reports:
    image: reports
    build:
      context: .
      dockerfile: src/reports/Dockerfile

  authors:
    image: authors
    build:
      context: .
      dockerfile: src/authors/Dockerfile

  auth:
    image: auth
    build:
      context: .
      dockerfile: src/auth/Dockerfile

  admin:
    image: admin
    build:
      context: .
      dockerfile: src/admin/Dockerfile

  memecoin:
    image: memecoin
    build:
      context: .
      dockerfile: src/memecoin/Dockerfile

  payments:
    image: payments
    build:
      context: .
      dockerfile: src/payments/Dockerfile

  search:
    image: search
    build:
      context: .
      dockerfile: src/search/Dockerfile

  memetracker:
    image: memetracker
    build:
      context: .
      dockerfile: src/memetracker/Dockerfile

  memeprice:
    image: memeprice
    build:
      context: .
      dockerfile: src/memeprice/Dockerfile

  memekline:
    image: memekline
    build:
      context: .
      dockerfile: src/memekline/Dockerfile

  memekafka:
    image: memekafka
    build:
      context: .
      dockerfile: src/memekafka/Dockerfile

  media_core:
    image: media_core
    build:
      context: .
      dockerfile: src/media_core/Dockerfile

  feedback:
    image: feedback
    build:
      context: .
      dockerfile: src/feedback/Dockerfile

  worker:
    image: worker
    build:
      context: .
      dockerfile: src/worker/Dockerfile

  worker_beat:
    image: worker_beat
    build:
      context: .
      dockerfile: src/worker/Dockerfile

  agora:
    image: agora
    build:
      context: .
      dockerfile: src/agora/Dockerfile