# Recommendation API Load Testing Script

A comprehensive load testing script for the recommendation API endpoint that supports both authenticated and anonymous users with configurable concurrency and performance metrics collection.

## Features

- **Dual User Support**: Test both authenticated and anonymous user scenarios
- **Configurable Concurrency**: Control the number of concurrent users and request rates
- **Performance Metrics**: Detailed response time analysis, throughput measurement, and success rates
- **Error Handling**: Comprehensive error tracking and reporting
- **Flexible Configuration**: Command-line arguments or JSON configuration file
- **Detailed Reporting**: Console output and JSON results file

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements_load_test.txt
```

2. Make the script executable:
```bash
chmod +x load_test_recommendations.py
```

## Quick Start

### Basic Usage

Test with default settings (10 concurrent users, 100 requests per user type):
```bash
python load_test_recommendations.py
```

### With Custom Parameters

```bash
python load_test_recommendations.py \
  --base-url http://localhost:8000 \
  --concurrent-users 50 \
  --total-requests 500 \
  --requests-per-second 25
```

### Using Configuration File

1. Edit the `load_test_config.json` file with your settings
2. Run with configuration:
```bash
python load_test_recommendations.py --config load_test_config.json
```

### Authenticated Testing

To test authenticated users, provide authentication tokens:
```bash
python load_test_recommendations.py \
  --auth-tokens "token1" "token2" "token3" \
  --concurrent-users 20 \
  --total-requests 200
```

## Configuration Options

### Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--config` | Path to JSON configuration file | None |
| `--base-url` | Base URL of the API | http://localhost:8000 |
| `--endpoint` | API endpoint to test | /recommendations |
| `--concurrent-users` | Number of concurrent users | 10 |
| `--total-requests` | Total requests per user type | 100 |
| `--requests-per-second` | Target requests per second | 10 |
| `--auth-tokens` | List of authentication tokens | None |
| `--no-anonymous` | Skip anonymous user testing | False |
| `--no-authenticated` | Skip authenticated user testing | False |
| `--post-types` | Post types to include in requests | None |
| `--regions` | Regions to include in requests | None |
| `--page-size` | Page size for requests | 20 |
| `--output` | Output file for results | load_test_results.json |
| `--verbose` | Enable verbose logging | False |

### Configuration File Format

```json
{
  "base_url": "http://localhost:8000",
  "endpoint": "/recommendations",
  "concurrent_users": 20,
  "total_requests_per_user_type": 200,
  "requests_per_second": 15,
  "auth_tokens": ["token1", "token2", "token3"],
  "test_anonymous": true,
  "test_authenticated": true,
  "post_types": ["text", "image", "video"],
  "regions": ["US", "EU"],
  "page_size": 20,
  "output_file": "load_test_results.json",
  "verbose": true
}
```

## Authentication Setup

### Getting Authentication Tokens

1. **Manual Token Extraction**: 
   - Login to your application
   - Open browser developer tools
   - Check the Authorization header in network requests
   - Extract the Bearer token

2. **API Login** (if available):
   ```bash
   curl -X POST http://localhost:8000/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password"}'
   ```

3. **Update Configuration**:
   - Add tokens to `load_test_config.json`
   - Or pass via command line: `--auth-tokens "token1" "token2"`

## Test Scenarios

### Anonymous Users
- Tests unauthenticated requests to the recommendation endpoint
- Simulates users browsing without logging in
- Measures baseline performance

### Authenticated Users
- Tests requests with valid authentication tokens
- Simulates logged-in user behavior
- Compares performance with anonymous users

## Performance Metrics

The script collects and reports the following metrics:

### Response Time Metrics
- Average response time
- Median response time
- 95th percentile response time
- 99th percentile response time
- Minimum and maximum response times

### Throughput Metrics
- Requests per second (RPS)
- Total requests processed
- Success rate percentage

### Error Analysis
- Failed request count
- Error categorization by status code
- Error message details

## Output

### Console Output
```
================================================================================
LOAD TEST RESULTS SUMMARY
================================================================================
Test Duration: 45.23 seconds
Total Requests: 400
Successful Requests: 395
Failed Requests: 5
Overall Success Rate: 98.75%
Overall Throughput: 8.84 RPS

----------------------------------------
RESPONSE TIME METRICS (Overall)
----------------------------------------
Average: 245.67 ms
Median: 198.34 ms
95th Percentile: 456.78 ms
99th Percentile: 678.90 ms
Min: 89.12 ms
Max: 1234.56 ms
```

### JSON Results File
Detailed results are saved to a JSON file containing:
- Individual request results
- Aggregated metrics by user type
- Error details
- Test configuration

## Best Practices

### Load Testing Guidelines

1. **Start Small**: Begin with low concurrency and gradually increase
2. **Monitor Resources**: Watch server CPU, memory, and database performance
3. **Test Realistic Scenarios**: Use actual user patterns and data
4. **Baseline Testing**: Establish performance baselines before optimization

### Recommended Test Scenarios

1. **Smoke Test**: 5 concurrent users, 50 requests
2. **Load Test**: 20 concurrent users, 200 requests
3. **Stress Test**: 50+ concurrent users, 500+ requests
4. **Spike Test**: Sudden increase in concurrent users

### Performance Targets

- **Response Time**: < 200ms average, < 500ms 95th percentile
- **Throughput**: Depends on requirements (e.g., 100+ RPS)
- **Success Rate**: > 99.5%
- **Error Rate**: < 0.5%

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Check if the API server is running
   - Verify the base URL is correct
   - Ensure network connectivity

2. **Authentication Failures**:
   - Verify tokens are valid and not expired
   - Check token format (Bearer prefix handled automatically)
   - Ensure user accounts are active

3. **Rate Limiting**:
   - Reduce requests per second
   - Implement proper delays between requests
   - Check API rate limiting policies

4. **High Error Rates**:
   - Monitor server resources
   - Check database connections
   - Review application logs

### Debug Mode

Enable verbose logging for detailed information:
```bash
python load_test_recommendations.py --verbose
```

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: Load Test
on: [push, pull_request]

jobs:
  load-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements_load_test.txt
      - name: Run load test
        run: |
          python load_test_recommendations.py \
            --concurrent-users 10 \
            --total-requests 100 \
            --base-url ${{ secrets.API_BASE_URL }}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This load testing script is provided as-is for testing purposes. Ensure you have permission to test the target API endpoints.
