.PHONY: test test-authors test-cov clean

# Python path
PYTHONPATH := .

# Test commands
test:
	PYTHONPATH=$(PYTHONPATH) pytest tests/ -v -n auto

test-authors:
	PYTHONPATH=$(PYTHONPATH) pytest tests/test_authors.py --cov=src.authors.main --cov-report=term-missing -v -n auto

test-feed:
	PYTHONPATH=$(PYTHONPATH) pytest tests/test_feed.py --cov=src.feed.main --cov-report=term-missing -v -n auto

test-posts:
	PYTHONPATH=$(PYTHONPATH) pytest tests/test_posts.py --cov=src.posts.main --cov-report=term-missing -v -n auto

test-memecoin:
	PYTHONPATH=$(PYTHONPATH) pytest tests/test_memecoin.py --cov=src.memecoin.main --cov-report=term-missing -v -n auto

test-collections:
	PYTHONPATH=$(PYTHONPATH) pytest tests/test_collections.py --cov=src.posts.collections.router --cov-report=term-missing -v -n auto
# Clean up
clean:
	find . -type d -name "__pycache__" -exec rm -r {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -r {} +
	find . -type d -name "*.egg" -exec rm -r {} +
	find . -type d -name ".pytest_cache" -exec rm -r {} +
	find . -type d -name ".coverage" -exec rm -r {} +
	find . -type d -name "htmlcov" -exec rm -r {} +
	find . -type d -name "dist" -exec rm -r {} +
	find . -type d -name "build" -exec rm -r {} +

# Help command
help:
	@echo "Available commands:"
	@echo "  make test              - Run all tests"
	@echo "  make test-authors      - Run only author tests"
	@echo "  make clean             - Clean up cache files"
	@echo "  make help              - Show this help message" 