# Multi-stage build for optimized base image with ALL common patterns
# This base image includes everything that's common across all services

# Arguments for flexibility
ARG PYTHON_VERSION=3.10.12

# STAGE 1: Builder base with all build tools
FROM python:${PYTHON_VERSION}-slim AS builder-base

# Install build dependencies and uv for faster package installation
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && pip install --no-cache-dir uv \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# STAGE 2: Builder with common dependencies
FROM builder-base AS builder

# Copy and install common requirements
COPY src/common/requirements.txt /tmp/common-requirements.txt
COPY src/auth/requirements.txt /tmp/auth-requirements.txt
RUN uv pip install --no-cache -r /tmp/common-requirements.txt && \
    uv pip install --no-cache -r /tmp/auth-requirements.txt

# STAGE 3: Runtime base with all common setup
FROM python:${PYTHON_VERSION}-slim AS runtime

# Install only runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy virtual environment from builder with correct ownership
COPY --from=builder --chown=appuser:appuser /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set environment variables
ENV PYTHONPATH="$PYTHONPATH:/" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Set working directory and ensure correct permissions
WORKDIR /src
RUN chown -R appuser:appuser /src && \
    chown -R appuser:appuser /opt/venv

# Switch to non-root user
USER appuser

# Clean up Python cache
RUN find /opt/venv -type f -name "*.pyc" -delete 2>/dev/null || true && \
    find /opt/venv -type d -name "__pycache__" -delete 2>/dev/null || true

# Add default health check (services can override if needed)
HEALTHCHECK --interval=30s --timeout=10s --retries=3 --start-period=10s \
    CMD curl -f http://localhost:8000/health || exit 1

# Copy common code that ALL services need
COPY --chown=appuser:appuser src/common /src/common
COPY --chown=appuser:appuser src/database /src/database
COPY --chown=appuser:appuser src/auth /src/auth 
COPY --chown=appuser:appuser src/infra /src/infra 
