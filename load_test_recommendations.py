#!/usr/bin/env python3
"""
Load Testing Script for Recommendation API Endpoint

This script performs load testing on the /recommendations endpoint with support for:
- Authenticated and anonymous users
- Configurable concurrency and request rates
- Performance metrics collection
- Detailed reporting

Usage:
    python load_test_recommendations.py --help
    python load_test_recommendations.py --config config.json
    python load_test_recommendations.py --concurrent-users 50 --total-requests 1000
"""

import asyncio
import aiohttp
import argparse
import json
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import sys


@dataclass
class TestConfig:
    """Configuration for load testing"""
    base_url: str = "http://localhost:8000"
    endpoint: str = "/recommendations"
    
    # Concurrency settings
    concurrent_users: int = 10
    total_requests_per_user_type: int = 100
    requests_per_second: int = 10
    
    # Authentication settings
    auth_tokens: List[str] = None
    test_anonymous: bool = True
    test_authenticated: bool = True
    
    # Request parameters
    post_types: List[str] = None
    regions: List[str] = None
    page_size: int = 20
    
    # Output settings
    output_file: str = "load_test_results.json"
    verbose: bool = False


@dataclass
class RequestResult:
    """Result of a single request"""
    timestamp: float
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    user_type: str = "anonymous"
    response_size: int = 0


@dataclass
class TestResults:
    """Aggregated test results"""
    start_time: datetime
    end_time: datetime
    total_duration: float
    
    # Request statistics
    total_requests: int
    successful_requests: int
    failed_requests: int
    
    # Performance metrics by user type
    authenticated_metrics: Dict[str, Any]
    anonymous_metrics: Dict[str, Any]
    
    # Overall metrics
    overall_metrics: Dict[str, Any]
    
    # Error details
    errors: List[Dict[str, Any]]


class LoadTester:
    """Main load testing class"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.results: List[RequestResult] = []
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore: Optional[asyncio.Semaphore] = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO if config.verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(limit=self.config.concurrent_users * 2)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        self.semaphore = asyncio.Semaphore(self.config.concurrent_users)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, user_type: str, auth_token: Optional[str] = None) -> RequestResult:
        """Make a single request to the recommendations endpoint"""
        async with self.semaphore:
            start_time = time.time()
            
            # Prepare headers
            headers = {"Content-Type": "application/json"}
            if auth_token:
                headers["Authorization"] = f"Bearer {auth_token}"
            
            # Prepare query parameters
            params = {
                "page": 1,
                "size": self.config.page_size
            }
            
            if self.config.post_types:
                for post_type in self.config.post_types:
                    params[f"post_type"] = post_type
            
            if self.config.regions:
                params["region"] = self.config.regions[0]  # Use first region
            
            url = f"{self.config.base_url}{self.config.endpoint}"
            
            try:
                async with self.session.get(url, headers=headers, params=params) as response:
                    response_time = time.time() - start_time
                    
                    # Read response content to measure size
                    content = await response.read()
                    response_size = len(content)
                    
                    success = 200 <= response.status < 300
                    error_message = None if success else f"HTTP {response.status}"
                    
                    return RequestResult(
                        timestamp=start_time,
                        response_time=response_time,
                        status_code=response.status,
                        success=success,
                        error_message=error_message,
                        user_type=user_type,
                        response_size=response_size
                    )
            
            except Exception as e:
                response_time = time.time() - start_time
                return RequestResult(
                    timestamp=start_time,
                    response_time=response_time,
                    status_code=0,
                    success=False,
                    error_message=str(e),
                    user_type=user_type,
                    response_size=0
                )
    
    async def run_user_scenario(self, user_type: str, auth_token: Optional[str] = None):
        """Run load test scenario for a specific user type"""
        self.logger.info(f"Starting {user_type} user scenario with {self.config.total_requests_per_user_type} requests")
        
        # Calculate delay between requests to achieve target RPS
        delay_between_requests = 1.0 / self.config.requests_per_second if self.config.requests_per_second > 0 else 0
        
        tasks = []
        for i in range(self.config.total_requests_per_user_type):
            # Add delay to control request rate
            if i > 0 and delay_between_requests > 0:
                await asyncio.sleep(delay_between_requests)
            
            task = asyncio.create_task(self.make_request(user_type, auth_token))
            tasks.append(task)
        
        # Wait for all requests to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, RequestResult):
                self.results.append(result)
            else:
                self.logger.error(f"Request failed with exception: {result}")
    
    async def run_load_test(self) -> TestResults:
        """Run the complete load test"""
        start_time = datetime.now()
        self.logger.info(f"Starting load test at {start_time}")
        
        scenarios = []
        
        # Add anonymous user scenario
        if self.config.test_anonymous:
            scenarios.append(self.run_user_scenario("anonymous"))
        
        # Add authenticated user scenarios
        if self.config.test_authenticated and self.config.auth_tokens:
            for i, token in enumerate(self.config.auth_tokens):
                scenarios.append(self.run_user_scenario(f"authenticated_{i}", token))
        
        # Run all scenarios concurrently
        await asyncio.gather(*scenarios)
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        self.logger.info(f"Load test completed in {total_duration:.2f} seconds")
        
        return self._analyze_results(start_time, end_time, total_duration)
    
    def _analyze_results(self, start_time: datetime, end_time: datetime, total_duration: float) -> TestResults:
        """Analyze test results and generate metrics"""
        
        # Separate results by user type
        authenticated_results = [r for r in self.results if r.user_type.startswith("authenticated")]
        anonymous_results = [r for r in self.results if r.user_type == "anonymous"]
        
        # Calculate metrics for each user type
        authenticated_metrics = self._calculate_metrics(authenticated_results) if authenticated_results else {}
        anonymous_metrics = self._calculate_metrics(anonymous_results) if anonymous_results else {}
        overall_metrics = self._calculate_metrics(self.results)
        
        # Collect error details
        errors = []
        for result in self.results:
            if not result.success:
                errors.append({
                    "timestamp": result.timestamp,
                    "user_type": result.user_type,
                    "status_code": result.status_code,
                    "error_message": result.error_message
                })
        
        return TestResults(
            start_time=start_time,
            end_time=end_time,
            total_duration=total_duration,
            total_requests=len(self.results),
            successful_requests=sum(1 for r in self.results if r.success),
            failed_requests=sum(1 for r in self.results if not r.success),
            authenticated_metrics=authenticated_metrics,
            anonymous_metrics=anonymous_metrics,
            overall_metrics=overall_metrics,
            errors=errors
        )
    
    def _calculate_metrics(self, results: List[RequestResult]) -> Dict[str, Any]:
        """Calculate performance metrics for a set of results"""
        if not results:
            return {}
        
        response_times = [r.response_time for r in results]
        successful_results = [r for r in results if r.success]
        
        # Calculate throughput (requests per second)
        if results:
            time_span = max(r.timestamp for r in results) - min(r.timestamp for r in results)
            throughput = len(results) / max(time_span, 1)  # Avoid division by zero
        else:
            throughput = 0
        
        return {
            "total_requests": len(results),
            "successful_requests": len(successful_results),
            "failed_requests": len(results) - len(successful_results),
            "success_rate": len(successful_results) / len(results) * 100 if results else 0,
            "avg_response_time": statistics.mean(response_times) if response_times else 0,
            "min_response_time": min(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "median_response_time": statistics.median(response_times) if response_times else 0,
            "p95_response_time": self._percentile(response_times, 95) if response_times else 0,
            "p99_response_time": self._percentile(response_times, 99) if response_times else 0,
            "throughput_rps": throughput,
            "avg_response_size": statistics.mean([r.response_size for r in results]) if results else 0
        }
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of a dataset"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


def load_config_from_file(config_file: str) -> TestConfig:
    """Load configuration from JSON file"""
    try:
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        return TestConfig(**config_data)
    except FileNotFoundError:
        print(f"Configuration file {config_file} not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in configuration file: {e}")
        sys.exit(1)


def save_results(results: TestResults, output_file: str):
    """Save test results to JSON file"""
    results_dict = asdict(results)
    
    # Convert datetime objects to strings
    results_dict["start_time"] = results.start_time.isoformat()
    results_dict["end_time"] = results.end_time.isoformat()
    
    with open(output_file, 'w') as f:
        json.dump(results_dict, f, indent=2)


def print_results_summary(results: TestResults):
    """Print a summary of test results to console"""
    print("\n" + "="*80)
    print("LOAD TEST RESULTS SUMMARY")
    print("="*80)
    
    print(f"Test Duration: {results.total_duration:.2f} seconds")
    print(f"Total Requests: {results.total_requests}")
    print(f"Successful Requests: {results.successful_requests}")
    print(f"Failed Requests: {results.failed_requests}")
    print(f"Overall Success Rate: {results.overall_metrics.get('success_rate', 0):.2f}%")
    print(f"Overall Throughput: {results.overall_metrics.get('throughput_rps', 0):.2f} RPS")
    
    print("\n" + "-"*40)
    print("RESPONSE TIME METRICS (Overall)")
    print("-"*40)
    print(f"Average: {results.overall_metrics.get('avg_response_time', 0)*1000:.2f} ms")
    print(f"Median: {results.overall_metrics.get('median_response_time', 0)*1000:.2f} ms")
    print(f"95th Percentile: {results.overall_metrics.get('p95_response_time', 0)*1000:.2f} ms")
    print(f"99th Percentile: {results.overall_metrics.get('p99_response_time', 0)*1000:.2f} ms")
    print(f"Min: {results.overall_metrics.get('min_response_time', 0)*1000:.2f} ms")
    print(f"Max: {results.overall_metrics.get('max_response_time', 0)*1000:.2f} ms")
    
    # Anonymous user metrics
    if results.anonymous_metrics:
        print("\n" + "-"*40)
        print("ANONYMOUS USER METRICS")
        print("-"*40)
        print(f"Requests: {results.anonymous_metrics.get('total_requests', 0)}")
        print(f"Success Rate: {results.anonymous_metrics.get('success_rate', 0):.2f}%")
        print(f"Avg Response Time: {results.anonymous_metrics.get('avg_response_time', 0)*1000:.2f} ms")
        print(f"Throughput: {results.anonymous_metrics.get('throughput_rps', 0):.2f} RPS")
    
    # Authenticated user metrics
    if results.authenticated_metrics:
        print("\n" + "-"*40)
        print("AUTHENTICATED USER METRICS")
        print("-"*40)
        print(f"Requests: {results.authenticated_metrics.get('total_requests', 0)}")
        print(f"Success Rate: {results.authenticated_metrics.get('success_rate', 0):.2f}%")
        print(f"Avg Response Time: {results.authenticated_metrics.get('avg_response_time', 0)*1000:.2f} ms")
        print(f"Throughput: {results.authenticated_metrics.get('throughput_rps', 0):.2f} RPS")
    
    # Error summary
    if results.errors:
        print("\n" + "-"*40)
        print("ERROR SUMMARY")
        print("-"*40)
        error_counts = {}
        for error in results.errors:
            key = f"{error['status_code']}: {error['error_message']}"
            error_counts[key] = error_counts.get(key, 0) + 1
        
        for error_type, count in error_counts.items():
            print(f"{error_type}: {count} occurrences")
    
    print("\n" + "="*80)


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Load test the recommendation API endpoint")
    
    # Configuration options
    parser.add_argument("--config", help="Path to JSON configuration file")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL of the API")
    parser.add_argument("--endpoint", default="/recommendations", help="API endpoint to test")
    
    # Load testing parameters
    parser.add_argument("--concurrent-users", type=int, default=10, help="Number of concurrent users")
    parser.add_argument("--total-requests", type=int, default=100, help="Total requests per user type")
    parser.add_argument("--requests-per-second", type=int, default=10, help="Target requests per second")
    
    # Authentication
    parser.add_argument("--auth-tokens", nargs="+", help="List of authentication tokens for testing")
    parser.add_argument("--no-anonymous", action="store_true", help="Skip anonymous user testing")
    parser.add_argument("--no-authenticated", action="store_true", help="Skip authenticated user testing")
    
    # Request parameters
    parser.add_argument("--post-types", nargs="+", help="Post types to include in requests")
    parser.add_argument("--regions", nargs="+", help="Regions to include in requests")
    parser.add_argument("--page-size", type=int, default=20, help="Page size for requests")
    
    # Output options
    parser.add_argument("--output", default="load_test_results.json", help="Output file for results")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        config = load_config_from_file(args.config)
    else:
        config = TestConfig(
            base_url=args.base_url,
            endpoint=args.endpoint,
            concurrent_users=args.concurrent_users,
            total_requests_per_user_type=args.total_requests,
            requests_per_second=args.requests_per_second,
            auth_tokens=args.auth_tokens or [],
            test_anonymous=not args.no_anonymous,
            test_authenticated=not args.no_authenticated,
            post_types=args.post_types,
            regions=args.regions,
            page_size=args.page_size,
            output_file=args.output,
            verbose=args.verbose
        )
    
    # Validate configuration
    if not config.test_anonymous and not config.test_authenticated:
        print("Error: At least one user type (anonymous or authenticated) must be tested")
        sys.exit(1)
    
    if config.test_authenticated and not config.auth_tokens:
        print("Warning: Authenticated testing enabled but no auth tokens provided")
        config.test_authenticated = False
    
    # Run load test
    async with LoadTester(config) as tester:
        results = await tester.run_load_test()
    
    # Save and display results
    save_results(results, config.output_file)
    print_results_summary(results)
    print(f"\nDetailed results saved to: {config.output_file}")


if __name__ == "__main__":
    asyncio.run(main())
